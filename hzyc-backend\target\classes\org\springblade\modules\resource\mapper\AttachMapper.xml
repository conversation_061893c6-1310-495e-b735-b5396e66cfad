<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.resource.mapper.AttachMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="attachResultMap" type="org.springblade.modules.resource.pojo.entity.Attach">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="link" property="link"/>
        <result column="domain_url" property="domainUrl"/>
        <result column="name" property="name"/>
        <result column="original_name" property="originalName"/>
        <result column="extension" property="extension"/>
        <result column="attach_size" property="attachSize"/>
    </resultMap>


    <select id="selectAttachPage" resultMap="attachResultMap">
        select * from blade_attach where is_deleted = 0
    </select>

</mapper>
