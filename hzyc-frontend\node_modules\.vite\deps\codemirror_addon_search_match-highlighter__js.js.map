{"version": 3, "sources": ["../../codemirror/addon/search/match-highlighter.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n// Highlighting text that matches the selection\n//\n// Defines an option highlightSelectionMatches, which, when enabled,\n// will style strings that match the selection throughout the\n// document.\n//\n// The option can be set to true to simply enable it, or to a\n// {minChars, style, wordsOnly, showToken, delay} object to explicitly\n// configure it. minChars is the minimum amount of characters that should be\n// selected for the behavior to occur, and style is the token style to\n// apply to the matches. This will be prefixed by \"cm-\" to create an\n// actual CSS class name. If wordsOnly is enabled, the matches will be\n// highlighted only if the selected text is a word. showToken, when enabled,\n// will cause the current token to be highlighted when nothing is selected.\n// delay is used to specify how much time to wait, in milliseconds, before\n// highlighting the matches. If annotateScrollbar is enabled, the occurrences\n// will be highlighted on the scrollbar via the matchesonscrollbar addon.\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"), require(\"./matchesonscrollbar\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\", \"./matchesonscrollbar\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  var defaults = {\n    style: \"matchhighlight\",\n    minChars: 2,\n    delay: 100,\n    wordsOnly: false,\n    annotateScrollbar: false,\n    showToken: false,\n    trim: true\n  }\n\n  function State(options) {\n    this.options = {}\n    for (var name in defaults)\n      this.options[name] = (options && options.hasOwnProperty(name) ? options : defaults)[name]\n    this.overlay = this.timeout = null;\n    this.matchesonscroll = null;\n    this.active = false;\n  }\n\n  CodeMirror.defineOption(\"highlightSelectionMatches\", false, function(cm, val, old) {\n    if (old && old != CodeMirror.Init) {\n      removeOverlay(cm);\n      clearTimeout(cm.state.matchHighlighter.timeout);\n      cm.state.matchHighlighter = null;\n      cm.off(\"cursorActivity\", cursorActivity);\n      cm.off(\"focus\", onFocus)\n    }\n    if (val) {\n      var state = cm.state.matchHighlighter = new State(val);\n      if (cm.hasFocus()) {\n        state.active = true\n        highlightMatches(cm)\n      } else {\n        cm.on(\"focus\", onFocus)\n      }\n      cm.on(\"cursorActivity\", cursorActivity);\n    }\n  });\n\n  function cursorActivity(cm) {\n    var state = cm.state.matchHighlighter;\n    if (state.active || cm.hasFocus()) scheduleHighlight(cm, state)\n  }\n\n  function onFocus(cm) {\n    var state = cm.state.matchHighlighter\n    if (!state.active) {\n      state.active = true\n      scheduleHighlight(cm, state)\n    }\n  }\n\n  function scheduleHighlight(cm, state) {\n    clearTimeout(state.timeout);\n    state.timeout = setTimeout(function() {highlightMatches(cm);}, state.options.delay);\n  }\n\n  function addOverlay(cm, query, hasBoundary, style) {\n    var state = cm.state.matchHighlighter;\n    cm.addOverlay(state.overlay = makeOverlay(query, hasBoundary, style));\n    if (state.options.annotateScrollbar && cm.showMatchesOnScrollbar) {\n      var searchFor = hasBoundary ? new RegExp((/\\w/.test(query.charAt(0)) ? \"\\\\b\" : \"\") +\n                                               query.replace(/[\\\\\\[.+*?(){|^$]/g, \"\\\\$&\") +\n                                               (/\\w/.test(query.charAt(query.length - 1)) ? \"\\\\b\" : \"\")) : query;\n      state.matchesonscroll = cm.showMatchesOnScrollbar(searchFor, false,\n        {className: \"CodeMirror-selection-highlight-scrollbar\"});\n    }\n  }\n\n  function removeOverlay(cm) {\n    var state = cm.state.matchHighlighter;\n    if (state.overlay) {\n      cm.removeOverlay(state.overlay);\n      state.overlay = null;\n      if (state.matchesonscroll) {\n        state.matchesonscroll.clear();\n        state.matchesonscroll = null;\n      }\n    }\n  }\n\n  function highlightMatches(cm) {\n    cm.operation(function() {\n      var state = cm.state.matchHighlighter;\n      removeOverlay(cm);\n      if (!cm.somethingSelected() && state.options.showToken) {\n        var re = state.options.showToken === true ? /[\\w$]/ : state.options.showToken;\n        var cur = cm.getCursor(), line = cm.getLine(cur.line), start = cur.ch, end = start;\n        while (start && re.test(line.charAt(start - 1))) --start;\n        while (end < line.length && re.test(line.charAt(end))) ++end;\n        if (start < end)\n          addOverlay(cm, line.slice(start, end), re, state.options.style);\n        return;\n      }\n      var from = cm.getCursor(\"from\"), to = cm.getCursor(\"to\");\n      if (from.line != to.line) return;\n      if (state.options.wordsOnly && !isWord(cm, from, to)) return;\n      var selection = cm.getRange(from, to)\n      if (state.options.trim) selection = selection.replace(/^\\s+|\\s+$/g, \"\")\n      if (selection.length >= state.options.minChars)\n        addOverlay(cm, selection, false, state.options.style);\n    });\n  }\n\n  function isWord(cm, from, to) {\n    var str = cm.getRange(from, to);\n    if (str.match(/^\\w+$/) !== null) {\n        if (from.ch > 0) {\n            var pos = {line: from.line, ch: from.ch - 1};\n            var chr = cm.getRange(pos, from);\n            if (chr.match(/\\W/) === null) return false;\n        }\n        if (to.ch < cm.getLine(from.line).length) {\n            var pos = {line: to.line, ch: to.ch + 1};\n            var chr = cm.getRange(to, pos);\n            if (chr.match(/\\W/) === null) return false;\n        }\n        return true;\n    } else return false;\n  }\n\n  function boundariesAround(stream, re) {\n    return (!stream.start || !re.test(stream.string.charAt(stream.start - 1))) &&\n      (stream.pos == stream.string.length || !re.test(stream.string.charAt(stream.pos)));\n  }\n\n  function makeOverlay(query, hasBoundary, style) {\n    return {token: function(stream) {\n      if (stream.match(query) &&\n          (!hasBoundary || boundariesAround(stream, hasBoundary)))\n        return style;\n      stream.next();\n      stream.skipTo(query.charAt(0)) || stream.skipToEnd();\n    }};\n  }\n});\n"], "mappings": ";;;;;;;;;;;;;AAAA;AAAA;AAqBA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,sBAAiC,4BAA+B;AAAA,eAC7D,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,wBAAwB,sBAAsB,GAAG,GAAG;AAAA;AAE5D,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACtB;AAEA,UAAI,WAAW;AAAA,QACb,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,QACP,WAAW;AAAA,QACX,mBAAmB;AAAA,QACnB,WAAW;AAAA,QACX,MAAM;AAAA,MACR;AAEA,eAAS,MAAM,SAAS;AACtB,aAAK,UAAU,CAAC;AAChB,iBAAS,QAAQ;AACf,eAAK,QAAQ,IAAI,KAAK,WAAW,QAAQ,eAAe,IAAI,IAAI,UAAU,UAAU,IAAI;AAC1F,aAAK,UAAU,KAAK,UAAU;AAC9B,aAAK,kBAAkB;AACvB,aAAK,SAAS;AAAA,MAChB;AAEA,MAAAA,YAAW,aAAa,6BAA6B,OAAO,SAAS,IAAI,KAAK,KAAK;AACjF,YAAI,OAAO,OAAOA,YAAW,MAAM;AACjC,wBAAc,EAAE;AAChB,uBAAa,GAAG,MAAM,iBAAiB,OAAO;AAC9C,aAAG,MAAM,mBAAmB;AAC5B,aAAG,IAAI,kBAAkB,cAAc;AACvC,aAAG,IAAI,SAAS,OAAO;AAAA,QACzB;AACA,YAAI,KAAK;AACP,cAAI,QAAQ,GAAG,MAAM,mBAAmB,IAAI,MAAM,GAAG;AACrD,cAAI,GAAG,SAAS,GAAG;AACjB,kBAAM,SAAS;AACf,6BAAiB,EAAE;AAAA,UACrB,OAAO;AACL,eAAG,GAAG,SAAS,OAAO;AAAA,UACxB;AACA,aAAG,GAAG,kBAAkB,cAAc;AAAA,QACxC;AAAA,MACF,CAAC;AAED,eAAS,eAAe,IAAI;AAC1B,YAAI,QAAQ,GAAG,MAAM;AACrB,YAAI,MAAM,UAAU,GAAG,SAAS,EAAG,mBAAkB,IAAI,KAAK;AAAA,MAChE;AAEA,eAAS,QAAQ,IAAI;AACnB,YAAI,QAAQ,GAAG,MAAM;AACrB,YAAI,CAAC,MAAM,QAAQ;AACjB,gBAAM,SAAS;AACf,4BAAkB,IAAI,KAAK;AAAA,QAC7B;AAAA,MACF;AAEA,eAAS,kBAAkB,IAAI,OAAO;AACpC,qBAAa,MAAM,OAAO;AAC1B,cAAM,UAAU,WAAW,WAAW;AAAC,2BAAiB,EAAE;AAAA,QAAE,GAAG,MAAM,QAAQ,KAAK;AAAA,MACpF;AAEA,eAAS,WAAW,IAAI,OAAO,aAAa,OAAO;AACjD,YAAI,QAAQ,GAAG,MAAM;AACrB,WAAG,WAAW,MAAM,UAAU,YAAY,OAAO,aAAa,KAAK,CAAC;AACpE,YAAI,MAAM,QAAQ,qBAAqB,GAAG,wBAAwB;AAChE,cAAI,YAAY,cAAc,IAAI,QAAQ,KAAK,KAAK,MAAM,OAAO,CAAC,CAAC,IAAI,QAAQ,MACtC,MAAM,QAAQ,qBAAqB,MAAM,KACxC,KAAK,KAAK,MAAM,OAAO,MAAM,SAAS,CAAC,CAAC,IAAI,QAAQ,GAAG,IAAI;AACrG,gBAAM,kBAAkB,GAAG;AAAA,YAAuB;AAAA,YAAW;AAAA,YAC3D,EAAC,WAAW,2CAA0C;AAAA,UAAC;AAAA,QAC3D;AAAA,MACF;AAEA,eAAS,cAAc,IAAI;AACzB,YAAI,QAAQ,GAAG,MAAM;AACrB,YAAI,MAAM,SAAS;AACjB,aAAG,cAAc,MAAM,OAAO;AAC9B,gBAAM,UAAU;AAChB,cAAI,MAAM,iBAAiB;AACzB,kBAAM,gBAAgB,MAAM;AAC5B,kBAAM,kBAAkB;AAAA,UAC1B;AAAA,QACF;AAAA,MACF;AAEA,eAAS,iBAAiB,IAAI;AAC5B,WAAG,UAAU,WAAW;AACtB,cAAI,QAAQ,GAAG,MAAM;AACrB,wBAAc,EAAE;AAChB,cAAI,CAAC,GAAG,kBAAkB,KAAK,MAAM,QAAQ,WAAW;AACtD,gBAAI,KAAK,MAAM,QAAQ,cAAc,OAAO,UAAU,MAAM,QAAQ;AACpE,gBAAI,MAAM,GAAG,UAAU,GAAG,OAAO,GAAG,QAAQ,IAAI,IAAI,GAAG,QAAQ,IAAI,IAAI,MAAM;AAC7E,mBAAO,SAAS,GAAG,KAAK,KAAK,OAAO,QAAQ,CAAC,CAAC,EAAG,GAAE;AACnD,mBAAO,MAAM,KAAK,UAAU,GAAG,KAAK,KAAK,OAAO,GAAG,CAAC,EAAG,GAAE;AACzD,gBAAI,QAAQ;AACV,yBAAW,IAAI,KAAK,MAAM,OAAO,GAAG,GAAG,IAAI,MAAM,QAAQ,KAAK;AAChE;AAAA,UACF;AACA,cAAI,OAAO,GAAG,UAAU,MAAM,GAAG,KAAK,GAAG,UAAU,IAAI;AACvD,cAAI,KAAK,QAAQ,GAAG,KAAM;AAC1B,cAAI,MAAM,QAAQ,aAAa,CAAC,OAAO,IAAI,MAAM,EAAE,EAAG;AACtD,cAAI,YAAY,GAAG,SAAS,MAAM,EAAE;AACpC,cAAI,MAAM,QAAQ,KAAM,aAAY,UAAU,QAAQ,cAAc,EAAE;AACtE,cAAI,UAAU,UAAU,MAAM,QAAQ;AACpC,uBAAW,IAAI,WAAW,OAAO,MAAM,QAAQ,KAAK;AAAA,QACxD,CAAC;AAAA,MACH;AAEA,eAAS,OAAO,IAAI,MAAM,IAAI;AAC5B,YAAI,MAAM,GAAG,SAAS,MAAM,EAAE;AAC9B,YAAI,IAAI,MAAM,OAAO,MAAM,MAAM;AAC7B,cAAI,KAAK,KAAK,GAAG;AACb,gBAAI,MAAM,EAAC,MAAM,KAAK,MAAM,IAAI,KAAK,KAAK,EAAC;AAC3C,gBAAI,MAAM,GAAG,SAAS,KAAK,IAAI;AAC/B,gBAAI,IAAI,MAAM,IAAI,MAAM,KAAM,QAAO;AAAA,UACzC;AACA,cAAI,GAAG,KAAK,GAAG,QAAQ,KAAK,IAAI,EAAE,QAAQ;AACtC,gBAAI,MAAM,EAAC,MAAM,GAAG,MAAM,IAAI,GAAG,KAAK,EAAC;AACvC,gBAAI,MAAM,GAAG,SAAS,IAAI,GAAG;AAC7B,gBAAI,IAAI,MAAM,IAAI,MAAM,KAAM,QAAO;AAAA,UACzC;AACA,iBAAO;AAAA,QACX,MAAO,QAAO;AAAA,MAChB;AAEA,eAAS,iBAAiB,QAAQ,IAAI;AACpC,gBAAQ,CAAC,OAAO,SAAS,CAAC,GAAG,KAAK,OAAO,OAAO,OAAO,OAAO,QAAQ,CAAC,CAAC,OACrE,OAAO,OAAO,OAAO,OAAO,UAAU,CAAC,GAAG,KAAK,OAAO,OAAO,OAAO,OAAO,GAAG,CAAC;AAAA,MACpF;AAEA,eAAS,YAAY,OAAO,aAAa,OAAO;AAC9C,eAAO,EAAC,OAAO,SAAS,QAAQ;AAC9B,cAAI,OAAO,MAAM,KAAK,MACjB,CAAC,eAAe,iBAAiB,QAAQ,WAAW;AACvD,mBAAO;AACT,iBAAO,KAAK;AACZ,iBAAO,OAAO,MAAM,OAAO,CAAC,CAAC,KAAK,OAAO,UAAU;AAAA,QACrD,EAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA;AAAA;", "names": ["CodeMirror"]}