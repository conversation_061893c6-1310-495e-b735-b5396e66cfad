{"version": 3, "sources": ["../../disable-devtool/disable-devtool.min.js"], "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define(t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).DisableDevtool=t()}(this,function(){\"use strict\";function o(e){return(o=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\"function\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\"symbol\":typeof e})(e)}function i(e,t){if(!(e instanceof t))throw new TypeError(\"Cannot call a class as a function\")}function r(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,\"value\"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function u(e,t,n){t&&r(e.prototype,t),n&&r(e,n),Object.defineProperty(e,\"prototype\",{writable:!1})}function e(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}function n(e,t){if(\"function\"!=typeof t&&null!==t)throw new TypeError(\"Super expression must either be null or a function\");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,\"prototype\",{writable:!1}),t&&a(e,t)}function c(e){return(c=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function a(e,t){return(a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function H(e,t){if(t&&(\"object\"==typeof t||\"function\"==typeof t))return t;if(void 0!==t)throw new TypeError(\"Derived constructors may only return object or undefined\");t=e;if(void 0===t)throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");return t}function l(n){var i=function(){if(\"undefined\"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if(\"function\"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var e,t=c(n);return H(this,i?(e=c(this).constructor,Reflect.construct(t,arguments,e)):t.apply(this,arguments))}}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function s(e,t){var n,i=\"undefined\"!=typeof Symbol&&e[Symbol.iterator]||e[\"@@iterator\"];if(!i){if(Array.isArray(e)||(i=function(e,t){if(e){if(\"string\"==typeof e)return f(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return\"Map\"===(n=\"Object\"===n&&e.constructor?e.constructor.name:n)||\"Set\"===n?Array.from(e):\"Arguments\"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(e,t):void 0}}(e))||t&&e&&\"number\"==typeof e.length)return i&&(e=i),n=0,{s:t=function(){},n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:t};throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\")}var o,r=!0,u=!1;return{s:function(){i=i.call(e)},n:function(){var e=i.next();return r=e.done,e},e:function(e){u=!0,o=e},f:function(){try{r||null==i.return||i.return()}finally{if(u)throw o}}}}function t(){if(d.url)window.location.href=d.url;else if(d.rewriteHTML)try{document.documentElement.innerHTML=d.rewriteHTML}catch(e){document.documentElement.innerText=d.rewriteHTML}else{try{window.opener=null,window.open(\"\",\"_self\"),window.close(),window.history.back()}catch(e){console.log(e)}setTimeout(function(){window.location.href=d.timeOutUrl||\"https://theajack.github.io/disable-devtool/404.html?h=\".concat(encodeURIComponent(location.host))},500)}}var d={md5:\"\",ondevtoolopen:t,ondevtoolclose:null,url:\"\",timeOutUrl:\"\",tkName:\"ddtk\",interval:500,disableMenu:!0,stopIntervalTime:5e3,clearIntervalWhenDevOpenTrigger:!1,detectors:[0,1,3,4,5,6,7],clearLog:!0,disableSelect:!1,disableCopy:!1,disableCut:!1,disablePaste:!1,ignore:null,disableIframeParents:!0,seo:!0,rewriteHTML:\"\"},U=[\"detectors\",\"ondevtoolclose\",\"ignore\"];function q(e){var t,n=0<arguments.length&&void 0!==e?e:{};for(t in d){var i=t;void 0===n[i]||o(d[i])!==o(n[i])&&-1===U.indexOf(i)||(d[i]=n[i])}\"function\"==typeof d.ondevtoolclose&&!0===d.clearIntervalWhenDevOpenTrigger&&(d.clearIntervalWhenDevOpenTrigger=!1,console.warn(\"【DISABLE-DEVTOOL】clearIntervalWhenDevOpenTrigger 在使用 ondevtoolclose 时无效\"))}function v(){return(new Date).getTime()}function h(e){var t=v();return e(),v()-t}function z(n,i){function e(t){return function(){n&&n();var e=t.apply(void 0,arguments);return i&&i(),e}}var t=window.alert,o=window.confirm,r=window.prompt;try{window.alert=e(t),window.confirm=e(o),window.prompt=e(r)}catch(e){}}var p,y,B,b={iframe:!1,pc:!1,qqBrowser:!1,firefox:!1,macos:!1,edge:!1,oldEdge:!1,ie:!1,iosChrome:!1,iosEdge:!1,chrome:!1,seoBot:!1,mobile:!1};function W(){function e(e){return-1!==t.indexOf(e)}var t=navigator.userAgent.toLowerCase(),n=function(){var e=navigator,t=e.platform,e=e.maxTouchPoints;if(\"number\"==typeof e)return 1<e;if(\"string\"==typeof t){e=t.toLowerCase();if(/(mac|win)/i.test(e))return!1;if(/(android|iphone|ipad|ipod|arch)/i.test(e))return!0}return/(iphone|ipad|ipod|ios|android)/i.test(navigator.userAgent.toLowerCase())}(),i=!!window.top&&window!==window.top,o=!n,r=e(\"qqbrowser\"),u=e(\"firefox\"),c=e(\"macintosh\"),a=e(\"edge\"),l=a&&!e(\"chrome\"),f=l||e(\"trident\")||e(\"msie\"),s=e(\"crios\"),d=e(\"edgios\"),v=e(\"chrome\")||s,h=!n&&/(googlebot|baiduspider|bingbot|applebot|petalbot|yandexbot|bytespider|chrome\\-lighthouse|moto g power)/i.test(t);Object.assign(b,{iframe:i,pc:o,qqBrowser:r,firefox:u,macos:c,edge:a,oldEdge:l,ie:f,iosChrome:s,iosEdge:d,chrome:v,seoBot:h,mobile:n})}function M(){for(var e=function(){for(var e={},t=0;t<500;t++)e[\"\".concat(t)]=\"\".concat(t);return e}(),t=[],n=0;n<50;n++)t.push(e);return t}function w(){d.clearLog&&B()}var K=\"\",V=!1;function F(){var e=d.ignore;if(e){if(\"function\"==typeof e)return e();if(0!==e.length){var t=location.href;if(K===t)return V;K=t;var n,i=!1,o=s(e);try{for(o.s();!(n=o.n()).done;){var r=n.value;if(\"string\"==typeof r){if(-1!==t.indexOf(r)){i=!0;break}}else if(r.test(t)){i=!0;break}}}catch(e){o.e(e)}finally{o.f()}return V=i}}}var X=function(){return!1};function g(n){var t,e,i=74,o=73,r=85,u=83,c=123,a=b.macos?function(e,t){return e.metaKey&&e.altKey&&(t===o||t===i)}:function(e,t){return e.ctrlKey&&e.shiftKey&&(t===o||t===i)},l=b.macos?function(e,t){return e.metaKey&&e.altKey&&t===r||e.metaKey&&t===u}:function(e,t){return e.ctrlKey&&(t===u||t===r)};n.addEventListener(\"keydown\",function(e){var t=(e=e||n.event).keyCode||e.which;if(t===c||a(e,t)||l(e,t))return T(n,e)},!0),t=n,d.disableMenu&&t.addEventListener(\"contextmenu\",function(e){if(\"touch\"!==e.pointerType)return T(t,e)}),e=n,d.disableSelect&&m(e,\"selectstart\"),e=n,d.disableCopy&&m(e,\"copy\"),e=n,d.disableCut&&m(e,\"cut\"),e=n,d.disablePaste&&m(e,\"paste\")}function m(t,e){t.addEventListener(e,function(e){return T(t,e)})}function T(e,t){if(!F()&&!X())return(t=t||e.event).returnValue=!1,t.preventDefault(),!1}var O,D=!1,S={};function N(e){S[e]=!1}function $(){for(var e in S)if(S[e])return D=!0;return D=!1}(_=O=O||{})[_.Unknown=-1]=\"Unknown\",_[_.RegToString=0]=\"RegToString\",_[_.DefineId=1]=\"DefineId\",_[_.Size=2]=\"Size\",_[_.DateToString=3]=\"DateToString\",_[_.FuncToString=4]=\"FuncToString\",_[_.Debugger=5]=\"Debugger\",_[_.Performance=6]=\"Performance\",_[_.DebugLib=7]=\"DebugLib\";var k=function(){function n(e){var t=e.type,e=e.enabled,e=void 0===e||e;i(this,n),this.type=O.Unknown,this.enabled=!0,this.type=t,this.enabled=e,this.enabled&&(t=this,Q.push(t),this.init())}return u(n,[{key:\"onDevToolOpen\",value:function(){var e;console.warn(\"You don't have permission to use DEVTOOL!【type = \".concat(this.type,\"】\")),d.clearIntervalWhenDevOpenTrigger&&te(),window.clearTimeout(J),d.ondevtoolopen(this.type,t),e=this.type,S[e]=!0}},{key:\"init\",value:function(){}}]),n}(),G=function(){n(t,k);var e=l(t);function t(){return i(this,t),e.call(this,{type:O.DebugLib})}return u(t,[{key:\"init\",value:function(){}},{key:\"detect\",value:function(){var e;(!0===(null==(e=null==(e=window.eruda)?void 0:e._devTools)?void 0:e._isShow)||window._vcOrigConsole&&window.document.querySelector(\"#__vconsole.vc-toggle\"))&&this.onDevToolOpen()}}],[{key:\"isUsing\",value:function(){return!!window.eruda||!!window._vcOrigConsole}}]),t}(),Y=0,J=0,Q=[],Z=0;function ee(o){function e(){l=!0}function t(){l=!1}var n,i,r,u,c,a,l=!1;function f(){(a[u]===r?i:n)()}z(e,t),n=t,i=e,void 0!==(a=document).hidden?(r=\"hidden\",c=\"visibilitychange\",u=\"visibilityState\"):void 0!==a.mozHidden?(r=\"mozHidden\",c=\"mozvisibilitychange\",u=\"mozVisibilityState\"):void 0!==a.msHidden?(r=\"msHidden\",c=\"msvisibilitychange\",u=\"msVisibilityState\"):void 0!==a.webkitHidden&&(r=\"webkitHidden\",c=\"webkitvisibilitychange\",u=\"webkitVisibilityState\"),a.removeEventListener(c,f,!1),a.addEventListener(c,f,!1),Y=window.setInterval(function(){if(!(o.isSuspend||l||F())){var e,t,n=s(Q);try{for(n.s();!(e=n.n()).done;){var i=e.value;N(i.type),i.detect(Z++)}}catch(e){n.e(e)}finally{n.f()}w(),\"function\"==typeof d.ondevtoolclose&&(t=D,!$()&&t&&d.ondevtoolclose())}},d.interval),J=setTimeout(function(){b.pc||G.isUsing()||te()},d.stopIntervalTime)}function te(){window.clearInterval(Y)}var P=8;function ne(e){for(var t=function(e,t){e[t>>5]|=128<<t%32,e[14+(t+64>>>9<<4)]=t;for(var n=1732584193,i=-271733879,o=-1732584194,r=271733878,u=0;u<e.length;u+=16){var c=n,a=i,l=o,f=r;n=E(n,i,o,r,e[u+0],7,-680876936),r=E(r,n,i,o,e[u+1],12,-389564586),o=E(o,r,n,i,e[u+2],17,606105819),i=E(i,o,r,n,e[u+3],22,-1044525330),n=E(n,i,o,r,e[u+4],7,-176418897),r=E(r,n,i,o,e[u+5],12,1200080426),o=E(o,r,n,i,e[u+6],17,-1473231341),i=E(i,o,r,n,e[u+7],22,-45705983),n=E(n,i,o,r,e[u+8],7,1770035416),r=E(r,n,i,o,e[u+9],12,-1958414417),o=E(o,r,n,i,e[u+10],17,-42063),i=E(i,o,r,n,e[u+11],22,-1990404162),n=E(n,i,o,r,e[u+12],7,1804603682),r=E(r,n,i,o,e[u+13],12,-40341101),o=E(o,r,n,i,e[u+14],17,-1502002290),i=E(i,o,r,n,e[u+15],22,1236535329),n=j(n,i,o,r,e[u+1],5,-165796510),r=j(r,n,i,o,e[u+6],9,-1069501632),o=j(o,r,n,i,e[u+11],14,643717713),i=j(i,o,r,n,e[u+0],20,-373897302),n=j(n,i,o,r,e[u+5],5,-701558691),r=j(r,n,i,o,e[u+10],9,38016083),o=j(o,r,n,i,e[u+15],14,-660478335),i=j(i,o,r,n,e[u+4],20,-405537848),n=j(n,i,o,r,e[u+9],5,568446438),r=j(r,n,i,o,e[u+14],9,-1019803690),o=j(o,r,n,i,e[u+3],14,-187363961),i=j(i,o,r,n,e[u+8],20,1163531501),n=j(n,i,o,r,e[u+13],5,-1444681467),r=j(r,n,i,o,e[u+2],9,-51403784),o=j(o,r,n,i,e[u+7],14,1735328473),i=j(i,o,r,n,e[u+12],20,-1926607734),n=I(n,i,o,r,e[u+5],4,-378558),r=I(r,n,i,o,e[u+8],11,-2022574463),o=I(o,r,n,i,e[u+11],16,1839030562),i=I(i,o,r,n,e[u+14],23,-35309556),n=I(n,i,o,r,e[u+1],4,-1530992060),r=I(r,n,i,o,e[u+4],11,1272893353),o=I(o,r,n,i,e[u+7],16,-155497632),i=I(i,o,r,n,e[u+10],23,-1094730640),n=I(n,i,o,r,e[u+13],4,681279174),r=I(r,n,i,o,e[u+0],11,-358537222),o=I(o,r,n,i,e[u+3],16,-722521979),i=I(i,o,r,n,e[u+6],23,76029189),n=I(n,i,o,r,e[u+9],4,-640364487),r=I(r,n,i,o,e[u+12],11,-421815835),o=I(o,r,n,i,e[u+15],16,530742520),i=I(i,o,r,n,e[u+2],23,-995338651),n=L(n,i,o,r,e[u+0],6,-198630844),r=L(r,n,i,o,e[u+7],10,1126891415),o=L(o,r,n,i,e[u+14],15,-1416354905),i=L(i,o,r,n,e[u+5],21,-57434055),n=L(n,i,o,r,e[u+12],6,1700485571),r=L(r,n,i,o,e[u+3],10,-1894986606),o=L(o,r,n,i,e[u+10],15,-1051523),i=L(i,o,r,n,e[u+1],21,-2054922799),n=L(n,i,o,r,e[u+8],6,1873313359),r=L(r,n,i,o,e[u+15],10,-30611744),o=L(o,r,n,i,e[u+6],15,-1560198380),i=L(i,o,r,n,e[u+13],21,1309151649),n=L(n,i,o,r,e[u+4],6,-145523070),r=L(r,n,i,o,e[u+11],10,-1120210379),o=L(o,r,n,i,e[u+2],15,718787259),i=L(i,o,r,n,e[u+9],21,-343485551),n=C(n,c),i=C(i,a),o=C(o,l),r=C(r,f)}return Array(n,i,o,r)}(function(e){for(var t=Array(),n=(1<<P)-1,i=0;i<e.length*P;i+=P)t[i>>5]|=(e.charCodeAt(i/P)&n)<<i%32;return t}(e),e.length*P),n=\"0123456789abcdef\",i=\"\",o=0;o<4*t.length;o++)i+=n.charAt(t[o>>2]>>o%4*8+4&15)+n.charAt(t[o>>2]>>o%4*8&15);return i}function x(e,t,n,i,o,r){return C((t=C(C(t,e),C(i,r)))<<o|t>>>32-o,n)}function E(e,t,n,i,o,r,u){return x(t&n|~t&i,e,t,o,r,u)}function j(e,t,n,i,o,r,u){return x(t&i|n&~i,e,t,o,r,u)}function I(e,t,n,i,o,r,u){return x(t^n^i,e,t,o,r,u)}function L(e,t,n,i,o,r,u){return x(n^(t|~i),e,t,o,r,u)}function C(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}var _=function(){n(t,k);var e=l(t);function t(){return i(this,t),e.call(this,{type:O.RegToString,enabled:b.qqBrowser||b.firefox})}return u(t,[{key:\"init\",value:function(){var t=this;this.lastTime=0,this.reg=/./,p(this.reg),this.reg.toString=function(){var e;return b.qqBrowser?(e=(new Date).getTime(),t.lastTime&&e-t.lastTime<100?t.onDevToolOpen():t.lastTime=e):b.firefox&&t.onDevToolOpen(),\"\"}}},{key:\"detect\",value:function(){p(this.reg)}}]),t}(),ie=function(){n(t,k);var e=l(t);function t(){return i(this,t),e.call(this,{type:O.DefineId})}return u(t,[{key:\"init\",value:function(){var e=this;this.div=document.createElement(\"div\"),this.div.__defineGetter__(\"id\",function(){e.onDevToolOpen()}),Object.defineProperty(this.div,\"id\",{get:function(){e.onDevToolOpen()}})}},{key:\"detect\",value:function(){p(this.div)}}]),t}(),oe=function(){n(t,k);var e=l(t);function t(){return i(this,t),e.call(this,{type:O.Size,enabled:!b.iframe&&!b.edge})}return u(t,[{key:\"init\",value:function(){var e=this;this.checkWindowSizeUneven(),window.addEventListener(\"resize\",function(){setTimeout(function(){e.checkWindowSizeUneven()},100)},!0)}},{key:\"detect\",value:function(){}},{key:\"checkWindowSizeUneven\",value:function(){var e=function(){if(re(window.devicePixelRatio))return window.devicePixelRatio;var e=window.screen;return!(re(e)||!e.deviceXDPI||!e.logicalXDPI)&&e.deviceXDPI/e.logicalXDPI}();if(!1!==e){var t=200<window.outerWidth-window.innerWidth*e,e=300<window.outerHeight-window.innerHeight*e;if(t||e)return this.onDevToolOpen(),!1;N(this.type)}return!0}}]),t}();function re(e){return null!=e}var A,ue=function(){n(t,k);var e=l(t);function t(){return i(this,t),e.call(this,{type:O.DateToString,enabled:!b.iosChrome&&!b.iosEdge})}return u(t,[{key:\"init\",value:function(){var e=this;this.count=0,this.date=new Date,this.date.toString=function(){return e.count++,\"\"}}},{key:\"detect\",value:function(){this.count=0,p(this.date),w(),2<=this.count&&this.onDevToolOpen()}}]),t}(),ce=function(){n(t,k);var e=l(t);function t(){return i(this,t),e.call(this,{type:O.FuncToString,enabled:!b.iosChrome&&!b.iosEdge})}return u(t,[{key:\"init\",value:function(){var e=this;this.count=0,this.func=function(){},this.func.toString=function(){return e.count++,\"\"}}},{key:\"detect\",value:function(){this.count=0,p(this.func),w(),2<=this.count&&this.onDevToolOpen()}}]),t}(),ae=function(){n(t,k);var e=l(t);function t(){return i(this,t),e.call(this,{type:O.Debugger,enabled:b.iosChrome||b.iosEdge})}return u(t,[{key:\"detect\",value:function(){var e=v();100<v()-e&&this.onDevToolOpen()}}]),t}(),le=function(){n(t,k);var e=l(t);function t(){return i(this,t),e.call(this,{type:O.Performance,enabled:b.chrome||!b.mobile})}return u(t,[{key:\"init\",value:function(){this.maxPrintTime=0,this.largeObjectArray=M()}},{key:\"detect\",value:function(){var e=this,t=h(function(){y(e.largeObjectArray)}),n=h(function(){p(e.largeObjectArray)});if(this.maxPrintTime=Math.max(this.maxPrintTime,n),w(),0===t||0===this.maxPrintTime)return!1;t>10*this.maxPrintTime&&this.onDevToolOpen()}}]),t}(),fe=(e(A={},O.RegToString,_),e(A,O.DefineId,ie),e(A,O.Size,oe),e(A,O.DateToString,ue),e(A,O.FuncToString,ce),e(A,O.Debugger,ae),e(A,O.Performance,le),e(A,O.DebugLib,G),A);var R=Object.assign(function(e){function t(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:\"\";return{success:!e,reason:e}}var n;if(R.isRunning)return t(\"already running\");if(W(),n=window.console||{log:function(){},table:function(){},clear:function(){}},B=b.ie?(p=function(){return n.log.apply(n,arguments)},y=function(){return n.table.apply(n,arguments)},function(){return n.clear()}):(p=n.log,y=n.table,n.clear),q(e),d.md5&&ne(function(e){var t=window.location.search,n=window.location.hash;if(\"\"!==(t=\"\"===t&&\"\"!==n?\"?\".concat(n.split(\"?\")[1]):t)&&void 0!==t){n=new RegExp(\"(^|&)\"+e+\"=([^&]*)(&|$)\",\"i\"),e=t.substr(1).match(n);if(null!=e)return unescape(e[2])}return\"\"}(d.tkName))===d.md5)return t(\"token passed\");if(d.seo&&b.seoBot)return t(\"seobot\");R.isRunning=!0,ee(R);var i=R,o=(X=function(){return i.isSuspend},window.top),r=window.parent;if(g(window),d.disableIframeParents&&o&&r&&o!==window){for(;r!==o;)g(r),r=r.parent;g(o)}return(\"all\"===d.detectors?Object.keys(fe):d.detectors).forEach(function(e){new fe[e]}),t()},{isRunning:!1,isSuspend:!1,md5:ne,version:\"0.3.8\",DetectorType:O,isDevToolOpened:$});_=function(){if(\"undefined\"==typeof window||!window.document)return null;var n=document.querySelector(\"[disable-devtool-auto]\");if(!n)return null;var i=[\"disable-menu\",\"disable-select\",\"disable-copy\",\"disable-cut\",\"disable-paste\",\"clear-log\"],o=[\"interval\"],r={};return[\"md5\",\"url\",\"tk-name\",\"detectors\"].concat(i,o).forEach(function(e){var t=n.getAttribute(e);null!==t&&(-1!==o.indexOf(e)?t=parseInt(t):-1!==i.indexOf(e)?t=\"false\"!==t:\"detector\"===e&&\"all\"!==t&&(t=t.split(\" \")),r[function(e){if(-1===e.indexOf(\"-\"))return e;var t=!1;return e.split(\"\").map(function(e){return\"-\"===e?(t=!0,\"\"):t?(t=!1,e.toUpperCase()):e}).join(\"\")}(e)]=t)}),r}();return _&&R(_),R});\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,iBAAe,EAAE;AAAA,IAAC,EAAE,SAAK,WAAU;AAAC;AAAa,eAAS,EAAEA,IAAE;AAAC,gBAAO,IAAE,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,iBAAO,OAAOA;AAAA,QAAC,IAAE,SAASA,IAAE;AAAC,iBAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,QAAC,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,EAAEA,IAAEC,IAAE;AAAC,YAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAA,MAAC;AAAC,eAAS,EAAED,IAAEC,IAAE;AAAC,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAIC,KAAEF,GAAEC,EAAC;AAAE,UAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAEG,GAAE,KAAIA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,EAAEH,IAAEC,IAAEC,IAAE;AAAC,QAAAD,MAAG,EAAED,GAAE,WAAUC,EAAC,GAAEC,MAAG,EAAEF,IAAEE,EAAC,GAAE,OAAO,eAAeF,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC;AAAA,MAAC;AAAC,eAAS,EAAEA,IAAEC,IAAEC,IAAE;AAAC,QAAAD,MAAKD,KAAE,OAAO,eAAeA,IAAEC,IAAE,EAAC,OAAMC,IAAE,YAAW,MAAG,cAAa,MAAG,UAAS,KAAE,CAAC,IAAEF,GAAEC,EAAC,IAAEC;AAAA,MAAC;AAAC,eAAS,EAAEF,IAAEC,IAAE;AAAC,YAAG,cAAY,OAAOA,MAAG,SAAOA,GAAE,OAAM,IAAI,UAAU,oDAAoD;AAAE,QAAAD,GAAE,YAAU,OAAO,OAAOC,MAAGA,GAAE,WAAU,EAAC,aAAY,EAAC,OAAMD,IAAE,UAAS,MAAG,cAAa,KAAE,EAAC,CAAC,GAAE,OAAO,eAAeA,IAAE,aAAY,EAAC,UAAS,MAAE,CAAC,GAAEC,MAAG,EAAED,IAAEC,EAAC;AAAA,MAAC;AAAC,eAAS,EAAED,IAAE;AAAC,gBAAO,IAAE,OAAO,iBAAe,OAAO,eAAe,KAAK,IAAE,SAASA,IAAE;AAAC,iBAAOA,GAAE,aAAW,OAAO,eAAeA,EAAC;AAAA,QAAC,GAAGA,EAAC;AAAA,MAAC;AAAC,eAAS,EAAEA,IAAEC,IAAE;AAAC,gBAAO,IAAE,OAAO,iBAAe,OAAO,eAAe,KAAK,IAAE,SAASD,IAAEC,IAAE;AAAC,iBAAOD,GAAE,YAAUC,IAAED;AAAA,QAAC,GAAGA,IAAEC,EAAC;AAAA,MAAC;AAAC,eAAS,EAAED,IAAEC,IAAE;AAAC,YAAGA,OAAI,YAAU,OAAOA,MAAG,cAAY,OAAOA,IAAG,QAAOA;AAAE,YAAG,WAASA,GAAE,OAAM,IAAI,UAAU,0DAA0D;AAAE,QAAAA,KAAED;AAAE,YAAG,WAASC,GAAE,OAAM,IAAI,eAAe,2DAA2D;AAAE,eAAOA;AAAA,MAAC;AAAC,eAAS,EAAEC,IAAE;AAAC,YAAIC,KAAE,WAAU;AAAC,cAAG,eAAa,OAAO,WAAS,CAAC,QAAQ,UAAU,QAAM;AAAG,cAAG,QAAQ,UAAU,KAAK,QAAM;AAAG,cAAG,cAAY,OAAO,MAAM,QAAM;AAAG,cAAG;AAAC,mBAAO,QAAQ,UAAU,QAAQ,KAAK,QAAQ,UAAU,SAAQ,CAAC,GAAE,WAAU;AAAA,YAAC,CAAC,CAAC,GAAE;AAAA,UAAE,SAAOH,IAAE;AAAC,mBAAM;AAAA,UAAE;AAAA,QAAC,EAAE;AAAE,eAAO,WAAU;AAAC,cAAIA,IAAEC,KAAE,EAAEC,EAAC;AAAE,iBAAO,EAAE,MAAKC,MAAGH,KAAE,EAAE,IAAI,EAAE,aAAY,QAAQ,UAAUC,IAAE,WAAUD,EAAC,KAAGC,GAAE,MAAM,MAAK,SAAS,CAAC;AAAA,QAAC;AAAA,MAAC;AAAC,eAAS,EAAED,IAAEC,IAAE;AAAC,SAAC,QAAMA,MAAGA,KAAED,GAAE,YAAUC,KAAED,GAAE;AAAQ,iBAAQE,KAAE,GAAEC,KAAE,IAAI,MAAMF,EAAC,GAAEC,KAAED,IAAEC,KAAI,CAAAC,GAAED,EAAC,IAAEF,GAAEE,EAAC;AAAE,eAAOC;AAAA,MAAC;AAAC,eAAS,EAAEH,IAAEC,IAAE;AAAC,YAAIC,IAAEC,KAAE,eAAa,OAAO,UAAQH,GAAE,OAAO,QAAQ,KAAGA,GAAE,YAAY;AAAE,YAAG,CAACG,IAAE;AAAC,cAAG,MAAM,QAAQH,EAAC,MAAIG,KAAE,SAASH,IAAEC,IAAE;AAAC,gBAAGD,IAAE;AAAC,kBAAG,YAAU,OAAOA,GAAE,QAAO,EAAEA,IAAEC,EAAC;AAAE,kBAAIC,KAAE,OAAO,UAAU,SAAS,KAAKF,EAAC,EAAE,MAAM,GAAE,EAAE;AAAE,qBAAM,WAASE,KAAE,aAAWA,MAAGF,GAAE,cAAYA,GAAE,YAAY,OAAKE,OAAI,UAAQA,KAAE,MAAM,KAAKF,EAAC,IAAE,gBAAcE,MAAG,2CAA2C,KAAKA,EAAC,IAAE,EAAEF,IAAEC,EAAC,IAAE;AAAA,YAAM;AAAA,UAAC,EAAED,EAAC,MAAIC,MAAGD,MAAG,YAAU,OAAOA,GAAE,OAAO,QAAOG,OAAIH,KAAEG,KAAGD,KAAE,GAAE,EAAC,GAAED,KAAE,WAAU;AAAA,UAAC,GAAE,GAAE,WAAU;AAAC,mBAAOC,MAAGF,GAAE,SAAO,EAAC,MAAK,KAAE,IAAE,EAAC,MAAK,OAAG,OAAMA,GAAEE,IAAG,EAAC;AAAA,UAAC,GAAE,GAAE,SAASF,IAAE;AAAC,kBAAMA;AAAA,UAAC,GAAE,GAAEC,GAAC;AAAE,gBAAM,IAAI,UAAU,uIAAuI;AAAA,QAAC;AAAC,YAAIG,IAAEC,KAAE,MAAGC,KAAE;AAAG,eAAM,EAAC,GAAE,WAAU;AAAC,UAAAH,KAAEA,GAAE,KAAKH,EAAC;AAAA,QAAC,GAAE,GAAE,WAAU;AAAC,cAAIA,KAAEG,GAAE,KAAK;AAAE,iBAAOE,KAAEL,GAAE,MAAKA;AAAA,QAAC,GAAE,GAAE,SAASA,IAAE;AAAC,UAAAM,KAAE,MAAGF,KAAEJ;AAAA,QAAC,GAAE,GAAE,WAAU;AAAC,cAAG;AAAC,YAAAK,MAAG,QAAMF,GAAE,UAAQA,GAAE,OAAO;AAAA,UAAC,UAAC;AAAQ,gBAAGG,GAAE,OAAMF;AAAA,UAAC;AAAA,QAAC,EAAC;AAAA,MAAC;AAAC,eAAS,IAAG;AAAC,YAAG,EAAE,IAAI,QAAO,SAAS,OAAK,EAAE;AAAA,iBAAY,EAAE,YAAY,KAAG;AAAC,mBAAS,gBAAgB,YAAU,EAAE;AAAA,QAAW,SAAOJ,IAAE;AAAC,mBAAS,gBAAgB,YAAU,EAAE;AAAA,QAAW;AAAA,aAAK;AAAC,cAAG;AAAC,mBAAO,SAAO,MAAK,OAAO,KAAK,IAAG,OAAO,GAAE,OAAO,MAAM,GAAE,OAAO,QAAQ,KAAK;AAAA,UAAC,SAAOA,IAAE;AAAC,oBAAQ,IAAIA,EAAC;AAAA,UAAC;AAAC,qBAAW,WAAU;AAAC,mBAAO,SAAS,OAAK,EAAE,cAAY,yDAAyD,OAAO,mBAAmB,SAAS,IAAI,CAAC;AAAA,UAAC,GAAE,GAAG;AAAA,QAAC;AAAA,MAAC;AAAC,UAAI,IAAE,EAAC,KAAI,IAAG,eAAc,GAAE,gBAAe,MAAK,KAAI,IAAG,YAAW,IAAG,QAAO,QAAO,UAAS,KAAI,aAAY,MAAG,kBAAiB,KAAI,iCAAgC,OAAG,WAAU,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,UAAS,MAAG,eAAc,OAAG,aAAY,OAAG,YAAW,OAAG,cAAa,OAAG,QAAO,MAAK,sBAAqB,MAAG,KAAI,MAAG,aAAY,GAAE,GAAE,IAAE,CAAC,aAAY,kBAAiB,QAAQ;AAAE,eAAS,EAAEA,IAAE;AAAC,YAAIC,IAAEC,KAAE,IAAE,UAAU,UAAQ,WAASF,KAAEA,KAAE,CAAC;AAAE,aAAIC,MAAK,GAAE;AAAC,cAAIE,KAAEF;AAAE,qBAASC,GAAEC,EAAC,KAAG,EAAE,EAAEA,EAAC,CAAC,MAAI,EAAED,GAAEC,EAAC,CAAC,KAAG,OAAK,EAAE,QAAQA,EAAC,MAAI,EAAEA,EAAC,IAAED,GAAEC,EAAC;AAAA,QAAE;AAAC,sBAAY,OAAO,EAAE,kBAAgB,SAAK,EAAE,oCAAkC,EAAE,kCAAgC,OAAG,QAAQ,KAAK,yEAAyE;AAAA,MAAE;AAAC,eAAS,IAAG;AAAC,gBAAO,oBAAI,QAAM,QAAQ;AAAA,MAAC;AAAC,eAAS,EAAEH,IAAE;AAAC,YAAIC,KAAE,EAAE;AAAE,eAAOD,GAAE,GAAE,EAAE,IAAEC;AAAA,MAAC;AAAC,eAAS,EAAEC,IAAEC,IAAE;AAAC,iBAASH,GAAEC,IAAE;AAAC,iBAAO,WAAU;AAAC,YAAAC,MAAGA,GAAE;AAAE,gBAAIF,KAAEC,GAAE,MAAM,QAAO,SAAS;AAAE,mBAAOE,MAAGA,GAAE,GAAEH;AAAA,UAAC;AAAA,QAAC;AAAC,YAAIC,KAAE,OAAO,OAAMG,KAAE,OAAO,SAAQC,KAAE,OAAO;AAAO,YAAG;AAAC,iBAAO,QAAML,GAAEC,EAAC,GAAE,OAAO,UAAQD,GAAEI,EAAC,GAAE,OAAO,SAAOJ,GAAEK,EAAC;AAAA,QAAC,SAAOL,IAAE;AAAA,QAAC;AAAA,MAAC;AAAC,UAAI,GAAE,GAAE,GAAE,IAAE,EAAC,QAAO,OAAG,IAAG,OAAG,WAAU,OAAG,SAAQ,OAAG,OAAM,OAAG,MAAK,OAAG,SAAQ,OAAG,IAAG,OAAG,WAAU,OAAG,SAAQ,OAAG,QAAO,OAAG,QAAO,OAAG,QAAO,MAAE;AAAE,eAAS,IAAG;AAAC,iBAASA,GAAEA,IAAE;AAAC,iBAAM,OAAKC,GAAE,QAAQD,EAAC;AAAA,QAAC;AAAC,YAAIC,KAAE,UAAU,UAAU,YAAY,GAAEC,KAAE,WAAU;AAAC,cAAIF,KAAE,WAAUC,KAAED,GAAE,UAASA,KAAEA,GAAE;AAAe,cAAG,YAAU,OAAOA,GAAE,QAAO,IAAEA;AAAE,cAAG,YAAU,OAAOC,IAAE;AAAC,YAAAD,KAAEC,GAAE,YAAY;AAAE,gBAAG,aAAa,KAAKD,EAAC,EAAE,QAAM;AAAG,gBAAG,mCAAmC,KAAKA,EAAC,EAAE,QAAM;AAAA,UAAE;AAAC,iBAAM,kCAAkC,KAAK,UAAU,UAAU,YAAY,CAAC;AAAA,QAAC,EAAE,GAAEG,KAAE,CAAC,CAAC,OAAO,OAAK,WAAS,OAAO,KAAIC,KAAE,CAACF,IAAEG,KAAEL,GAAE,WAAW,GAAEM,KAAEN,GAAE,SAAS,GAAEO,KAAEP,GAAE,WAAW,GAAEQ,KAAER,GAAE,MAAM,GAAES,KAAED,MAAG,CAACR,GAAE,QAAQ,GAAEU,KAAED,MAAGT,GAAE,SAAS,KAAGA,GAAE,MAAM,GAAEW,KAAEX,GAAE,OAAO,GAAEY,KAAEZ,GAAE,QAAQ,GAAEa,KAAEb,GAAE,QAAQ,KAAGW,IAAEG,KAAE,CAACZ,MAAG,0GAA0G,KAAKD,EAAC;AAAE,eAAO,OAAO,GAAE,EAAC,QAAOE,IAAE,IAAGC,IAAE,WAAUC,IAAE,SAAQC,IAAE,OAAMC,IAAE,MAAKC,IAAE,SAAQC,IAAE,IAAGC,IAAE,WAAUC,IAAE,SAAQC,IAAE,QAAOC,IAAE,QAAOC,IAAE,QAAOZ,GAAC,CAAC;AAAA,MAAC;AAAC,eAAS,IAAG;AAAC,iBAAQF,KAAE,WAAU;AAAC,mBAAQA,KAAE,CAAC,GAAEC,KAAE,GAAEA,KAAE,KAAIA,KAAI,CAAAD,GAAE,GAAG,OAAOC,EAAC,CAAC,IAAE,GAAG,OAAOA,EAAC;AAAE,iBAAOD;AAAA,QAAC,EAAE,GAAEC,KAAE,CAAC,GAAEC,KAAE,GAAEA,KAAE,IAAGA,KAAI,CAAAD,GAAE,KAAKD,EAAC;AAAE,eAAOC;AAAA,MAAC;AAAC,eAAS,IAAG;AAAC,UAAE,YAAU,EAAE;AAAA,MAAC;AAAC,UAAI,IAAE,IAAG,IAAE;AAAG,eAAS,IAAG;AAAC,YAAID,KAAE,EAAE;AAAO,YAAGA,IAAE;AAAC,cAAG,cAAY,OAAOA,GAAE,QAAOA,GAAE;AAAE,cAAG,MAAIA,GAAE,QAAO;AAAC,gBAAIC,KAAE,SAAS;AAAK,gBAAG,MAAIA,GAAE,QAAO;AAAE,gBAAEA;AAAE,gBAAIC,IAAEC,KAAE,OAAGC,KAAE,EAAEJ,EAAC;AAAE,gBAAG;AAAC,mBAAII,GAAE,EAAE,GAAE,EAAEF,KAAEE,GAAE,EAAE,GAAG,QAAM;AAAC,oBAAIC,KAAEH,GAAE;AAAM,oBAAG,YAAU,OAAOG,IAAE;AAAC,sBAAG,OAAKJ,GAAE,QAAQI,EAAC,GAAE;AAAC,oBAAAF,KAAE;AAAG;AAAA,kBAAK;AAAA,gBAAC,WAASE,GAAE,KAAKJ,EAAC,GAAE;AAAC,kBAAAE,KAAE;AAAG;AAAA,gBAAK;AAAA,cAAC;AAAA,YAAC,SAAOH,IAAE;AAAC,cAAAI,GAAE,EAAEJ,EAAC;AAAA,YAAC,UAAC;AAAQ,cAAAI,GAAE,EAAE;AAAA,YAAC;AAAC,mBAAO,IAAED;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAC,UAAI,IAAE,WAAU;AAAC,eAAM;AAAA,MAAE;AAAE,eAAS,EAAED,IAAE;AAAC,YAAID,IAAED,IAAEG,KAAE,IAAGC,KAAE,IAAGC,KAAE,IAAGC,KAAE,IAAGC,KAAE,KAAIC,KAAE,EAAE,QAAM,SAASR,IAAEC,IAAE;AAAC,iBAAOD,GAAE,WAASA,GAAE,WAASC,OAAIG,MAAGH,OAAIE;AAAA,QAAE,IAAE,SAASH,IAAEC,IAAE;AAAC,iBAAOD,GAAE,WAASA,GAAE,aAAWC,OAAIG,MAAGH,OAAIE;AAAA,QAAE,GAAEM,KAAE,EAAE,QAAM,SAAST,IAAEC,IAAE;AAAC,iBAAOD,GAAE,WAASA,GAAE,UAAQC,OAAII,MAAGL,GAAE,WAASC,OAAIK;AAAA,QAAC,IAAE,SAASN,IAAEC,IAAE;AAAC,iBAAOD,GAAE,YAAUC,OAAIK,MAAGL,OAAII;AAAA,QAAE;AAAE,QAAAH,GAAE,iBAAiB,WAAU,SAASF,IAAE;AAAC,cAAIC,MAAGD,KAAEA,MAAGE,GAAE,OAAO,WAASF,GAAE;AAAM,cAAGC,OAAIM,MAAGC,GAAER,IAAEC,EAAC,KAAGQ,GAAET,IAAEC,EAAC,EAAE,QAAO,EAAEC,IAAEF,EAAC;AAAA,QAAC,GAAE,IAAE,GAAEC,KAAEC,IAAE,EAAE,eAAaD,GAAE,iBAAiB,eAAc,SAASD,IAAE;AAAC,cAAG,YAAUA,GAAE,YAAY,QAAO,EAAEC,IAAED,EAAC;AAAA,QAAC,CAAC,GAAEA,KAAEE,IAAE,EAAE,iBAAe,EAAEF,IAAE,aAAa,GAAEA,KAAEE,IAAE,EAAE,eAAa,EAAEF,IAAE,MAAM,GAAEA,KAAEE,IAAE,EAAE,cAAY,EAAEF,IAAE,KAAK,GAAEA,KAAEE,IAAE,EAAE,gBAAc,EAAEF,IAAE,OAAO;AAAA,MAAC;AAAC,eAAS,EAAEC,IAAED,IAAE;AAAC,QAAAC,GAAE,iBAAiBD,IAAE,SAASA,IAAE;AAAC,iBAAO,EAAEC,IAAED,EAAC;AAAA,QAAC,CAAC;AAAA,MAAC;AAAC,eAAS,EAAEA,IAAEC,IAAE;AAAC,YAAG,CAAC,EAAE,KAAG,CAAC,EAAE,EAAE,SAAOA,KAAEA,MAAGD,GAAE,OAAO,cAAY,OAAGC,GAAE,eAAe,GAAE;AAAA,MAAE;AAAC,UAAI,GAAE,IAAE,OAAG,IAAE,CAAC;AAAE,eAAS,EAAED,IAAE;AAAC,UAAEA,EAAC,IAAE;AAAA,MAAE;AAAC,eAAS,IAAG;AAAC,iBAAQA,MAAK,EAAE,KAAG,EAAEA,EAAC,EAAE,QAAO,IAAE;AAAG,eAAO,IAAE;AAAA,MAAE;AAAC,OAAC,IAAE,IAAE,KAAG,CAAC,GAAG,EAAE,UAAQ,EAAE,IAAE,WAAU,EAAE,EAAE,cAAY,CAAC,IAAE,eAAc,EAAE,EAAE,WAAS,CAAC,IAAE,YAAW,EAAE,EAAE,OAAK,CAAC,IAAE,QAAO,EAAE,EAAE,eAAa,CAAC,IAAE,gBAAe,EAAE,EAAE,eAAa,CAAC,IAAE,gBAAe,EAAE,EAAE,WAAS,CAAC,IAAE,YAAW,EAAE,EAAE,cAAY,CAAC,IAAE,eAAc,EAAE,EAAE,WAAS,CAAC,IAAE;AAAW,UAAI,IAAE,WAAU;AAAC,iBAASE,GAAEF,IAAE;AAAC,cAAIC,KAAED,GAAE,MAAKA,KAAEA,GAAE,SAAQA,KAAE,WAASA,MAAGA;AAAE,YAAE,MAAKE,EAAC,GAAE,KAAK,OAAK,EAAE,SAAQ,KAAK,UAAQ,MAAG,KAAK,OAAKD,IAAE,KAAK,UAAQD,IAAE,KAAK,YAAUC,KAAE,MAAK,EAAE,KAAKA,EAAC,GAAE,KAAK,KAAK;AAAA,QAAE;AAAC,eAAO,EAAEC,IAAE,CAAC,EAAC,KAAI,iBAAgB,OAAM,WAAU;AAAC,cAAIF;AAAE,kBAAQ,KAAK,oDAAoD,OAAO,KAAK,MAAK,GAAG,CAAC,GAAE,EAAE,mCAAiC,GAAG,GAAE,OAAO,aAAa,CAAC,GAAE,EAAE,cAAc,KAAK,MAAK,CAAC,GAAEA,KAAE,KAAK,MAAK,EAAEA,EAAC,IAAE;AAAA,QAAE,EAAC,GAAE,EAAC,KAAI,QAAO,OAAM,WAAU;AAAA,QAAC,EAAC,CAAC,CAAC,GAAEE;AAAA,MAAC,EAAE,GAAE,IAAE,WAAU;AAAC,UAAED,IAAE,CAAC;AAAE,YAAID,KAAE,EAAEC,EAAC;AAAE,iBAASA,KAAG;AAAC,iBAAO,EAAE,MAAKA,EAAC,GAAED,GAAE,KAAK,MAAK,EAAC,MAAK,EAAE,SAAQ,CAAC;AAAA,QAAC;AAAC,eAAO,EAAEC,IAAE,CAAC,EAAC,KAAI,QAAO,OAAM,WAAU;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,WAAU;AAAC,cAAID;AAAE,WAAC,UAAM,SAAOA,KAAE,SAAOA,KAAE,OAAO,SAAO,SAAOA,GAAE,aAAW,SAAOA,GAAE,YAAU,OAAO,kBAAgB,OAAO,SAAS,cAAc,uBAAuB,MAAI,KAAK,cAAc;AAAA,QAAC,EAAC,CAAC,GAAE,CAAC,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,iBAAM,CAAC,CAAC,OAAO,SAAO,CAAC,CAAC,OAAO;AAAA,QAAc,EAAC,CAAC,CAAC,GAAEC;AAAA,MAAC,EAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,CAAC,GAAE,IAAE;AAAE,eAAS,GAAGG,IAAE;AAAC,iBAASJ,KAAG;AAAC,UAAAS,KAAE;AAAA,QAAE;AAAC,iBAASR,KAAG;AAAC,UAAAQ,KAAE;AAAA,QAAE;AAAC,YAAIP,IAAEC,IAAEE,IAAEC,IAAEC,IAAEC,IAAEC,KAAE;AAAG,iBAASC,KAAG;AAAC,WAACF,GAAEF,EAAC,MAAID,KAAEF,KAAED,IAAG;AAAA,QAAC;AAAC,UAAEF,IAAEC,EAAC,GAAEC,KAAED,IAAEE,KAAEH,IAAE,YAAUQ,KAAE,UAAU,UAAQH,KAAE,UAASE,KAAE,oBAAmBD,KAAE,qBAAmB,WAASE,GAAE,aAAWH,KAAE,aAAYE,KAAE,uBAAsBD,KAAE,wBAAsB,WAASE,GAAE,YAAUH,KAAE,YAAWE,KAAE,sBAAqBD,KAAE,uBAAqB,WAASE,GAAE,iBAAeH,KAAE,gBAAeE,KAAE,0BAAyBD,KAAE,0BAAyBE,GAAE,oBAAoBD,IAAEG,IAAE,KAAE,GAAEF,GAAE,iBAAiBD,IAAEG,IAAE,KAAE,GAAE,IAAE,OAAO,YAAY,WAAU;AAAC,cAAG,EAAEN,GAAE,aAAWK,MAAG,EAAE,IAAG;AAAC,gBAAIT,IAAEC,IAAEC,KAAE,EAAE,CAAC;AAAE,gBAAG;AAAC,mBAAIA,GAAE,EAAE,GAAE,EAAEF,KAAEE,GAAE,EAAE,GAAG,QAAM;AAAC,oBAAIC,KAAEH,GAAE;AAAM,kBAAEG,GAAE,IAAI,GAAEA,GAAE,OAAO,GAAG;AAAA,cAAC;AAAA,YAAC,SAAOH,IAAE;AAAC,cAAAE,GAAE,EAAEF,EAAC;AAAA,YAAC,UAAC;AAAQ,cAAAE,GAAE,EAAE;AAAA,YAAC;AAAC,cAAE,GAAE,cAAY,OAAO,EAAE,mBAAiBD,KAAE,GAAE,CAAC,EAAE,KAAGA,MAAG,EAAE,eAAe;AAAA,UAAE;AAAA,QAAC,GAAE,EAAE,QAAQ,GAAE,IAAE,WAAW,WAAU;AAAC,YAAE,MAAI,EAAE,QAAQ,KAAG,GAAG;AAAA,QAAC,GAAE,EAAE,gBAAgB;AAAA,MAAC;AAAC,eAAS,KAAI;AAAC,eAAO,cAAc,CAAC;AAAA,MAAC;AAAC,UAAI,IAAE;AAAE,eAAS,GAAGD,IAAE;AAAC,iBAAQC,KAAE,SAASD,IAAEC,IAAE;AAAC,UAAAD,GAAEC,MAAG,CAAC,KAAG,OAAKA,KAAE,IAAGD,GAAE,MAAIC,KAAE,OAAK,KAAG,EAAE,IAAEA;AAAE,mBAAQC,KAAE,YAAWC,KAAE,YAAWC,KAAE,aAAYC,KAAE,WAAUC,KAAE,GAAEA,KAAEN,GAAE,QAAOM,MAAG,IAAG;AAAC,gBAAIC,KAAEL,IAAEM,KAAEL,IAAEM,KAAEL,IAAEM,KAAEL;AAAE,YAAAH,KAAE,EAAEA,IAAEC,IAAEC,IAAEC,IAAEL,GAAEM,KAAE,CAAC,GAAE,GAAE,UAAU,GAAED,KAAE,EAAEA,IAAEH,IAAEC,IAAEC,IAAEJ,GAAEM,KAAE,CAAC,GAAE,IAAG,UAAU,GAAEF,KAAE,EAAEA,IAAEC,IAAEH,IAAEC,IAAEH,GAAEM,KAAE,CAAC,GAAE,IAAG,SAAS,GAAEH,KAAE,EAAEA,IAAEC,IAAEC,IAAEH,IAAEF,GAAEM,KAAE,CAAC,GAAE,IAAG,WAAW,GAAEJ,KAAE,EAAEA,IAAEC,IAAEC,IAAEC,IAAEL,GAAEM,KAAE,CAAC,GAAE,GAAE,UAAU,GAAED,KAAE,EAAEA,IAAEH,IAAEC,IAAEC,IAAEJ,GAAEM,KAAE,CAAC,GAAE,IAAG,UAAU,GAAEF,KAAE,EAAEA,IAAEC,IAAEH,IAAEC,IAAEH,GAAEM,KAAE,CAAC,GAAE,IAAG,WAAW,GAAEH,KAAE,EAAEA,IAAEC,IAAEC,IAAEH,IAAEF,GAAEM,KAAE,CAAC,GAAE,IAAG,SAAS,GAAEJ,KAAE,EAAEA,IAAEC,IAAEC,IAAEC,IAAEL,GAAEM,KAAE,CAAC,GAAE,GAAE,UAAU,GAAED,KAAE,EAAEA,IAAEH,IAAEC,IAAEC,IAAEJ,GAAEM,KAAE,CAAC,GAAE,IAAG,WAAW,GAAEF,KAAE,EAAEA,IAAEC,IAAEH,IAAEC,IAAEH,GAAEM,KAAE,EAAE,GAAE,IAAG,MAAM,GAAEH,KAAE,EAAEA,IAAEC,IAAEC,IAAEH,IAAEF,GAAEM,KAAE,EAAE,GAAE,IAAG,WAAW,GAAEJ,KAAE,EAAEA,IAAEC,IAAEC,IAAEC,IAAEL,GAAEM,KAAE,EAAE,GAAE,GAAE,UAAU,GAAED,KAAE,EAAEA,IAAEH,IAAEC,IAAEC,IAAEJ,GAAEM,KAAE,EAAE,GAAE,IAAG,SAAS,GAAEF,KAAE,EAAEA,IAAEC,IAAEH,IAAEC,IAAEH,GAAEM,KAAE,EAAE,GAAE,IAAG,WAAW,GAAEH,KAAE,EAAEA,IAAEC,IAAEC,IAAEH,IAAEF,GAAEM,KAAE,EAAE,GAAE,IAAG,UAAU,GAAEJ,KAAE,EAAEA,IAAEC,IAAEC,IAAEC,IAAEL,GAAEM,KAAE,CAAC,GAAE,GAAE,UAAU,GAAED,KAAE,EAAEA,IAAEH,IAAEC,IAAEC,IAAEJ,GAAEM,KAAE,CAAC,GAAE,GAAE,WAAW,GAAEF,KAAE,EAAEA,IAAEC,IAAEH,IAAEC,IAAEH,GAAEM,KAAE,EAAE,GAAE,IAAG,SAAS,GAAEH,KAAE,EAAEA,IAAEC,IAAEC,IAAEH,IAAEF,GAAEM,KAAE,CAAC,GAAE,IAAG,UAAU,GAAEJ,KAAE,EAAEA,IAAEC,IAAEC,IAAEC,IAAEL,GAAEM,KAAE,CAAC,GAAE,GAAE,UAAU,GAAED,KAAE,EAAEA,IAAEH,IAAEC,IAAEC,IAAEJ,GAAEM,KAAE,EAAE,GAAE,GAAE,QAAQ,GAAEF,KAAE,EAAEA,IAAEC,IAAEH,IAAEC,IAAEH,GAAEM,KAAE,EAAE,GAAE,IAAG,UAAU,GAAEH,KAAE,EAAEA,IAAEC,IAAEC,IAAEH,IAAEF,GAAEM,KAAE,CAAC,GAAE,IAAG,UAAU,GAAEJ,KAAE,EAAEA,IAAEC,IAAEC,IAAEC,IAAEL,GAAEM,KAAE,CAAC,GAAE,GAAE,SAAS,GAAED,KAAE,EAAEA,IAAEH,IAAEC,IAAEC,IAAEJ,GAAEM,KAAE,EAAE,GAAE,GAAE,WAAW,GAAEF,KAAE,EAAEA,IAAEC,IAAEH,IAAEC,IAAEH,GAAEM,KAAE,CAAC,GAAE,IAAG,UAAU,GAAEH,KAAE,EAAEA,IAAEC,IAAEC,IAAEH,IAAEF,GAAEM,KAAE,CAAC,GAAE,IAAG,UAAU,GAAEJ,KAAE,EAAEA,IAAEC,IAAEC,IAAEC,IAAEL,GAAEM,KAAE,EAAE,GAAE,GAAE,WAAW,GAAED,KAAE,EAAEA,IAAEH,IAAEC,IAAEC,IAAEJ,GAAEM,KAAE,CAAC,GAAE,GAAE,SAAS,GAAEF,KAAE,EAAEA,IAAEC,IAAEH,IAAEC,IAAEH,GAAEM,KAAE,CAAC,GAAE,IAAG,UAAU,GAAEH,KAAE,EAAEA,IAAEC,IAAEC,IAAEH,IAAEF,GAAEM,KAAE,EAAE,GAAE,IAAG,WAAW,GAAEJ,KAAE,EAAEA,IAAEC,IAAEC,IAAEC,IAAEL,GAAEM,KAAE,CAAC,GAAE,GAAE,OAAO,GAAED,KAAE,EAAEA,IAAEH,IAAEC,IAAEC,IAAEJ,GAAEM,KAAE,CAAC,GAAE,IAAG,WAAW,GAAEF,KAAE,EAAEA,IAAEC,IAAEH,IAAEC,IAAEH,GAAEM,KAAE,EAAE,GAAE,IAAG,UAAU,GAAEH,KAAE,EAAEA,IAAEC,IAAEC,IAAEH,IAAEF,GAAEM,KAAE,EAAE,GAAE,IAAG,SAAS,GAAEJ,KAAE,EAAEA,IAAEC,IAAEC,IAAEC,IAAEL,GAAEM,KAAE,CAAC,GAAE,GAAE,WAAW,GAAED,KAAE,EAAEA,IAAEH,IAAEC,IAAEC,IAAEJ,GAAEM,KAAE,CAAC,GAAE,IAAG,UAAU,GAAEF,KAAE,EAAEA,IAAEC,IAAEH,IAAEC,IAAEH,GAAEM,KAAE,CAAC,GAAE,IAAG,UAAU,GAAEH,KAAE,EAAEA,IAAEC,IAAEC,IAAEH,IAAEF,GAAEM,KAAE,EAAE,GAAE,IAAG,WAAW,GAAEJ,KAAE,EAAEA,IAAEC,IAAEC,IAAEC,IAAEL,GAAEM,KAAE,EAAE,GAAE,GAAE,SAAS,GAAED,KAAE,EAAEA,IAAEH,IAAEC,IAAEC,IAAEJ,GAAEM,KAAE,CAAC,GAAE,IAAG,UAAU,GAAEF,KAAE,EAAEA,IAAEC,IAAEH,IAAEC,IAAEH,GAAEM,KAAE,CAAC,GAAE,IAAG,UAAU,GAAEH,KAAE,EAAEA,IAAEC,IAAEC,IAAEH,IAAEF,GAAEM,KAAE,CAAC,GAAE,IAAG,QAAQ,GAAEJ,KAAE,EAAEA,IAAEC,IAAEC,IAAEC,IAAEL,GAAEM,KAAE,CAAC,GAAE,GAAE,UAAU,GAAED,KAAE,EAAEA,IAAEH,IAAEC,IAAEC,IAAEJ,GAAEM,KAAE,EAAE,GAAE,IAAG,UAAU,GAAEF,KAAE,EAAEA,IAAEC,IAAEH,IAAEC,IAAEH,GAAEM,KAAE,EAAE,GAAE,IAAG,SAAS,GAAEH,KAAE,EAAEA,IAAEC,IAAEC,IAAEH,IAAEF,GAAEM,KAAE,CAAC,GAAE,IAAG,UAAU,GAAEJ,KAAE,EAAEA,IAAEC,IAAEC,IAAEC,IAAEL,GAAEM,KAAE,CAAC,GAAE,GAAE,UAAU,GAAED,KAAE,EAAEA,IAAEH,IAAEC,IAAEC,IAAEJ,GAAEM,KAAE,CAAC,GAAE,IAAG,UAAU,GAAEF,KAAE,EAAEA,IAAEC,IAAEH,IAAEC,IAAEH,GAAEM,KAAE,EAAE,GAAE,IAAG,WAAW,GAAEH,KAAE,EAAEA,IAAEC,IAAEC,IAAEH,IAAEF,GAAEM,KAAE,CAAC,GAAE,IAAG,SAAS,GAAEJ,KAAE,EAAEA,IAAEC,IAAEC,IAAEC,IAAEL,GAAEM,KAAE,EAAE,GAAE,GAAE,UAAU,GAAED,KAAE,EAAEA,IAAEH,IAAEC,IAAEC,IAAEJ,GAAEM,KAAE,CAAC,GAAE,IAAG,WAAW,GAAEF,KAAE,EAAEA,IAAEC,IAAEH,IAAEC,IAAEH,GAAEM,KAAE,EAAE,GAAE,IAAG,QAAQ,GAAEH,KAAE,EAAEA,IAAEC,IAAEC,IAAEH,IAAEF,GAAEM,KAAE,CAAC,GAAE,IAAG,WAAW,GAAEJ,KAAE,EAAEA,IAAEC,IAAEC,IAAEC,IAAEL,GAAEM,KAAE,CAAC,GAAE,GAAE,UAAU,GAAED,KAAE,EAAEA,IAAEH,IAAEC,IAAEC,IAAEJ,GAAEM,KAAE,EAAE,GAAE,IAAG,SAAS,GAAEF,KAAE,EAAEA,IAAEC,IAAEH,IAAEC,IAAEH,GAAEM,KAAE,CAAC,GAAE,IAAG,WAAW,GAAEH,KAAE,EAAEA,IAAEC,IAAEC,IAAEH,IAAEF,GAAEM,KAAE,EAAE,GAAE,IAAG,UAAU,GAAEJ,KAAE,EAAEA,IAAEC,IAAEC,IAAEC,IAAEL,GAAEM,KAAE,CAAC,GAAE,GAAE,UAAU,GAAED,KAAE,EAAEA,IAAEH,IAAEC,IAAEC,IAAEJ,GAAEM,KAAE,EAAE,GAAE,IAAG,WAAW,GAAEF,KAAE,EAAEA,IAAEC,IAAEH,IAAEC,IAAEH,GAAEM,KAAE,CAAC,GAAE,IAAG,SAAS,GAAEH,KAAE,EAAEA,IAAEC,IAAEC,IAAEH,IAAEF,GAAEM,KAAE,CAAC,GAAE,IAAG,UAAU,GAAEJ,KAAE,EAAEA,IAAEK,EAAC,GAAEJ,KAAE,EAAEA,IAAEK,EAAC,GAAEJ,KAAE,EAAEA,IAAEK,EAAC,GAAEJ,KAAE,EAAEA,IAAEK,EAAC;AAAA,UAAC;AAAC,iBAAO,MAAMR,IAAEC,IAAEC,IAAEC,EAAC;AAAA,QAAC,EAAE,SAASL,IAAE;AAAC,mBAAQC,KAAE,MAAM,GAAEC,MAAG,KAAG,KAAG,GAAEC,KAAE,GAAEA,KAAEH,GAAE,SAAO,GAAEG,MAAG,EAAE,CAAAF,GAAEE,MAAG,CAAC,MAAIH,GAAE,WAAWG,KAAE,CAAC,IAAED,OAAIC,KAAE;AAAG,iBAAOF;AAAA,QAAC,EAAED,EAAC,GAAEA,GAAE,SAAO,CAAC,GAAEE,KAAE,oBAAmBC,KAAE,IAAGC,KAAE,GAAEA,KAAE,IAAEH,GAAE,QAAOG,KAAI,CAAAD,MAAGD,GAAE,OAAOD,GAAEG,MAAG,CAAC,KAAGA,KAAE,IAAE,IAAE,IAAE,EAAE,IAAEF,GAAE,OAAOD,GAAEG,MAAG,CAAC,KAAGA,KAAE,IAAE,IAAE,EAAE;AAAE,eAAOD;AAAA,MAAC;AAAC,eAAS,EAAEH,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,eAAO,GAAGJ,KAAE,EAAE,EAAEA,IAAED,EAAC,GAAE,EAAEG,IAAEE,EAAC,CAAC,MAAID,KAAEH,OAAI,KAAGG,IAAEF,EAAC;AAAA,MAAC;AAAC,eAAS,EAAEF,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,eAAO,EAAEL,KAAEC,KAAE,CAACD,KAAEE,IAAEH,IAAEC,IAAEG,IAAEC,IAAEC,EAAC;AAAA,MAAC;AAAC,eAAS,EAAEN,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,eAAO,EAAEL,KAAEE,KAAED,KAAE,CAACC,IAAEH,IAAEC,IAAEG,IAAEC,IAAEC,EAAC;AAAA,MAAC;AAAC,eAAS,EAAEN,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,eAAO,EAAEL,KAAEC,KAAEC,IAAEH,IAAEC,IAAEG,IAAEC,IAAEC,EAAC;AAAA,MAAC;AAAC,eAAS,EAAEN,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,eAAO,EAAEJ,MAAGD,KAAE,CAACE,KAAGH,IAAEC,IAAEG,IAAEC,IAAEC,EAAC;AAAA,MAAC;AAAC,eAAS,EAAEN,IAAEC,IAAE;AAAC,YAAIC,MAAG,QAAMF,OAAI,QAAMC;AAAG,gBAAOD,MAAG,OAAKC,MAAG,OAAKC,MAAG,OAAK,KAAG,QAAMA;AAAA,MAAC;AAAC,UAAI,IAAE,WAAU;AAAC,UAAED,IAAE,CAAC;AAAE,YAAID,KAAE,EAAEC,EAAC;AAAE,iBAASA,KAAG;AAAC,iBAAO,EAAE,MAAKA,EAAC,GAAED,GAAE,KAAK,MAAK,EAAC,MAAK,EAAE,aAAY,SAAQ,EAAE,aAAW,EAAE,QAAO,CAAC;AAAA,QAAC;AAAC,eAAO,EAAEC,IAAE,CAAC,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,cAAIA,KAAE;AAAK,eAAK,WAAS,GAAE,KAAK,MAAI,KAAI,EAAE,KAAK,GAAG,GAAE,KAAK,IAAI,WAAS,WAAU;AAAC,gBAAID;AAAE,mBAAO,EAAE,aAAWA,MAAG,oBAAI,QAAM,QAAQ,GAAEC,GAAE,YAAUD,KAAEC,GAAE,WAAS,MAAIA,GAAE,cAAc,IAAEA,GAAE,WAASD,MAAG,EAAE,WAASC,GAAE,cAAc,GAAE;AAAA,UAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,WAAU;AAAC,YAAE,KAAK,GAAG;AAAA,QAAC,EAAC,CAAC,CAAC,GAAEA;AAAA,MAAC,EAAE,GAAE,KAAG,WAAU;AAAC,UAAEA,IAAE,CAAC;AAAE,YAAID,KAAE,EAAEC,EAAC;AAAE,iBAASA,KAAG;AAAC,iBAAO,EAAE,MAAKA,EAAC,GAAED,GAAE,KAAK,MAAK,EAAC,MAAK,EAAE,SAAQ,CAAC;AAAA,QAAC;AAAC,eAAO,EAAEC,IAAE,CAAC,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,cAAID,KAAE;AAAK,eAAK,MAAI,SAAS,cAAc,KAAK,GAAE,KAAK,IAAI,iBAAiB,MAAK,WAAU;AAAC,YAAAA,GAAE,cAAc;AAAA,UAAC,CAAC,GAAE,OAAO,eAAe,KAAK,KAAI,MAAK,EAAC,KAAI,WAAU;AAAC,YAAAA,GAAE,cAAc;AAAA,UAAC,EAAC,CAAC;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,WAAU;AAAC,YAAE,KAAK,GAAG;AAAA,QAAC,EAAC,CAAC,CAAC,GAAEC;AAAA,MAAC,EAAE,GAAE,KAAG,WAAU;AAAC,UAAEA,IAAE,CAAC;AAAE,YAAID,KAAE,EAAEC,EAAC;AAAE,iBAASA,KAAG;AAAC,iBAAO,EAAE,MAAKA,EAAC,GAAED,GAAE,KAAK,MAAK,EAAC,MAAK,EAAE,MAAK,SAAQ,CAAC,EAAE,UAAQ,CAAC,EAAE,KAAI,CAAC;AAAA,QAAC;AAAC,eAAO,EAAEC,IAAE,CAAC,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,cAAID,KAAE;AAAK,eAAK,sBAAsB,GAAE,OAAO,iBAAiB,UAAS,WAAU;AAAC,uBAAW,WAAU;AAAC,cAAAA,GAAE,sBAAsB;AAAA,YAAC,GAAE,GAAG;AAAA,UAAC,GAAE,IAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,WAAU;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,yBAAwB,OAAM,WAAU;AAAC,cAAIA,KAAE,WAAU;AAAC,gBAAG,GAAG,OAAO,gBAAgB,EAAE,QAAO,OAAO;AAAiB,gBAAIA,KAAE,OAAO;AAAO,mBAAM,EAAE,GAAGA,EAAC,KAAG,CAACA,GAAE,cAAY,CAACA,GAAE,gBAAcA,GAAE,aAAWA,GAAE;AAAA,UAAW,EAAE;AAAE,cAAG,UAAKA,IAAE;AAAC,gBAAIC,KAAE,MAAI,OAAO,aAAW,OAAO,aAAWD,IAAEA,KAAE,MAAI,OAAO,cAAY,OAAO,cAAYA;AAAE,gBAAGC,MAAGD,GAAE,QAAO,KAAK,cAAc,GAAE;AAAG,cAAE,KAAK,IAAI;AAAA,UAAC;AAAC,iBAAM;AAAA,QAAE,EAAC,CAAC,CAAC,GAAEC;AAAA,MAAC,EAAE;AAAE,eAAS,GAAGD,IAAE;AAAC,eAAO,QAAMA;AAAA,MAAC;AAAC,UAAI,GAAE,KAAG,WAAU;AAAC,UAAEC,IAAE,CAAC;AAAE,YAAID,KAAE,EAAEC,EAAC;AAAE,iBAASA,KAAG;AAAC,iBAAO,EAAE,MAAKA,EAAC,GAAED,GAAE,KAAK,MAAK,EAAC,MAAK,EAAE,cAAa,SAAQ,CAAC,EAAE,aAAW,CAAC,EAAE,QAAO,CAAC;AAAA,QAAC;AAAC,eAAO,EAAEC,IAAE,CAAC,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,cAAID,KAAE;AAAK,eAAK,QAAM,GAAE,KAAK,OAAK,oBAAI,QAAK,KAAK,KAAK,WAAS,WAAU;AAAC,mBAAOA,GAAE,SAAQ;AAAA,UAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,WAAU;AAAC,eAAK,QAAM,GAAE,EAAE,KAAK,IAAI,GAAE,EAAE,GAAE,KAAG,KAAK,SAAO,KAAK,cAAc;AAAA,QAAC,EAAC,CAAC,CAAC,GAAEC;AAAA,MAAC,EAAE,GAAE,KAAG,WAAU;AAAC,UAAEA,IAAE,CAAC;AAAE,YAAID,KAAE,EAAEC,EAAC;AAAE,iBAASA,KAAG;AAAC,iBAAO,EAAE,MAAKA,EAAC,GAAED,GAAE,KAAK,MAAK,EAAC,MAAK,EAAE,cAAa,SAAQ,CAAC,EAAE,aAAW,CAAC,EAAE,QAAO,CAAC;AAAA,QAAC;AAAC,eAAO,EAAEC,IAAE,CAAC,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,cAAID,KAAE;AAAK,eAAK,QAAM,GAAE,KAAK,OAAK,WAAU;AAAA,UAAC,GAAE,KAAK,KAAK,WAAS,WAAU;AAAC,mBAAOA,GAAE,SAAQ;AAAA,UAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,WAAU;AAAC,eAAK,QAAM,GAAE,EAAE,KAAK,IAAI,GAAE,EAAE,GAAE,KAAG,KAAK,SAAO,KAAK,cAAc;AAAA,QAAC,EAAC,CAAC,CAAC,GAAEC;AAAA,MAAC,EAAE,GAAE,KAAG,WAAU;AAAC,UAAEA,IAAE,CAAC;AAAE,YAAID,KAAE,EAAEC,EAAC;AAAE,iBAASA,KAAG;AAAC,iBAAO,EAAE,MAAKA,EAAC,GAAED,GAAE,KAAK,MAAK,EAAC,MAAK,EAAE,UAAS,SAAQ,EAAE,aAAW,EAAE,QAAO,CAAC;AAAA,QAAC;AAAC,eAAO,EAAEC,IAAE,CAAC,EAAC,KAAI,UAAS,OAAM,WAAU;AAAC,cAAID,KAAE,EAAE;AAAE,gBAAI,EAAE,IAAEA,MAAG,KAAK,cAAc;AAAA,QAAC,EAAC,CAAC,CAAC,GAAEC;AAAA,MAAC,EAAE,GAAE,KAAG,WAAU;AAAC,UAAEA,IAAE,CAAC;AAAE,YAAID,KAAE,EAAEC,EAAC;AAAE,iBAASA,KAAG;AAAC,iBAAO,EAAE,MAAKA,EAAC,GAAED,GAAE,KAAK,MAAK,EAAC,MAAK,EAAE,aAAY,SAAQ,EAAE,UAAQ,CAAC,EAAE,OAAM,CAAC;AAAA,QAAC;AAAC,eAAO,EAAEC,IAAE,CAAC,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,eAAK,eAAa,GAAE,KAAK,mBAAiB,EAAE;AAAA,QAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,WAAU;AAAC,cAAID,KAAE,MAAKC,KAAE,EAAE,WAAU;AAAC,cAAED,GAAE,gBAAgB;AAAA,UAAC,CAAC,GAAEE,KAAE,EAAE,WAAU;AAAC,cAAEF,GAAE,gBAAgB;AAAA,UAAC,CAAC;AAAE,cAAG,KAAK,eAAa,KAAK,IAAI,KAAK,cAAaE,EAAC,GAAE,EAAE,GAAE,MAAID,MAAG,MAAI,KAAK,aAAa,QAAM;AAAG,UAAAA,KAAE,KAAG,KAAK,gBAAc,KAAK,cAAc;AAAA,QAAC,EAAC,CAAC,CAAC,GAAEA;AAAA,MAAC,EAAE,GAAE,MAAI,EAAE,IAAE,CAAC,GAAE,EAAE,aAAY,CAAC,GAAE,EAAE,GAAE,EAAE,UAAS,EAAE,GAAE,EAAE,GAAE,EAAE,MAAK,EAAE,GAAE,EAAE,GAAE,EAAE,cAAa,EAAE,GAAE,EAAE,GAAE,EAAE,cAAa,EAAE,GAAE,EAAE,GAAE,EAAE,UAAS,EAAE,GAAE,EAAE,GAAE,EAAE,aAAY,EAAE,GAAE,EAAE,GAAE,EAAE,UAAS,CAAC,GAAE;AAAG,UAAI,IAAE,OAAO,OAAO,SAASD,IAAE;AAAC,iBAASC,KAAG;AAAC,cAAID,KAAE,IAAE,UAAU,UAAQ,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE;AAAG,iBAAM,EAAC,SAAQ,CAACA,IAAE,QAAOA,GAAC;AAAA,QAAC;AAAC,YAAIE;AAAE,YAAG,EAAE,UAAU,QAAOD,GAAE,iBAAiB;AAAE,YAAG,EAAE,GAAEC,KAAE,OAAO,WAAS,EAAC,KAAI,WAAU;AAAA,QAAC,GAAE,OAAM,WAAU;AAAA,QAAC,GAAE,OAAM,WAAU;AAAA,QAAC,EAAC,GAAE,IAAE,EAAE,MAAI,IAAE,WAAU;AAAC,iBAAOA,GAAE,IAAI,MAAMA,IAAE,SAAS;AAAA,QAAC,GAAE,IAAE,WAAU;AAAC,iBAAOA,GAAE,MAAM,MAAMA,IAAE,SAAS;AAAA,QAAC,GAAE,WAAU;AAAC,iBAAOA,GAAE,MAAM;AAAA,QAAC,MAAI,IAAEA,GAAE,KAAI,IAAEA,GAAE,OAAMA,GAAE,QAAO,EAAEF,EAAC,GAAE,EAAE,OAAK,GAAG,SAASA,IAAE;AAAC,cAAIC,KAAE,OAAO,SAAS,QAAOC,KAAE,OAAO,SAAS;AAAK,cAAG,QAAMD,KAAE,OAAKA,MAAG,OAAKC,KAAE,IAAI,OAAOA,GAAE,MAAM,GAAG,EAAE,CAAC,CAAC,IAAED,OAAI,WAASA,IAAE;AAAC,YAAAC,KAAE,IAAI,OAAO,UAAQF,KAAE,iBAAgB,GAAG,GAAEA,KAAEC,GAAE,OAAO,CAAC,EAAE,MAAMC,EAAC;AAAE,gBAAG,QAAMF,GAAE,QAAO,SAASA,GAAE,CAAC,CAAC;AAAA,UAAC;AAAC,iBAAM;AAAA,QAAE,EAAE,EAAE,MAAM,CAAC,MAAI,EAAE,IAAI,QAAOC,GAAE,cAAc;AAAE,YAAG,EAAE,OAAK,EAAE,OAAO,QAAOA,GAAE,QAAQ;AAAE,UAAE,YAAU,MAAG,GAAG,CAAC;AAAE,YAAIE,KAAE,GAAEC,MAAG,IAAE,WAAU;AAAC,iBAAOD,GAAE;AAAA,QAAS,GAAE,OAAO,MAAKE,KAAE,OAAO;AAAO,YAAG,EAAE,MAAM,GAAE,EAAE,wBAAsBD,MAAGC,MAAGD,OAAI,QAAO;AAAC,iBAAKC,OAAID,KAAG,GAAEC,EAAC,GAAEA,KAAEA,GAAE;AAAO,YAAED,EAAC;AAAA,QAAC;AAAC,gBAAO,UAAQ,EAAE,YAAU,OAAO,KAAK,EAAE,IAAE,EAAE,WAAW,QAAQ,SAASJ,IAAE;AAAC,cAAI,GAAGA,EAAC;AAAA,QAAC,CAAC,GAAEC,GAAE;AAAA,MAAC,GAAE,EAAC,WAAU,OAAG,WAAU,OAAG,KAAI,IAAG,SAAQ,SAAQ,cAAa,GAAE,iBAAgB,EAAC,CAAC;AAAE,UAAE,WAAU;AAAC,YAAG,eAAa,OAAO,UAAQ,CAAC,OAAO,SAAS,QAAO;AAAK,YAAIC,KAAE,SAAS,cAAc,wBAAwB;AAAE,YAAG,CAACA,GAAE,QAAO;AAAK,YAAIC,KAAE,CAAC,gBAAe,kBAAiB,gBAAe,eAAc,iBAAgB,WAAW,GAAEC,KAAE,CAAC,UAAU,GAAEC,KAAE,CAAC;AAAE,eAAM,CAAC,OAAM,OAAM,WAAU,WAAW,EAAE,OAAOF,IAAEC,EAAC,EAAE,QAAQ,SAASJ,IAAE;AAAC,cAAIC,KAAEC,GAAE,aAAaF,EAAC;AAAE,mBAAOC,OAAI,OAAKG,GAAE,QAAQJ,EAAC,IAAEC,KAAE,SAASA,EAAC,IAAE,OAAKE,GAAE,QAAQH,EAAC,IAAEC,KAAE,YAAUA,KAAE,eAAaD,MAAG,UAAQC,OAAIA,KAAEA,GAAE,MAAM,GAAG,IAAGI,GAAE,SAASL,IAAE;AAAC,gBAAG,OAAKA,GAAE,QAAQ,GAAG,EAAE,QAAOA;AAAE,gBAAIC,KAAE;AAAG,mBAAOD,GAAE,MAAM,EAAE,EAAE,IAAI,SAASA,IAAE;AAAC,qBAAM,QAAMA,MAAGC,KAAE,MAAG,MAAIA,MAAGA,KAAE,OAAGD,GAAE,YAAY,KAAGA;AAAA,YAAC,CAAC,EAAE,KAAK,EAAE;AAAA,UAAC,EAAEA,EAAC,CAAC,IAAEC;AAAA,QAAE,CAAC,GAAEI;AAAA,MAAC,EAAE;AAAE,aAAO,KAAG,EAAE,CAAC,GAAE;AAAA,IAAC,CAAC;AAAA;AAAA;", "names": ["e", "t", "n", "i", "o", "r", "u", "c", "a", "l", "f", "s", "d", "v", "h"]}