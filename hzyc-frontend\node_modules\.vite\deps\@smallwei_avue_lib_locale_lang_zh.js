import "./chunk-PLDDJCW6.js";

// node_modules/@smallwei/avue/lib/locale/lang/zh.js
var zh_default = {
  common: {
    submitBtn: "确 定",
    cancelBtn: "取 消",
    condition: "条件",
    display: "显示",
    hide: "隐藏"
  },
  tip: {
    select: "请选择",
    input: "请输入"
  },
  check: {
    checkAll: "全选"
  },
  upload: {
    upload: "点击上传",
    tip: "将文件拖到此处，或"
  },
  time: {
    start: "开始",
    end: "结束"
  },
  date: {
    start: "开始",
    end: "结束",
    t: "今日",
    y: "昨日",
    n: "近7天",
    a: "全部"
  },
  form: {
    printBtn: "打 印",
    mockBtn: "模 拟",
    submitBtn: "提 交",
    emptyBtn: "清 空"
  },
  crud: {
    excel: {
      name: "文件名",
      type: "数据",
      typeDic: {
        true: "当前数据(当前页全部的数据)",
        false: "选中的数据(当前页选中的数据)"
      },
      prop: "字段",
      params: "参数",
      paramsDic: {
        header: "表头",
        data: "数据源",
        headers: "复杂表头",
        sum: "合计统计"
      }
    },
    filter: {
      addBtn: "新增条件",
      clearBtn: "清空数据",
      resetBtn: "清空条件",
      cancelBtn: "取 消",
      submitBtn: "确 定"
    },
    column: {
      name: "列名",
      hide: "隐藏",
      fixed: "冻结",
      filters: "过滤",
      sortable: "排序",
      index: "顺序",
      width: "宽度"
    },
    emptyText: "暂无数据",
    tipStartTitle: "当前表格已选择",
    tipEndTitle: "项",
    editTitle: "编 辑",
    copyTitle: "复 制",
    addTitle: "新 增",
    viewTitle: "查 看",
    filterTitle: "过滤条件",
    showTitle: "列显隐",
    menu: "操作",
    addBtn: "新 增",
    show: "显 示",
    hide: "隐 藏",
    open: "展 开",
    shrink: "收 缩",
    printBtn: "打 印",
    mockBtn: "模 拟",
    excelBtn: "导 出",
    updateBtn: "修 改",
    cancelBtn: "取 消",
    searchBtn: "搜 索",
    emptyBtn: "清 空",
    menuBtn: "功 能",
    saveBtn: "保 存",
    viewBtn: "查 看",
    editBtn: "编 辑",
    copyBtn: "复 制",
    delBtn: "删 除"
  }
};
export {
  zh_default as default
};
//# sourceMappingURL=@smallwei_avue_lib_locale_lang_zh.js.map
