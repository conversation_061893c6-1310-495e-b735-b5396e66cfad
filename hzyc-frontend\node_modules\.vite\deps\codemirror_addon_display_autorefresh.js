import {
  require_codemirror
} from "./chunk-JE3NRKX2.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/codemirror/addon/display/autorefresh.js
var require_autorefresh = __commonJS({
  "node_modules/codemirror/addon/display/autorefresh.js"(exports, module) {
    (function(mod) {
      if (typeof exports == "object" && typeof module == "object")
        mod(require_codemirror());
      else if (typeof define == "function" && define.amd)
        define(["../../lib/codemirror"], mod);
      else
        mod(CodeMirror);
    })(function(CodeMirror2) {
      "use strict";
      CodeMirror2.defineOption("autoRefresh", false, function(cm, val) {
        if (cm.state.autoRefresh) {
          stopListening(cm, cm.state.autoRefresh);
          cm.state.autoRefresh = null;
        }
        if (val && cm.display.wrapper.offsetHeight == 0)
          startListening(cm, cm.state.autoRefresh = { delay: val.delay || 250 });
      });
      function startListening(cm, state) {
        function check() {
          if (cm.display.wrapper.offsetHeight) {
            stopListening(cm, state);
            if (cm.display.lastWrapHeight != cm.display.wrapper.clientHeight)
              cm.refresh();
          } else {
            state.timeout = setTimeout(check, state.delay);
          }
        }
        state.timeout = setTimeout(check, state.delay);
        state.hurry = function() {
          clearTimeout(state.timeout);
          state.timeout = setTimeout(check, 50);
        };
        CodeMirror2.on(window, "mouseup", state.hurry);
        CodeMirror2.on(window, "keyup", state.hurry);
      }
      function stopListening(_cm, state) {
        clearTimeout(state.timeout);
        CodeMirror2.off(window, "mouseup", state.hurry);
        CodeMirror2.off(window, "keyup", state.hurry);
      }
    });
  }
});
export default require_autorefresh();
//# sourceMappingURL=codemirror_addon_display_autorefresh.js.map
