{"version": 3, "sources": ["../../codemirror/addon/dialog/dialog.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n// Open simple dialogs on top of an editor. Relies on dialog.css.\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  function dialogDiv(cm, template, bottom) {\n    var wrap = cm.getWrapperElement();\n    var dialog;\n    dialog = wrap.appendChild(document.createElement(\"div\"));\n    if (bottom)\n      dialog.className = \"CodeMirror-dialog CodeMirror-dialog-bottom\";\n    else\n      dialog.className = \"CodeMirror-dialog CodeMirror-dialog-top\";\n\n    if (typeof template == \"string\") {\n      dialog.innerHTML = template;\n    } else { // Assuming it's a detached DOM element.\n      dialog.appendChild(template);\n    }\n    CodeMirror.addClass(wrap, 'dialog-opened');\n    return dialog;\n  }\n\n  function closeNotification(cm, newVal) {\n    if (cm.state.currentNotificationClose)\n      cm.state.currentNotificationClose();\n    cm.state.currentNotificationClose = newVal;\n  }\n\n  CodeMirror.defineExtension(\"openDialog\", function(template, callback, options) {\n    if (!options) options = {};\n\n    closeNotification(this, null);\n\n    var dialog = dialogDiv(this, template, options.bottom);\n    var closed = false, me = this;\n    function close(newVal) {\n      if (typeof newVal == 'string') {\n        inp.value = newVal;\n      } else {\n        if (closed) return;\n        closed = true;\n        CodeMirror.rmClass(dialog.parentNode, 'dialog-opened');\n        dialog.parentNode.removeChild(dialog);\n        me.focus();\n\n        if (options.onClose) options.onClose(dialog);\n      }\n    }\n\n    var inp = dialog.getElementsByTagName(\"input\")[0], button;\n    if (inp) {\n      inp.focus();\n\n      if (options.value) {\n        inp.value = options.value;\n        if (options.selectValueOnOpen !== false) {\n          inp.select();\n        }\n      }\n\n      if (options.onInput)\n        CodeMirror.on(inp, \"input\", function(e) { options.onInput(e, inp.value, close);});\n      if (options.onKeyUp)\n        CodeMirror.on(inp, \"keyup\", function(e) {options.onKeyUp(e, inp.value, close);});\n\n      CodeMirror.on(inp, \"keydown\", function(e) {\n        if (options && options.onKeyDown && options.onKeyDown(e, inp.value, close)) { return; }\n        if (e.keyCode == 27 || (options.closeOnEnter !== false && e.keyCode == 13)) {\n          inp.blur();\n          CodeMirror.e_stop(e);\n          close();\n        }\n        if (e.keyCode == 13) callback(inp.value, e);\n      });\n\n      if (options.closeOnBlur !== false) CodeMirror.on(dialog, \"focusout\", function (evt) {\n        if (evt.relatedTarget !== null) close();\n      });\n    } else if (button = dialog.getElementsByTagName(\"button\")[0]) {\n      CodeMirror.on(button, \"click\", function() {\n        close();\n        me.focus();\n      });\n\n      if (options.closeOnBlur !== false) CodeMirror.on(button, \"blur\", close);\n\n      button.focus();\n    }\n    return close;\n  });\n\n  CodeMirror.defineExtension(\"openConfirm\", function(template, callbacks, options) {\n    closeNotification(this, null);\n    var dialog = dialogDiv(this, template, options && options.bottom);\n    var buttons = dialog.getElementsByTagName(\"button\");\n    var closed = false, me = this, blurring = 1;\n    function close() {\n      if (closed) return;\n      closed = true;\n      CodeMirror.rmClass(dialog.parentNode, 'dialog-opened');\n      dialog.parentNode.removeChild(dialog);\n      me.focus();\n    }\n    buttons[0].focus();\n    for (var i = 0; i < buttons.length; ++i) {\n      var b = buttons[i];\n      (function(callback) {\n        CodeMirror.on(b, \"click\", function(e) {\n          CodeMirror.e_preventDefault(e);\n          close();\n          if (callback) callback(me);\n        });\n      })(callbacks[i]);\n      CodeMirror.on(b, \"blur\", function() {\n        --blurring;\n        setTimeout(function() { if (blurring <= 0) close(); }, 200);\n      });\n      CodeMirror.on(b, \"focus\", function() { ++blurring; });\n    }\n  });\n\n  /*\n   * openNotification\n   * Opens a notification, that can be closed with an optional timer\n   * (default 5000ms timer) and always closes on click.\n   *\n   * If a notification is opened while another is opened, it will close the\n   * currently opened one and open the new one immediately.\n   */\n  CodeMirror.defineExtension(\"openNotification\", function(template, options) {\n    closeNotification(this, close);\n    var dialog = dialogDiv(this, template, options && options.bottom);\n    var closed = false, doneTimer;\n    var duration = options && typeof options.duration !== \"undefined\" ? options.duration : 5000;\n\n    function close() {\n      if (closed) return;\n      closed = true;\n      clearTimeout(doneTimer);\n      CodeMirror.rmClass(dialog.parentNode, 'dialog-opened');\n      dialog.parentNode.removeChild(dialog);\n    }\n\n    CodeMirror.on(dialog, 'click', function(e) {\n      CodeMirror.e_preventDefault(e);\n      close();\n    });\n\n    if (duration)\n      doneTimer = setTimeout(close, duration);\n\n    return close;\n  });\n});\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAKA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACtB,eAAS,UAAU,IAAI,UAAU,QAAQ;AACvC,YAAI,OAAO,GAAG,kBAAkB;AAChC,YAAI;AACJ,iBAAS,KAAK,YAAY,SAAS,cAAc,KAAK,CAAC;AACvD,YAAI;AACF,iBAAO,YAAY;AAAA;AAEnB,iBAAO,YAAY;AAErB,YAAI,OAAO,YAAY,UAAU;AAC/B,iBAAO,YAAY;AAAA,QACrB,OAAO;AACL,iBAAO,YAAY,QAAQ;AAAA,QAC7B;AACA,QAAAA,YAAW,SAAS,MAAM,eAAe;AACzC,eAAO;AAAA,MACT;AAEA,eAAS,kBAAkB,IAAI,QAAQ;AACrC,YAAI,GAAG,MAAM;AACX,aAAG,MAAM,yBAAyB;AACpC,WAAG,MAAM,2BAA2B;AAAA,MACtC;AAEA,MAAAA,YAAW,gBAAgB,cAAc,SAAS,UAAU,UAAU,SAAS;AAC7E,YAAI,CAAC,QAAS,WAAU,CAAC;AAEzB,0BAAkB,MAAM,IAAI;AAE5B,YAAI,SAAS,UAAU,MAAM,UAAU,QAAQ,MAAM;AACrD,YAAI,SAAS,OAAO,KAAK;AACzB,iBAAS,MAAM,QAAQ;AACrB,cAAI,OAAO,UAAU,UAAU;AAC7B,gBAAI,QAAQ;AAAA,UACd,OAAO;AACL,gBAAI,OAAQ;AACZ,qBAAS;AACT,YAAAA,YAAW,QAAQ,OAAO,YAAY,eAAe;AACrD,mBAAO,WAAW,YAAY,MAAM;AACpC,eAAG,MAAM;AAET,gBAAI,QAAQ,QAAS,SAAQ,QAAQ,MAAM;AAAA,UAC7C;AAAA,QACF;AAEA,YAAI,MAAM,OAAO,qBAAqB,OAAO,EAAE,CAAC,GAAG;AACnD,YAAI,KAAK;AACP,cAAI,MAAM;AAEV,cAAI,QAAQ,OAAO;AACjB,gBAAI,QAAQ,QAAQ;AACpB,gBAAI,QAAQ,sBAAsB,OAAO;AACvC,kBAAI,OAAO;AAAA,YACb;AAAA,UACF;AAEA,cAAI,QAAQ;AACV,YAAAA,YAAW,GAAG,KAAK,SAAS,SAAS,GAAG;AAAE,sBAAQ,QAAQ,GAAG,IAAI,OAAO,KAAK;AAAA,YAAE,CAAC;AAClF,cAAI,QAAQ;AACV,YAAAA,YAAW,GAAG,KAAK,SAAS,SAAS,GAAG;AAAC,sBAAQ,QAAQ,GAAG,IAAI,OAAO,KAAK;AAAA,YAAE,CAAC;AAEjF,UAAAA,YAAW,GAAG,KAAK,WAAW,SAAS,GAAG;AACxC,gBAAI,WAAW,QAAQ,aAAa,QAAQ,UAAU,GAAG,IAAI,OAAO,KAAK,GAAG;AAAE;AAAA,YAAQ;AACtF,gBAAI,EAAE,WAAW,MAAO,QAAQ,iBAAiB,SAAS,EAAE,WAAW,IAAK;AAC1E,kBAAI,KAAK;AACT,cAAAA,YAAW,OAAO,CAAC;AACnB,oBAAM;AAAA,YACR;AACA,gBAAI,EAAE,WAAW,GAAI,UAAS,IAAI,OAAO,CAAC;AAAA,UAC5C,CAAC;AAED,cAAI,QAAQ,gBAAgB,MAAO,CAAAA,YAAW,GAAG,QAAQ,YAAY,SAAU,KAAK;AAClF,gBAAI,IAAI,kBAAkB,KAAM,OAAM;AAAA,UACxC,CAAC;AAAA,QACH,WAAW,SAAS,OAAO,qBAAqB,QAAQ,EAAE,CAAC,GAAG;AAC5D,UAAAA,YAAW,GAAG,QAAQ,SAAS,WAAW;AACxC,kBAAM;AACN,eAAG,MAAM;AAAA,UACX,CAAC;AAED,cAAI,QAAQ,gBAAgB,MAAO,CAAAA,YAAW,GAAG,QAAQ,QAAQ,KAAK;AAEtE,iBAAO,MAAM;AAAA,QACf;AACA,eAAO;AAAA,MACT,CAAC;AAED,MAAAA,YAAW,gBAAgB,eAAe,SAAS,UAAU,WAAW,SAAS;AAC/E,0BAAkB,MAAM,IAAI;AAC5B,YAAI,SAAS,UAAU,MAAM,UAAU,WAAW,QAAQ,MAAM;AAChE,YAAI,UAAU,OAAO,qBAAqB,QAAQ;AAClD,YAAI,SAAS,OAAO,KAAK,MAAM,WAAW;AAC1C,iBAAS,QAAQ;AACf,cAAI,OAAQ;AACZ,mBAAS;AACT,UAAAA,YAAW,QAAQ,OAAO,YAAY,eAAe;AACrD,iBAAO,WAAW,YAAY,MAAM;AACpC,aAAG,MAAM;AAAA,QACX;AACA,gBAAQ,CAAC,EAAE,MAAM;AACjB,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,EAAE,GAAG;AACvC,cAAI,IAAI,QAAQ,CAAC;AACjB,WAAC,SAAS,UAAU;AAClB,YAAAA,YAAW,GAAG,GAAG,SAAS,SAAS,GAAG;AACpC,cAAAA,YAAW,iBAAiB,CAAC;AAC7B,oBAAM;AACN,kBAAI,SAAU,UAAS,EAAE;AAAA,YAC3B,CAAC;AAAA,UACH,GAAG,UAAU,CAAC,CAAC;AACf,UAAAA,YAAW,GAAG,GAAG,QAAQ,WAAW;AAClC,cAAE;AACF,uBAAW,WAAW;AAAE,kBAAI,YAAY,EAAG,OAAM;AAAA,YAAG,GAAG,GAAG;AAAA,UAC5D,CAAC;AACD,UAAAA,YAAW,GAAG,GAAG,SAAS,WAAW;AAAE,cAAE;AAAA,UAAU,CAAC;AAAA,QACtD;AAAA,MACF,CAAC;AAUD,MAAAA,YAAW,gBAAgB,oBAAoB,SAAS,UAAU,SAAS;AACzE,0BAAkB,MAAM,KAAK;AAC7B,YAAI,SAAS,UAAU,MAAM,UAAU,WAAW,QAAQ,MAAM;AAChE,YAAI,SAAS,OAAO;AACpB,YAAI,WAAW,WAAW,OAAO,QAAQ,aAAa,cAAc,QAAQ,WAAW;AAEvF,iBAAS,QAAQ;AACf,cAAI,OAAQ;AACZ,mBAAS;AACT,uBAAa,SAAS;AACtB,UAAAA,YAAW,QAAQ,OAAO,YAAY,eAAe;AACrD,iBAAO,WAAW,YAAY,MAAM;AAAA,QACtC;AAEA,QAAAA,YAAW,GAAG,QAAQ,SAAS,SAAS,GAAG;AACzC,UAAAA,YAAW,iBAAiB,CAAC;AAC7B,gBAAM;AAAA,QACR,CAAC;AAED,YAAI;AACF,sBAAY,WAAW,OAAO,QAAQ;AAExC,eAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA;AAAA;", "names": ["CodeMirror"]}