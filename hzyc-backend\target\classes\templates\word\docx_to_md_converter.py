#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DOCX to Markdown 批量转换工具
将指定目录下的所有DOCX文件转换为Markdown格式
并输出到同目录的output文件夹中
"""

import os
import sys
from pathlib import Path
import argparse
from typing import List, <PERSON><PERSON>

try:
    import mammoth
except ImportError:
    print("错误: 请先安装mammoth库")
    print("运行命令: pip install mammoth")
    sys.exit(1)

def find_docx_files(directory: str) -> List[Path]:
    """
    在指定目录中查找所有DOCX文件
    
    Args:
        directory: 目标目录路径
        
    Returns:
        DOCX文件路径列表
    """
    directory_path = Path(directory)
    if not directory_path.exists():
        raise FileNotFoundError(f"目录不存在: {directory}")
    
    if not directory_path.is_dir():
        raise NotADirectoryError(f"路径不是目录: {directory}")
    
    # 查找所有.docx文件（不区分大小写）
    docx_files = []
    for file_path in directory_path.rglob('*'):
        if file_path.is_file() and file_path.suffix.lower() == '.docx':
            # 排除临时文件（以~$开头的文件）
            if not file_path.name.startswith('~$'):
                docx_files.append(file_path)
    
    return docx_files

def convert_docx_to_markdown(docx_path: Path, output_dir: Path) -> Tuple[bool, str]:
    """
    将单个DOCX文件转换为Markdown
    
    Args:
        docx_path: DOCX文件路径
        output_dir: 输出目录路径
        
    Returns:
        (成功标志, 错误信息或成功信息)
    """
    try:
        # 读取DOCX文件
        with open(docx_path, 'rb') as docx_file:
            result = mammoth.convert_to_markdown(docx_file)
            markdown_content = result.value
            
            # 如果有警告信息，打印出来
            if result.messages:
                print(f"警告 ({docx_path.name}):")
                for message in result.messages:
                    print(f"  - {message}")
        
        # 生成输出文件路径
        md_filename = docx_path.stem + '.md'
        output_path = output_dir / md_filename
        
        # 写入Markdown文件
        with open(output_path, 'w', encoding='utf-8') as md_file:
            md_file.write(markdown_content)
        
        return True, f"成功转换: {docx_path.name} -> {md_filename}"
        
    except Exception as e:
        return False, f"转换失败 ({docx_path.name}): {str(e)}"

def create_output_directory(base_dir: Path) -> Path:
    """
    创建输出目录
    
    Args:
        base_dir: 基础目录路径
        
    Returns:
        输出目录路径
    """
    output_dir = base_dir / 'output'
    output_dir.mkdir(exist_ok=True)
    return output_dir

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(
        description='批量将DOCX文件转换为Markdown格式',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python docx_to_md_converter.py /path/to/docx/files
  python docx_to_md_converter.py . --verbose
  python docx_to_md_converter.py /documents --output-dir /custom/output
        """
    )
    
    parser.add_argument(
        'directory',
        help='包含DOCX文件的目录路径'
    )
    
    parser.add_argument(
        '--output-dir',
        help='自定义输出目录（默认为源目录下的output文件夹）'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='显示详细输出信息'
    )
    
    parser.add_argument(
        '--recursive', '-r',
        action='store_true',
        help='递归搜索子目录中的DOCX文件'
    )
    
    args = parser.parse_args()
    
    try:
        # 获取源目录
        source_dir = Path(args.directory).resolve()
        print(f"源目录: {source_dir}")
        
        # 查找DOCX文件
        print("正在搜索DOCX文件...")
        if args.recursive:
            docx_files = find_docx_files(source_dir)
        else:
            # 只搜索当前目录，不递归
            docx_files = [f for f in source_dir.iterdir() 
                         if f.is_file() and f.suffix.lower() == '.docx' 
                         and not f.name.startswith('~$')]
        
        if not docx_files:
            print("未找到任何DOCX文件")
            return
        
        print(f"找到 {len(docx_files)} 个DOCX文件")
        
        # 创建输出目录
        if args.output_dir:
            output_dir = Path(args.output_dir).resolve()
            output_dir.mkdir(parents=True, exist_ok=True)
        else:
            output_dir = create_output_directory(source_dir)
        
        print(f"输出目录: {output_dir}")
        
        # 批量转换
        print("\n开始转换...")
        success_count = 0
        failed_count = 0
        
        for i, docx_file in enumerate(docx_files, 1):
            if args.verbose:
                print(f"\n[{i}/{len(docx_files)}] 正在处理: {docx_file.name}")
            
            success, message = convert_docx_to_markdown(docx_file, output_dir)
            
            if success:
                success_count += 1
                if args.verbose:
                    print(f"  ✓ {message}")
            else:
                failed_count += 1
                print(f"  ✗ {message}")
        
        # 输出统计信息
        print(f"\n转换完成!")
        print(f"成功: {success_count} 个文件")
        print(f"失败: {failed_count} 个文件")
        print(f"输出目录: {output_dir}")
        
        if failed_count > 0:
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()