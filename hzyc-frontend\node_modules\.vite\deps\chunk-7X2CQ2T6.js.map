{"version": 3, "sources": ["../../codemirror/addon/search/matchesonscrollbar.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"), require(\"./searchcursor\"), require(\"../scroll/annotatescrollbar\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\", \"./searchcursor\", \"../scroll/annotatescrollbar\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  CodeMirror.defineExtension(\"showMatchesOnScrollbar\", function(query, caseFold, options) {\n    if (typeof options == \"string\") options = {className: options};\n    if (!options) options = {};\n    return new SearchAnnotation(this, query, caseFold, options);\n  });\n\n  function SearchAnnotation(cm, query, caseFold, options) {\n    this.cm = cm;\n    this.options = options;\n    var annotateOptions = {listenForChanges: false};\n    for (var prop in options) annotateOptions[prop] = options[prop];\n    if (!annotateOptions.className) annotateOptions.className = \"CodeMirror-search-match\";\n    this.annotation = cm.annotateScrollbar(annotateOptions);\n    this.query = query;\n    this.caseFold = caseFold;\n    this.gap = {from: cm.firstLine(), to: cm.lastLine() + 1};\n    this.matches = [];\n    this.update = null;\n\n    this.findMatches();\n    this.annotation.update(this.matches);\n\n    var self = this;\n    cm.on(\"change\", this.changeHandler = function(_cm, change) { self.onChange(change); });\n  }\n\n  var MAX_MATCHES = 1000;\n\n  SearchAnnotation.prototype.findMatches = function() {\n    if (!this.gap) return;\n    for (var i = 0; i < this.matches.length; i++) {\n      var match = this.matches[i];\n      if (match.from.line >= this.gap.to) break;\n      if (match.to.line >= this.gap.from) this.matches.splice(i--, 1);\n    }\n    var cursor = this.cm.getSearchCursor(this.query, CodeMirror.Pos(this.gap.from, 0), {caseFold: this.caseFold, multiline: this.options.multiline});\n    var maxMatches = this.options && this.options.maxMatches || MAX_MATCHES;\n    while (cursor.findNext()) {\n      var match = {from: cursor.from(), to: cursor.to()};\n      if (match.from.line >= this.gap.to) break;\n      this.matches.splice(i++, 0, match);\n      if (this.matches.length > maxMatches) break;\n    }\n    this.gap = null;\n  };\n\n  function offsetLine(line, changeStart, sizeChange) {\n    if (line <= changeStart) return line;\n    return Math.max(changeStart, line + sizeChange);\n  }\n\n  SearchAnnotation.prototype.onChange = function(change) {\n    var startLine = change.from.line;\n    var endLine = CodeMirror.changeEnd(change).line;\n    var sizeChange = endLine - change.to.line;\n    if (this.gap) {\n      this.gap.from = Math.min(offsetLine(this.gap.from, startLine, sizeChange), change.from.line);\n      this.gap.to = Math.max(offsetLine(this.gap.to, startLine, sizeChange), change.from.line);\n    } else {\n      this.gap = {from: change.from.line, to: endLine + 1};\n    }\n\n    if (sizeChange) for (var i = 0; i < this.matches.length; i++) {\n      var match = this.matches[i];\n      var newFrom = offsetLine(match.from.line, startLine, sizeChange);\n      if (newFrom != match.from.line) match.from = CodeMirror.Pos(newFrom, match.from.ch);\n      var newTo = offsetLine(match.to.line, startLine, sizeChange);\n      if (newTo != match.to.line) match.to = CodeMirror.Pos(newTo, match.to.ch);\n    }\n    clearTimeout(this.update);\n    var self = this;\n    this.update = setTimeout(function() { self.updateAfterChange(); }, 250);\n  };\n\n  SearchAnnotation.prototype.updateAfterChange = function() {\n    this.findMatches();\n    this.annotation.update(this.matches);\n  };\n\n  SearchAnnotation.prototype.clear = function() {\n    this.cm.off(\"change\", this.changeHandler);\n    this.annotation.clear();\n  };\n});\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,sBAAiC,wBAA2B,2BAAsC;AAAA,eAC/F,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,wBAAwB,kBAAkB,6BAA6B,GAAG,GAAG;AAAA;AAErF,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACtB;AAEA,MAAAA,YAAW,gBAAgB,0BAA0B,SAAS,OAAO,UAAU,SAAS;AACtF,YAAI,OAAO,WAAW,SAAU,WAAU,EAAC,WAAW,QAAO;AAC7D,YAAI,CAAC,QAAS,WAAU,CAAC;AACzB,eAAO,IAAI,iBAAiB,MAAM,OAAO,UAAU,OAAO;AAAA,MAC5D,CAAC;AAED,eAAS,iBAAiB,IAAI,OAAO,UAAU,SAAS;AACtD,aAAK,KAAK;AACV,aAAK,UAAU;AACf,YAAI,kBAAkB,EAAC,kBAAkB,MAAK;AAC9C,iBAAS,QAAQ,QAAS,iBAAgB,IAAI,IAAI,QAAQ,IAAI;AAC9D,YAAI,CAAC,gBAAgB,UAAW,iBAAgB,YAAY;AAC5D,aAAK,aAAa,GAAG,kBAAkB,eAAe;AACtD,aAAK,QAAQ;AACb,aAAK,WAAW;AAChB,aAAK,MAAM,EAAC,MAAM,GAAG,UAAU,GAAG,IAAI,GAAG,SAAS,IAAI,EAAC;AACvD,aAAK,UAAU,CAAC;AAChB,aAAK,SAAS;AAEd,aAAK,YAAY;AACjB,aAAK,WAAW,OAAO,KAAK,OAAO;AAEnC,YAAI,OAAO;AACX,WAAG,GAAG,UAAU,KAAK,gBAAgB,SAAS,KAAK,QAAQ;AAAE,eAAK,SAAS,MAAM;AAAA,QAAG,CAAC;AAAA,MACvF;AAEA,UAAI,cAAc;AAElB,uBAAiB,UAAU,cAAc,WAAW;AAClD,YAAI,CAAC,KAAK,IAAK;AACf,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK;AAC5C,cAAI,QAAQ,KAAK,QAAQ,CAAC;AAC1B,cAAI,MAAM,KAAK,QAAQ,KAAK,IAAI,GAAI;AACpC,cAAI,MAAM,GAAG,QAAQ,KAAK,IAAI,KAAM,MAAK,QAAQ,OAAO,KAAK,CAAC;AAAA,QAChE;AACA,YAAI,SAAS,KAAK,GAAG,gBAAgB,KAAK,OAAOA,YAAW,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,EAAC,UAAU,KAAK,UAAU,WAAW,KAAK,QAAQ,UAAS,CAAC;AAC/I,YAAI,aAAa,KAAK,WAAW,KAAK,QAAQ,cAAc;AAC5D,eAAO,OAAO,SAAS,GAAG;AACxB,cAAI,QAAQ,EAAC,MAAM,OAAO,KAAK,GAAG,IAAI,OAAO,GAAG,EAAC;AACjD,cAAI,MAAM,KAAK,QAAQ,KAAK,IAAI,GAAI;AACpC,eAAK,QAAQ,OAAO,KAAK,GAAG,KAAK;AACjC,cAAI,KAAK,QAAQ,SAAS,WAAY;AAAA,QACxC;AACA,aAAK,MAAM;AAAA,MACb;AAEA,eAAS,WAAW,MAAM,aAAa,YAAY;AACjD,YAAI,QAAQ,YAAa,QAAO;AAChC,eAAO,KAAK,IAAI,aAAa,OAAO,UAAU;AAAA,MAChD;AAEA,uBAAiB,UAAU,WAAW,SAAS,QAAQ;AACrD,YAAI,YAAY,OAAO,KAAK;AAC5B,YAAI,UAAUA,YAAW,UAAU,MAAM,EAAE;AAC3C,YAAI,aAAa,UAAU,OAAO,GAAG;AACrC,YAAI,KAAK,KAAK;AACZ,eAAK,IAAI,OAAO,KAAK,IAAI,WAAW,KAAK,IAAI,MAAM,WAAW,UAAU,GAAG,OAAO,KAAK,IAAI;AAC3F,eAAK,IAAI,KAAK,KAAK,IAAI,WAAW,KAAK,IAAI,IAAI,WAAW,UAAU,GAAG,OAAO,KAAK,IAAI;AAAA,QACzF,OAAO;AACL,eAAK,MAAM,EAAC,MAAM,OAAO,KAAK,MAAM,IAAI,UAAU,EAAC;AAAA,QACrD;AAEA,YAAI,WAAY,UAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK;AAC5D,cAAI,QAAQ,KAAK,QAAQ,CAAC;AAC1B,cAAI,UAAU,WAAW,MAAM,KAAK,MAAM,WAAW,UAAU;AAC/D,cAAI,WAAW,MAAM,KAAK,KAAM,OAAM,OAAOA,YAAW,IAAI,SAAS,MAAM,KAAK,EAAE;AAClF,cAAI,QAAQ,WAAW,MAAM,GAAG,MAAM,WAAW,UAAU;AAC3D,cAAI,SAAS,MAAM,GAAG,KAAM,OAAM,KAAKA,YAAW,IAAI,OAAO,MAAM,GAAG,EAAE;AAAA,QAC1E;AACA,qBAAa,KAAK,MAAM;AACxB,YAAI,OAAO;AACX,aAAK,SAAS,WAAW,WAAW;AAAE,eAAK,kBAAkB;AAAA,QAAG,GAAG,GAAG;AAAA,MACxE;AAEA,uBAAiB,UAAU,oBAAoB,WAAW;AACxD,aAAK,YAAY;AACjB,aAAK,WAAW,OAAO,KAAK,OAAO;AAAA,MACrC;AAEA,uBAAiB,UAAU,QAAQ,WAAW;AAC5C,aAAK,GAAG,IAAI,UAAU,KAAK,aAAa;AACxC,aAAK,WAAW,MAAM;AAAA,MACxB;AAAA,IACF,CAAC;AAAA;AAAA;", "names": ["CodeMirror"]}