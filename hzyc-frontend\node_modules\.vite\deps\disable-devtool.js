import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/disable-devtool/disable-devtool.min.js
var require_disable_devtool_min = __commonJS({
  "node_modules/disable-devtool/disable-devtool.min.js"(exports, module) {
    !function(e, t) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = t() : "function" == typeof define && define.amd ? define(t) : (e = "undefined" != typeof globalThis ? globalThis : e || self).DisableDevtool = t();
    }(exports, function() {
      "use strict";
      function o(e2) {
        return (o = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e3) {
          return typeof e3;
        } : function(e3) {
          return e3 && "function" == typeof Symbol && e3.constructor === Symbol && e3 !== Symbol.prototype ? "symbol" : typeof e3;
        })(e2);
      }
      function i(e2, t2) {
        if (!(e2 instanceof t2)) throw new TypeError("Cannot call a class as a function");
      }
      function r(e2, t2) {
        for (var n2 = 0; n2 < t2.length; n2++) {
          var i2 = t2[n2];
          i2.enumerable = i2.enumerable || false, i2.configurable = true, "value" in i2 && (i2.writable = true), Object.defineProperty(e2, i2.key, i2);
        }
      }
      function u(e2, t2, n2) {
        t2 && r(e2.prototype, t2), n2 && r(e2, n2), Object.defineProperty(e2, "prototype", { writable: false });
      }
      function e(e2, t2, n2) {
        t2 in e2 ? Object.defineProperty(e2, t2, { value: n2, enumerable: true, configurable: true, writable: true }) : e2[t2] = n2;
      }
      function n(e2, t2) {
        if ("function" != typeof t2 && null !== t2) throw new TypeError("Super expression must either be null or a function");
        e2.prototype = Object.create(t2 && t2.prototype, { constructor: { value: e2, writable: true, configurable: true } }), Object.defineProperty(e2, "prototype", { writable: false }), t2 && a(e2, t2);
      }
      function c(e2) {
        return (c = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(e3) {
          return e3.__proto__ || Object.getPrototypeOf(e3);
        })(e2);
      }
      function a(e2, t2) {
        return (a = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(e3, t3) {
          return e3.__proto__ = t3, e3;
        })(e2, t2);
      }
      function H(e2, t2) {
        if (t2 && ("object" == typeof t2 || "function" == typeof t2)) return t2;
        if (void 0 !== t2) throw new TypeError("Derived constructors may only return object or undefined");
        t2 = e2;
        if (void 0 === t2) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
        return t2;
      }
      function l(n2) {
        var i2 = function() {
          if ("undefined" == typeof Reflect || !Reflect.construct) return false;
          if (Reflect.construct.sham) return false;
          if ("function" == typeof Proxy) return true;
          try {
            return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
            })), true;
          } catch (e2) {
            return false;
          }
        }();
        return function() {
          var e2, t2 = c(n2);
          return H(this, i2 ? (e2 = c(this).constructor, Reflect.construct(t2, arguments, e2)) : t2.apply(this, arguments));
        };
      }
      function f(e2, t2) {
        (null == t2 || t2 > e2.length) && (t2 = e2.length);
        for (var n2 = 0, i2 = new Array(t2); n2 < t2; n2++) i2[n2] = e2[n2];
        return i2;
      }
      function s(e2, t2) {
        var n2, i2 = "undefined" != typeof Symbol && e2[Symbol.iterator] || e2["@@iterator"];
        if (!i2) {
          if (Array.isArray(e2) || (i2 = function(e3, t3) {
            if (e3) {
              if ("string" == typeof e3) return f(e3, t3);
              var n3 = Object.prototype.toString.call(e3).slice(8, -1);
              return "Map" === (n3 = "Object" === n3 && e3.constructor ? e3.constructor.name : n3) || "Set" === n3 ? Array.from(e3) : "Arguments" === n3 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n3) ? f(e3, t3) : void 0;
            }
          }(e2)) || t2 && e2 && "number" == typeof e2.length) return i2 && (e2 = i2), n2 = 0, { s: t2 = function() {
          }, n: function() {
            return n2 >= e2.length ? { done: true } : { done: false, value: e2[n2++] };
          }, e: function(e3) {
            throw e3;
          }, f: t2 };
          throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
        }
        var o2, r2 = true, u2 = false;
        return { s: function() {
          i2 = i2.call(e2);
        }, n: function() {
          var e3 = i2.next();
          return r2 = e3.done, e3;
        }, e: function(e3) {
          u2 = true, o2 = e3;
        }, f: function() {
          try {
            r2 || null == i2.return || i2.return();
          } finally {
            if (u2) throw o2;
          }
        } };
      }
      function t() {
        if (d.url) window.location.href = d.url;
        else if (d.rewriteHTML) try {
          document.documentElement.innerHTML = d.rewriteHTML;
        } catch (e2) {
          document.documentElement.innerText = d.rewriteHTML;
        }
        else {
          try {
            window.opener = null, window.open("", "_self"), window.close(), window.history.back();
          } catch (e2) {
            console.log(e2);
          }
          setTimeout(function() {
            window.location.href = d.timeOutUrl || "https://theajack.github.io/disable-devtool/404.html?h=".concat(encodeURIComponent(location.host));
          }, 500);
        }
      }
      var d = { md5: "", ondevtoolopen: t, ondevtoolclose: null, url: "", timeOutUrl: "", tkName: "ddtk", interval: 500, disableMenu: true, stopIntervalTime: 5e3, clearIntervalWhenDevOpenTrigger: false, detectors: [0, 1, 3, 4, 5, 6, 7], clearLog: true, disableSelect: false, disableCopy: false, disableCut: false, disablePaste: false, ignore: null, disableIframeParents: true, seo: true, rewriteHTML: "" }, U = ["detectors", "ondevtoolclose", "ignore"];
      function q(e2) {
        var t2, n2 = 0 < arguments.length && void 0 !== e2 ? e2 : {};
        for (t2 in d) {
          var i2 = t2;
          void 0 === n2[i2] || o(d[i2]) !== o(n2[i2]) && -1 === U.indexOf(i2) || (d[i2] = n2[i2]);
        }
        "function" == typeof d.ondevtoolclose && true === d.clearIntervalWhenDevOpenTrigger && (d.clearIntervalWhenDevOpenTrigger = false, console.warn("【DISABLE-DEVTOOL】clearIntervalWhenDevOpenTrigger 在使用 ondevtoolclose 时无效"));
      }
      function v() {
        return (/* @__PURE__ */ new Date()).getTime();
      }
      function h(e2) {
        var t2 = v();
        return e2(), v() - t2;
      }
      function z(n2, i2) {
        function e2(t3) {
          return function() {
            n2 && n2();
            var e3 = t3.apply(void 0, arguments);
            return i2 && i2(), e3;
          };
        }
        var t2 = window.alert, o2 = window.confirm, r2 = window.prompt;
        try {
          window.alert = e2(t2), window.confirm = e2(o2), window.prompt = e2(r2);
        } catch (e3) {
        }
      }
      var p, y, B, b = { iframe: false, pc: false, qqBrowser: false, firefox: false, macos: false, edge: false, oldEdge: false, ie: false, iosChrome: false, iosEdge: false, chrome: false, seoBot: false, mobile: false };
      function W() {
        function e2(e3) {
          return -1 !== t2.indexOf(e3);
        }
        var t2 = navigator.userAgent.toLowerCase(), n2 = function() {
          var e3 = navigator, t3 = e3.platform, e3 = e3.maxTouchPoints;
          if ("number" == typeof e3) return 1 < e3;
          if ("string" == typeof t3) {
            e3 = t3.toLowerCase();
            if (/(mac|win)/i.test(e3)) return false;
            if (/(android|iphone|ipad|ipod|arch)/i.test(e3)) return true;
          }
          return /(iphone|ipad|ipod|ios|android)/i.test(navigator.userAgent.toLowerCase());
        }(), i2 = !!window.top && window !== window.top, o2 = !n2, r2 = e2("qqbrowser"), u2 = e2("firefox"), c2 = e2("macintosh"), a2 = e2("edge"), l2 = a2 && !e2("chrome"), f2 = l2 || e2("trident") || e2("msie"), s2 = e2("crios"), d2 = e2("edgios"), v2 = e2("chrome") || s2, h2 = !n2 && /(googlebot|baiduspider|bingbot|applebot|petalbot|yandexbot|bytespider|chrome\-lighthouse|moto g power)/i.test(t2);
        Object.assign(b, { iframe: i2, pc: o2, qqBrowser: r2, firefox: u2, macos: c2, edge: a2, oldEdge: l2, ie: f2, iosChrome: s2, iosEdge: d2, chrome: v2, seoBot: h2, mobile: n2 });
      }
      function M() {
        for (var e2 = function() {
          for (var e3 = {}, t3 = 0; t3 < 500; t3++) e3["".concat(t3)] = "".concat(t3);
          return e3;
        }(), t2 = [], n2 = 0; n2 < 50; n2++) t2.push(e2);
        return t2;
      }
      function w() {
        d.clearLog && B();
      }
      var K = "", V = false;
      function F() {
        var e2 = d.ignore;
        if (e2) {
          if ("function" == typeof e2) return e2();
          if (0 !== e2.length) {
            var t2 = location.href;
            if (K === t2) return V;
            K = t2;
            var n2, i2 = false, o2 = s(e2);
            try {
              for (o2.s(); !(n2 = o2.n()).done; ) {
                var r2 = n2.value;
                if ("string" == typeof r2) {
                  if (-1 !== t2.indexOf(r2)) {
                    i2 = true;
                    break;
                  }
                } else if (r2.test(t2)) {
                  i2 = true;
                  break;
                }
              }
            } catch (e3) {
              o2.e(e3);
            } finally {
              o2.f();
            }
            return V = i2;
          }
        }
      }
      var X = function() {
        return false;
      };
      function g(n2) {
        var t2, e2, i2 = 74, o2 = 73, r2 = 85, u2 = 83, c2 = 123, a2 = b.macos ? function(e3, t3) {
          return e3.metaKey && e3.altKey && (t3 === o2 || t3 === i2);
        } : function(e3, t3) {
          return e3.ctrlKey && e3.shiftKey && (t3 === o2 || t3 === i2);
        }, l2 = b.macos ? function(e3, t3) {
          return e3.metaKey && e3.altKey && t3 === r2 || e3.metaKey && t3 === u2;
        } : function(e3, t3) {
          return e3.ctrlKey && (t3 === u2 || t3 === r2);
        };
        n2.addEventListener("keydown", function(e3) {
          var t3 = (e3 = e3 || n2.event).keyCode || e3.which;
          if (t3 === c2 || a2(e3, t3) || l2(e3, t3)) return T(n2, e3);
        }, true), t2 = n2, d.disableMenu && t2.addEventListener("contextmenu", function(e3) {
          if ("touch" !== e3.pointerType) return T(t2, e3);
        }), e2 = n2, d.disableSelect && m(e2, "selectstart"), e2 = n2, d.disableCopy && m(e2, "copy"), e2 = n2, d.disableCut && m(e2, "cut"), e2 = n2, d.disablePaste && m(e2, "paste");
      }
      function m(t2, e2) {
        t2.addEventListener(e2, function(e3) {
          return T(t2, e3);
        });
      }
      function T(e2, t2) {
        if (!F() && !X()) return (t2 = t2 || e2.event).returnValue = false, t2.preventDefault(), false;
      }
      var O, D = false, S = {};
      function N(e2) {
        S[e2] = false;
      }
      function $() {
        for (var e2 in S) if (S[e2]) return D = true;
        return D = false;
      }
      (_ = O = O || {})[_.Unknown = -1] = "Unknown", _[_.RegToString = 0] = "RegToString", _[_.DefineId = 1] = "DefineId", _[_.Size = 2] = "Size", _[_.DateToString = 3] = "DateToString", _[_.FuncToString = 4] = "FuncToString", _[_.Debugger = 5] = "Debugger", _[_.Performance = 6] = "Performance", _[_.DebugLib = 7] = "DebugLib";
      var k = function() {
        function n2(e2) {
          var t2 = e2.type, e2 = e2.enabled, e2 = void 0 === e2 || e2;
          i(this, n2), this.type = O.Unknown, this.enabled = true, this.type = t2, this.enabled = e2, this.enabled && (t2 = this, Q.push(t2), this.init());
        }
        return u(n2, [{ key: "onDevToolOpen", value: function() {
          var e2;
          console.warn("You don't have permission to use DEVTOOL!【type = ".concat(this.type, "】")), d.clearIntervalWhenDevOpenTrigger && te(), window.clearTimeout(J), d.ondevtoolopen(this.type, t), e2 = this.type, S[e2] = true;
        } }, { key: "init", value: function() {
        } }]), n2;
      }(), G = function() {
        n(t2, k);
        var e2 = l(t2);
        function t2() {
          return i(this, t2), e2.call(this, { type: O.DebugLib });
        }
        return u(t2, [{ key: "init", value: function() {
        } }, { key: "detect", value: function() {
          var e3;
          (true === (null == (e3 = null == (e3 = window.eruda) ? void 0 : e3._devTools) ? void 0 : e3._isShow) || window._vcOrigConsole && window.document.querySelector("#__vconsole.vc-toggle")) && this.onDevToolOpen();
        } }], [{ key: "isUsing", value: function() {
          return !!window.eruda || !!window._vcOrigConsole;
        } }]), t2;
      }(), Y = 0, J = 0, Q = [], Z = 0;
      function ee(o2) {
        function e2() {
          l2 = true;
        }
        function t2() {
          l2 = false;
        }
        var n2, i2, r2, u2, c2, a2, l2 = false;
        function f2() {
          (a2[u2] === r2 ? i2 : n2)();
        }
        z(e2, t2), n2 = t2, i2 = e2, void 0 !== (a2 = document).hidden ? (r2 = "hidden", c2 = "visibilitychange", u2 = "visibilityState") : void 0 !== a2.mozHidden ? (r2 = "mozHidden", c2 = "mozvisibilitychange", u2 = "mozVisibilityState") : void 0 !== a2.msHidden ? (r2 = "msHidden", c2 = "msvisibilitychange", u2 = "msVisibilityState") : void 0 !== a2.webkitHidden && (r2 = "webkitHidden", c2 = "webkitvisibilitychange", u2 = "webkitVisibilityState"), a2.removeEventListener(c2, f2, false), a2.addEventListener(c2, f2, false), Y = window.setInterval(function() {
          if (!(o2.isSuspend || l2 || F())) {
            var e3, t3, n3 = s(Q);
            try {
              for (n3.s(); !(e3 = n3.n()).done; ) {
                var i3 = e3.value;
                N(i3.type), i3.detect(Z++);
              }
            } catch (e4) {
              n3.e(e4);
            } finally {
              n3.f();
            }
            w(), "function" == typeof d.ondevtoolclose && (t3 = D, !$() && t3 && d.ondevtoolclose());
          }
        }, d.interval), J = setTimeout(function() {
          b.pc || G.isUsing() || te();
        }, d.stopIntervalTime);
      }
      function te() {
        window.clearInterval(Y);
      }
      var P = 8;
      function ne(e2) {
        for (var t2 = function(e3, t3) {
          e3[t3 >> 5] |= 128 << t3 % 32, e3[14 + (t3 + 64 >>> 9 << 4)] = t3;
          for (var n3 = 1732584193, i3 = -271733879, o3 = -1732584194, r2 = 271733878, u2 = 0; u2 < e3.length; u2 += 16) {
            var c2 = n3, a2 = i3, l2 = o3, f2 = r2;
            n3 = E(n3, i3, o3, r2, e3[u2 + 0], 7, -680876936), r2 = E(r2, n3, i3, o3, e3[u2 + 1], 12, -389564586), o3 = E(o3, r2, n3, i3, e3[u2 + 2], 17, 606105819), i3 = E(i3, o3, r2, n3, e3[u2 + 3], 22, -1044525330), n3 = E(n3, i3, o3, r2, e3[u2 + 4], 7, -176418897), r2 = E(r2, n3, i3, o3, e3[u2 + 5], 12, 1200080426), o3 = E(o3, r2, n3, i3, e3[u2 + 6], 17, -1473231341), i3 = E(i3, o3, r2, n3, e3[u2 + 7], 22, -45705983), n3 = E(n3, i3, o3, r2, e3[u2 + 8], 7, 1770035416), r2 = E(r2, n3, i3, o3, e3[u2 + 9], 12, -1958414417), o3 = E(o3, r2, n3, i3, e3[u2 + 10], 17, -42063), i3 = E(i3, o3, r2, n3, e3[u2 + 11], 22, -1990404162), n3 = E(n3, i3, o3, r2, e3[u2 + 12], 7, 1804603682), r2 = E(r2, n3, i3, o3, e3[u2 + 13], 12, -40341101), o3 = E(o3, r2, n3, i3, e3[u2 + 14], 17, -1502002290), i3 = E(i3, o3, r2, n3, e3[u2 + 15], 22, 1236535329), n3 = j(n3, i3, o3, r2, e3[u2 + 1], 5, -165796510), r2 = j(r2, n3, i3, o3, e3[u2 + 6], 9, -1069501632), o3 = j(o3, r2, n3, i3, e3[u2 + 11], 14, 643717713), i3 = j(i3, o3, r2, n3, e3[u2 + 0], 20, -373897302), n3 = j(n3, i3, o3, r2, e3[u2 + 5], 5, -701558691), r2 = j(r2, n3, i3, o3, e3[u2 + 10], 9, 38016083), o3 = j(o3, r2, n3, i3, e3[u2 + 15], 14, -660478335), i3 = j(i3, o3, r2, n3, e3[u2 + 4], 20, -405537848), n3 = j(n3, i3, o3, r2, e3[u2 + 9], 5, 568446438), r2 = j(r2, n3, i3, o3, e3[u2 + 14], 9, -1019803690), o3 = j(o3, r2, n3, i3, e3[u2 + 3], 14, -187363961), i3 = j(i3, o3, r2, n3, e3[u2 + 8], 20, 1163531501), n3 = j(n3, i3, o3, r2, e3[u2 + 13], 5, -1444681467), r2 = j(r2, n3, i3, o3, e3[u2 + 2], 9, -51403784), o3 = j(o3, r2, n3, i3, e3[u2 + 7], 14, 1735328473), i3 = j(i3, o3, r2, n3, e3[u2 + 12], 20, -1926607734), n3 = I(n3, i3, o3, r2, e3[u2 + 5], 4, -378558), r2 = I(r2, n3, i3, o3, e3[u2 + 8], 11, -2022574463), o3 = I(o3, r2, n3, i3, e3[u2 + 11], 16, 1839030562), i3 = I(i3, o3, r2, n3, e3[u2 + 14], 23, -35309556), n3 = I(n3, i3, o3, r2, e3[u2 + 1], 4, -1530992060), r2 = I(r2, n3, i3, o3, e3[u2 + 4], 11, 1272893353), o3 = I(o3, r2, n3, i3, e3[u2 + 7], 16, -155497632), i3 = I(i3, o3, r2, n3, e3[u2 + 10], 23, -1094730640), n3 = I(n3, i3, o3, r2, e3[u2 + 13], 4, 681279174), r2 = I(r2, n3, i3, o3, e3[u2 + 0], 11, -358537222), o3 = I(o3, r2, n3, i3, e3[u2 + 3], 16, -722521979), i3 = I(i3, o3, r2, n3, e3[u2 + 6], 23, 76029189), n3 = I(n3, i3, o3, r2, e3[u2 + 9], 4, -640364487), r2 = I(r2, n3, i3, o3, e3[u2 + 12], 11, -421815835), o3 = I(o3, r2, n3, i3, e3[u2 + 15], 16, 530742520), i3 = I(i3, o3, r2, n3, e3[u2 + 2], 23, -995338651), n3 = L(n3, i3, o3, r2, e3[u2 + 0], 6, -198630844), r2 = L(r2, n3, i3, o3, e3[u2 + 7], 10, 1126891415), o3 = L(o3, r2, n3, i3, e3[u2 + 14], 15, -1416354905), i3 = L(i3, o3, r2, n3, e3[u2 + 5], 21, -57434055), n3 = L(n3, i3, o3, r2, e3[u2 + 12], 6, 1700485571), r2 = L(r2, n3, i3, o3, e3[u2 + 3], 10, -1894986606), o3 = L(o3, r2, n3, i3, e3[u2 + 10], 15, -1051523), i3 = L(i3, o3, r2, n3, e3[u2 + 1], 21, -2054922799), n3 = L(n3, i3, o3, r2, e3[u2 + 8], 6, 1873313359), r2 = L(r2, n3, i3, o3, e3[u2 + 15], 10, -30611744), o3 = L(o3, r2, n3, i3, e3[u2 + 6], 15, -1560198380), i3 = L(i3, o3, r2, n3, e3[u2 + 13], 21, 1309151649), n3 = L(n3, i3, o3, r2, e3[u2 + 4], 6, -145523070), r2 = L(r2, n3, i3, o3, e3[u2 + 11], 10, -1120210379), o3 = L(o3, r2, n3, i3, e3[u2 + 2], 15, 718787259), i3 = L(i3, o3, r2, n3, e3[u2 + 9], 21, -343485551), n3 = C(n3, c2), i3 = C(i3, a2), o3 = C(o3, l2), r2 = C(r2, f2);
          }
          return Array(n3, i3, o3, r2);
        }(function(e3) {
          for (var t3 = Array(), n3 = (1 << P) - 1, i3 = 0; i3 < e3.length * P; i3 += P) t3[i3 >> 5] |= (e3.charCodeAt(i3 / P) & n3) << i3 % 32;
          return t3;
        }(e2), e2.length * P), n2 = "0123456789abcdef", i2 = "", o2 = 0; o2 < 4 * t2.length; o2++) i2 += n2.charAt(t2[o2 >> 2] >> o2 % 4 * 8 + 4 & 15) + n2.charAt(t2[o2 >> 2] >> o2 % 4 * 8 & 15);
        return i2;
      }
      function x(e2, t2, n2, i2, o2, r2) {
        return C((t2 = C(C(t2, e2), C(i2, r2))) << o2 | t2 >>> 32 - o2, n2);
      }
      function E(e2, t2, n2, i2, o2, r2, u2) {
        return x(t2 & n2 | ~t2 & i2, e2, t2, o2, r2, u2);
      }
      function j(e2, t2, n2, i2, o2, r2, u2) {
        return x(t2 & i2 | n2 & ~i2, e2, t2, o2, r2, u2);
      }
      function I(e2, t2, n2, i2, o2, r2, u2) {
        return x(t2 ^ n2 ^ i2, e2, t2, o2, r2, u2);
      }
      function L(e2, t2, n2, i2, o2, r2, u2) {
        return x(n2 ^ (t2 | ~i2), e2, t2, o2, r2, u2);
      }
      function C(e2, t2) {
        var n2 = (65535 & e2) + (65535 & t2);
        return (e2 >> 16) + (t2 >> 16) + (n2 >> 16) << 16 | 65535 & n2;
      }
      var _ = function() {
        n(t2, k);
        var e2 = l(t2);
        function t2() {
          return i(this, t2), e2.call(this, { type: O.RegToString, enabled: b.qqBrowser || b.firefox });
        }
        return u(t2, [{ key: "init", value: function() {
          var t3 = this;
          this.lastTime = 0, this.reg = /./, p(this.reg), this.reg.toString = function() {
            var e3;
            return b.qqBrowser ? (e3 = (/* @__PURE__ */ new Date()).getTime(), t3.lastTime && e3 - t3.lastTime < 100 ? t3.onDevToolOpen() : t3.lastTime = e3) : b.firefox && t3.onDevToolOpen(), "";
          };
        } }, { key: "detect", value: function() {
          p(this.reg);
        } }]), t2;
      }(), ie = function() {
        n(t2, k);
        var e2 = l(t2);
        function t2() {
          return i(this, t2), e2.call(this, { type: O.DefineId });
        }
        return u(t2, [{ key: "init", value: function() {
          var e3 = this;
          this.div = document.createElement("div"), this.div.__defineGetter__("id", function() {
            e3.onDevToolOpen();
          }), Object.defineProperty(this.div, "id", { get: function() {
            e3.onDevToolOpen();
          } });
        } }, { key: "detect", value: function() {
          p(this.div);
        } }]), t2;
      }(), oe = function() {
        n(t2, k);
        var e2 = l(t2);
        function t2() {
          return i(this, t2), e2.call(this, { type: O.Size, enabled: !b.iframe && !b.edge });
        }
        return u(t2, [{ key: "init", value: function() {
          var e3 = this;
          this.checkWindowSizeUneven(), window.addEventListener("resize", function() {
            setTimeout(function() {
              e3.checkWindowSizeUneven();
            }, 100);
          }, true);
        } }, { key: "detect", value: function() {
        } }, { key: "checkWindowSizeUneven", value: function() {
          var e3 = function() {
            if (re(window.devicePixelRatio)) return window.devicePixelRatio;
            var e4 = window.screen;
            return !(re(e4) || !e4.deviceXDPI || !e4.logicalXDPI) && e4.deviceXDPI / e4.logicalXDPI;
          }();
          if (false !== e3) {
            var t3 = 200 < window.outerWidth - window.innerWidth * e3, e3 = 300 < window.outerHeight - window.innerHeight * e3;
            if (t3 || e3) return this.onDevToolOpen(), false;
            N(this.type);
          }
          return true;
        } }]), t2;
      }();
      function re(e2) {
        return null != e2;
      }
      var A, ue = function() {
        n(t2, k);
        var e2 = l(t2);
        function t2() {
          return i(this, t2), e2.call(this, { type: O.DateToString, enabled: !b.iosChrome && !b.iosEdge });
        }
        return u(t2, [{ key: "init", value: function() {
          var e3 = this;
          this.count = 0, this.date = /* @__PURE__ */ new Date(), this.date.toString = function() {
            return e3.count++, "";
          };
        } }, { key: "detect", value: function() {
          this.count = 0, p(this.date), w(), 2 <= this.count && this.onDevToolOpen();
        } }]), t2;
      }(), ce = function() {
        n(t2, k);
        var e2 = l(t2);
        function t2() {
          return i(this, t2), e2.call(this, { type: O.FuncToString, enabled: !b.iosChrome && !b.iosEdge });
        }
        return u(t2, [{ key: "init", value: function() {
          var e3 = this;
          this.count = 0, this.func = function() {
          }, this.func.toString = function() {
            return e3.count++, "";
          };
        } }, { key: "detect", value: function() {
          this.count = 0, p(this.func), w(), 2 <= this.count && this.onDevToolOpen();
        } }]), t2;
      }(), ae = function() {
        n(t2, k);
        var e2 = l(t2);
        function t2() {
          return i(this, t2), e2.call(this, { type: O.Debugger, enabled: b.iosChrome || b.iosEdge });
        }
        return u(t2, [{ key: "detect", value: function() {
          var e3 = v();
          100 < v() - e3 && this.onDevToolOpen();
        } }]), t2;
      }(), le = function() {
        n(t2, k);
        var e2 = l(t2);
        function t2() {
          return i(this, t2), e2.call(this, { type: O.Performance, enabled: b.chrome || !b.mobile });
        }
        return u(t2, [{ key: "init", value: function() {
          this.maxPrintTime = 0, this.largeObjectArray = M();
        } }, { key: "detect", value: function() {
          var e3 = this, t3 = h(function() {
            y(e3.largeObjectArray);
          }), n2 = h(function() {
            p(e3.largeObjectArray);
          });
          if (this.maxPrintTime = Math.max(this.maxPrintTime, n2), w(), 0 === t3 || 0 === this.maxPrintTime) return false;
          t3 > 10 * this.maxPrintTime && this.onDevToolOpen();
        } }]), t2;
      }(), fe = (e(A = {}, O.RegToString, _), e(A, O.DefineId, ie), e(A, O.Size, oe), e(A, O.DateToString, ue), e(A, O.FuncToString, ce), e(A, O.Debugger, ae), e(A, O.Performance, le), e(A, O.DebugLib, G), A);
      var R = Object.assign(function(e2) {
        function t2() {
          var e3 = 0 < arguments.length && void 0 !== arguments[0] ? arguments[0] : "";
          return { success: !e3, reason: e3 };
        }
        var n2;
        if (R.isRunning) return t2("already running");
        if (W(), n2 = window.console || { log: function() {
        }, table: function() {
        }, clear: function() {
        } }, B = b.ie ? (p = function() {
          return n2.log.apply(n2, arguments);
        }, y = function() {
          return n2.table.apply(n2, arguments);
        }, function() {
          return n2.clear();
        }) : (p = n2.log, y = n2.table, n2.clear), q(e2), d.md5 && ne(function(e3) {
          var t3 = window.location.search, n3 = window.location.hash;
          if ("" !== (t3 = "" === t3 && "" !== n3 ? "?".concat(n3.split("?")[1]) : t3) && void 0 !== t3) {
            n3 = new RegExp("(^|&)" + e3 + "=([^&]*)(&|$)", "i"), e3 = t3.substr(1).match(n3);
            if (null != e3) return unescape(e3[2]);
          }
          return "";
        }(d.tkName)) === d.md5) return t2("token passed");
        if (d.seo && b.seoBot) return t2("seobot");
        R.isRunning = true, ee(R);
        var i2 = R, o2 = (X = function() {
          return i2.isSuspend;
        }, window.top), r2 = window.parent;
        if (g(window), d.disableIframeParents && o2 && r2 && o2 !== window) {
          for (; r2 !== o2; ) g(r2), r2 = r2.parent;
          g(o2);
        }
        return ("all" === d.detectors ? Object.keys(fe) : d.detectors).forEach(function(e3) {
          new fe[e3]();
        }), t2();
      }, { isRunning: false, isSuspend: false, md5: ne, version: "0.3.8", DetectorType: O, isDevToolOpened: $ });
      _ = function() {
        if ("undefined" == typeof window || !window.document) return null;
        var n2 = document.querySelector("[disable-devtool-auto]");
        if (!n2) return null;
        var i2 = ["disable-menu", "disable-select", "disable-copy", "disable-cut", "disable-paste", "clear-log"], o2 = ["interval"], r2 = {};
        return ["md5", "url", "tk-name", "detectors"].concat(i2, o2).forEach(function(e2) {
          var t2 = n2.getAttribute(e2);
          null !== t2 && (-1 !== o2.indexOf(e2) ? t2 = parseInt(t2) : -1 !== i2.indexOf(e2) ? t2 = "false" !== t2 : "detector" === e2 && "all" !== t2 && (t2 = t2.split(" ")), r2[function(e3) {
            if (-1 === e3.indexOf("-")) return e3;
            var t3 = false;
            return e3.split("").map(function(e4) {
              return "-" === e4 ? (t3 = true, "") : t3 ? (t3 = false, e4.toUpperCase()) : e4;
            }).join("");
          }(e2)] = t2);
        }), r2;
      }();
      return _ && R(_), R;
    });
  }
});
export default require_disable_devtool_min();
//# sourceMappingURL=disable-devtool.js.map
