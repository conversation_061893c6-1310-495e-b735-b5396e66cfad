import "./chunk-PLDDJCW6.js";

// node_modules/@smallwei/avue/lib/locale/lang/en.js
var en_default = {
  common: {
    submitBtn: "Submit",
    cancelBtn: "Cancel",
    condition: "Condition",
    display: "Display",
    hide: "Hide"
  },
  tip: {
    select: "Please select",
    input: "Please input"
  },
  check: {
    checkAll: "Select All"
  },
  upload: {
    upload: "Click to Upload",
    tip: "Drop files here, or"
  },
  time: {
    start: "Start",
    end: "End"
  },
  date: {
    start: "Start Date",
    end: "End Date",
    t: "Today",
    y: "Yesterday",
    n: "Last 7 days",
    a: "All"
  },
  form: {
    printBtn: "Print",
    mockBtn: "Mock",
    submitBtn: "Submit",
    emptyBtn: "Empty"
  },
  crud: {
    excel: {
      name: "File Name",
      type: "Data",
      typeDic: {
        true: "Data on this page (all of the data on this page)",
        false: "Selected Data (the selected data on this page)"
      },
      prop: "Field",
      params: "Parameters",
      paramsDic: {
        header: "Table Header",
        data: "Data Source",
        headers: "Complex Table Header",
        sum: "Total"
      }
    },
    filter: {
      addBtn: "Add",
      clearBtn: "Clear",
      resetBtn: "Reset",
      cancelBtn: "Cancel",
      submitBtn: "Submit"
    },
    column: {
      name: "Name",
      hide: "Hide",
      fixed: "Fixed",
      filters: "Filter",
      sortable: "Sort",
      index: "Index",
      width: "Width"
    },
    emptyText: "No Data",
    tipStartTitle: "Selected",
    tipEndTitle: "Items　",
    editTitle: "Edit",
    copyTitle: "Copy",
    addTitle: "Add",
    viewTitle: "View",
    filterTitle: "Filter Conditions",
    showTitle: "Column Display",
    menu: "Menu",
    addBtn: "Add",
    show: "Show",
    hide: "Hide",
    open: "Open",
    shrink: "Shrink",
    printBtn: "Print",
    mockBtn: "Mock",
    excelBtn: "Export",
    updateBtn: "Update",
    cancelBtn: "Cancel",
    searchBtn: "Search",
    emptyBtn: "Empty",
    menuBtn: "Menu",
    saveBtn: "Save",
    viewBtn: "View",
    editBtn: "Edit",
    copyBtn: "Copy",
    delBtn: "Delete"
  }
};
export {
  en_default as default
};
//# sourceMappingURL=@smallwei_avue_lib_locale_lang_en.js.map
