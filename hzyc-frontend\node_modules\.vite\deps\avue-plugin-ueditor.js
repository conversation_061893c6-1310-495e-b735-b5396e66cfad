import "./chunk-PLDDJCW6.js";

// node_modules/avue-plugin-ueditor/packages/ueditor/index.js
import Ueditor from "D:/yueshu/惠州烟草/hzyc-frontend/node_modules/avue-plugin-ueditor/packages/ueditor/src/index.vue";
HTMLElement.prototype.appendHTML = function(html) {
  var divTemp = document.createElement("div"), nodes = null, fragment = document.createDocumentFragment();
  divTemp.innerHTML = html;
  nodes = divTemp.childNodes;
  for (var i = 0, length = nodes.length; i < length; i += 1) {
    fragment.appendChild(nodes[i].cloneNode(true));
  }
  this.appendChild(fragment);
  nodes = null;
  fragment = null;
};
function hasClass(obj, cls) {
  return obj.className.match(new RegExp("(\\s|^)" + cls + "(\\s|$)"));
}
function addClass(obj, cls) {
  if (!hasClass(obj, cls)) obj.className += " " + cls;
}
function removeClass(obj, cls) {
  if (hasClass(obj, cls)) {
    var reg = new RegExp("(\\s|^)" + cls + "(\\s|$)");
    obj.className = obj.className.replace(reg, " ");
  }
}
HTMLElement.prototype.toggleClass = function(cls) {
  if (hasClass(this, cls)) {
    removeClass(this, cls);
  } else {
    addClass(this, cls);
  }
};
Ueditor.install = function(Vue) {
  Vue.component(Ueditor.name, Ueditor);
};
var ueditor_default = Ueditor;

// node_modules/avue-plugin-ueditor/packages/index.js
var components = [
  ueditor_default
];
function install(Vue, opt = {}) {
  Vue.config.globalProperties.$axios = opt.axios || window.axios;
  components.map((component) => {
    Vue.component(component.name, component);
  });
}
var packages_default = install;
export {
  packages_default as default
};
//# sourceMappingURL=avue-plugin-ueditor.js.map
