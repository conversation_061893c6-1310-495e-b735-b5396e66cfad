{"version": 3, "sources": ["../../@smallwei/avue/lib/locale/lang/zh.js"], "sourcesContent": ["export default {\r\n  common: {\r\n    submitBtn: '确 定',\r\n    cancelBtn: '取 消',\r\n    condition: '条件',\r\n    display: '显示',\r\n    hide: '隐藏'\r\n  },\r\n  tip: {\r\n    select: '请选择',\r\n    input: '请输入'\r\n  },\r\n  check: {\r\n    checkAll: '全选'\r\n  },\r\n  upload: {\r\n    upload: '点击上传',\r\n    tip: '将文件拖到此处，或'\r\n  },\r\n  time: {\r\n    start: '开始',\r\n    end: '结束'\r\n  },\r\n  date: {\r\n    start: '开始',\r\n    end: '结束',\r\n    t: '今日',\r\n    y: '昨日',\r\n    n: '近7天',\r\n    a: '全部'\r\n  },\r\n  form: {\r\n    printBtn: '打 印',\r\n    mockBtn: '模 拟',\r\n    submitBtn: '提 交',\r\n    emptyBtn: '清 空'\r\n  },\r\n  crud: {\r\n    excel: {\r\n      name: '文件名',\r\n      type: '数据',\r\n      typeDic: {\r\n        true: '当前数据(当前页全部的数据)',\r\n        false: '选中的数据(当前页选中的数据)'\r\n      },\r\n      prop: '字段',\r\n      params: '参数',\r\n      paramsDic: {\r\n        header: '表头',\r\n        data: '数据源',\r\n        headers: '复杂表头',\r\n        sum: '合计统计'\r\n      }\r\n    },\r\n    filter: {\r\n      addBtn: '新增条件',\r\n      clearBtn: '清空数据',\r\n      resetBtn: '清空条件',\r\n      cancelBtn: '取 消',\r\n      submitBtn: '确 定'\r\n    },\r\n    column: {\r\n      name: '列名',\r\n      hide: '隐藏',\r\n      fixed: '冻结',\r\n      filters: '过滤',\r\n      sortable: '排序',\r\n      index: '顺序',\r\n      width: '宽度'\r\n    },\r\n    emptyText: '暂无数据',\r\n    tipStartTitle: '当前表格已选择',\r\n    tipEndTitle: '项',\r\n    editTitle: '编 辑',\r\n    copyTitle: '复 制',\r\n    addTitle: '新 增',\r\n    viewTitle: '查 看',\r\n    filterTitle: '过滤条件',\r\n    showTitle: '列显隐',\r\n    menu: '操作',\r\n    addBtn: '新 增',\r\n    show: '显 示',\r\n    hide: '隐 藏',\r\n    open: '展 开',\r\n    shrink: '收 缩',\r\n    printBtn: '打 印',\r\n    mockBtn: '模 拟',\r\n    excelBtn: '导 出',\r\n    updateBtn: '修 改',\r\n    cancelBtn: '取 消',\r\n    searchBtn: '搜 索',\r\n    emptyBtn: '清 空',\r\n    menuBtn: '功 能',\r\n    saveBtn: '保 存',\r\n    viewBtn: '查 看',\r\n    editBtn: '编 辑',\r\n    copyBtn: '复 制',\r\n    delBtn: '删 除'\r\n  }\r\n};\r\n"], "mappings": ";;;AAAA,IAAO,aAAQ;AAAA,EACb,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,WAAW;AAAA,IACX,SAAS;AAAA,IACT,MAAM;AAAA,EACR;AAAA,EACA,KAAK;AAAA,IACH,QAAQ;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,UAAU;AAAA,EACZ;AAAA,EACA,QAAQ;AAAA,IACN,QAAQ;AAAA,IACR,KAAK;AAAA,EACP;AAAA,EACA,MAAM;AAAA,IACJ,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA,MAAM;AAAA,IACJ,OAAO;AAAA,IACP,KAAK;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AAAA,EACA,MAAM;AAAA,IACJ,UAAU;AAAA,IACV,SAAS;AAAA,IACT,WAAW;AAAA,IACX,UAAU;AAAA,EACZ;AAAA,EACA,MAAM;AAAA,IACJ,OAAO;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,MACA,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,WAAW;AAAA,QACT,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,SAAS;AAAA,QACT,KAAK;AAAA,MACP;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW;AAAA,MACX,WAAW;AAAA,IACb;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,IACX,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU;AAAA,IACV,WAAW;AAAA,IACX,aAAa;AAAA,IACb,WAAW;AAAA,IACX,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,SAAS;AAAA,IACT,UAAU;AAAA,IACV,WAAW;AAAA,IACX,WAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU;AAAA,IACV,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AACF;", "names": []}