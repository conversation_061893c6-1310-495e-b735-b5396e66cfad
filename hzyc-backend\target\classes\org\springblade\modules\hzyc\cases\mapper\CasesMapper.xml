<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.hzyc.cases.mapper.CasesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="casesResultMap" type="org.springblade.modules.hzyc.cases.pojo.entity.CasesEntity">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="litigant_name" property="litigantName"/>
        <result column="litigant_sex" property="litigantSex"/>
        <result column="litigant_nation" property="litigantNation"/>
        <result column="litigant_id_number" property="litigantIdNumber"/>
        <result column="description" property="description"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectCasesPage" resultMap="casesResultMap">
        select * from ca_cases where is_deleted = 0
    </select>


    <select id="exportCases" resultType="org.springblade.modules.hzyc.cases.excel.CasesExcel">
        SELECT * FROM ca_cases ${ew.customSqlSegment}
    </select>

</mapper>
