{"version": 3, "sources": ["../../codemirror/addon/search/jump-to-line.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n// Defines jumpToLine command. Uses dialog.js if present.\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"), require(\"../dialog/dialog\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\", \"../dialog/dialog\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  // default search panel location\n  CodeMirror.defineOption(\"search\", {bottom: false});\n\n  function dialog(cm, text, shortText, deflt, f) {\n    if (cm.openDialog) cm.openDialog(text, f, {value: deflt, selectValueOnOpen: true, bottom: cm.options.search.bottom});\n    else f(prompt(short<PERSON><PERSON>, deflt));\n  }\n\n  function getJumpDialog(cm) {\n    return cm.phrase(\"Jump to line:\") + ' <input type=\"text\" style=\"width: 10em\" class=\"CodeMirror-search-field\"/> <span style=\"color: #888\" class=\"CodeMirror-search-hint\">' + cm.phrase(\"(Use line:column or scroll% syntax)\") + '</span>';\n  }\n\n  function interpretLine(cm, string) {\n    var num = Number(string)\n    if (/^[-+]/.test(string)) return cm.getCursor().line + num\n    else return num - 1\n  }\n\n  CodeMirror.commands.jumpToLine = function(cm) {\n    var cur = cm.getCursor();\n    dialog(cm, getJumpDialog(cm), cm.phrase(\"Jump to line:\"), (cur.line + 1) + \":\" + cur.ch, function(posStr) {\n      if (!posStr) return;\n\n      var match;\n      if (match = /^\\s*([\\+\\-]?\\d+)\\s*\\:\\s*(\\d+)\\s*$/.exec(posStr)) {\n        cm.setCursor(interpretLine(cm, match[1]), Number(match[2]))\n      } else if (match = /^\\s*([\\+\\-]?\\d+(\\.\\d+)?)\\%\\s*/.exec(posStr)) {\n        var line = Math.round(cm.lineCount() * Number(match[1]) / 100);\n        if (/^[-+]/.test(match[1])) line = cur.line + line + 1;\n        cm.setCursor(line - 1, cur.ch);\n      } else if (match = /^\\s*\\:?\\s*([\\+\\-]?\\d+)\\s*/.exec(posStr)) {\n        cm.setCursor(interpretLine(cm, match[1]), cur.ch);\n      }\n    });\n  };\n\n  CodeMirror.keyMap[\"default\"][\"Alt-G\"] = \"jumpToLine\";\n});\n"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAKA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,sBAAiC,gBAA2B;AAAA,eACzD,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,wBAAwB,kBAAkB,GAAG,GAAG;AAAA;AAExD,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACtB;AAGA,MAAAA,YAAW,aAAa,UAAU,EAAC,QAAQ,MAAK,CAAC;AAEjD,eAAS,OAAO,IAAI,MAAM,WAAW,OAAO,GAAG;AAC7C,YAAI,GAAG,WAAY,IAAG,WAAW,MAAM,GAAG,EAAC,OAAO,OAAO,mBAAmB,MAAM,QAAQ,GAAG,QAAQ,OAAO,OAAM,CAAC;AAAA,YAC9G,GAAE,OAAO,WAAW,KAAK,CAAC;AAAA,MACjC;AAEA,eAAS,cAAc,IAAI;AACzB,eAAO,GAAG,OAAO,eAAe,IAAI,wIAAwI,GAAG,OAAO,qCAAqC,IAAI;AAAA,MACjO;AAEA,eAAS,cAAc,IAAI,QAAQ;AACjC,YAAI,MAAM,OAAO,MAAM;AACvB,YAAI,QAAQ,KAAK,MAAM,EAAG,QAAO,GAAG,UAAU,EAAE,OAAO;AAAA,YAClD,QAAO,MAAM;AAAA,MACpB;AAEA,MAAAA,YAAW,SAAS,aAAa,SAAS,IAAI;AAC5C,YAAI,MAAM,GAAG,UAAU;AACvB,eAAO,IAAI,cAAc,EAAE,GAAG,GAAG,OAAO,eAAe,GAAI,IAAI,OAAO,IAAK,MAAM,IAAI,IAAI,SAAS,QAAQ;AACxG,cAAI,CAAC,OAAQ;AAEb,cAAI;AACJ,cAAI,QAAQ,oCAAoC,KAAK,MAAM,GAAG;AAC5D,eAAG,UAAU,cAAc,IAAI,MAAM,CAAC,CAAC,GAAG,OAAO,MAAM,CAAC,CAAC,CAAC;AAAA,UAC5D,WAAW,QAAQ,gCAAgC,KAAK,MAAM,GAAG;AAC/D,gBAAI,OAAO,KAAK,MAAM,GAAG,UAAU,IAAI,OAAO,MAAM,CAAC,CAAC,IAAI,GAAG;AAC7D,gBAAI,QAAQ,KAAK,MAAM,CAAC,CAAC,EAAG,QAAO,IAAI,OAAO,OAAO;AACrD,eAAG,UAAU,OAAO,GAAG,IAAI,EAAE;AAAA,UAC/B,WAAW,QAAQ,4BAA4B,KAAK,MAAM,GAAG;AAC3D,eAAG,UAAU,cAAc,IAAI,MAAM,CAAC,CAAC,GAAG,IAAI,EAAE;AAAA,UAClD;AAAA,QACF,CAAC;AAAA,MACH;AAEA,MAAAA,YAAW,OAAO,SAAS,EAAE,OAAO,IAAI;AAAA,IAC1C,CAAC;AAAA;AAAA;", "names": ["CodeMirror"]}