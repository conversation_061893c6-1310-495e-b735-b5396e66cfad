package org.springblade.modules.hzyc.DocumentGeneration.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import org.springblade.modules.hzyc.DocumentGeneration.service.DocumentGenerator;
import org.springblade.modules.hzyc.DocumentGeneration.service.ICaseInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 询问笔录文档生成实现类
 *
 * <AUTHOR>
 */
@Service("inquiryRecordDocument")
public class InquiryRecordDocumentImpl implements DocumentGenerator {
	@Autowired
	private ICaseInfoService icaseInfoService;

    @Override
    public  List<Map<String, Object>> processData(Map<String, Object> rawData, String caseId,String mode, String fileId) {
        Map<String, Object> processedData = new HashMap<>(rawData);

        // 询问笔录特有的数据处理逻辑
        processedData.put("documentTitle", "询问笔录");


		processedData = getData(caseId);
        // 处理询问相关信息
        processInquiryInfo(processedData);
        return List.of(processedData);
    }

    @Override
    public String getTemplateName() {
        return "16询问笔录.docx";
    }

    @Override
    public String getDocumentType() {
        return "INQUIRY_RECORD";
    }



	private Map<String, Object> getData(String caseId) {
		Map<String, Object> data = new HashMap<>();
		Map<String, Object> query=new HashMap<>();
		query.put("AJBS", caseId);
		JSONArray array = icaseInfoService.getCaseInquiryRecordDailyReport(query);
		if(array != null && !array.isEmpty()) {
			Map<String, Object> firstData = (Map<String, Object>) array.get(0);
			Map<String, String> mapper = getTableFieldMapping();
			if(firstData != null) {
				// 处理数据
				firstData.forEach((key, value) -> {
					String newKey = mapper.get(key);
					if (StrUtil.isBlank(newKey)) {
						newKey = key;
					}
					data.put(newKey, value);
				});
				System.out.println(data);
				return data;
			}
		}
		return data;
	}

	public static Map<String, String> getTableFieldMapping() {
		Map<String, String> fieldMap = new HashMap<>();

		fieldMap.put("BXWRXB", "by_ask_sex");
		fieldMap.put("XWSY", "ask_cause");
		fieldMap.put("BMSFJL", "id_record_desc");
		fieldMap.put("BXWRDW", "by_ask_unit");
		fieldMap.put("WSCJSJ", "create_time");
		fieldMap.put("WSHQ", "full_doc_no");
		fieldMap.put("BXWRNL", "by_ask_age");
		fieldMap.put("WSZT", "finsh_status");
		fieldMap.put("BXWRLXDH", "by_ask_phone");
		fieldMap.put("HD2", "answer_two");
		fieldMap.put("XWRUUID", "ask_person_uuids");
		fieldMap.put("BXWRJYDZ", "by_ask_addr");
		fieldMap.put("SFYYD0WYD1YYD", "is_read");
		fieldMap.put("SJSSBMYYTYSJQXCL", "own_dept_uuid");
		fieldMap.put("BXWRCSRQ", "by_ask_birthday");
		fieldMap.put("KZZD1", "ext1");
		fieldMap.put("SFYX", "is_active");
		fieldMap.put("HD1", "answer_one");
		fieldMap.put("MCRKSJ", "mc_tec_ctime");
		fieldMap.put("XGR", "modifier");
		fieldMap.put("CHBMUUID", "get_dept_uuid");
		fieldMap.put("XWBLBS", "ask_record_uuid");
		fieldMap.put("CBBMUUID", "reg_dept_uuid");
		fieldMap.put("BXWRZJLX", "by_ask_id_type");
		fieldMap.put("AJMC", "case_name");
		fieldMap.put("AJBS", "case_uuid");
		fieldMap.put("FWZXZDTBSJYFWZXSJG", "sysupdatetime");
		fieldMap.put("TAR", "same_party");
		fieldMap.put("SJMC", "city_org_name");
		fieldMap.put("FWZXZDTBSJYFWZXSJS", "sysisdelete");
		fieldMap.put("XWR", "ask_person");
		fieldMap.put("ZZJGUUID", "org_uuid");
		fieldMap.put("GZCS", "stmt");
		fieldMap.put("XTGXSJCXBYDX", "sys_modify_time");
		fieldMap.put("XWRZFZH", "ask_psn_insp_no");
		fieldMap.put("XWRSZZMJ", "ask_org_name");
		fieldMap.put("BXWRHM", "by_ask_id_no");
		fieldMap.put("XWDD", "handling_addr");
		fieldMap.put("FDDBRFZR", "corporation");
		fieldMap.put("XGSJ", "modify_time");
		fieldMap.put("BXWR", "by_ask_psn");
		fieldMap.put("SJSSDWYYTYSJQXCL", "own_org_uuid");
		fieldMap.put("JGSXZ", "org_abbr");
		fieldMap.put("CJR", "creator");
		fieldMap.put("SJBM", "city_org_code");
		fieldMap.put("XWSJZ", "ask_time_e");
		fieldMap.put("XYWYBS", "tid");
		fieldMap.put("JGJC", "org_short_name");
		fieldMap.put("AJBH", "case_code");
		fieldMap.put("DSR", "party");
		fieldMap.put("XTCJSJCXBYDX", "sys_create_time");
		fieldMap.put("BZ", "remark");
		fieldMap.put("XWNR", "ask_content");
		fieldMap.put("XWSJQ", "ask_time_s");
		fieldMap.put("BXWRMZ", "by_ask_nation");
		fieldMap.put("JLR", "record_person");
		fieldMap.put("BXWRZZ", "by_ask_home_addr");
		fieldMap.put("KZZD3", "ext3");
		fieldMap.put("YBAGX", "by_ask_relation");
		fieldMap.put("KZZD2", "ext2");

		return fieldMap;
	}

    private void processInquiryInfo(Map<String, Object> data) {
        // 处理询问信息
        String party = (String) data.get("party");
        String idCard = (String) data.get("idCard");

        if (party != null && idCard != null) {
            data.put("inquiredPersonInfo", String.format("被询问人：%s，身份证号：%s", party, idCard));
        }
    }
}
