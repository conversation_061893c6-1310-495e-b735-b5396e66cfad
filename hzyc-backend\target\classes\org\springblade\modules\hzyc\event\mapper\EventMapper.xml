<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.hzyc.event.mapper.EventMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="eventResultMap" type="org.springblade.modules.hzyc.event.pojo.entity.EventEntity">
        <result column="id" property="id"/>
        <result column="event_type" property="eventType"/>
        <result column="content" property="content"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectEventPage" resultMap="eventResultMap">
        select * from ca_event where is_deleted = 0
    </select>


    <select id="exportEvent" resultType="org.springblade.modules.hzyc.event.excel.EventExcel">
        SELECT * FROM ca_event ${ew.customSqlSegment}
    </select>

</mapper>
