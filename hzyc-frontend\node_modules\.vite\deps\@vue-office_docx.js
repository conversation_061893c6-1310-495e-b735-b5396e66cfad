import {
  require_lib
} from "./chunk-MSCGCEBA.js";
import {
  require_vue
} from "./chunk-CZCGTF2U.js";
import "./chunk-7PYUCYS5.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/@vue-office/docx/lib/index.js
var require_lib2 = __commonJS({
  "node_modules/@vue-office/docx/lib/index.js"(exports, module) {
    (function(dt, ft) {
      typeof exports == "object" && typeof module != "undefined" ? module.exports = ft(require_lib(), require_vue()) : typeof define == "function" && define.amd ? define(["vue-demi", "vue"], ft) : (dt = typeof globalThis != "undefined" ? globalThis : dt || self, dt["vue-office-docx"] = ft(dt.VueDemi, dt.Vue));
    })(exports, function(dt, ft) {
      "use strict";
      var gn = Object.defineProperty, bn = Object.defineProperties;
      var vn = Object.getOwnPropertyDescriptors;
      var Re = Object.getOwnPropertySymbols;
      var kn = Object.prototype.hasOwnProperty, yn = Object.prototype.propertyIsEnumerable;
      var Me = (dt2, ft2, vt2) => ft2 in dt2 ? gn(dt2, ft2, { enumerable: true, configurable: true, writable: true, value: vt2 }) : dt2[ft2] = vt2, _t = (dt2, ft2) => {
        for (var vt2 in ft2 || (ft2 = {})) kn.call(ft2, vt2) && Me(dt2, vt2, ft2[vt2]);
        if (Re) for (var vt2 of Re(ft2)) yn.call(ft2, vt2) && Me(dt2, vt2, ft2[vt2]);
        return dt2;
      }, Wt = (dt2, ft2) => bn(dt2, vn(ft2));
      var wt = (dt2, ft2, vt2) => new Promise((Jt2, Bt2) => {
        var Ht2 = (ot2) => {
          try {
            It2(vt2.next(ot2));
          } catch (Ot2) {
            Bt2(Ot2);
          }
        }, Qt2 = (ot2) => {
          try {
            It2(vt2.throw(ot2));
          } catch (Ot2) {
            Bt2(Ot2);
          }
        }, It2 = (ot2) => ot2.done ? Jt2(ot2.value) : Promise.resolve(ot2.value).then(Ht2, Qt2);
        It2((vt2 = vt2.apply(dt2, ft2)).next());
      });
      typeof window.setImmediate == "undefined" && (window.setImmediate = function(i, ...e) {
        setTimeout(() => i(e));
      });
      var vt = typeof globalThis != "undefined" ? globalThis : typeof window != "undefined" ? window : typeof global != "undefined" ? global : typeof self != "undefined" ? self : {};
      function Jt(i) {
        return i && i.__esModule && Object.prototype.hasOwnProperty.call(i, "default") ? i.default : i;
      }
      function Bt(i) {
        throw new Error('Could not dynamically require "' + i + '". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.');
      }
      var Ht = { exports: {} };
      (function(i, e) {
        (function(t) {
          i.exports = t();
        })(function() {
          return function t(r, n, a) {
            function l(b, y) {
              if (!n[b]) {
                if (!r[b]) {
                  var _ = typeof Bt == "function" && Bt;
                  if (!y && _) return _(b, true);
                  if (o) return o(b, true);
                  var w = new Error("Cannot find module '" + b + "'");
                  throw w.code = "MODULE_NOT_FOUND", w;
                }
                var d = n[b] = { exports: {} };
                r[b][0].call(d.exports, function(k) {
                  var h = r[b][1][k];
                  return l(h || k);
                }, d, d.exports, t, r, n, a);
              }
              return n[b].exports;
            }
            for (var o = typeof Bt == "function" && Bt, u = 0; u < a.length; u++) l(a[u]);
            return l;
          }({ 1: [function(t, r, n) {
            var a = t("./utils"), l = t("./support"), o = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
            n.encode = function(u) {
              for (var b, y, _, w, d, k, h, m = [], f = 0, v = u.length, C = v, A = a.getTypeOf(u) !== "string"; f < u.length; ) C = v - f, _ = A ? (b = u[f++], y = f < v ? u[f++] : 0, f < v ? u[f++] : 0) : (b = u.charCodeAt(f++), y = f < v ? u.charCodeAt(f++) : 0, f < v ? u.charCodeAt(f++) : 0), w = b >> 2, d = (3 & b) << 4 | y >> 4, k = 1 < C ? (15 & y) << 2 | _ >> 6 : 64, h = 2 < C ? 63 & _ : 64, m.push(o.charAt(w) + o.charAt(d) + o.charAt(k) + o.charAt(h));
              return m.join("");
            }, n.decode = function(u) {
              var b, y, _, w, d, k, h = 0, m = 0, f = "data:";
              if (u.substr(0, f.length) === f) throw new Error("Invalid base64 input, it looks like a data url.");
              var v, C = 3 * (u = u.replace(/[^A-Za-z0-9+/=]/g, "")).length / 4;
              if (u.charAt(u.length - 1) === o.charAt(64) && C--, u.charAt(u.length - 2) === o.charAt(64) && C--, C % 1 != 0) throw new Error("Invalid base64 input, bad content length.");
              for (v = l.uint8array ? new Uint8Array(0 | C) : new Array(0 | C); h < u.length; ) b = o.indexOf(u.charAt(h++)) << 2 | (w = o.indexOf(u.charAt(h++))) >> 4, y = (15 & w) << 4 | (d = o.indexOf(u.charAt(h++))) >> 2, _ = (3 & d) << 6 | (k = o.indexOf(u.charAt(h++))), v[m++] = b, d !== 64 && (v[m++] = y), k !== 64 && (v[m++] = _);
              return v;
            };
          }, { "./support": 30, "./utils": 32 }], 2: [function(t, r, n) {
            var a = t("./external"), l = t("./stream/DataWorker"), o = t("./stream/Crc32Probe"), u = t("./stream/DataLengthProbe");
            function b(y, _, w, d, k) {
              this.compressedSize = y, this.uncompressedSize = _, this.crc32 = w, this.compression = d, this.compressedContent = k;
            }
            b.prototype = { getContentWorker: function() {
              var y = new l(a.Promise.resolve(this.compressedContent)).pipe(this.compression.uncompressWorker()).pipe(new u("data_length")), _ = this;
              return y.on("end", function() {
                if (this.streamInfo.data_length !== _.uncompressedSize) throw new Error("Bug : uncompressed data size mismatch");
              }), y;
            }, getCompressedWorker: function() {
              return new l(a.Promise.resolve(this.compressedContent)).withStreamInfo("compressedSize", this.compressedSize).withStreamInfo("uncompressedSize", this.uncompressedSize).withStreamInfo("crc32", this.crc32).withStreamInfo("compression", this.compression);
            } }, b.createWorkerFrom = function(y, _, w) {
              return y.pipe(new o()).pipe(new u("uncompressedSize")).pipe(_.compressWorker(w)).pipe(new u("compressedSize")).withStreamInfo("compression", _);
            }, r.exports = b;
          }, { "./external": 6, "./stream/Crc32Probe": 25, "./stream/DataLengthProbe": 26, "./stream/DataWorker": 27 }], 3: [function(t, r, n) {
            var a = t("./stream/GenericWorker");
            n.STORE = { magic: "\0\0", compressWorker: function() {
              return new a("STORE compression");
            }, uncompressWorker: function() {
              return new a("STORE decompression");
            } }, n.DEFLATE = t("./flate");
          }, { "./flate": 7, "./stream/GenericWorker": 28 }], 4: [function(t, r, n) {
            var a = t("./utils"), l = function() {
              for (var o, u = [], b = 0; b < 256; b++) {
                o = b;
                for (var y = 0; y < 8; y++) o = 1 & o ? 3988292384 ^ o >>> 1 : o >>> 1;
                u[b] = o;
              }
              return u;
            }();
            r.exports = function(o, u) {
              return o !== void 0 && o.length ? a.getTypeOf(o) !== "string" ? function(b, y, _, w) {
                var d = l, k = w + _;
                b ^= -1;
                for (var h = w; h < k; h++) b = b >>> 8 ^ d[255 & (b ^ y[h])];
                return -1 ^ b;
              }(0 | u, o, o.length, 0) : function(b, y, _, w) {
                var d = l, k = w + _;
                b ^= -1;
                for (var h = w; h < k; h++) b = b >>> 8 ^ d[255 & (b ^ y.charCodeAt(h))];
                return -1 ^ b;
              }(0 | u, o, o.length, 0) : 0;
            };
          }, { "./utils": 32 }], 5: [function(t, r, n) {
            n.base64 = false, n.binary = false, n.dir = false, n.createFolders = true, n.date = null, n.compression = null, n.compressionOptions = null, n.comment = null, n.unixPermissions = null, n.dosPermissions = null;
          }, {}], 6: [function(t, r, n) {
            var a = null;
            a = typeof Promise != "undefined" ? Promise : t("lie"), r.exports = { Promise: a };
          }, { lie: 37 }], 7: [function(t, r, n) {
            var a = typeof Uint8Array != "undefined" && typeof Uint16Array != "undefined" && typeof Uint32Array != "undefined", l = t("pako"), o = t("./utils"), u = t("./stream/GenericWorker"), b = a ? "uint8array" : "array";
            function y(_, w) {
              u.call(this, "FlateWorker/" + _), this._pako = null, this._pakoAction = _, this._pakoOptions = w, this.meta = {};
            }
            n.magic = "\b\0", o.inherits(y, u), y.prototype.processChunk = function(_) {
              this.meta = _.meta, this._pako === null && this._createPako(), this._pako.push(o.transformTo(b, _.data), false);
            }, y.prototype.flush = function() {
              u.prototype.flush.call(this), this._pako === null && this._createPako(), this._pako.push([], true);
            }, y.prototype.cleanUp = function() {
              u.prototype.cleanUp.call(this), this._pako = null;
            }, y.prototype._createPako = function() {
              this._pako = new l[this._pakoAction]({ raw: true, level: this._pakoOptions.level || -1 });
              var _ = this;
              this._pako.onData = function(w) {
                _.push({ data: w, meta: _.meta });
              };
            }, n.compressWorker = function(_) {
              return new y("Deflate", _);
            }, n.uncompressWorker = function() {
              return new y("Inflate", {});
            };
          }, { "./stream/GenericWorker": 28, "./utils": 32, pako: 38 }], 8: [function(t, r, n) {
            function a(d, k) {
              var h, m = "";
              for (h = 0; h < k; h++) m += String.fromCharCode(255 & d), d >>>= 8;
              return m;
            }
            function l(d, k, h, m, f, v) {
              var C, A, E = d.file, D = d.compression, I = v !== b.utf8encode, W = o.transformTo("string", v(E.name)), T = o.transformTo("string", b.utf8encode(E.name)), X = E.comment, Q = o.transformTo("string", v(X)), S = o.transformTo("string", b.utf8encode(X)), O = T.length !== E.name.length, c = S.length !== X.length, L = "", et = "", $ = "", rt = E.dir, H = E.date, tt = { crc32: 0, compressedSize: 0, uncompressedSize: 0 };
              k && !h || (tt.crc32 = d.crc32, tt.compressedSize = d.compressedSize, tt.uncompressedSize = d.uncompressedSize);
              var R = 0;
              k && (R |= 8), I || !O && !c || (R |= 2048);
              var B = 0, J = 0;
              rt && (B |= 16), f === "UNIX" ? (J = 798, B |= function(G, mt) {
                var yt = G;
                return G || (yt = mt ? 16893 : 33204), (65535 & yt) << 16;
              }(E.unixPermissions, rt)) : (J = 20, B |= function(G) {
                return 63 & (G || 0);
              }(E.dosPermissions)), C = H.getUTCHours(), C <<= 6, C |= H.getUTCMinutes(), C <<= 5, C |= H.getUTCSeconds() / 2, A = H.getUTCFullYear() - 1980, A <<= 4, A |= H.getUTCMonth() + 1, A <<= 5, A |= H.getUTCDate(), O && (et = a(1, 1) + a(y(W), 4) + T, L += "up" + a(et.length, 2) + et), c && ($ = a(1, 1) + a(y(Q), 4) + S, L += "uc" + a($.length, 2) + $);
              var q = "";
              return q += `
\0`, q += a(R, 2), q += D.magic, q += a(C, 2), q += a(A, 2), q += a(tt.crc32, 4), q += a(tt.compressedSize, 4), q += a(tt.uncompressedSize, 4), q += a(W.length, 2), q += a(L.length, 2), { fileRecord: _.LOCAL_FILE_HEADER + q + W + L, dirRecord: _.CENTRAL_FILE_HEADER + a(J, 2) + q + a(Q.length, 2) + "\0\0\0\0" + a(B, 4) + a(m, 4) + W + L + Q };
            }
            var o = t("../utils"), u = t("../stream/GenericWorker"), b = t("../utf8"), y = t("../crc32"), _ = t("../signature");
            function w(d, k, h, m) {
              u.call(this, "ZipFileWorker"), this.bytesWritten = 0, this.zipComment = k, this.zipPlatform = h, this.encodeFileName = m, this.streamFiles = d, this.accumulate = false, this.contentBuffer = [], this.dirRecords = [], this.currentSourceOffset = 0, this.entriesCount = 0, this.currentFile = null, this._sources = [];
            }
            o.inherits(w, u), w.prototype.push = function(d) {
              var k = d.meta.percent || 0, h = this.entriesCount, m = this._sources.length;
              this.accumulate ? this.contentBuffer.push(d) : (this.bytesWritten += d.data.length, u.prototype.push.call(this, { data: d.data, meta: { currentFile: this.currentFile, percent: h ? (k + 100 * (h - m - 1)) / h : 100 } }));
            }, w.prototype.openedSource = function(d) {
              this.currentSourceOffset = this.bytesWritten, this.currentFile = d.file.name;
              var k = this.streamFiles && !d.file.dir;
              if (k) {
                var h = l(d, k, false, this.currentSourceOffset, this.zipPlatform, this.encodeFileName);
                this.push({ data: h.fileRecord, meta: { percent: 0 } });
              } else this.accumulate = true;
            }, w.prototype.closedSource = function(d) {
              this.accumulate = false;
              var k = this.streamFiles && !d.file.dir, h = l(d, k, true, this.currentSourceOffset, this.zipPlatform, this.encodeFileName);
              if (this.dirRecords.push(h.dirRecord), k) this.push({ data: function(m) {
                return _.DATA_DESCRIPTOR + a(m.crc32, 4) + a(m.compressedSize, 4) + a(m.uncompressedSize, 4);
              }(d), meta: { percent: 100 } });
              else for (this.push({ data: h.fileRecord, meta: { percent: 0 } }); this.contentBuffer.length; ) this.push(this.contentBuffer.shift());
              this.currentFile = null;
            }, w.prototype.flush = function() {
              for (var d = this.bytesWritten, k = 0; k < this.dirRecords.length; k++) this.push({ data: this.dirRecords[k], meta: { percent: 100 } });
              var h = this.bytesWritten - d, m = function(f, v, C, A, E) {
                var D = o.transformTo("string", E(A));
                return _.CENTRAL_DIRECTORY_END + "\0\0\0\0" + a(f, 2) + a(f, 2) + a(v, 4) + a(C, 4) + a(D.length, 2) + D;
              }(this.dirRecords.length, h, d, this.zipComment, this.encodeFileName);
              this.push({ data: m, meta: { percent: 100 } });
            }, w.prototype.prepareNextSource = function() {
              this.previous = this._sources.shift(), this.openedSource(this.previous.streamInfo), this.isPaused ? this.previous.pause() : this.previous.resume();
            }, w.prototype.registerPrevious = function(d) {
              this._sources.push(d);
              var k = this;
              return d.on("data", function(h) {
                k.processChunk(h);
              }), d.on("end", function() {
                k.closedSource(k.previous.streamInfo), k._sources.length ? k.prepareNextSource() : k.end();
              }), d.on("error", function(h) {
                k.error(h);
              }), this;
            }, w.prototype.resume = function() {
              return !!u.prototype.resume.call(this) && (!this.previous && this._sources.length ? (this.prepareNextSource(), true) : this.previous || this._sources.length || this.generatedError ? void 0 : (this.end(), true));
            }, w.prototype.error = function(d) {
              var k = this._sources;
              if (!u.prototype.error.call(this, d)) return false;
              for (var h = 0; h < k.length; h++) try {
                k[h].error(d);
              } catch (m) {
              }
              return true;
            }, w.prototype.lock = function() {
              u.prototype.lock.call(this);
              for (var d = this._sources, k = 0; k < d.length; k++) d[k].lock();
            }, r.exports = w;
          }, { "../crc32": 4, "../signature": 23, "../stream/GenericWorker": 28, "../utf8": 31, "../utils": 32 }], 9: [function(t, r, n) {
            var a = t("../compressions"), l = t("./ZipFileWorker");
            n.generateWorker = function(o, u, b) {
              var y = new l(u.streamFiles, b, u.platform, u.encodeFileName), _ = 0;
              try {
                o.forEach(function(w, d) {
                  _++;
                  var k = function(v, C) {
                    var A = v || C, E = a[A];
                    if (!E) throw new Error(A + " is not a valid compression method !");
                    return E;
                  }(d.options.compression, u.compression), h = d.options.compressionOptions || u.compressionOptions || {}, m = d.dir, f = d.date;
                  d._compressWorker(k, h).withStreamInfo("file", { name: w, dir: m, date: f, comment: d.comment || "", unixPermissions: d.unixPermissions, dosPermissions: d.dosPermissions }).pipe(y);
                }), y.entriesCount = _;
              } catch (w) {
                y.error(w);
              }
              return y;
            };
          }, { "../compressions": 3, "./ZipFileWorker": 8 }], 10: [function(t, r, n) {
            function a() {
              if (!(this instanceof a)) return new a();
              if (arguments.length) throw new Error("The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.");
              this.files = /* @__PURE__ */ Object.create(null), this.comment = null, this.root = "", this.clone = function() {
                var l = new a();
                for (var o in this) typeof this[o] != "function" && (l[o] = this[o]);
                return l;
              };
            }
            (a.prototype = t("./object")).loadAsync = t("./load"), a.support = t("./support"), a.defaults = t("./defaults"), a.version = "3.10.1", a.loadAsync = function(l, o) {
              return new a().loadAsync(l, o);
            }, a.external = t("./external"), r.exports = a;
          }, { "./defaults": 5, "./external": 6, "./load": 11, "./object": 15, "./support": 30 }], 11: [function(t, r, n) {
            var a = t("./utils"), l = t("./external"), o = t("./utf8"), u = t("./zipEntries"), b = t("./stream/Crc32Probe"), y = t("./nodejsUtils");
            function _(w) {
              return new l.Promise(function(d, k) {
                var h = w.decompressed.getContentWorker().pipe(new b());
                h.on("error", function(m) {
                  k(m);
                }).on("end", function() {
                  h.streamInfo.crc32 !== w.decompressed.crc32 ? k(new Error("Corrupted zip : CRC32 mismatch")) : d();
                }).resume();
              });
            }
            r.exports = function(w, d) {
              var k = this;
              return d = a.extend(d || {}, { base64: false, checkCRC32: false, optimizedBinaryString: false, createFolders: false, decodeFileName: o.utf8decode }), y.isNode && y.isStream(w) ? l.Promise.reject(new Error("JSZip can't accept a stream when loading a zip file.")) : a.prepareContent("the loaded zip file", w, true, d.optimizedBinaryString, d.base64).then(function(h) {
                var m = new u(d);
                return m.load(h), m;
              }).then(function(h) {
                var m = [l.Promise.resolve(h)], f = h.files;
                if (d.checkCRC32) for (var v = 0; v < f.length; v++) m.push(_(f[v]));
                return l.Promise.all(m);
              }).then(function(h) {
                for (var m = h.shift(), f = m.files, v = 0; v < f.length; v++) {
                  var C = f[v], A = C.fileNameStr, E = a.resolve(C.fileNameStr);
                  k.file(E, C.decompressed, { binary: true, optimizedBinaryString: true, date: C.date, dir: C.dir, comment: C.fileCommentStr.length ? C.fileCommentStr : null, unixPermissions: C.unixPermissions, dosPermissions: C.dosPermissions, createFolders: d.createFolders }), C.dir || (k.file(E).unsafeOriginalName = A);
                }
                return m.zipComment.length && (k.comment = m.zipComment), k;
              });
            };
          }, { "./external": 6, "./nodejsUtils": 14, "./stream/Crc32Probe": 25, "./utf8": 31, "./utils": 32, "./zipEntries": 33 }], 12: [function(t, r, n) {
            var a = t("../utils"), l = t("../stream/GenericWorker");
            function o(u, b) {
              l.call(this, "Nodejs stream input adapter for " + u), this._upstreamEnded = false, this._bindStream(b);
            }
            a.inherits(o, l), o.prototype._bindStream = function(u) {
              var b = this;
              (this._stream = u).pause(), u.on("data", function(y) {
                b.push({ data: y, meta: { percent: 0 } });
              }).on("error", function(y) {
                b.isPaused ? this.generatedError = y : b.error(y);
              }).on("end", function() {
                b.isPaused ? b._upstreamEnded = true : b.end();
              });
            }, o.prototype.pause = function() {
              return !!l.prototype.pause.call(this) && (this._stream.pause(), true);
            }, o.prototype.resume = function() {
              return !!l.prototype.resume.call(this) && (this._upstreamEnded ? this.end() : this._stream.resume(), true);
            }, r.exports = o;
          }, { "../stream/GenericWorker": 28, "../utils": 32 }], 13: [function(t, r, n) {
            var a = t("readable-stream").Readable;
            function l(o, u, b) {
              a.call(this, u), this._helper = o;
              var y = this;
              o.on("data", function(_, w) {
                y.push(_) || y._helper.pause(), b && b(w);
              }).on("error", function(_) {
                y.emit("error", _);
              }).on("end", function() {
                y.push(null);
              });
            }
            t("../utils").inherits(l, a), l.prototype._read = function() {
              this._helper.resume();
            }, r.exports = l;
          }, { "../utils": 32, "readable-stream": 16 }], 14: [function(t, r, n) {
            r.exports = { isNode: typeof Buffer != "undefined", newBufferFrom: function(a, l) {
              if (Buffer.from && Buffer.from !== Uint8Array.from) return Buffer.from(a, l);
              if (typeof a == "number") throw new Error('The "data" argument must not be a number');
              return new Buffer(a, l);
            }, allocBuffer: function(a) {
              if (Buffer.alloc) return Buffer.alloc(a);
              var l = new Buffer(a);
              return l.fill(0), l;
            }, isBuffer: function(a) {
              return Buffer.isBuffer(a);
            }, isStream: function(a) {
              return a && typeof a.on == "function" && typeof a.pause == "function" && typeof a.resume == "function";
            } };
          }, {}], 15: [function(t, r, n) {
            function a(E, D, I) {
              var W, T = o.getTypeOf(D), X = o.extend(I || {}, y);
              X.date = X.date || /* @__PURE__ */ new Date(), X.compression !== null && (X.compression = X.compression.toUpperCase()), typeof X.unixPermissions == "string" && (X.unixPermissions = parseInt(X.unixPermissions, 8)), X.unixPermissions && 16384 & X.unixPermissions && (X.dir = true), X.dosPermissions && 16 & X.dosPermissions && (X.dir = true), X.dir && (E = f(E)), X.createFolders && (W = m(E)) && v.call(this, W, true);
              var Q = T === "string" && X.binary === false && X.base64 === false;
              I && I.binary !== void 0 || (X.binary = !Q), (D instanceof _ && D.uncompressedSize === 0 || X.dir || !D || D.length === 0) && (X.base64 = false, X.binary = true, D = "", X.compression = "STORE", T = "string");
              var S = null;
              S = D instanceof _ || D instanceof u ? D : k.isNode && k.isStream(D) ? new h(E, D) : o.prepareContent(E, D, X.binary, X.optimizedBinaryString, X.base64);
              var O = new w(E, S, X);
              this.files[E] = O;
            }
            var l = t("./utf8"), o = t("./utils"), u = t("./stream/GenericWorker"), b = t("./stream/StreamHelper"), y = t("./defaults"), _ = t("./compressedObject"), w = t("./zipObject"), d = t("./generate"), k = t("./nodejsUtils"), h = t("./nodejs/NodejsStreamInputAdapter"), m = function(E) {
              E.slice(-1) === "/" && (E = E.substring(0, E.length - 1));
              var D = E.lastIndexOf("/");
              return 0 < D ? E.substring(0, D) : "";
            }, f = function(E) {
              return E.slice(-1) !== "/" && (E += "/"), E;
            }, v = function(E, D) {
              return D = D !== void 0 ? D : y.createFolders, E = f(E), this.files[E] || a.call(this, E, null, { dir: true, createFolders: D }), this.files[E];
            };
            function C(E) {
              return Object.prototype.toString.call(E) === "[object RegExp]";
            }
            var A = { load: function() {
              throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.");
            }, forEach: function(E) {
              var D, I, W;
              for (D in this.files) W = this.files[D], (I = D.slice(this.root.length, D.length)) && D.slice(0, this.root.length) === this.root && E(I, W);
            }, filter: function(E) {
              var D = [];
              return this.forEach(function(I, W) {
                E(I, W) && D.push(W);
              }), D;
            }, file: function(E, D, I) {
              if (arguments.length !== 1) return E = this.root + E, a.call(this, E, D, I), this;
              if (C(E)) {
                var W = E;
                return this.filter(function(X, Q) {
                  return !Q.dir && W.test(X);
                });
              }
              var T = this.files[this.root + E];
              return T && !T.dir ? T : null;
            }, folder: function(E) {
              if (!E) return this;
              if (C(E)) return this.filter(function(T, X) {
                return X.dir && E.test(T);
              });
              var D = this.root + E, I = v.call(this, D), W = this.clone();
              return W.root = I.name, W;
            }, remove: function(E) {
              E = this.root + E;
              var D = this.files[E];
              if (D || (E.slice(-1) !== "/" && (E += "/"), D = this.files[E]), D && !D.dir) delete this.files[E];
              else for (var I = this.filter(function(T, X) {
                return X.name.slice(0, E.length) === E;
              }), W = 0; W < I.length; W++) delete this.files[I[W].name];
              return this;
            }, generate: function() {
              throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.");
            }, generateInternalStream: function(E) {
              var D, I = {};
              try {
                if ((I = o.extend(E || {}, { streamFiles: false, compression: "STORE", compressionOptions: null, type: "", platform: "DOS", comment: null, mimeType: "application/zip", encodeFileName: l.utf8encode })).type = I.type.toLowerCase(), I.compression = I.compression.toUpperCase(), I.type === "binarystring" && (I.type = "string"), !I.type) throw new Error("No output type specified.");
                o.checkSupport(I.type), I.platform !== "darwin" && I.platform !== "freebsd" && I.platform !== "linux" && I.platform !== "sunos" || (I.platform = "UNIX"), I.platform === "win32" && (I.platform = "DOS");
                var W = I.comment || this.comment || "";
                D = d.generateWorker(this, I, W);
              } catch (T) {
                (D = new u("error")).error(T);
              }
              return new b(D, I.type || "string", I.mimeType);
            }, generateAsync: function(E, D) {
              return this.generateInternalStream(E).accumulate(D);
            }, generateNodeStream: function(E, D) {
              return (E = E || {}).type || (E.type = "nodebuffer"), this.generateInternalStream(E).toNodejsStream(D);
            } };
            r.exports = A;
          }, { "./compressedObject": 2, "./defaults": 5, "./generate": 9, "./nodejs/NodejsStreamInputAdapter": 12, "./nodejsUtils": 14, "./stream/GenericWorker": 28, "./stream/StreamHelper": 29, "./utf8": 31, "./utils": 32, "./zipObject": 35 }], 16: [function(t, r, n) {
            r.exports = t("stream");
          }, { stream: void 0 }], 17: [function(t, r, n) {
            var a = t("./DataReader");
            function l(o) {
              a.call(this, o);
              for (var u = 0; u < this.data.length; u++) o[u] = 255 & o[u];
            }
            t("../utils").inherits(l, a), l.prototype.byteAt = function(o) {
              return this.data[this.zero + o];
            }, l.prototype.lastIndexOfSignature = function(o) {
              for (var u = o.charCodeAt(0), b = o.charCodeAt(1), y = o.charCodeAt(2), _ = o.charCodeAt(3), w = this.length - 4; 0 <= w; --w) if (this.data[w] === u && this.data[w + 1] === b && this.data[w + 2] === y && this.data[w + 3] === _) return w - this.zero;
              return -1;
            }, l.prototype.readAndCheckSignature = function(o) {
              var u = o.charCodeAt(0), b = o.charCodeAt(1), y = o.charCodeAt(2), _ = o.charCodeAt(3), w = this.readData(4);
              return u === w[0] && b === w[1] && y === w[2] && _ === w[3];
            }, l.prototype.readData = function(o) {
              if (this.checkOffset(o), o === 0) return [];
              var u = this.data.slice(this.zero + this.index, this.zero + this.index + o);
              return this.index += o, u;
            }, r.exports = l;
          }, { "../utils": 32, "./DataReader": 18 }], 18: [function(t, r, n) {
            var a = t("../utils");
            function l(o) {
              this.data = o, this.length = o.length, this.index = 0, this.zero = 0;
            }
            l.prototype = { checkOffset: function(o) {
              this.checkIndex(this.index + o);
            }, checkIndex: function(o) {
              if (this.length < this.zero + o || o < 0) throw new Error("End of data reached (data length = " + this.length + ", asked index = " + o + "). Corrupted zip ?");
            }, setIndex: function(o) {
              this.checkIndex(o), this.index = o;
            }, skip: function(o) {
              this.setIndex(this.index + o);
            }, byteAt: function() {
            }, readInt: function(o) {
              var u, b = 0;
              for (this.checkOffset(o), u = this.index + o - 1; u >= this.index; u--) b = (b << 8) + this.byteAt(u);
              return this.index += o, b;
            }, readString: function(o) {
              return a.transformTo("string", this.readData(o));
            }, readData: function() {
            }, lastIndexOfSignature: function() {
            }, readAndCheckSignature: function() {
            }, readDate: function() {
              var o = this.readInt(4);
              return new Date(Date.UTC(1980 + (o >> 25 & 127), (o >> 21 & 15) - 1, o >> 16 & 31, o >> 11 & 31, o >> 5 & 63, (31 & o) << 1));
            } }, r.exports = l;
          }, { "../utils": 32 }], 19: [function(t, r, n) {
            var a = t("./Uint8ArrayReader");
            function l(o) {
              a.call(this, o);
            }
            t("../utils").inherits(l, a), l.prototype.readData = function(o) {
              this.checkOffset(o);
              var u = this.data.slice(this.zero + this.index, this.zero + this.index + o);
              return this.index += o, u;
            }, r.exports = l;
          }, { "../utils": 32, "./Uint8ArrayReader": 21 }], 20: [function(t, r, n) {
            var a = t("./DataReader");
            function l(o) {
              a.call(this, o);
            }
            t("../utils").inherits(l, a), l.prototype.byteAt = function(o) {
              return this.data.charCodeAt(this.zero + o);
            }, l.prototype.lastIndexOfSignature = function(o) {
              return this.data.lastIndexOf(o) - this.zero;
            }, l.prototype.readAndCheckSignature = function(o) {
              return o === this.readData(4);
            }, l.prototype.readData = function(o) {
              this.checkOffset(o);
              var u = this.data.slice(this.zero + this.index, this.zero + this.index + o);
              return this.index += o, u;
            }, r.exports = l;
          }, { "../utils": 32, "./DataReader": 18 }], 21: [function(t, r, n) {
            var a = t("./ArrayReader");
            function l(o) {
              a.call(this, o);
            }
            t("../utils").inherits(l, a), l.prototype.readData = function(o) {
              if (this.checkOffset(o), o === 0) return new Uint8Array(0);
              var u = this.data.subarray(this.zero + this.index, this.zero + this.index + o);
              return this.index += o, u;
            }, r.exports = l;
          }, { "../utils": 32, "./ArrayReader": 17 }], 22: [function(t, r, n) {
            var a = t("../utils"), l = t("../support"), o = t("./ArrayReader"), u = t("./StringReader"), b = t("./NodeBufferReader"), y = t("./Uint8ArrayReader");
            r.exports = function(_) {
              var w = a.getTypeOf(_);
              return a.checkSupport(w), w !== "string" || l.uint8array ? w === "nodebuffer" ? new b(_) : l.uint8array ? new y(a.transformTo("uint8array", _)) : new o(a.transformTo("array", _)) : new u(_);
            };
          }, { "../support": 30, "../utils": 32, "./ArrayReader": 17, "./NodeBufferReader": 19, "./StringReader": 20, "./Uint8ArrayReader": 21 }], 23: [function(t, r, n) {
            n.LOCAL_FILE_HEADER = "PK", n.CENTRAL_FILE_HEADER = "PK", n.CENTRAL_DIRECTORY_END = "PK", n.ZIP64_CENTRAL_DIRECTORY_LOCATOR = "PK\x07", n.ZIP64_CENTRAL_DIRECTORY_END = "PK", n.DATA_DESCRIPTOR = "PK\x07\b";
          }, {}], 24: [function(t, r, n) {
            var a = t("./GenericWorker"), l = t("../utils");
            function o(u) {
              a.call(this, "ConvertWorker to " + u), this.destType = u;
            }
            l.inherits(o, a), o.prototype.processChunk = function(u) {
              this.push({ data: l.transformTo(this.destType, u.data), meta: u.meta });
            }, r.exports = o;
          }, { "../utils": 32, "./GenericWorker": 28 }], 25: [function(t, r, n) {
            var a = t("./GenericWorker"), l = t("../crc32");
            function o() {
              a.call(this, "Crc32Probe"), this.withStreamInfo("crc32", 0);
            }
            t("../utils").inherits(o, a), o.prototype.processChunk = function(u) {
              this.streamInfo.crc32 = l(u.data, this.streamInfo.crc32 || 0), this.push(u);
            }, r.exports = o;
          }, { "../crc32": 4, "../utils": 32, "./GenericWorker": 28 }], 26: [function(t, r, n) {
            var a = t("../utils"), l = t("./GenericWorker");
            function o(u) {
              l.call(this, "DataLengthProbe for " + u), this.propName = u, this.withStreamInfo(u, 0);
            }
            a.inherits(o, l), o.prototype.processChunk = function(u) {
              if (u) {
                var b = this.streamInfo[this.propName] || 0;
                this.streamInfo[this.propName] = b + u.data.length;
              }
              l.prototype.processChunk.call(this, u);
            }, r.exports = o;
          }, { "../utils": 32, "./GenericWorker": 28 }], 27: [function(t, r, n) {
            var a = t("../utils"), l = t("./GenericWorker");
            function o(u) {
              l.call(this, "DataWorker");
              var b = this;
              this.dataIsReady = false, this.index = 0, this.max = 0, this.data = null, this.type = "", this._tickScheduled = false, u.then(function(y) {
                b.dataIsReady = true, b.data = y, b.max = y && y.length || 0, b.type = a.getTypeOf(y), b.isPaused || b._tickAndRepeat();
              }, function(y) {
                b.error(y);
              });
            }
            a.inherits(o, l), o.prototype.cleanUp = function() {
              l.prototype.cleanUp.call(this), this.data = null;
            }, o.prototype.resume = function() {
              return !!l.prototype.resume.call(this) && (!this._tickScheduled && this.dataIsReady && (this._tickScheduled = true, a.delay(this._tickAndRepeat, [], this)), true);
            }, o.prototype._tickAndRepeat = function() {
              this._tickScheduled = false, this.isPaused || this.isFinished || (this._tick(), this.isFinished || (a.delay(this._tickAndRepeat, [], this), this._tickScheduled = true));
            }, o.prototype._tick = function() {
              if (this.isPaused || this.isFinished) return false;
              var u = null, b = Math.min(this.max, this.index + 16384);
              if (this.index >= this.max) return this.end();
              switch (this.type) {
                case "string":
                  u = this.data.substring(this.index, b);
                  break;
                case "uint8array":
                  u = this.data.subarray(this.index, b);
                  break;
                case "array":
                case "nodebuffer":
                  u = this.data.slice(this.index, b);
              }
              return this.index = b, this.push({ data: u, meta: { percent: this.max ? this.index / this.max * 100 : 0 } });
            }, r.exports = o;
          }, { "../utils": 32, "./GenericWorker": 28 }], 28: [function(t, r, n) {
            function a(l) {
              this.name = l || "default", this.streamInfo = {}, this.generatedError = null, this.extraStreamInfo = {}, this.isPaused = true, this.isFinished = false, this.isLocked = false, this._listeners = { data: [], end: [], error: [] }, this.previous = null;
            }
            a.prototype = { push: function(l) {
              this.emit("data", l);
            }, end: function() {
              if (this.isFinished) return false;
              this.flush();
              try {
                this.emit("end"), this.cleanUp(), this.isFinished = true;
              } catch (l) {
                this.emit("error", l);
              }
              return true;
            }, error: function(l) {
              return !this.isFinished && (this.isPaused ? this.generatedError = l : (this.isFinished = true, this.emit("error", l), this.previous && this.previous.error(l), this.cleanUp()), true);
            }, on: function(l, o) {
              return this._listeners[l].push(o), this;
            }, cleanUp: function() {
              this.streamInfo = this.generatedError = this.extraStreamInfo = null, this._listeners = [];
            }, emit: function(l, o) {
              if (this._listeners[l]) for (var u = 0; u < this._listeners[l].length; u++) this._listeners[l][u].call(this, o);
            }, pipe: function(l) {
              return l.registerPrevious(this);
            }, registerPrevious: function(l) {
              if (this.isLocked) throw new Error("The stream '" + this + "' has already been used.");
              this.streamInfo = l.streamInfo, this.mergeStreamInfo(), this.previous = l;
              var o = this;
              return l.on("data", function(u) {
                o.processChunk(u);
              }), l.on("end", function() {
                o.end();
              }), l.on("error", function(u) {
                o.error(u);
              }), this;
            }, pause: function() {
              return !this.isPaused && !this.isFinished && (this.isPaused = true, this.previous && this.previous.pause(), true);
            }, resume: function() {
              if (!this.isPaused || this.isFinished) return false;
              var l = this.isPaused = false;
              return this.generatedError && (this.error(this.generatedError), l = true), this.previous && this.previous.resume(), !l;
            }, flush: function() {
            }, processChunk: function(l) {
              this.push(l);
            }, withStreamInfo: function(l, o) {
              return this.extraStreamInfo[l] = o, this.mergeStreamInfo(), this;
            }, mergeStreamInfo: function() {
              for (var l in this.extraStreamInfo) Object.prototype.hasOwnProperty.call(this.extraStreamInfo, l) && (this.streamInfo[l] = this.extraStreamInfo[l]);
            }, lock: function() {
              if (this.isLocked) throw new Error("The stream '" + this + "' has already been used.");
              this.isLocked = true, this.previous && this.previous.lock();
            }, toString: function() {
              var l = "Worker " + this.name;
              return this.previous ? this.previous + " -> " + l : l;
            } }, r.exports = a;
          }, {}], 29: [function(t, r, n) {
            var a = t("../utils"), l = t("./ConvertWorker"), o = t("./GenericWorker"), u = t("../base64"), b = t("../support"), y = t("../external"), _ = null;
            if (b.nodestream) try {
              _ = t("../nodejs/NodejsStreamOutputAdapter");
            } catch (k) {
            }
            function w(k, h) {
              return new y.Promise(function(m, f) {
                var v = [], C = k._internalType, A = k._outputType, E = k._mimeType;
                k.on("data", function(D, I) {
                  v.push(D), h && h(I);
                }).on("error", function(D) {
                  v = [], f(D);
                }).on("end", function() {
                  try {
                    var D = function(I, W, T) {
                      switch (I) {
                        case "blob":
                          return a.newBlob(a.transformTo("arraybuffer", W), T);
                        case "base64":
                          return u.encode(W);
                        default:
                          return a.transformTo(I, W);
                      }
                    }(A, function(I, W) {
                      var T, X = 0, Q = null, S = 0;
                      for (T = 0; T < W.length; T++) S += W[T].length;
                      switch (I) {
                        case "string":
                          return W.join("");
                        case "array":
                          return Array.prototype.concat.apply([], W);
                        case "uint8array":
                          for (Q = new Uint8Array(S), T = 0; T < W.length; T++) Q.set(W[T], X), X += W[T].length;
                          return Q;
                        case "nodebuffer":
                          return Buffer.concat(W);
                        default:
                          throw new Error("concat : unsupported type '" + I + "'");
                      }
                    }(C, v), E);
                    m(D);
                  } catch (I) {
                    f(I);
                  }
                  v = [];
                }).resume();
              });
            }
            function d(k, h, m) {
              var f = h;
              switch (h) {
                case "blob":
                case "arraybuffer":
                  f = "uint8array";
                  break;
                case "base64":
                  f = "string";
              }
              try {
                this._internalType = f, this._outputType = h, this._mimeType = m, a.checkSupport(f), this._worker = k.pipe(new l(f)), k.lock();
              } catch (v) {
                this._worker = new o("error"), this._worker.error(v);
              }
            }
            d.prototype = { accumulate: function(k) {
              return w(this, k);
            }, on: function(k, h) {
              var m = this;
              return k === "data" ? this._worker.on(k, function(f) {
                h.call(m, f.data, f.meta);
              }) : this._worker.on(k, function() {
                a.delay(h, arguments, m);
              }), this;
            }, resume: function() {
              return a.delay(this._worker.resume, [], this._worker), this;
            }, pause: function() {
              return this._worker.pause(), this;
            }, toNodejsStream: function(k) {
              if (a.checkSupport("nodestream"), this._outputType !== "nodebuffer") throw new Error(this._outputType + " is not supported by this method");
              return new _(this, { objectMode: this._outputType !== "nodebuffer" }, k);
            } }, r.exports = d;
          }, { "../base64": 1, "../external": 6, "../nodejs/NodejsStreamOutputAdapter": 13, "../support": 30, "../utils": 32, "./ConvertWorker": 24, "./GenericWorker": 28 }], 30: [function(t, r, n) {
            if (n.base64 = true, n.array = true, n.string = true, n.arraybuffer = typeof ArrayBuffer != "undefined" && typeof Uint8Array != "undefined", n.nodebuffer = typeof Buffer != "undefined", n.uint8array = typeof Uint8Array != "undefined", typeof ArrayBuffer == "undefined") n.blob = false;
            else {
              var a = new ArrayBuffer(0);
              try {
                n.blob = new Blob([a], { type: "application/zip" }).size === 0;
              } catch (o) {
                try {
                  var l = new (self.BlobBuilder || self.WebKitBlobBuilder || self.MozBlobBuilder || self.MSBlobBuilder)();
                  l.append(a), n.blob = l.getBlob("application/zip").size === 0;
                } catch (u) {
                  n.blob = false;
                }
              }
            }
            try {
              n.nodestream = !!t("readable-stream").Readable;
            } catch (o) {
              n.nodestream = false;
            }
          }, { "readable-stream": 16 }], 31: [function(t, r, n) {
            for (var a = t("./utils"), l = t("./support"), o = t("./nodejsUtils"), u = t("./stream/GenericWorker"), b = new Array(256), y = 0; y < 256; y++) b[y] = 252 <= y ? 6 : 248 <= y ? 5 : 240 <= y ? 4 : 224 <= y ? 3 : 192 <= y ? 2 : 1;
            b[254] = b[254] = 1;
            function _() {
              u.call(this, "utf-8 decode"), this.leftOver = null;
            }
            function w() {
              u.call(this, "utf-8 encode");
            }
            n.utf8encode = function(d) {
              return l.nodebuffer ? o.newBufferFrom(d, "utf-8") : function(k) {
                var h, m, f, v, C, A = k.length, E = 0;
                for (v = 0; v < A; v++) (64512 & (m = k.charCodeAt(v))) == 55296 && v + 1 < A && (64512 & (f = k.charCodeAt(v + 1))) == 56320 && (m = 65536 + (m - 55296 << 10) + (f - 56320), v++), E += m < 128 ? 1 : m < 2048 ? 2 : m < 65536 ? 3 : 4;
                for (h = l.uint8array ? new Uint8Array(E) : new Array(E), v = C = 0; C < E; v++) (64512 & (m = k.charCodeAt(v))) == 55296 && v + 1 < A && (64512 & (f = k.charCodeAt(v + 1))) == 56320 && (m = 65536 + (m - 55296 << 10) + (f - 56320), v++), m < 128 ? h[C++] = m : (m < 2048 ? h[C++] = 192 | m >>> 6 : (m < 65536 ? h[C++] = 224 | m >>> 12 : (h[C++] = 240 | m >>> 18, h[C++] = 128 | m >>> 12 & 63), h[C++] = 128 | m >>> 6 & 63), h[C++] = 128 | 63 & m);
                return h;
              }(d);
            }, n.utf8decode = function(d) {
              return l.nodebuffer ? a.transformTo("nodebuffer", d).toString("utf-8") : function(k) {
                var h, m, f, v, C = k.length, A = new Array(2 * C);
                for (h = m = 0; h < C; ) if ((f = k[h++]) < 128) A[m++] = f;
                else if (4 < (v = b[f])) A[m++] = 65533, h += v - 1;
                else {
                  for (f &= v === 2 ? 31 : v === 3 ? 15 : 7; 1 < v && h < C; ) f = f << 6 | 63 & k[h++], v--;
                  1 < v ? A[m++] = 65533 : f < 65536 ? A[m++] = f : (f -= 65536, A[m++] = 55296 | f >> 10 & 1023, A[m++] = 56320 | 1023 & f);
                }
                return A.length !== m && (A.subarray ? A = A.subarray(0, m) : A.length = m), a.applyFromCharCode(A);
              }(d = a.transformTo(l.uint8array ? "uint8array" : "array", d));
            }, a.inherits(_, u), _.prototype.processChunk = function(d) {
              var k = a.transformTo(l.uint8array ? "uint8array" : "array", d.data);
              if (this.leftOver && this.leftOver.length) {
                if (l.uint8array) {
                  var h = k;
                  (k = new Uint8Array(h.length + this.leftOver.length)).set(this.leftOver, 0), k.set(h, this.leftOver.length);
                } else k = this.leftOver.concat(k);
                this.leftOver = null;
              }
              var m = function(v, C) {
                var A;
                for ((C = C || v.length) > v.length && (C = v.length), A = C - 1; 0 <= A && (192 & v[A]) == 128; ) A--;
                return A < 0 || A === 0 ? C : A + b[v[A]] > C ? A : C;
              }(k), f = k;
              m !== k.length && (l.uint8array ? (f = k.subarray(0, m), this.leftOver = k.subarray(m, k.length)) : (f = k.slice(0, m), this.leftOver = k.slice(m, k.length))), this.push({ data: n.utf8decode(f), meta: d.meta });
            }, _.prototype.flush = function() {
              this.leftOver && this.leftOver.length && (this.push({ data: n.utf8decode(this.leftOver), meta: {} }), this.leftOver = null);
            }, n.Utf8DecodeWorker = _, a.inherits(w, u), w.prototype.processChunk = function(d) {
              this.push({ data: n.utf8encode(d.data), meta: d.meta });
            }, n.Utf8EncodeWorker = w;
          }, { "./nodejsUtils": 14, "./stream/GenericWorker": 28, "./support": 30, "./utils": 32 }], 32: [function(t, r, n) {
            var a = t("./support"), l = t("./base64"), o = t("./nodejsUtils"), u = t("./external");
            function b(h) {
              return h;
            }
            function y(h, m) {
              for (var f = 0; f < h.length; ++f) m[f] = 255 & h.charCodeAt(f);
              return m;
            }
            t("setimmediate"), n.newBlob = function(h, m) {
              n.checkSupport("blob");
              try {
                return new Blob([h], { type: m });
              } catch (v) {
                try {
                  var f = new (self.BlobBuilder || self.WebKitBlobBuilder || self.MozBlobBuilder || self.MSBlobBuilder)();
                  return f.append(h), f.getBlob(m);
                } catch (C) {
                  throw new Error("Bug : can't construct the Blob.");
                }
              }
            };
            var _ = { stringifyByChunk: function(h, m, f) {
              var v = [], C = 0, A = h.length;
              if (A <= f) return String.fromCharCode.apply(null, h);
              for (; C < A; ) m === "array" || m === "nodebuffer" ? v.push(String.fromCharCode.apply(null, h.slice(C, Math.min(C + f, A)))) : v.push(String.fromCharCode.apply(null, h.subarray(C, Math.min(C + f, A)))), C += f;
              return v.join("");
            }, stringifyByChar: function(h) {
              for (var m = "", f = 0; f < h.length; f++) m += String.fromCharCode(h[f]);
              return m;
            }, applyCanBeUsed: { uint8array: function() {
              try {
                return a.uint8array && String.fromCharCode.apply(null, new Uint8Array(1)).length === 1;
              } catch (h) {
                return false;
              }
            }(), nodebuffer: function() {
              try {
                return a.nodebuffer && String.fromCharCode.apply(null, o.allocBuffer(1)).length === 1;
              } catch (h) {
                return false;
              }
            }() } };
            function w(h) {
              var m = 65536, f = n.getTypeOf(h), v = true;
              if (f === "uint8array" ? v = _.applyCanBeUsed.uint8array : f === "nodebuffer" && (v = _.applyCanBeUsed.nodebuffer), v) for (; 1 < m; ) try {
                return _.stringifyByChunk(h, f, m);
              } catch (C) {
                m = Math.floor(m / 2);
              }
              return _.stringifyByChar(h);
            }
            function d(h, m) {
              for (var f = 0; f < h.length; f++) m[f] = h[f];
              return m;
            }
            n.applyFromCharCode = w;
            var k = {};
            k.string = { string: b, array: function(h) {
              return y(h, new Array(h.length));
            }, arraybuffer: function(h) {
              return k.string.uint8array(h).buffer;
            }, uint8array: function(h) {
              return y(h, new Uint8Array(h.length));
            }, nodebuffer: function(h) {
              return y(h, o.allocBuffer(h.length));
            } }, k.array = { string: w, array: b, arraybuffer: function(h) {
              return new Uint8Array(h).buffer;
            }, uint8array: function(h) {
              return new Uint8Array(h);
            }, nodebuffer: function(h) {
              return o.newBufferFrom(h);
            } }, k.arraybuffer = { string: function(h) {
              return w(new Uint8Array(h));
            }, array: function(h) {
              return d(new Uint8Array(h), new Array(h.byteLength));
            }, arraybuffer: b, uint8array: function(h) {
              return new Uint8Array(h);
            }, nodebuffer: function(h) {
              return o.newBufferFrom(new Uint8Array(h));
            } }, k.uint8array = { string: w, array: function(h) {
              return d(h, new Array(h.length));
            }, arraybuffer: function(h) {
              return h.buffer;
            }, uint8array: b, nodebuffer: function(h) {
              return o.newBufferFrom(h);
            } }, k.nodebuffer = { string: w, array: function(h) {
              return d(h, new Array(h.length));
            }, arraybuffer: function(h) {
              return k.nodebuffer.uint8array(h).buffer;
            }, uint8array: function(h) {
              return d(h, new Uint8Array(h.length));
            }, nodebuffer: b }, n.transformTo = function(h, m) {
              if (m = m || "", !h) return m;
              n.checkSupport(h);
              var f = n.getTypeOf(m);
              return k[f][h](m);
            }, n.resolve = function(h) {
              for (var m = h.split("/"), f = [], v = 0; v < m.length; v++) {
                var C = m[v];
                C === "." || C === "" && v !== 0 && v !== m.length - 1 || (C === ".." ? f.pop() : f.push(C));
              }
              return f.join("/");
            }, n.getTypeOf = function(h) {
              return typeof h == "string" ? "string" : Object.prototype.toString.call(h) === "[object Array]" ? "array" : a.nodebuffer && o.isBuffer(h) ? "nodebuffer" : a.uint8array && h instanceof Uint8Array ? "uint8array" : a.arraybuffer && h instanceof ArrayBuffer ? "arraybuffer" : void 0;
            }, n.checkSupport = function(h) {
              if (!a[h.toLowerCase()]) throw new Error(h + " is not supported by this platform");
            }, n.MAX_VALUE_16BITS = 65535, n.MAX_VALUE_32BITS = -1, n.pretty = function(h) {
              var m, f, v = "";
              for (f = 0; f < (h || "").length; f++) v += "\\x" + ((m = h.charCodeAt(f)) < 16 ? "0" : "") + m.toString(16).toUpperCase();
              return v;
            }, n.delay = function(h, m, f) {
              setImmediate(function() {
                h.apply(f || null, m || []);
              });
            }, n.inherits = function(h, m) {
              function f() {
              }
              f.prototype = m.prototype, h.prototype = new f();
            }, n.extend = function() {
              var h, m, f = {};
              for (h = 0; h < arguments.length; h++) for (m in arguments[h]) Object.prototype.hasOwnProperty.call(arguments[h], m) && f[m] === void 0 && (f[m] = arguments[h][m]);
              return f;
            }, n.prepareContent = function(h, m, f, v, C) {
              return u.Promise.resolve(m).then(function(A) {
                return a.blob && (A instanceof Blob || ["[object File]", "[object Blob]"].indexOf(Object.prototype.toString.call(A)) !== -1) && typeof FileReader != "undefined" ? new u.Promise(function(E, D) {
                  var I = new FileReader();
                  I.onload = function(W) {
                    E(W.target.result);
                  }, I.onerror = function(W) {
                    D(W.target.error);
                  }, I.readAsArrayBuffer(A);
                }) : A;
              }).then(function(A) {
                var E = n.getTypeOf(A);
                return E ? (E === "arraybuffer" ? A = n.transformTo("uint8array", A) : E === "string" && (C ? A = l.decode(A) : f && v !== true && (A = function(D) {
                  return y(D, a.uint8array ? new Uint8Array(D.length) : new Array(D.length));
                }(A))), A) : u.Promise.reject(new Error("Can't read the data of '" + h + "'. Is it in a supported JavaScript type (String, Blob, ArrayBuffer, etc) ?"));
              });
            };
          }, { "./base64": 1, "./external": 6, "./nodejsUtils": 14, "./support": 30, setimmediate: 54 }], 33: [function(t, r, n) {
            var a = t("./reader/readerFor"), l = t("./utils"), o = t("./signature"), u = t("./zipEntry"), b = t("./support");
            function y(_) {
              this.files = [], this.loadOptions = _;
            }
            y.prototype = { checkSignature: function(_) {
              if (!this.reader.readAndCheckSignature(_)) {
                this.reader.index -= 4;
                var w = this.reader.readString(4);
                throw new Error("Corrupted zip or bug: unexpected signature (" + l.pretty(w) + ", expected " + l.pretty(_) + ")");
              }
            }, isSignature: function(_, w) {
              var d = this.reader.index;
              this.reader.setIndex(_);
              var k = this.reader.readString(4) === w;
              return this.reader.setIndex(d), k;
            }, readBlockEndOfCentral: function() {
              this.diskNumber = this.reader.readInt(2), this.diskWithCentralDirStart = this.reader.readInt(2), this.centralDirRecordsOnThisDisk = this.reader.readInt(2), this.centralDirRecords = this.reader.readInt(2), this.centralDirSize = this.reader.readInt(4), this.centralDirOffset = this.reader.readInt(4), this.zipCommentLength = this.reader.readInt(2);
              var _ = this.reader.readData(this.zipCommentLength), w = b.uint8array ? "uint8array" : "array", d = l.transformTo(w, _);
              this.zipComment = this.loadOptions.decodeFileName(d);
            }, readBlockZip64EndOfCentral: function() {
              this.zip64EndOfCentralSize = this.reader.readInt(8), this.reader.skip(4), this.diskNumber = this.reader.readInt(4), this.diskWithCentralDirStart = this.reader.readInt(4), this.centralDirRecordsOnThisDisk = this.reader.readInt(8), this.centralDirRecords = this.reader.readInt(8), this.centralDirSize = this.reader.readInt(8), this.centralDirOffset = this.reader.readInt(8), this.zip64ExtensibleData = {};
              for (var _, w, d, k = this.zip64EndOfCentralSize - 44; 0 < k; ) _ = this.reader.readInt(2), w = this.reader.readInt(4), d = this.reader.readData(w), this.zip64ExtensibleData[_] = { id: _, length: w, value: d };
            }, readBlockZip64EndOfCentralLocator: function() {
              if (this.diskWithZip64CentralDirStart = this.reader.readInt(4), this.relativeOffsetEndOfZip64CentralDir = this.reader.readInt(8), this.disksCount = this.reader.readInt(4), 1 < this.disksCount) throw new Error("Multi-volumes zip are not supported");
            }, readLocalFiles: function() {
              var _, w;
              for (_ = 0; _ < this.files.length; _++) w = this.files[_], this.reader.setIndex(w.localHeaderOffset), this.checkSignature(o.LOCAL_FILE_HEADER), w.readLocalPart(this.reader), w.handleUTF8(), w.processAttributes();
            }, readCentralDir: function() {
              var _;
              for (this.reader.setIndex(this.centralDirOffset); this.reader.readAndCheckSignature(o.CENTRAL_FILE_HEADER); ) (_ = new u({ zip64: this.zip64 }, this.loadOptions)).readCentralPart(this.reader), this.files.push(_);
              if (this.centralDirRecords !== this.files.length && this.centralDirRecords !== 0 && this.files.length === 0) throw new Error("Corrupted zip or bug: expected " + this.centralDirRecords + " records in central dir, got " + this.files.length);
            }, readEndOfCentral: function() {
              var _ = this.reader.lastIndexOfSignature(o.CENTRAL_DIRECTORY_END);
              if (_ < 0) throw this.isSignature(0, o.LOCAL_FILE_HEADER) ? new Error("Corrupted zip: can't find end of central directory") : new Error("Can't find end of central directory : is this a zip file ? If it is, see https://stuk.github.io/jszip/documentation/howto/read_zip.html");
              this.reader.setIndex(_);
              var w = _;
              if (this.checkSignature(o.CENTRAL_DIRECTORY_END), this.readBlockEndOfCentral(), this.diskNumber === l.MAX_VALUE_16BITS || this.diskWithCentralDirStart === l.MAX_VALUE_16BITS || this.centralDirRecordsOnThisDisk === l.MAX_VALUE_16BITS || this.centralDirRecords === l.MAX_VALUE_16BITS || this.centralDirSize === l.MAX_VALUE_32BITS || this.centralDirOffset === l.MAX_VALUE_32BITS) {
                if (this.zip64 = true, (_ = this.reader.lastIndexOfSignature(o.ZIP64_CENTRAL_DIRECTORY_LOCATOR)) < 0) throw new Error("Corrupted zip: can't find the ZIP64 end of central directory locator");
                if (this.reader.setIndex(_), this.checkSignature(o.ZIP64_CENTRAL_DIRECTORY_LOCATOR), this.readBlockZip64EndOfCentralLocator(), !this.isSignature(this.relativeOffsetEndOfZip64CentralDir, o.ZIP64_CENTRAL_DIRECTORY_END) && (this.relativeOffsetEndOfZip64CentralDir = this.reader.lastIndexOfSignature(o.ZIP64_CENTRAL_DIRECTORY_END), this.relativeOffsetEndOfZip64CentralDir < 0)) throw new Error("Corrupted zip: can't find the ZIP64 end of central directory");
                this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir), this.checkSignature(o.ZIP64_CENTRAL_DIRECTORY_END), this.readBlockZip64EndOfCentral();
              }
              var d = this.centralDirOffset + this.centralDirSize;
              this.zip64 && (d += 20, d += 12 + this.zip64EndOfCentralSize);
              var k = w - d;
              if (0 < k) this.isSignature(w, o.CENTRAL_FILE_HEADER) || (this.reader.zero = k);
              else if (k < 0) throw new Error("Corrupted zip: missing " + Math.abs(k) + " bytes.");
            }, prepareReader: function(_) {
              this.reader = a(_);
            }, load: function(_) {
              this.prepareReader(_), this.readEndOfCentral(), this.readCentralDir(), this.readLocalFiles();
            } }, r.exports = y;
          }, { "./reader/readerFor": 22, "./signature": 23, "./support": 30, "./utils": 32, "./zipEntry": 34 }], 34: [function(t, r, n) {
            var a = t("./reader/readerFor"), l = t("./utils"), o = t("./compressedObject"), u = t("./crc32"), b = t("./utf8"), y = t("./compressions"), _ = t("./support");
            function w(d, k) {
              this.options = d, this.loadOptions = k;
            }
            w.prototype = { isEncrypted: function() {
              return (1 & this.bitFlag) == 1;
            }, useUTF8: function() {
              return (2048 & this.bitFlag) == 2048;
            }, readLocalPart: function(d) {
              var k, h;
              if (d.skip(22), this.fileNameLength = d.readInt(2), h = d.readInt(2), this.fileName = d.readData(this.fileNameLength), d.skip(h), this.compressedSize === -1 || this.uncompressedSize === -1) throw new Error("Bug or corrupted zip : didn't get enough information from the central directory (compressedSize === -1 || uncompressedSize === -1)");
              if ((k = function(m) {
                for (var f in y) if (Object.prototype.hasOwnProperty.call(y, f) && y[f].magic === m) return y[f];
                return null;
              }(this.compressionMethod)) === null) throw new Error("Corrupted zip : compression " + l.pretty(this.compressionMethod) + " unknown (inner file : " + l.transformTo("string", this.fileName) + ")");
              this.decompressed = new o(this.compressedSize, this.uncompressedSize, this.crc32, k, d.readData(this.compressedSize));
            }, readCentralPart: function(d) {
              this.versionMadeBy = d.readInt(2), d.skip(2), this.bitFlag = d.readInt(2), this.compressionMethod = d.readString(2), this.date = d.readDate(), this.crc32 = d.readInt(4), this.compressedSize = d.readInt(4), this.uncompressedSize = d.readInt(4);
              var k = d.readInt(2);
              if (this.extraFieldsLength = d.readInt(2), this.fileCommentLength = d.readInt(2), this.diskNumberStart = d.readInt(2), this.internalFileAttributes = d.readInt(2), this.externalFileAttributes = d.readInt(4), this.localHeaderOffset = d.readInt(4), this.isEncrypted()) throw new Error("Encrypted zip are not supported");
              d.skip(k), this.readExtraFields(d), this.parseZIP64ExtraField(d), this.fileComment = d.readData(this.fileCommentLength);
            }, processAttributes: function() {
              this.unixPermissions = null, this.dosPermissions = null;
              var d = this.versionMadeBy >> 8;
              this.dir = !!(16 & this.externalFileAttributes), d == 0 && (this.dosPermissions = 63 & this.externalFileAttributes), d == 3 && (this.unixPermissions = this.externalFileAttributes >> 16 & 65535), this.dir || this.fileNameStr.slice(-1) !== "/" || (this.dir = true);
            }, parseZIP64ExtraField: function() {
              if (this.extraFields[1]) {
                var d = a(this.extraFields[1].value);
                this.uncompressedSize === l.MAX_VALUE_32BITS && (this.uncompressedSize = d.readInt(8)), this.compressedSize === l.MAX_VALUE_32BITS && (this.compressedSize = d.readInt(8)), this.localHeaderOffset === l.MAX_VALUE_32BITS && (this.localHeaderOffset = d.readInt(8)), this.diskNumberStart === l.MAX_VALUE_32BITS && (this.diskNumberStart = d.readInt(4));
              }
            }, readExtraFields: function(d) {
              var k, h, m, f = d.index + this.extraFieldsLength;
              for (this.extraFields || (this.extraFields = {}); d.index + 4 < f; ) k = d.readInt(2), h = d.readInt(2), m = d.readData(h), this.extraFields[k] = { id: k, length: h, value: m };
              d.setIndex(f);
            }, handleUTF8: function() {
              var d = _.uint8array ? "uint8array" : "array";
              if (this.useUTF8()) this.fileNameStr = b.utf8decode(this.fileName), this.fileCommentStr = b.utf8decode(this.fileComment);
              else {
                var k = this.findExtraFieldUnicodePath();
                if (k !== null) this.fileNameStr = k;
                else {
                  var h = l.transformTo(d, this.fileName);
                  this.fileNameStr = this.loadOptions.decodeFileName(h);
                }
                var m = this.findExtraFieldUnicodeComment();
                if (m !== null) this.fileCommentStr = m;
                else {
                  var f = l.transformTo(d, this.fileComment);
                  this.fileCommentStr = this.loadOptions.decodeFileName(f);
                }
              }
            }, findExtraFieldUnicodePath: function() {
              var d = this.extraFields[28789];
              if (d) {
                var k = a(d.value);
                return k.readInt(1) !== 1 || u(this.fileName) !== k.readInt(4) ? null : b.utf8decode(k.readData(d.length - 5));
              }
              return null;
            }, findExtraFieldUnicodeComment: function() {
              var d = this.extraFields[25461];
              if (d) {
                var k = a(d.value);
                return k.readInt(1) !== 1 || u(this.fileComment) !== k.readInt(4) ? null : b.utf8decode(k.readData(d.length - 5));
              }
              return null;
            } }, r.exports = w;
          }, { "./compressedObject": 2, "./compressions": 3, "./crc32": 4, "./reader/readerFor": 22, "./support": 30, "./utf8": 31, "./utils": 32 }], 35: [function(t, r, n) {
            function a(k, h, m) {
              this.name = k, this.dir = m.dir, this.date = m.date, this.comment = m.comment, this.unixPermissions = m.unixPermissions, this.dosPermissions = m.dosPermissions, this._data = h, this._dataBinary = m.binary, this.options = { compression: m.compression, compressionOptions: m.compressionOptions };
            }
            var l = t("./stream/StreamHelper"), o = t("./stream/DataWorker"), u = t("./utf8"), b = t("./compressedObject"), y = t("./stream/GenericWorker");
            a.prototype = { internalStream: function(k) {
              var h = null, m = "string";
              try {
                if (!k) throw new Error("No output type specified.");
                var f = (m = k.toLowerCase()) === "string" || m === "text";
                m !== "binarystring" && m !== "text" || (m = "string"), h = this._decompressWorker();
                var v = !this._dataBinary;
                v && !f && (h = h.pipe(new u.Utf8EncodeWorker())), !v && f && (h = h.pipe(new u.Utf8DecodeWorker()));
              } catch (C) {
                (h = new y("error")).error(C);
              }
              return new l(h, m, "");
            }, async: function(k, h) {
              return this.internalStream(k).accumulate(h);
            }, nodeStream: function(k, h) {
              return this.internalStream(k || "nodebuffer").toNodejsStream(h);
            }, _compressWorker: function(k, h) {
              if (this._data instanceof b && this._data.compression.magic === k.magic) return this._data.getCompressedWorker();
              var m = this._decompressWorker();
              return this._dataBinary || (m = m.pipe(new u.Utf8EncodeWorker())), b.createWorkerFrom(m, k, h);
            }, _decompressWorker: function() {
              return this._data instanceof b ? this._data.getContentWorker() : this._data instanceof y ? this._data : new o(this._data);
            } };
            for (var _ = ["asText", "asBinary", "asNodeBuffer", "asUint8Array", "asArrayBuffer"], w = function() {
              throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.");
            }, d = 0; d < _.length; d++) a.prototype[_[d]] = w;
            r.exports = a;
          }, { "./compressedObject": 2, "./stream/DataWorker": 27, "./stream/GenericWorker": 28, "./stream/StreamHelper": 29, "./utf8": 31 }], 36: [function(t, r, n) {
            (function(a) {
              var l, o, u = a.MutationObserver || a.WebKitMutationObserver;
              if (u) {
                var b = 0, y = new u(k), _ = a.document.createTextNode("");
                y.observe(_, { characterData: true }), l = function() {
                  _.data = b = ++b % 2;
                };
              } else if (a.setImmediate || a.MessageChannel === void 0) l = "document" in a && "onreadystatechange" in a.document.createElement("script") ? function() {
                var h = a.document.createElement("script");
                h.onreadystatechange = function() {
                  k(), h.onreadystatechange = null, h.parentNode.removeChild(h), h = null;
                }, a.document.documentElement.appendChild(h);
              } : function() {
                setTimeout(k, 0);
              };
              else {
                var w = new a.MessageChannel();
                w.port1.onmessage = k, l = function() {
                  w.port2.postMessage(0);
                };
              }
              var d = [];
              function k() {
                var h, m;
                o = true;
                for (var f = d.length; f; ) {
                  for (m = d, d = [], h = -1; ++h < f; ) m[h]();
                  f = d.length;
                }
                o = false;
              }
              r.exports = function(h) {
                d.push(h) !== 1 || o || l();
              };
            }).call(this, typeof vt != "undefined" ? vt : typeof self != "undefined" ? self : typeof window != "undefined" ? window : {});
          }, {}], 37: [function(t, r, n) {
            var a = t("immediate");
            function l() {
            }
            var o = {}, u = ["REJECTED"], b = ["FULFILLED"], y = ["PENDING"];
            function _(f) {
              if (typeof f != "function") throw new TypeError("resolver must be a function");
              this.state = y, this.queue = [], this.outcome = void 0, f !== l && h(this, f);
            }
            function w(f, v, C) {
              this.promise = f, typeof v == "function" && (this.onFulfilled = v, this.callFulfilled = this.otherCallFulfilled), typeof C == "function" && (this.onRejected = C, this.callRejected = this.otherCallRejected);
            }
            function d(f, v, C) {
              a(function() {
                var A;
                try {
                  A = v(C);
                } catch (E) {
                  return o.reject(f, E);
                }
                A === f ? o.reject(f, new TypeError("Cannot resolve promise with itself")) : o.resolve(f, A);
              });
            }
            function k(f) {
              var v = f && f.then;
              if (f && (typeof f == "object" || typeof f == "function") && typeof v == "function") return function() {
                v.apply(f, arguments);
              };
            }
            function h(f, v) {
              var C = false;
              function A(I) {
                C || (C = true, o.reject(f, I));
              }
              function E(I) {
                C || (C = true, o.resolve(f, I));
              }
              var D = m(function() {
                v(E, A);
              });
              D.status === "error" && A(D.value);
            }
            function m(f, v) {
              var C = {};
              try {
                C.value = f(v), C.status = "success";
              } catch (A) {
                C.status = "error", C.value = A;
              }
              return C;
            }
            (r.exports = _).prototype.finally = function(f) {
              if (typeof f != "function") return this;
              var v = this.constructor;
              return this.then(function(C) {
                return v.resolve(f()).then(function() {
                  return C;
                });
              }, function(C) {
                return v.resolve(f()).then(function() {
                  throw C;
                });
              });
            }, _.prototype.catch = function(f) {
              return this.then(null, f);
            }, _.prototype.then = function(f, v) {
              if (typeof f != "function" && this.state === b || typeof v != "function" && this.state === u) return this;
              var C = new this.constructor(l);
              return this.state !== y ? d(C, this.state === b ? f : v, this.outcome) : this.queue.push(new w(C, f, v)), C;
            }, w.prototype.callFulfilled = function(f) {
              o.resolve(this.promise, f);
            }, w.prototype.otherCallFulfilled = function(f) {
              d(this.promise, this.onFulfilled, f);
            }, w.prototype.callRejected = function(f) {
              o.reject(this.promise, f);
            }, w.prototype.otherCallRejected = function(f) {
              d(this.promise, this.onRejected, f);
            }, o.resolve = function(f, v) {
              var C = m(k, v);
              if (C.status === "error") return o.reject(f, C.value);
              var A = C.value;
              if (A) h(f, A);
              else {
                f.state = b, f.outcome = v;
                for (var E = -1, D = f.queue.length; ++E < D; ) f.queue[E].callFulfilled(v);
              }
              return f;
            }, o.reject = function(f, v) {
              f.state = u, f.outcome = v;
              for (var C = -1, A = f.queue.length; ++C < A; ) f.queue[C].callRejected(v);
              return f;
            }, _.resolve = function(f) {
              return f instanceof this ? f : o.resolve(new this(l), f);
            }, _.reject = function(f) {
              var v = new this(l);
              return o.reject(v, f);
            }, _.all = function(f) {
              var v = this;
              if (Object.prototype.toString.call(f) !== "[object Array]") return this.reject(new TypeError("must be an array"));
              var C = f.length, A = false;
              if (!C) return this.resolve([]);
              for (var E = new Array(C), D = 0, I = -1, W = new this(l); ++I < C; ) T(f[I], I);
              return W;
              function T(X, Q) {
                v.resolve(X).then(function(S) {
                  E[Q] = S, ++D !== C || A || (A = true, o.resolve(W, E));
                }, function(S) {
                  A || (A = true, o.reject(W, S));
                });
              }
            }, _.race = function(f) {
              var v = this;
              if (Object.prototype.toString.call(f) !== "[object Array]") return this.reject(new TypeError("must be an array"));
              var C = f.length, A = false;
              if (!C) return this.resolve([]);
              for (var E = -1, D = new this(l); ++E < C; ) I = f[E], v.resolve(I).then(function(W) {
                A || (A = true, o.resolve(D, W));
              }, function(W) {
                A || (A = true, o.reject(D, W));
              });
              var I;
              return D;
            };
          }, { immediate: 36 }], 38: [function(t, r, n) {
            var a = {};
            (0, t("./lib/utils/common").assign)(a, t("./lib/deflate"), t("./lib/inflate"), t("./lib/zlib/constants")), r.exports = a;
          }, { "./lib/deflate": 39, "./lib/inflate": 40, "./lib/utils/common": 41, "./lib/zlib/constants": 44 }], 39: [function(t, r, n) {
            var a = t("./zlib/deflate"), l = t("./utils/common"), o = t("./utils/strings"), u = t("./zlib/messages"), b = t("./zlib/zstream"), y = Object.prototype.toString, _ = 0, w = -1, d = 0, k = 8;
            function h(f) {
              if (!(this instanceof h)) return new h(f);
              this.options = l.assign({ level: w, method: k, chunkSize: 16384, windowBits: 15, memLevel: 8, strategy: d, to: "" }, f || {});
              var v = this.options;
              v.raw && 0 < v.windowBits ? v.windowBits = -v.windowBits : v.gzip && 0 < v.windowBits && v.windowBits < 16 && (v.windowBits += 16), this.err = 0, this.msg = "", this.ended = false, this.chunks = [], this.strm = new b(), this.strm.avail_out = 0;
              var C = a.deflateInit2(this.strm, v.level, v.method, v.windowBits, v.memLevel, v.strategy);
              if (C !== _) throw new Error(u[C]);
              if (v.header && a.deflateSetHeader(this.strm, v.header), v.dictionary) {
                var A;
                if (A = typeof v.dictionary == "string" ? o.string2buf(v.dictionary) : y.call(v.dictionary) === "[object ArrayBuffer]" ? new Uint8Array(v.dictionary) : v.dictionary, (C = a.deflateSetDictionary(this.strm, A)) !== _) throw new Error(u[C]);
                this._dict_set = true;
              }
            }
            function m(f, v) {
              var C = new h(v);
              if (C.push(f, true), C.err) throw C.msg || u[C.err];
              return C.result;
            }
            h.prototype.push = function(f, v) {
              var C, A, E = this.strm, D = this.options.chunkSize;
              if (this.ended) return false;
              A = v === ~~v ? v : v === true ? 4 : 0, typeof f == "string" ? E.input = o.string2buf(f) : y.call(f) === "[object ArrayBuffer]" ? E.input = new Uint8Array(f) : E.input = f, E.next_in = 0, E.avail_in = E.input.length;
              do {
                if (E.avail_out === 0 && (E.output = new l.Buf8(D), E.next_out = 0, E.avail_out = D), (C = a.deflate(E, A)) !== 1 && C !== _) return this.onEnd(C), !(this.ended = true);
                E.avail_out !== 0 && (E.avail_in !== 0 || A !== 4 && A !== 2) || (this.options.to === "string" ? this.onData(o.buf2binstring(l.shrinkBuf(E.output, E.next_out))) : this.onData(l.shrinkBuf(E.output, E.next_out)));
              } while ((0 < E.avail_in || E.avail_out === 0) && C !== 1);
              return A === 4 ? (C = a.deflateEnd(this.strm), this.onEnd(C), this.ended = true, C === _) : A !== 2 || (this.onEnd(_), !(E.avail_out = 0));
            }, h.prototype.onData = function(f) {
              this.chunks.push(f);
            }, h.prototype.onEnd = function(f) {
              f === _ && (this.options.to === "string" ? this.result = this.chunks.join("") : this.result = l.flattenChunks(this.chunks)), this.chunks = [], this.err = f, this.msg = this.strm.msg;
            }, n.Deflate = h, n.deflate = m, n.deflateRaw = function(f, v) {
              return (v = v || {}).raw = true, m(f, v);
            }, n.gzip = function(f, v) {
              return (v = v || {}).gzip = true, m(f, v);
            };
          }, { "./utils/common": 41, "./utils/strings": 42, "./zlib/deflate": 46, "./zlib/messages": 51, "./zlib/zstream": 53 }], 40: [function(t, r, n) {
            var a = t("./zlib/inflate"), l = t("./utils/common"), o = t("./utils/strings"), u = t("./zlib/constants"), b = t("./zlib/messages"), y = t("./zlib/zstream"), _ = t("./zlib/gzheader"), w = Object.prototype.toString;
            function d(h) {
              if (!(this instanceof d)) return new d(h);
              this.options = l.assign({ chunkSize: 16384, windowBits: 0, to: "" }, h || {});
              var m = this.options;
              m.raw && 0 <= m.windowBits && m.windowBits < 16 && (m.windowBits = -m.windowBits, m.windowBits === 0 && (m.windowBits = -15)), !(0 <= m.windowBits && m.windowBits < 16) || h && h.windowBits || (m.windowBits += 32), 15 < m.windowBits && m.windowBits < 48 && !(15 & m.windowBits) && (m.windowBits |= 15), this.err = 0, this.msg = "", this.ended = false, this.chunks = [], this.strm = new y(), this.strm.avail_out = 0;
              var f = a.inflateInit2(this.strm, m.windowBits);
              if (f !== u.Z_OK) throw new Error(b[f]);
              this.header = new _(), a.inflateGetHeader(this.strm, this.header);
            }
            function k(h, m) {
              var f = new d(m);
              if (f.push(h, true), f.err) throw f.msg || b[f.err];
              return f.result;
            }
            d.prototype.push = function(h, m) {
              var f, v, C, A, E, D, I = this.strm, W = this.options.chunkSize, T = this.options.dictionary, X = false;
              if (this.ended) return false;
              v = m === ~~m ? m : m === true ? u.Z_FINISH : u.Z_NO_FLUSH, typeof h == "string" ? I.input = o.binstring2buf(h) : w.call(h) === "[object ArrayBuffer]" ? I.input = new Uint8Array(h) : I.input = h, I.next_in = 0, I.avail_in = I.input.length;
              do {
                if (I.avail_out === 0 && (I.output = new l.Buf8(W), I.next_out = 0, I.avail_out = W), (f = a.inflate(I, u.Z_NO_FLUSH)) === u.Z_NEED_DICT && T && (D = typeof T == "string" ? o.string2buf(T) : w.call(T) === "[object ArrayBuffer]" ? new Uint8Array(T) : T, f = a.inflateSetDictionary(this.strm, D)), f === u.Z_BUF_ERROR && X === true && (f = u.Z_OK, X = false), f !== u.Z_STREAM_END && f !== u.Z_OK) return this.onEnd(f), !(this.ended = true);
                I.next_out && (I.avail_out !== 0 && f !== u.Z_STREAM_END && (I.avail_in !== 0 || v !== u.Z_FINISH && v !== u.Z_SYNC_FLUSH) || (this.options.to === "string" ? (C = o.utf8border(I.output, I.next_out), A = I.next_out - C, E = o.buf2string(I.output, C), I.next_out = A, I.avail_out = W - A, A && l.arraySet(I.output, I.output, C, A, 0), this.onData(E)) : this.onData(l.shrinkBuf(I.output, I.next_out)))), I.avail_in === 0 && I.avail_out === 0 && (X = true);
              } while ((0 < I.avail_in || I.avail_out === 0) && f !== u.Z_STREAM_END);
              return f === u.Z_STREAM_END && (v = u.Z_FINISH), v === u.Z_FINISH ? (f = a.inflateEnd(this.strm), this.onEnd(f), this.ended = true, f === u.Z_OK) : v !== u.Z_SYNC_FLUSH || (this.onEnd(u.Z_OK), !(I.avail_out = 0));
            }, d.prototype.onData = function(h) {
              this.chunks.push(h);
            }, d.prototype.onEnd = function(h) {
              h === u.Z_OK && (this.options.to === "string" ? this.result = this.chunks.join("") : this.result = l.flattenChunks(this.chunks)), this.chunks = [], this.err = h, this.msg = this.strm.msg;
            }, n.Inflate = d, n.inflate = k, n.inflateRaw = function(h, m) {
              return (m = m || {}).raw = true, k(h, m);
            }, n.ungzip = k;
          }, { "./utils/common": 41, "./utils/strings": 42, "./zlib/constants": 44, "./zlib/gzheader": 47, "./zlib/inflate": 49, "./zlib/messages": 51, "./zlib/zstream": 53 }], 41: [function(t, r, n) {
            var a = typeof Uint8Array != "undefined" && typeof Uint16Array != "undefined" && typeof Int32Array != "undefined";
            n.assign = function(u) {
              for (var b = Array.prototype.slice.call(arguments, 1); b.length; ) {
                var y = b.shift();
                if (y) {
                  if (typeof y != "object") throw new TypeError(y + "must be non-object");
                  for (var _ in y) y.hasOwnProperty(_) && (u[_] = y[_]);
                }
              }
              return u;
            }, n.shrinkBuf = function(u, b) {
              return u.length === b ? u : u.subarray ? u.subarray(0, b) : (u.length = b, u);
            };
            var l = { arraySet: function(u, b, y, _, w) {
              if (b.subarray && u.subarray) u.set(b.subarray(y, y + _), w);
              else for (var d = 0; d < _; d++) u[w + d] = b[y + d];
            }, flattenChunks: function(u) {
              var b, y, _, w, d, k;
              for (b = _ = 0, y = u.length; b < y; b++) _ += u[b].length;
              for (k = new Uint8Array(_), b = w = 0, y = u.length; b < y; b++) d = u[b], k.set(d, w), w += d.length;
              return k;
            } }, o = { arraySet: function(u, b, y, _, w) {
              for (var d = 0; d < _; d++) u[w + d] = b[y + d];
            }, flattenChunks: function(u) {
              return [].concat.apply([], u);
            } };
            n.setTyped = function(u) {
              u ? (n.Buf8 = Uint8Array, n.Buf16 = Uint16Array, n.Buf32 = Int32Array, n.assign(n, l)) : (n.Buf8 = Array, n.Buf16 = Array, n.Buf32 = Array, n.assign(n, o));
            }, n.setTyped(a);
          }, {}], 42: [function(t, r, n) {
            var a = t("./common"), l = true, o = true;
            try {
              String.fromCharCode.apply(null, [0]);
            } catch (_) {
              l = false;
            }
            try {
              String.fromCharCode.apply(null, new Uint8Array(1));
            } catch (_) {
              o = false;
            }
            for (var u = new a.Buf8(256), b = 0; b < 256; b++) u[b] = 252 <= b ? 6 : 248 <= b ? 5 : 240 <= b ? 4 : 224 <= b ? 3 : 192 <= b ? 2 : 1;
            function y(_, w) {
              if (w < 65537 && (_.subarray && o || !_.subarray && l)) return String.fromCharCode.apply(null, a.shrinkBuf(_, w));
              for (var d = "", k = 0; k < w; k++) d += String.fromCharCode(_[k]);
              return d;
            }
            u[254] = u[254] = 1, n.string2buf = function(_) {
              var w, d, k, h, m, f = _.length, v = 0;
              for (h = 0; h < f; h++) (64512 & (d = _.charCodeAt(h))) == 55296 && h + 1 < f && (64512 & (k = _.charCodeAt(h + 1))) == 56320 && (d = 65536 + (d - 55296 << 10) + (k - 56320), h++), v += d < 128 ? 1 : d < 2048 ? 2 : d < 65536 ? 3 : 4;
              for (w = new a.Buf8(v), h = m = 0; m < v; h++) (64512 & (d = _.charCodeAt(h))) == 55296 && h + 1 < f && (64512 & (k = _.charCodeAt(h + 1))) == 56320 && (d = 65536 + (d - 55296 << 10) + (k - 56320), h++), d < 128 ? w[m++] = d : (d < 2048 ? w[m++] = 192 | d >>> 6 : (d < 65536 ? w[m++] = 224 | d >>> 12 : (w[m++] = 240 | d >>> 18, w[m++] = 128 | d >>> 12 & 63), w[m++] = 128 | d >>> 6 & 63), w[m++] = 128 | 63 & d);
              return w;
            }, n.buf2binstring = function(_) {
              return y(_, _.length);
            }, n.binstring2buf = function(_) {
              for (var w = new a.Buf8(_.length), d = 0, k = w.length; d < k; d++) w[d] = _.charCodeAt(d);
              return w;
            }, n.buf2string = function(_, w) {
              var d, k, h, m, f = w || _.length, v = new Array(2 * f);
              for (d = k = 0; d < f; ) if ((h = _[d++]) < 128) v[k++] = h;
              else if (4 < (m = u[h])) v[k++] = 65533, d += m - 1;
              else {
                for (h &= m === 2 ? 31 : m === 3 ? 15 : 7; 1 < m && d < f; ) h = h << 6 | 63 & _[d++], m--;
                1 < m ? v[k++] = 65533 : h < 65536 ? v[k++] = h : (h -= 65536, v[k++] = 55296 | h >> 10 & 1023, v[k++] = 56320 | 1023 & h);
              }
              return y(v, k);
            }, n.utf8border = function(_, w) {
              var d;
              for ((w = w || _.length) > _.length && (w = _.length), d = w - 1; 0 <= d && (192 & _[d]) == 128; ) d--;
              return d < 0 || d === 0 ? w : d + u[_[d]] > w ? d : w;
            };
          }, { "./common": 41 }], 43: [function(t, r, n) {
            r.exports = function(a, l, o, u) {
              for (var b = 65535 & a | 0, y = a >>> 16 & 65535 | 0, _ = 0; o !== 0; ) {
                for (o -= _ = 2e3 < o ? 2e3 : o; y = y + (b = b + l[u++] | 0) | 0, --_; ) ;
                b %= 65521, y %= 65521;
              }
              return b | y << 16 | 0;
            };
          }, {}], 44: [function(t, r, n) {
            r.exports = { Z_NO_FLUSH: 0, Z_PARTIAL_FLUSH: 1, Z_SYNC_FLUSH: 2, Z_FULL_FLUSH: 3, Z_FINISH: 4, Z_BLOCK: 5, Z_TREES: 6, Z_OK: 0, Z_STREAM_END: 1, Z_NEED_DICT: 2, Z_ERRNO: -1, Z_STREAM_ERROR: -2, Z_DATA_ERROR: -3, Z_BUF_ERROR: -5, Z_NO_COMPRESSION: 0, Z_BEST_SPEED: 1, Z_BEST_COMPRESSION: 9, Z_DEFAULT_COMPRESSION: -1, Z_FILTERED: 1, Z_HUFFMAN_ONLY: 2, Z_RLE: 3, Z_FIXED: 4, Z_DEFAULT_STRATEGY: 0, Z_BINARY: 0, Z_TEXT: 1, Z_UNKNOWN: 2, Z_DEFLATED: 8 };
          }, {}], 45: [function(t, r, n) {
            var a = function() {
              for (var l, o = [], u = 0; u < 256; u++) {
                l = u;
                for (var b = 0; b < 8; b++) l = 1 & l ? 3988292384 ^ l >>> 1 : l >>> 1;
                o[u] = l;
              }
              return o;
            }();
            r.exports = function(l, o, u, b) {
              var y = a, _ = b + u;
              l ^= -1;
              for (var w = b; w < _; w++) l = l >>> 8 ^ y[255 & (l ^ o[w])];
              return -1 ^ l;
            };
          }, {}], 46: [function(t, r, n) {
            var a, l = t("../utils/common"), o = t("./trees"), u = t("./adler32"), b = t("./crc32"), y = t("./messages"), _ = 0, w = 4, d = 0, k = -2, h = -1, m = 4, f = 2, v = 8, C = 9, A = 286, E = 30, D = 19, I = 2 * A + 1, W = 15, T = 3, X = 258, Q = X + T + 1, S = 42, O = 113, c = 1, L = 2, et = 3, $ = 4;
            function rt(s, F) {
              return s.msg = y[F], F;
            }
            function H(s) {
              return (s << 1) - (4 < s ? 9 : 0);
            }
            function tt(s) {
              for (var F = s.length; 0 <= --F; ) s[F] = 0;
            }
            function R(s) {
              var F = s.state, M = F.pending;
              M > s.avail_out && (M = s.avail_out), M !== 0 && (l.arraySet(s.output, F.pending_buf, F.pending_out, M, s.next_out), s.next_out += M, F.pending_out += M, s.total_out += M, s.avail_out -= M, F.pending -= M, F.pending === 0 && (F.pending_out = 0));
            }
            function B(s, F) {
              o._tr_flush_block(s, 0 <= s.block_start ? s.block_start : -1, s.strstart - s.block_start, F), s.block_start = s.strstart, R(s.strm);
            }
            function J(s, F) {
              s.pending_buf[s.pending++] = F;
            }
            function q(s, F) {
              s.pending_buf[s.pending++] = F >>> 8 & 255, s.pending_buf[s.pending++] = 255 & F;
            }
            function G(s, F) {
              var M, g, p = s.max_chain_length, x = s.strstart, j = s.prev_length, U = s.nice_match, N = s.strstart > s.w_size - Q ? s.strstart - (s.w_size - Q) : 0, Z = s.window, K = s.w_mask, V = s.prev, Y = s.strstart + X, lt = Z[x + j - 1], st = Z[x + j];
              s.prev_length >= s.good_match && (p >>= 2), U > s.lookahead && (U = s.lookahead);
              do
                if (Z[(M = F) + j] === st && Z[M + j - 1] === lt && Z[M] === Z[x] && Z[++M] === Z[x + 1]) {
                  x += 2, M++;
                  do
                    ;
                  while (Z[++x] === Z[++M] && Z[++x] === Z[++M] && Z[++x] === Z[++M] && Z[++x] === Z[++M] && Z[++x] === Z[++M] && Z[++x] === Z[++M] && Z[++x] === Z[++M] && Z[++x] === Z[++M] && x < Y);
                  if (g = X - (Y - x), x = Y - X, j < g) {
                    if (s.match_start = F, U <= (j = g)) break;
                    lt = Z[x + j - 1], st = Z[x + j];
                  }
                }
              while ((F = V[F & K]) > N && --p != 0);
              return j <= s.lookahead ? j : s.lookahead;
            }
            function mt(s) {
              var F, M, g, p, x, j, U, N, Z, K, V = s.w_size;
              do {
                if (p = s.window_size - s.lookahead - s.strstart, s.strstart >= V + (V - Q)) {
                  for (l.arraySet(s.window, s.window, V, V, 0), s.match_start -= V, s.strstart -= V, s.block_start -= V, F = M = s.hash_size; g = s.head[--F], s.head[F] = V <= g ? g - V : 0, --M; ) ;
                  for (F = M = V; g = s.prev[--F], s.prev[F] = V <= g ? g - V : 0, --M; ) ;
                  p += V;
                }
                if (s.strm.avail_in === 0) break;
                if (j = s.strm, U = s.window, N = s.strstart + s.lookahead, Z = p, K = void 0, K = j.avail_in, Z < K && (K = Z), M = K === 0 ? 0 : (j.avail_in -= K, l.arraySet(U, j.input, j.next_in, K, N), j.state.wrap === 1 ? j.adler = u(j.adler, U, K, N) : j.state.wrap === 2 && (j.adler = b(j.adler, U, K, N)), j.next_in += K, j.total_in += K, K), s.lookahead += M, s.lookahead + s.insert >= T) for (x = s.strstart - s.insert, s.ins_h = s.window[x], s.ins_h = (s.ins_h << s.hash_shift ^ s.window[x + 1]) & s.hash_mask; s.insert && (s.ins_h = (s.ins_h << s.hash_shift ^ s.window[x + T - 1]) & s.hash_mask, s.prev[x & s.w_mask] = s.head[s.ins_h], s.head[s.ins_h] = x, x++, s.insert--, !(s.lookahead + s.insert < T)); ) ;
              } while (s.lookahead < Q && s.strm.avail_in !== 0);
            }
            function yt(s, F) {
              for (var M, g; ; ) {
                if (s.lookahead < Q) {
                  if (mt(s), s.lookahead < Q && F === _) return c;
                  if (s.lookahead === 0) break;
                }
                if (M = 0, s.lookahead >= T && (s.ins_h = (s.ins_h << s.hash_shift ^ s.window[s.strstart + T - 1]) & s.hash_mask, M = s.prev[s.strstart & s.w_mask] = s.head[s.ins_h], s.head[s.ins_h] = s.strstart), M !== 0 && s.strstart - M <= s.w_size - Q && (s.match_length = G(s, M)), s.match_length >= T) if (g = o._tr_tally(s, s.strstart - s.match_start, s.match_length - T), s.lookahead -= s.match_length, s.match_length <= s.max_lazy_match && s.lookahead >= T) {
                  for (s.match_length--; s.strstart++, s.ins_h = (s.ins_h << s.hash_shift ^ s.window[s.strstart + T - 1]) & s.hash_mask, M = s.prev[s.strstart & s.w_mask] = s.head[s.ins_h], s.head[s.ins_h] = s.strstart, --s.match_length != 0; ) ;
                  s.strstart++;
                } else s.strstart += s.match_length, s.match_length = 0, s.ins_h = s.window[s.strstart], s.ins_h = (s.ins_h << s.hash_shift ^ s.window[s.strstart + 1]) & s.hash_mask;
                else g = o._tr_tally(s, 0, s.window[s.strstart]), s.lookahead--, s.strstart++;
                if (g && (B(s, false), s.strm.avail_out === 0)) return c;
              }
              return s.insert = s.strstart < T - 1 ? s.strstart : T - 1, F === w ? (B(s, true), s.strm.avail_out === 0 ? et : $) : s.last_lit && (B(s, false), s.strm.avail_out === 0) ? c : L;
            }
            function at(s, F) {
              for (var M, g, p; ; ) {
                if (s.lookahead < Q) {
                  if (mt(s), s.lookahead < Q && F === _) return c;
                  if (s.lookahead === 0) break;
                }
                if (M = 0, s.lookahead >= T && (s.ins_h = (s.ins_h << s.hash_shift ^ s.window[s.strstart + T - 1]) & s.hash_mask, M = s.prev[s.strstart & s.w_mask] = s.head[s.ins_h], s.head[s.ins_h] = s.strstart), s.prev_length = s.match_length, s.prev_match = s.match_start, s.match_length = T - 1, M !== 0 && s.prev_length < s.max_lazy_match && s.strstart - M <= s.w_size - Q && (s.match_length = G(s, M), s.match_length <= 5 && (s.strategy === 1 || s.match_length === T && 4096 < s.strstart - s.match_start) && (s.match_length = T - 1)), s.prev_length >= T && s.match_length <= s.prev_length) {
                  for (p = s.strstart + s.lookahead - T, g = o._tr_tally(s, s.strstart - 1 - s.prev_match, s.prev_length - T), s.lookahead -= s.prev_length - 1, s.prev_length -= 2; ++s.strstart <= p && (s.ins_h = (s.ins_h << s.hash_shift ^ s.window[s.strstart + T - 1]) & s.hash_mask, M = s.prev[s.strstart & s.w_mask] = s.head[s.ins_h], s.head[s.ins_h] = s.strstart), --s.prev_length != 0; ) ;
                  if (s.match_available = 0, s.match_length = T - 1, s.strstart++, g && (B(s, false), s.strm.avail_out === 0)) return c;
                } else if (s.match_available) {
                  if ((g = o._tr_tally(s, 0, s.window[s.strstart - 1])) && B(s, false), s.strstart++, s.lookahead--, s.strm.avail_out === 0) return c;
                } else s.match_available = 1, s.strstart++, s.lookahead--;
              }
              return s.match_available && (g = o._tr_tally(s, 0, s.window[s.strstart - 1]), s.match_available = 0), s.insert = s.strstart < T - 1 ? s.strstart : T - 1, F === w ? (B(s, true), s.strm.avail_out === 0 ? et : $) : s.last_lit && (B(s, false), s.strm.avail_out === 0) ? c : L;
            }
            function it(s, F, M, g, p) {
              this.good_length = s, this.max_lazy = F, this.nice_length = M, this.max_chain = g, this.func = p;
            }
            function kt() {
              this.strm = null, this.status = 0, this.pending_buf = null, this.pending_buf_size = 0, this.pending_out = 0, this.pending = 0, this.wrap = 0, this.gzhead = null, this.gzindex = 0, this.method = v, this.last_flush = -1, this.w_size = 0, this.w_bits = 0, this.w_mask = 0, this.window = null, this.window_size = 0, this.prev = null, this.head = null, this.ins_h = 0, this.hash_size = 0, this.hash_bits = 0, this.hash_mask = 0, this.hash_shift = 0, this.block_start = 0, this.match_length = 0, this.prev_match = 0, this.match_available = 0, this.strstart = 0, this.match_start = 0, this.lookahead = 0, this.prev_length = 0, this.max_chain_length = 0, this.max_lazy_match = 0, this.level = 0, this.strategy = 0, this.good_match = 0, this.nice_match = 0, this.dyn_ltree = new l.Buf16(2 * I), this.dyn_dtree = new l.Buf16(2 * (2 * E + 1)), this.bl_tree = new l.Buf16(2 * (2 * D + 1)), tt(this.dyn_ltree), tt(this.dyn_dtree), tt(this.bl_tree), this.l_desc = null, this.d_desc = null, this.bl_desc = null, this.bl_count = new l.Buf16(W + 1), this.heap = new l.Buf16(2 * A + 1), tt(this.heap), this.heap_len = 0, this.heap_max = 0, this.depth = new l.Buf16(2 * A + 1), tt(this.depth), this.l_buf = 0, this.lit_bufsize = 0, this.last_lit = 0, this.d_buf = 0, this.opt_len = 0, this.static_len = 0, this.matches = 0, this.insert = 0, this.bi_buf = 0, this.bi_valid = 0;
            }
            function gt(s) {
              var F;
              return s && s.state ? (s.total_in = s.total_out = 0, s.data_type = f, (F = s.state).pending = 0, F.pending_out = 0, F.wrap < 0 && (F.wrap = -F.wrap), F.status = F.wrap ? S : O, s.adler = F.wrap === 2 ? 0 : 1, F.last_flush = _, o._tr_init(F), d) : rt(s, k);
            }
            function Pt(s) {
              var F = gt(s);
              return F === d && function(M) {
                M.window_size = 2 * M.w_size, tt(M.head), M.max_lazy_match = a[M.level].max_lazy, M.good_match = a[M.level].good_length, M.nice_match = a[M.level].nice_length, M.max_chain_length = a[M.level].max_chain, M.strstart = 0, M.block_start = 0, M.lookahead = 0, M.insert = 0, M.match_length = M.prev_length = T - 1, M.match_available = 0, M.ins_h = 0;
              }(s.state), F;
            }
            function xt(s, F, M, g, p, x) {
              if (!s) return k;
              var j = 1;
              if (F === h && (F = 6), g < 0 ? (j = 0, g = -g) : 15 < g && (j = 2, g -= 16), p < 1 || C < p || M !== v || g < 8 || 15 < g || F < 0 || 9 < F || x < 0 || m < x) return rt(s, k);
              g === 8 && (g = 9);
              var U = new kt();
              return (s.state = U).strm = s, U.wrap = j, U.gzhead = null, U.w_bits = g, U.w_size = 1 << U.w_bits, U.w_mask = U.w_size - 1, U.hash_bits = p + 7, U.hash_size = 1 << U.hash_bits, U.hash_mask = U.hash_size - 1, U.hash_shift = ~~((U.hash_bits + T - 1) / T), U.window = new l.Buf8(2 * U.w_size), U.head = new l.Buf16(U.hash_size), U.prev = new l.Buf16(U.w_size), U.lit_bufsize = 1 << p + 6, U.pending_buf_size = 4 * U.lit_bufsize, U.pending_buf = new l.Buf8(U.pending_buf_size), U.d_buf = 1 * U.lit_bufsize, U.l_buf = 3 * U.lit_bufsize, U.level = F, U.strategy = x, U.method = M, Pt(s);
            }
            a = [new it(0, 0, 0, 0, function(s, F) {
              var M = 65535;
              for (M > s.pending_buf_size - 5 && (M = s.pending_buf_size - 5); ; ) {
                if (s.lookahead <= 1) {
                  if (mt(s), s.lookahead === 0 && F === _) return c;
                  if (s.lookahead === 0) break;
                }
                s.strstart += s.lookahead, s.lookahead = 0;
                var g = s.block_start + M;
                if ((s.strstart === 0 || s.strstart >= g) && (s.lookahead = s.strstart - g, s.strstart = g, B(s, false), s.strm.avail_out === 0) || s.strstart - s.block_start >= s.w_size - Q && (B(s, false), s.strm.avail_out === 0)) return c;
              }
              return s.insert = 0, F === w ? (B(s, true), s.strm.avail_out === 0 ? et : $) : (s.strstart > s.block_start && (B(s, false), s.strm.avail_out), c);
            }), new it(4, 4, 8, 4, yt), new it(4, 5, 16, 8, yt), new it(4, 6, 32, 32, yt), new it(4, 4, 16, 16, at), new it(8, 16, 32, 32, at), new it(8, 16, 128, 128, at), new it(8, 32, 128, 256, at), new it(32, 128, 258, 1024, at), new it(32, 258, 258, 4096, at)], n.deflateInit = function(s, F) {
              return xt(s, F, v, 15, 8, 0);
            }, n.deflateInit2 = xt, n.deflateReset = Pt, n.deflateResetKeep = gt, n.deflateSetHeader = function(s, F) {
              return s && s.state ? s.state.wrap !== 2 ? k : (s.state.gzhead = F, d) : k;
            }, n.deflate = function(s, F) {
              var M, g, p, x;
              if (!s || !s.state || 5 < F || F < 0) return s ? rt(s, k) : k;
              if (g = s.state, !s.output || !s.input && s.avail_in !== 0 || g.status === 666 && F !== w) return rt(s, s.avail_out === 0 ? -5 : k);
              if (g.strm = s, M = g.last_flush, g.last_flush = F, g.status === S) if (g.wrap === 2) s.adler = 0, J(g, 31), J(g, 139), J(g, 8), g.gzhead ? (J(g, (g.gzhead.text ? 1 : 0) + (g.gzhead.hcrc ? 2 : 0) + (g.gzhead.extra ? 4 : 0) + (g.gzhead.name ? 8 : 0) + (g.gzhead.comment ? 16 : 0)), J(g, 255 & g.gzhead.time), J(g, g.gzhead.time >> 8 & 255), J(g, g.gzhead.time >> 16 & 255), J(g, g.gzhead.time >> 24 & 255), J(g, g.level === 9 ? 2 : 2 <= g.strategy || g.level < 2 ? 4 : 0), J(g, 255 & g.gzhead.os), g.gzhead.extra && g.gzhead.extra.length && (J(g, 255 & g.gzhead.extra.length), J(g, g.gzhead.extra.length >> 8 & 255)), g.gzhead.hcrc && (s.adler = b(s.adler, g.pending_buf, g.pending, 0)), g.gzindex = 0, g.status = 69) : (J(g, 0), J(g, 0), J(g, 0), J(g, 0), J(g, 0), J(g, g.level === 9 ? 2 : 2 <= g.strategy || g.level < 2 ? 4 : 0), J(g, 3), g.status = O);
              else {
                var j = v + (g.w_bits - 8 << 4) << 8;
                j |= (2 <= g.strategy || g.level < 2 ? 0 : g.level < 6 ? 1 : g.level === 6 ? 2 : 3) << 6, g.strstart !== 0 && (j |= 32), j += 31 - j % 31, g.status = O, q(g, j), g.strstart !== 0 && (q(g, s.adler >>> 16), q(g, 65535 & s.adler)), s.adler = 1;
              }
              if (g.status === 69) if (g.gzhead.extra) {
                for (p = g.pending; g.gzindex < (65535 & g.gzhead.extra.length) && (g.pending !== g.pending_buf_size || (g.gzhead.hcrc && g.pending > p && (s.adler = b(s.adler, g.pending_buf, g.pending - p, p)), R(s), p = g.pending, g.pending !== g.pending_buf_size)); ) J(g, 255 & g.gzhead.extra[g.gzindex]), g.gzindex++;
                g.gzhead.hcrc && g.pending > p && (s.adler = b(s.adler, g.pending_buf, g.pending - p, p)), g.gzindex === g.gzhead.extra.length && (g.gzindex = 0, g.status = 73);
              } else g.status = 73;
              if (g.status === 73) if (g.gzhead.name) {
                p = g.pending;
                do {
                  if (g.pending === g.pending_buf_size && (g.gzhead.hcrc && g.pending > p && (s.adler = b(s.adler, g.pending_buf, g.pending - p, p)), R(s), p = g.pending, g.pending === g.pending_buf_size)) {
                    x = 1;
                    break;
                  }
                  x = g.gzindex < g.gzhead.name.length ? 255 & g.gzhead.name.charCodeAt(g.gzindex++) : 0, J(g, x);
                } while (x !== 0);
                g.gzhead.hcrc && g.pending > p && (s.adler = b(s.adler, g.pending_buf, g.pending - p, p)), x === 0 && (g.gzindex = 0, g.status = 91);
              } else g.status = 91;
              if (g.status === 91) if (g.gzhead.comment) {
                p = g.pending;
                do {
                  if (g.pending === g.pending_buf_size && (g.gzhead.hcrc && g.pending > p && (s.adler = b(s.adler, g.pending_buf, g.pending - p, p)), R(s), p = g.pending, g.pending === g.pending_buf_size)) {
                    x = 1;
                    break;
                  }
                  x = g.gzindex < g.gzhead.comment.length ? 255 & g.gzhead.comment.charCodeAt(g.gzindex++) : 0, J(g, x);
                } while (x !== 0);
                g.gzhead.hcrc && g.pending > p && (s.adler = b(s.adler, g.pending_buf, g.pending - p, p)), x === 0 && (g.status = 103);
              } else g.status = 103;
              if (g.status === 103 && (g.gzhead.hcrc ? (g.pending + 2 > g.pending_buf_size && R(s), g.pending + 2 <= g.pending_buf_size && (J(g, 255 & s.adler), J(g, s.adler >> 8 & 255), s.adler = 0, g.status = O)) : g.status = O), g.pending !== 0) {
                if (R(s), s.avail_out === 0) return g.last_flush = -1, d;
              } else if (s.avail_in === 0 && H(F) <= H(M) && F !== w) return rt(s, -5);
              if (g.status === 666 && s.avail_in !== 0) return rt(s, -5);
              if (s.avail_in !== 0 || g.lookahead !== 0 || F !== _ && g.status !== 666) {
                var U = g.strategy === 2 ? function(N, Z) {
                  for (var K; ; ) {
                    if (N.lookahead === 0 && (mt(N), N.lookahead === 0)) {
                      if (Z === _) return c;
                      break;
                    }
                    if (N.match_length = 0, K = o._tr_tally(N, 0, N.window[N.strstart]), N.lookahead--, N.strstart++, K && (B(N, false), N.strm.avail_out === 0)) return c;
                  }
                  return N.insert = 0, Z === w ? (B(N, true), N.strm.avail_out === 0 ? et : $) : N.last_lit && (B(N, false), N.strm.avail_out === 0) ? c : L;
                }(g, F) : g.strategy === 3 ? function(N, Z) {
                  for (var K, V, Y, lt, st = N.window; ; ) {
                    if (N.lookahead <= X) {
                      if (mt(N), N.lookahead <= X && Z === _) return c;
                      if (N.lookahead === 0) break;
                    }
                    if (N.match_length = 0, N.lookahead >= T && 0 < N.strstart && (V = st[Y = N.strstart - 1]) === st[++Y] && V === st[++Y] && V === st[++Y]) {
                      lt = N.strstart + X;
                      do
                        ;
                      while (V === st[++Y] && V === st[++Y] && V === st[++Y] && V === st[++Y] && V === st[++Y] && V === st[++Y] && V === st[++Y] && V === st[++Y] && Y < lt);
                      N.match_length = X - (lt - Y), N.match_length > N.lookahead && (N.match_length = N.lookahead);
                    }
                    if (N.match_length >= T ? (K = o._tr_tally(N, 1, N.match_length - T), N.lookahead -= N.match_length, N.strstart += N.match_length, N.match_length = 0) : (K = o._tr_tally(N, 0, N.window[N.strstart]), N.lookahead--, N.strstart++), K && (B(N, false), N.strm.avail_out === 0)) return c;
                  }
                  return N.insert = 0, Z === w ? (B(N, true), N.strm.avail_out === 0 ? et : $) : N.last_lit && (B(N, false), N.strm.avail_out === 0) ? c : L;
                }(g, F) : a[g.level].func(g, F);
                if (U !== et && U !== $ || (g.status = 666), U === c || U === et) return s.avail_out === 0 && (g.last_flush = -1), d;
                if (U === L && (F === 1 ? o._tr_align(g) : F !== 5 && (o._tr_stored_block(g, 0, 0, false), F === 3 && (tt(g.head), g.lookahead === 0 && (g.strstart = 0, g.block_start = 0, g.insert = 0))), R(s), s.avail_out === 0)) return g.last_flush = -1, d;
              }
              return F !== w ? d : g.wrap <= 0 ? 1 : (g.wrap === 2 ? (J(g, 255 & s.adler), J(g, s.adler >> 8 & 255), J(g, s.adler >> 16 & 255), J(g, s.adler >> 24 & 255), J(g, 255 & s.total_in), J(g, s.total_in >> 8 & 255), J(g, s.total_in >> 16 & 255), J(g, s.total_in >> 24 & 255)) : (q(g, s.adler >>> 16), q(g, 65535 & s.adler)), R(s), 0 < g.wrap && (g.wrap = -g.wrap), g.pending !== 0 ? d : 1);
            }, n.deflateEnd = function(s) {
              var F;
              return s && s.state ? (F = s.state.status) !== S && F !== 69 && F !== 73 && F !== 91 && F !== 103 && F !== O && F !== 666 ? rt(s, k) : (s.state = null, F === O ? rt(s, -3) : d) : k;
            }, n.deflateSetDictionary = function(s, F) {
              var M, g, p, x, j, U, N, Z, K = F.length;
              if (!s || !s.state || (x = (M = s.state).wrap) === 2 || x === 1 && M.status !== S || M.lookahead) return k;
              for (x === 1 && (s.adler = u(s.adler, F, K, 0)), M.wrap = 0, K >= M.w_size && (x === 0 && (tt(M.head), M.strstart = 0, M.block_start = 0, M.insert = 0), Z = new l.Buf8(M.w_size), l.arraySet(Z, F, K - M.w_size, M.w_size, 0), F = Z, K = M.w_size), j = s.avail_in, U = s.next_in, N = s.input, s.avail_in = K, s.next_in = 0, s.input = F, mt(M); M.lookahead >= T; ) {
                for (g = M.strstart, p = M.lookahead - (T - 1); M.ins_h = (M.ins_h << M.hash_shift ^ M.window[g + T - 1]) & M.hash_mask, M.prev[g & M.w_mask] = M.head[M.ins_h], M.head[M.ins_h] = g, g++, --p; ) ;
                M.strstart = g, M.lookahead = T - 1, mt(M);
              }
              return M.strstart += M.lookahead, M.block_start = M.strstart, M.insert = M.lookahead, M.lookahead = 0, M.match_length = M.prev_length = T - 1, M.match_available = 0, s.next_in = U, s.input = N, s.avail_in = j, M.wrap = x, d;
            }, n.deflateInfo = "pako deflate (from Nodeca project)";
          }, { "../utils/common": 41, "./adler32": 43, "./crc32": 45, "./messages": 51, "./trees": 52 }], 47: [function(t, r, n) {
            r.exports = function() {
              this.text = 0, this.time = 0, this.xflags = 0, this.os = 0, this.extra = null, this.extra_len = 0, this.name = "", this.comment = "", this.hcrc = 0, this.done = false;
            };
          }, {}], 48: [function(t, r, n) {
            r.exports = function(a, l) {
              var o, u, b, y, _, w, d, k, h, m, f, v, C, A, E, D, I, W, T, X, Q, S, O, c, L;
              o = a.state, u = a.next_in, c = a.input, b = u + (a.avail_in - 5), y = a.next_out, L = a.output, _ = y - (l - a.avail_out), w = y + (a.avail_out - 257), d = o.dmax, k = o.wsize, h = o.whave, m = o.wnext, f = o.window, v = o.hold, C = o.bits, A = o.lencode, E = o.distcode, D = (1 << o.lenbits) - 1, I = (1 << o.distbits) - 1;
              t: do {
                C < 15 && (v += c[u++] << C, C += 8, v += c[u++] << C, C += 8), W = A[v & D];
                e: for (; ; ) {
                  if (v >>>= T = W >>> 24, C -= T, (T = W >>> 16 & 255) === 0) L[y++] = 65535 & W;
                  else {
                    if (!(16 & T)) {
                      if (!(64 & T)) {
                        W = A[(65535 & W) + (v & (1 << T) - 1)];
                        continue e;
                      }
                      if (32 & T) {
                        o.mode = 12;
                        break t;
                      }
                      a.msg = "invalid literal/length code", o.mode = 30;
                      break t;
                    }
                    X = 65535 & W, (T &= 15) && (C < T && (v += c[u++] << C, C += 8), X += v & (1 << T) - 1, v >>>= T, C -= T), C < 15 && (v += c[u++] << C, C += 8, v += c[u++] << C, C += 8), W = E[v & I];
                    r: for (; ; ) {
                      if (v >>>= T = W >>> 24, C -= T, !(16 & (T = W >>> 16 & 255))) {
                        if (!(64 & T)) {
                          W = E[(65535 & W) + (v & (1 << T) - 1)];
                          continue r;
                        }
                        a.msg = "invalid distance code", o.mode = 30;
                        break t;
                      }
                      if (Q = 65535 & W, C < (T &= 15) && (v += c[u++] << C, (C += 8) < T && (v += c[u++] << C, C += 8)), d < (Q += v & (1 << T) - 1)) {
                        a.msg = "invalid distance too far back", o.mode = 30;
                        break t;
                      }
                      if (v >>>= T, C -= T, (T = y - _) < Q) {
                        if (h < (T = Q - T) && o.sane) {
                          a.msg = "invalid distance too far back", o.mode = 30;
                          break t;
                        }
                        if (O = f, (S = 0) === m) {
                          if (S += k - T, T < X) {
                            for (X -= T; L[y++] = f[S++], --T; ) ;
                            S = y - Q, O = L;
                          }
                        } else if (m < T) {
                          if (S += k + m - T, (T -= m) < X) {
                            for (X -= T; L[y++] = f[S++], --T; ) ;
                            if (S = 0, m < X) {
                              for (X -= T = m; L[y++] = f[S++], --T; ) ;
                              S = y - Q, O = L;
                            }
                          }
                        } else if (S += m - T, T < X) {
                          for (X -= T; L[y++] = f[S++], --T; ) ;
                          S = y - Q, O = L;
                        }
                        for (; 2 < X; ) L[y++] = O[S++], L[y++] = O[S++], L[y++] = O[S++], X -= 3;
                        X && (L[y++] = O[S++], 1 < X && (L[y++] = O[S++]));
                      } else {
                        for (S = y - Q; L[y++] = L[S++], L[y++] = L[S++], L[y++] = L[S++], 2 < (X -= 3); ) ;
                        X && (L[y++] = L[S++], 1 < X && (L[y++] = L[S++]));
                      }
                      break;
                    }
                  }
                  break;
                }
              } while (u < b && y < w);
              u -= X = C >> 3, v &= (1 << (C -= X << 3)) - 1, a.next_in = u, a.next_out = y, a.avail_in = u < b ? b - u + 5 : 5 - (u - b), a.avail_out = y < w ? w - y + 257 : 257 - (y - w), o.hold = v, o.bits = C;
            };
          }, {}], 49: [function(t, r, n) {
            var a = t("../utils/common"), l = t("./adler32"), o = t("./crc32"), u = t("./inffast"), b = t("./inftrees"), y = 1, _ = 2, w = 0, d = -2, k = 1, h = 852, m = 592;
            function f(S) {
              return (S >>> 24 & 255) + (S >>> 8 & 65280) + ((65280 & S) << 8) + ((255 & S) << 24);
            }
            function v() {
              this.mode = 0, this.last = false, this.wrap = 0, this.havedict = false, this.flags = 0, this.dmax = 0, this.check = 0, this.total = 0, this.head = null, this.wbits = 0, this.wsize = 0, this.whave = 0, this.wnext = 0, this.window = null, this.hold = 0, this.bits = 0, this.length = 0, this.offset = 0, this.extra = 0, this.lencode = null, this.distcode = null, this.lenbits = 0, this.distbits = 0, this.ncode = 0, this.nlen = 0, this.ndist = 0, this.have = 0, this.next = null, this.lens = new a.Buf16(320), this.work = new a.Buf16(288), this.lendyn = null, this.distdyn = null, this.sane = 0, this.back = 0, this.was = 0;
            }
            function C(S) {
              var O;
              return S && S.state ? (O = S.state, S.total_in = S.total_out = O.total = 0, S.msg = "", O.wrap && (S.adler = 1 & O.wrap), O.mode = k, O.last = 0, O.havedict = 0, O.dmax = 32768, O.head = null, O.hold = 0, O.bits = 0, O.lencode = O.lendyn = new a.Buf32(h), O.distcode = O.distdyn = new a.Buf32(m), O.sane = 1, O.back = -1, w) : d;
            }
            function A(S) {
              var O;
              return S && S.state ? ((O = S.state).wsize = 0, O.whave = 0, O.wnext = 0, C(S)) : d;
            }
            function E(S, O) {
              var c, L;
              return S && S.state ? (L = S.state, O < 0 ? (c = 0, O = -O) : (c = 1 + (O >> 4), O < 48 && (O &= 15)), O && (O < 8 || 15 < O) ? d : (L.window !== null && L.wbits !== O && (L.window = null), L.wrap = c, L.wbits = O, A(S))) : d;
            }
            function D(S, O) {
              var c, L;
              return S ? (L = new v(), (S.state = L).window = null, (c = E(S, O)) !== w && (S.state = null), c) : d;
            }
            var I, W, T = true;
            function X(S) {
              if (T) {
                var O;
                for (I = new a.Buf32(512), W = new a.Buf32(32), O = 0; O < 144; ) S.lens[O++] = 8;
                for (; O < 256; ) S.lens[O++] = 9;
                for (; O < 280; ) S.lens[O++] = 7;
                for (; O < 288; ) S.lens[O++] = 8;
                for (b(y, S.lens, 0, 288, I, 0, S.work, { bits: 9 }), O = 0; O < 32; ) S.lens[O++] = 5;
                b(_, S.lens, 0, 32, W, 0, S.work, { bits: 5 }), T = false;
              }
              S.lencode = I, S.lenbits = 9, S.distcode = W, S.distbits = 5;
            }
            function Q(S, O, c, L) {
              var et, $ = S.state;
              return $.window === null && ($.wsize = 1 << $.wbits, $.wnext = 0, $.whave = 0, $.window = new a.Buf8($.wsize)), L >= $.wsize ? (a.arraySet($.window, O, c - $.wsize, $.wsize, 0), $.wnext = 0, $.whave = $.wsize) : (L < (et = $.wsize - $.wnext) && (et = L), a.arraySet($.window, O, c - L, et, $.wnext), (L -= et) ? (a.arraySet($.window, O, c - L, L, 0), $.wnext = L, $.whave = $.wsize) : ($.wnext += et, $.wnext === $.wsize && ($.wnext = 0), $.whave < $.wsize && ($.whave += et))), 0;
            }
            n.inflateReset = A, n.inflateReset2 = E, n.inflateResetKeep = C, n.inflateInit = function(S) {
              return D(S, 15);
            }, n.inflateInit2 = D, n.inflate = function(S, O) {
              var c, L, et, $, rt, H, tt, R, B, J, q, G, mt, yt, at, it, kt, gt, Pt, xt, s, F, M, g, p = 0, x = new a.Buf8(4), j = [16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15];
              if (!S || !S.state || !S.output || !S.input && S.avail_in !== 0) return d;
              (c = S.state).mode === 12 && (c.mode = 13), rt = S.next_out, et = S.output, tt = S.avail_out, $ = S.next_in, L = S.input, H = S.avail_in, R = c.hold, B = c.bits, J = H, q = tt, F = w;
              t: for (; ; ) switch (c.mode) {
                case k:
                  if (c.wrap === 0) {
                    c.mode = 13;
                    break;
                  }
                  for (; B < 16; ) {
                    if (H === 0) break t;
                    H--, R += L[$++] << B, B += 8;
                  }
                  if (2 & c.wrap && R === 35615) {
                    x[c.check = 0] = 255 & R, x[1] = R >>> 8 & 255, c.check = o(c.check, x, 2, 0), B = R = 0, c.mode = 2;
                    break;
                  }
                  if (c.flags = 0, c.head && (c.head.done = false), !(1 & c.wrap) || (((255 & R) << 8) + (R >> 8)) % 31) {
                    S.msg = "incorrect header check", c.mode = 30;
                    break;
                  }
                  if ((15 & R) != 8) {
                    S.msg = "unknown compression method", c.mode = 30;
                    break;
                  }
                  if (B -= 4, s = 8 + (15 & (R >>>= 4)), c.wbits === 0) c.wbits = s;
                  else if (s > c.wbits) {
                    S.msg = "invalid window size", c.mode = 30;
                    break;
                  }
                  c.dmax = 1 << s, S.adler = c.check = 1, c.mode = 512 & R ? 10 : 12, B = R = 0;
                  break;
                case 2:
                  for (; B < 16; ) {
                    if (H === 0) break t;
                    H--, R += L[$++] << B, B += 8;
                  }
                  if (c.flags = R, (255 & c.flags) != 8) {
                    S.msg = "unknown compression method", c.mode = 30;
                    break;
                  }
                  if (57344 & c.flags) {
                    S.msg = "unknown header flags set", c.mode = 30;
                    break;
                  }
                  c.head && (c.head.text = R >> 8 & 1), 512 & c.flags && (x[0] = 255 & R, x[1] = R >>> 8 & 255, c.check = o(c.check, x, 2, 0)), B = R = 0, c.mode = 3;
                case 3:
                  for (; B < 32; ) {
                    if (H === 0) break t;
                    H--, R += L[$++] << B, B += 8;
                  }
                  c.head && (c.head.time = R), 512 & c.flags && (x[0] = 255 & R, x[1] = R >>> 8 & 255, x[2] = R >>> 16 & 255, x[3] = R >>> 24 & 255, c.check = o(c.check, x, 4, 0)), B = R = 0, c.mode = 4;
                case 4:
                  for (; B < 16; ) {
                    if (H === 0) break t;
                    H--, R += L[$++] << B, B += 8;
                  }
                  c.head && (c.head.xflags = 255 & R, c.head.os = R >> 8), 512 & c.flags && (x[0] = 255 & R, x[1] = R >>> 8 & 255, c.check = o(c.check, x, 2, 0)), B = R = 0, c.mode = 5;
                case 5:
                  if (1024 & c.flags) {
                    for (; B < 16; ) {
                      if (H === 0) break t;
                      H--, R += L[$++] << B, B += 8;
                    }
                    c.length = R, c.head && (c.head.extra_len = R), 512 & c.flags && (x[0] = 255 & R, x[1] = R >>> 8 & 255, c.check = o(c.check, x, 2, 0)), B = R = 0;
                  } else c.head && (c.head.extra = null);
                  c.mode = 6;
                case 6:
                  if (1024 & c.flags && (H < (G = c.length) && (G = H), G && (c.head && (s = c.head.extra_len - c.length, c.head.extra || (c.head.extra = new Array(c.head.extra_len)), a.arraySet(c.head.extra, L, $, G, s)), 512 & c.flags && (c.check = o(c.check, L, G, $)), H -= G, $ += G, c.length -= G), c.length)) break t;
                  c.length = 0, c.mode = 7;
                case 7:
                  if (2048 & c.flags) {
                    if (H === 0) break t;
                    for (G = 0; s = L[$ + G++], c.head && s && c.length < 65536 && (c.head.name += String.fromCharCode(s)), s && G < H; ) ;
                    if (512 & c.flags && (c.check = o(c.check, L, G, $)), H -= G, $ += G, s) break t;
                  } else c.head && (c.head.name = null);
                  c.length = 0, c.mode = 8;
                case 8:
                  if (4096 & c.flags) {
                    if (H === 0) break t;
                    for (G = 0; s = L[$ + G++], c.head && s && c.length < 65536 && (c.head.comment += String.fromCharCode(s)), s && G < H; ) ;
                    if (512 & c.flags && (c.check = o(c.check, L, G, $)), H -= G, $ += G, s) break t;
                  } else c.head && (c.head.comment = null);
                  c.mode = 9;
                case 9:
                  if (512 & c.flags) {
                    for (; B < 16; ) {
                      if (H === 0) break t;
                      H--, R += L[$++] << B, B += 8;
                    }
                    if (R !== (65535 & c.check)) {
                      S.msg = "header crc mismatch", c.mode = 30;
                      break;
                    }
                    B = R = 0;
                  }
                  c.head && (c.head.hcrc = c.flags >> 9 & 1, c.head.done = true), S.adler = c.check = 0, c.mode = 12;
                  break;
                case 10:
                  for (; B < 32; ) {
                    if (H === 0) break t;
                    H--, R += L[$++] << B, B += 8;
                  }
                  S.adler = c.check = f(R), B = R = 0, c.mode = 11;
                case 11:
                  if (c.havedict === 0) return S.next_out = rt, S.avail_out = tt, S.next_in = $, S.avail_in = H, c.hold = R, c.bits = B, 2;
                  S.adler = c.check = 1, c.mode = 12;
                case 12:
                  if (O === 5 || O === 6) break t;
                case 13:
                  if (c.last) {
                    R >>>= 7 & B, B -= 7 & B, c.mode = 27;
                    break;
                  }
                  for (; B < 3; ) {
                    if (H === 0) break t;
                    H--, R += L[$++] << B, B += 8;
                  }
                  switch (c.last = 1 & R, B -= 1, 3 & (R >>>= 1)) {
                    case 0:
                      c.mode = 14;
                      break;
                    case 1:
                      if (X(c), c.mode = 20, O !== 6) break;
                      R >>>= 2, B -= 2;
                      break t;
                    case 2:
                      c.mode = 17;
                      break;
                    case 3:
                      S.msg = "invalid block type", c.mode = 30;
                  }
                  R >>>= 2, B -= 2;
                  break;
                case 14:
                  for (R >>>= 7 & B, B -= 7 & B; B < 32; ) {
                    if (H === 0) break t;
                    H--, R += L[$++] << B, B += 8;
                  }
                  if ((65535 & R) != (R >>> 16 ^ 65535)) {
                    S.msg = "invalid stored block lengths", c.mode = 30;
                    break;
                  }
                  if (c.length = 65535 & R, B = R = 0, c.mode = 15, O === 6) break t;
                case 15:
                  c.mode = 16;
                case 16:
                  if (G = c.length) {
                    if (H < G && (G = H), tt < G && (G = tt), G === 0) break t;
                    a.arraySet(et, L, $, G, rt), H -= G, $ += G, tt -= G, rt += G, c.length -= G;
                    break;
                  }
                  c.mode = 12;
                  break;
                case 17:
                  for (; B < 14; ) {
                    if (H === 0) break t;
                    H--, R += L[$++] << B, B += 8;
                  }
                  if (c.nlen = 257 + (31 & R), R >>>= 5, B -= 5, c.ndist = 1 + (31 & R), R >>>= 5, B -= 5, c.ncode = 4 + (15 & R), R >>>= 4, B -= 4, 286 < c.nlen || 30 < c.ndist) {
                    S.msg = "too many length or distance symbols", c.mode = 30;
                    break;
                  }
                  c.have = 0, c.mode = 18;
                case 18:
                  for (; c.have < c.ncode; ) {
                    for (; B < 3; ) {
                      if (H === 0) break t;
                      H--, R += L[$++] << B, B += 8;
                    }
                    c.lens[j[c.have++]] = 7 & R, R >>>= 3, B -= 3;
                  }
                  for (; c.have < 19; ) c.lens[j[c.have++]] = 0;
                  if (c.lencode = c.lendyn, c.lenbits = 7, M = { bits: c.lenbits }, F = b(0, c.lens, 0, 19, c.lencode, 0, c.work, M), c.lenbits = M.bits, F) {
                    S.msg = "invalid code lengths set", c.mode = 30;
                    break;
                  }
                  c.have = 0, c.mode = 19;
                case 19:
                  for (; c.have < c.nlen + c.ndist; ) {
                    for (; it = (p = c.lencode[R & (1 << c.lenbits) - 1]) >>> 16 & 255, kt = 65535 & p, !((at = p >>> 24) <= B); ) {
                      if (H === 0) break t;
                      H--, R += L[$++] << B, B += 8;
                    }
                    if (kt < 16) R >>>= at, B -= at, c.lens[c.have++] = kt;
                    else {
                      if (kt === 16) {
                        for (g = at + 2; B < g; ) {
                          if (H === 0) break t;
                          H--, R += L[$++] << B, B += 8;
                        }
                        if (R >>>= at, B -= at, c.have === 0) {
                          S.msg = "invalid bit length repeat", c.mode = 30;
                          break;
                        }
                        s = c.lens[c.have - 1], G = 3 + (3 & R), R >>>= 2, B -= 2;
                      } else if (kt === 17) {
                        for (g = at + 3; B < g; ) {
                          if (H === 0) break t;
                          H--, R += L[$++] << B, B += 8;
                        }
                        B -= at, s = 0, G = 3 + (7 & (R >>>= at)), R >>>= 3, B -= 3;
                      } else {
                        for (g = at + 7; B < g; ) {
                          if (H === 0) break t;
                          H--, R += L[$++] << B, B += 8;
                        }
                        B -= at, s = 0, G = 11 + (127 & (R >>>= at)), R >>>= 7, B -= 7;
                      }
                      if (c.have + G > c.nlen + c.ndist) {
                        S.msg = "invalid bit length repeat", c.mode = 30;
                        break;
                      }
                      for (; G--; ) c.lens[c.have++] = s;
                    }
                  }
                  if (c.mode === 30) break;
                  if (c.lens[256] === 0) {
                    S.msg = "invalid code -- missing end-of-block", c.mode = 30;
                    break;
                  }
                  if (c.lenbits = 9, M = { bits: c.lenbits }, F = b(y, c.lens, 0, c.nlen, c.lencode, 0, c.work, M), c.lenbits = M.bits, F) {
                    S.msg = "invalid literal/lengths set", c.mode = 30;
                    break;
                  }
                  if (c.distbits = 6, c.distcode = c.distdyn, M = { bits: c.distbits }, F = b(_, c.lens, c.nlen, c.ndist, c.distcode, 0, c.work, M), c.distbits = M.bits, F) {
                    S.msg = "invalid distances set", c.mode = 30;
                    break;
                  }
                  if (c.mode = 20, O === 6) break t;
                case 20:
                  c.mode = 21;
                case 21:
                  if (6 <= H && 258 <= tt) {
                    S.next_out = rt, S.avail_out = tt, S.next_in = $, S.avail_in = H, c.hold = R, c.bits = B, u(S, q), rt = S.next_out, et = S.output, tt = S.avail_out, $ = S.next_in, L = S.input, H = S.avail_in, R = c.hold, B = c.bits, c.mode === 12 && (c.back = -1);
                    break;
                  }
                  for (c.back = 0; it = (p = c.lencode[R & (1 << c.lenbits) - 1]) >>> 16 & 255, kt = 65535 & p, !((at = p >>> 24) <= B); ) {
                    if (H === 0) break t;
                    H--, R += L[$++] << B, B += 8;
                  }
                  if (it && !(240 & it)) {
                    for (gt = at, Pt = it, xt = kt; it = (p = c.lencode[xt + ((R & (1 << gt + Pt) - 1) >> gt)]) >>> 16 & 255, kt = 65535 & p, !(gt + (at = p >>> 24) <= B); ) {
                      if (H === 0) break t;
                      H--, R += L[$++] << B, B += 8;
                    }
                    R >>>= gt, B -= gt, c.back += gt;
                  }
                  if (R >>>= at, B -= at, c.back += at, c.length = kt, it === 0) {
                    c.mode = 26;
                    break;
                  }
                  if (32 & it) {
                    c.back = -1, c.mode = 12;
                    break;
                  }
                  if (64 & it) {
                    S.msg = "invalid literal/length code", c.mode = 30;
                    break;
                  }
                  c.extra = 15 & it, c.mode = 22;
                case 22:
                  if (c.extra) {
                    for (g = c.extra; B < g; ) {
                      if (H === 0) break t;
                      H--, R += L[$++] << B, B += 8;
                    }
                    c.length += R & (1 << c.extra) - 1, R >>>= c.extra, B -= c.extra, c.back += c.extra;
                  }
                  c.was = c.length, c.mode = 23;
                case 23:
                  for (; it = (p = c.distcode[R & (1 << c.distbits) - 1]) >>> 16 & 255, kt = 65535 & p, !((at = p >>> 24) <= B); ) {
                    if (H === 0) break t;
                    H--, R += L[$++] << B, B += 8;
                  }
                  if (!(240 & it)) {
                    for (gt = at, Pt = it, xt = kt; it = (p = c.distcode[xt + ((R & (1 << gt + Pt) - 1) >> gt)]) >>> 16 & 255, kt = 65535 & p, !(gt + (at = p >>> 24) <= B); ) {
                      if (H === 0) break t;
                      H--, R += L[$++] << B, B += 8;
                    }
                    R >>>= gt, B -= gt, c.back += gt;
                  }
                  if (R >>>= at, B -= at, c.back += at, 64 & it) {
                    S.msg = "invalid distance code", c.mode = 30;
                    break;
                  }
                  c.offset = kt, c.extra = 15 & it, c.mode = 24;
                case 24:
                  if (c.extra) {
                    for (g = c.extra; B < g; ) {
                      if (H === 0) break t;
                      H--, R += L[$++] << B, B += 8;
                    }
                    c.offset += R & (1 << c.extra) - 1, R >>>= c.extra, B -= c.extra, c.back += c.extra;
                  }
                  if (c.offset > c.dmax) {
                    S.msg = "invalid distance too far back", c.mode = 30;
                    break;
                  }
                  c.mode = 25;
                case 25:
                  if (tt === 0) break t;
                  if (G = q - tt, c.offset > G) {
                    if ((G = c.offset - G) > c.whave && c.sane) {
                      S.msg = "invalid distance too far back", c.mode = 30;
                      break;
                    }
                    mt = G > c.wnext ? (G -= c.wnext, c.wsize - G) : c.wnext - G, G > c.length && (G = c.length), yt = c.window;
                  } else yt = et, mt = rt - c.offset, G = c.length;
                  for (tt < G && (G = tt), tt -= G, c.length -= G; et[rt++] = yt[mt++], --G; ) ;
                  c.length === 0 && (c.mode = 21);
                  break;
                case 26:
                  if (tt === 0) break t;
                  et[rt++] = c.length, tt--, c.mode = 21;
                  break;
                case 27:
                  if (c.wrap) {
                    for (; B < 32; ) {
                      if (H === 0) break t;
                      H--, R |= L[$++] << B, B += 8;
                    }
                    if (q -= tt, S.total_out += q, c.total += q, q && (S.adler = c.check = c.flags ? o(c.check, et, q, rt - q) : l(c.check, et, q, rt - q)), q = tt, (c.flags ? R : f(R)) !== c.check) {
                      S.msg = "incorrect data check", c.mode = 30;
                      break;
                    }
                    B = R = 0;
                  }
                  c.mode = 28;
                case 28:
                  if (c.wrap && c.flags) {
                    for (; B < 32; ) {
                      if (H === 0) break t;
                      H--, R += L[$++] << B, B += 8;
                    }
                    if (R !== (4294967295 & c.total)) {
                      S.msg = "incorrect length check", c.mode = 30;
                      break;
                    }
                    B = R = 0;
                  }
                  c.mode = 29;
                case 29:
                  F = 1;
                  break t;
                case 30:
                  F = -3;
                  break t;
                case 31:
                  return -4;
                case 32:
                default:
                  return d;
              }
              return S.next_out = rt, S.avail_out = tt, S.next_in = $, S.avail_in = H, c.hold = R, c.bits = B, (c.wsize || q !== S.avail_out && c.mode < 30 && (c.mode < 27 || O !== 4)) && Q(S, S.output, S.next_out, q - S.avail_out) ? (c.mode = 31, -4) : (J -= S.avail_in, q -= S.avail_out, S.total_in += J, S.total_out += q, c.total += q, c.wrap && q && (S.adler = c.check = c.flags ? o(c.check, et, q, S.next_out - q) : l(c.check, et, q, S.next_out - q)), S.data_type = c.bits + (c.last ? 64 : 0) + (c.mode === 12 ? 128 : 0) + (c.mode === 20 || c.mode === 15 ? 256 : 0), (J == 0 && q === 0 || O === 4) && F === w && (F = -5), F);
            }, n.inflateEnd = function(S) {
              if (!S || !S.state) return d;
              var O = S.state;
              return O.window && (O.window = null), S.state = null, w;
            }, n.inflateGetHeader = function(S, O) {
              var c;
              return S && S.state && 2 & (c = S.state).wrap ? ((c.head = O).done = false, w) : d;
            }, n.inflateSetDictionary = function(S, O) {
              var c, L = O.length;
              return S && S.state ? (c = S.state).wrap !== 0 && c.mode !== 11 ? d : c.mode === 11 && l(1, O, L, 0) !== c.check ? -3 : Q(S, O, L, L) ? (c.mode = 31, -4) : (c.havedict = 1, w) : d;
            }, n.inflateInfo = "pako inflate (from Nodeca project)";
          }, { "../utils/common": 41, "./adler32": 43, "./crc32": 45, "./inffast": 48, "./inftrees": 50 }], 50: [function(t, r, n) {
            var a = t("../utils/common"), l = [3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 15, 17, 19, 23, 27, 31, 35, 43, 51, 59, 67, 83, 99, 115, 131, 163, 195, 227, 258, 0, 0], o = [16, 16, 16, 16, 16, 16, 16, 16, 17, 17, 17, 17, 18, 18, 18, 18, 19, 19, 19, 19, 20, 20, 20, 20, 21, 21, 21, 21, 16, 72, 78], u = [1, 2, 3, 4, 5, 7, 9, 13, 17, 25, 33, 49, 65, 97, 129, 193, 257, 385, 513, 769, 1025, 1537, 2049, 3073, 4097, 6145, 8193, 12289, 16385, 24577, 0, 0], b = [16, 16, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22, 23, 23, 24, 24, 25, 25, 26, 26, 27, 27, 28, 28, 29, 29, 64, 64];
            r.exports = function(y, _, w, d, k, h, m, f) {
              var v, C, A, E, D, I, W, T, X, Q = f.bits, S = 0, O = 0, c = 0, L = 0, et = 0, $ = 0, rt = 0, H = 0, tt = 0, R = 0, B = null, J = 0, q = new a.Buf16(16), G = new a.Buf16(16), mt = null, yt = 0;
              for (S = 0; S <= 15; S++) q[S] = 0;
              for (O = 0; O < d; O++) q[_[w + O]]++;
              for (et = Q, L = 15; 1 <= L && q[L] === 0; L--) ;
              if (L < et && (et = L), L === 0) return k[h++] = 20971520, k[h++] = 20971520, f.bits = 1, 0;
              for (c = 1; c < L && q[c] === 0; c++) ;
              for (et < c && (et = c), S = H = 1; S <= 15; S++) if (H <<= 1, (H -= q[S]) < 0) return -1;
              if (0 < H && (y === 0 || L !== 1)) return -1;
              for (G[1] = 0, S = 1; S < 15; S++) G[S + 1] = G[S] + q[S];
              for (O = 0; O < d; O++) _[w + O] !== 0 && (m[G[_[w + O]]++] = O);
              if (I = y === 0 ? (B = mt = m, 19) : y === 1 ? (B = l, J -= 257, mt = o, yt -= 257, 256) : (B = u, mt = b, -1), S = c, D = h, rt = O = R = 0, A = -1, E = (tt = 1 << ($ = et)) - 1, y === 1 && 852 < tt || y === 2 && 592 < tt) return 1;
              for (; ; ) {
                for (W = S - rt, X = m[O] < I ? (T = 0, m[O]) : m[O] > I ? (T = mt[yt + m[O]], B[J + m[O]]) : (T = 96, 0), v = 1 << S - rt, c = C = 1 << $; k[D + (R >> rt) + (C -= v)] = W << 24 | T << 16 | X | 0, C !== 0; ) ;
                for (v = 1 << S - 1; R & v; ) v >>= 1;
                if (v !== 0 ? (R &= v - 1, R += v) : R = 0, O++, --q[S] == 0) {
                  if (S === L) break;
                  S = _[w + m[O]];
                }
                if (et < S && (R & E) !== A) {
                  for (rt === 0 && (rt = et), D += c, H = 1 << ($ = S - rt); $ + rt < L && !((H -= q[$ + rt]) <= 0); ) $++, H <<= 1;
                  if (tt += 1 << $, y === 1 && 852 < tt || y === 2 && 592 < tt) return 1;
                  k[A = R & E] = et << 24 | $ << 16 | D - h | 0;
                }
              }
              return R !== 0 && (k[D + R] = S - rt << 24 | 64 << 16 | 0), f.bits = et, 0;
            };
          }, { "../utils/common": 41 }], 51: [function(t, r, n) {
            r.exports = { 2: "need dictionary", 1: "stream end", 0: "", "-1": "file error", "-2": "stream error", "-3": "data error", "-4": "insufficient memory", "-5": "buffer error", "-6": "incompatible version" };
          }, {}], 52: [function(t, r, n) {
            var a = t("../utils/common"), l = 0, o = 1;
            function u(p) {
              for (var x = p.length; 0 <= --x; ) p[x] = 0;
            }
            var b = 0, y = 29, _ = 256, w = _ + 1 + y, d = 30, k = 19, h = 2 * w + 1, m = 15, f = 16, v = 7, C = 256, A = 16, E = 17, D = 18, I = [0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0], W = [0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13], T = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 3, 7], X = [16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15], Q = new Array(2 * (w + 2));
            u(Q);
            var S = new Array(2 * d);
            u(S);
            var O = new Array(512);
            u(O);
            var c = new Array(256);
            u(c);
            var L = new Array(y);
            u(L);
            var et, $, rt, H = new Array(d);
            function tt(p, x, j, U, N) {
              this.static_tree = p, this.extra_bits = x, this.extra_base = j, this.elems = U, this.max_length = N, this.has_stree = p && p.length;
            }
            function R(p, x) {
              this.dyn_tree = p, this.max_code = 0, this.stat_desc = x;
            }
            function B(p) {
              return p < 256 ? O[p] : O[256 + (p >>> 7)];
            }
            function J(p, x) {
              p.pending_buf[p.pending++] = 255 & x, p.pending_buf[p.pending++] = x >>> 8 & 255;
            }
            function q(p, x, j) {
              p.bi_valid > f - j ? (p.bi_buf |= x << p.bi_valid & 65535, J(p, p.bi_buf), p.bi_buf = x >> f - p.bi_valid, p.bi_valid += j - f) : (p.bi_buf |= x << p.bi_valid & 65535, p.bi_valid += j);
            }
            function G(p, x, j) {
              q(p, j[2 * x], j[2 * x + 1]);
            }
            function mt(p, x) {
              for (var j = 0; j |= 1 & p, p >>>= 1, j <<= 1, 0 < --x; ) ;
              return j >>> 1;
            }
            function yt(p, x, j) {
              var U, N, Z = new Array(m + 1), K = 0;
              for (U = 1; U <= m; U++) Z[U] = K = K + j[U - 1] << 1;
              for (N = 0; N <= x; N++) {
                var V = p[2 * N + 1];
                V !== 0 && (p[2 * N] = mt(Z[V]++, V));
              }
            }
            function at(p) {
              var x;
              for (x = 0; x < w; x++) p.dyn_ltree[2 * x] = 0;
              for (x = 0; x < d; x++) p.dyn_dtree[2 * x] = 0;
              for (x = 0; x < k; x++) p.bl_tree[2 * x] = 0;
              p.dyn_ltree[2 * C] = 1, p.opt_len = p.static_len = 0, p.last_lit = p.matches = 0;
            }
            function it(p) {
              8 < p.bi_valid ? J(p, p.bi_buf) : 0 < p.bi_valid && (p.pending_buf[p.pending++] = p.bi_buf), p.bi_buf = 0, p.bi_valid = 0;
            }
            function kt(p, x, j, U) {
              var N = 2 * x, Z = 2 * j;
              return p[N] < p[Z] || p[N] === p[Z] && U[x] <= U[j];
            }
            function gt(p, x, j) {
              for (var U = p.heap[j], N = j << 1; N <= p.heap_len && (N < p.heap_len && kt(x, p.heap[N + 1], p.heap[N], p.depth) && N++, !kt(x, U, p.heap[N], p.depth)); ) p.heap[j] = p.heap[N], j = N, N <<= 1;
              p.heap[j] = U;
            }
            function Pt(p, x, j) {
              var U, N, Z, K, V = 0;
              if (p.last_lit !== 0) for (; U = p.pending_buf[p.d_buf + 2 * V] << 8 | p.pending_buf[p.d_buf + 2 * V + 1], N = p.pending_buf[p.l_buf + V], V++, U === 0 ? G(p, N, x) : (G(p, (Z = c[N]) + _ + 1, x), (K = I[Z]) !== 0 && q(p, N -= L[Z], K), G(p, Z = B(--U), j), (K = W[Z]) !== 0 && q(p, U -= H[Z], K)), V < p.last_lit; ) ;
              G(p, C, x);
            }
            function xt(p, x) {
              var j, U, N, Z = x.dyn_tree, K = x.stat_desc.static_tree, V = x.stat_desc.has_stree, Y = x.stat_desc.elems, lt = -1;
              for (p.heap_len = 0, p.heap_max = h, j = 0; j < Y; j++) Z[2 * j] !== 0 ? (p.heap[++p.heap_len] = lt = j, p.depth[j] = 0) : Z[2 * j + 1] = 0;
              for (; p.heap_len < 2; ) Z[2 * (N = p.heap[++p.heap_len] = lt < 2 ? ++lt : 0)] = 1, p.depth[N] = 0, p.opt_len--, V && (p.static_len -= K[2 * N + 1]);
              for (x.max_code = lt, j = p.heap_len >> 1; 1 <= j; j--) gt(p, Z, j);
              for (N = Y; j = p.heap[1], p.heap[1] = p.heap[p.heap_len--], gt(p, Z, 1), U = p.heap[1], p.heap[--p.heap_max] = j, p.heap[--p.heap_max] = U, Z[2 * N] = Z[2 * j] + Z[2 * U], p.depth[N] = (p.depth[j] >= p.depth[U] ? p.depth[j] : p.depth[U]) + 1, Z[2 * j + 1] = Z[2 * U + 1] = N, p.heap[1] = N++, gt(p, Z, 1), 2 <= p.heap_len; ) ;
              p.heap[--p.heap_max] = p.heap[1], function(st, Ct) {
                var jt, Et, Ut, bt, Kt, ie, At = Ct.dyn_tree, Be = Ct.max_code, fn = Ct.stat_desc.static_tree, pn = Ct.stat_desc.has_stree, mn = Ct.stat_desc.extra_bits, ze = Ct.stat_desc.extra_base, $t = Ct.stat_desc.max_length, Yt = 0;
                for (bt = 0; bt <= m; bt++) st.bl_count[bt] = 0;
                for (At[2 * st.heap[st.heap_max] + 1] = 0, jt = st.heap_max + 1; jt < h; jt++) $t < (bt = At[2 * At[2 * (Et = st.heap[jt]) + 1] + 1] + 1) && (bt = $t, Yt++), At[2 * Et + 1] = bt, Be < Et || (st.bl_count[bt]++, Kt = 0, ze <= Et && (Kt = mn[Et - ze]), ie = At[2 * Et], st.opt_len += ie * (bt + Kt), pn && (st.static_len += ie * (fn[2 * Et + 1] + Kt)));
                if (Yt !== 0) {
                  do {
                    for (bt = $t - 1; st.bl_count[bt] === 0; ) bt--;
                    st.bl_count[bt]--, st.bl_count[bt + 1] += 2, st.bl_count[$t]--, Yt -= 2;
                  } while (0 < Yt);
                  for (bt = $t; bt !== 0; bt--) for (Et = st.bl_count[bt]; Et !== 0; ) Be < (Ut = st.heap[--jt]) || (At[2 * Ut + 1] !== bt && (st.opt_len += (bt - At[2 * Ut + 1]) * At[2 * Ut], At[2 * Ut + 1] = bt), Et--);
                }
              }(p, x), yt(Z, lt, p.bl_count);
            }
            function s(p, x, j) {
              var U, N, Z = -1, K = x[1], V = 0, Y = 7, lt = 4;
              for (K === 0 && (Y = 138, lt = 3), x[2 * (j + 1) + 1] = 65535, U = 0; U <= j; U++) N = K, K = x[2 * (U + 1) + 1], ++V < Y && N === K || (V < lt ? p.bl_tree[2 * N] += V : N !== 0 ? (N !== Z && p.bl_tree[2 * N]++, p.bl_tree[2 * A]++) : V <= 10 ? p.bl_tree[2 * E]++ : p.bl_tree[2 * D]++, Z = N, lt = (V = 0) === K ? (Y = 138, 3) : N === K ? (Y = 6, 3) : (Y = 7, 4));
            }
            function F(p, x, j) {
              var U, N, Z = -1, K = x[1], V = 0, Y = 7, lt = 4;
              for (K === 0 && (Y = 138, lt = 3), U = 0; U <= j; U++) if (N = K, K = x[2 * (U + 1) + 1], !(++V < Y && N === K)) {
                if (V < lt) for (; G(p, N, p.bl_tree), --V != 0; ) ;
                else N !== 0 ? (N !== Z && (G(p, N, p.bl_tree), V--), G(p, A, p.bl_tree), q(p, V - 3, 2)) : V <= 10 ? (G(p, E, p.bl_tree), q(p, V - 3, 3)) : (G(p, D, p.bl_tree), q(p, V - 11, 7));
                Z = N, lt = (V = 0) === K ? (Y = 138, 3) : N === K ? (Y = 6, 3) : (Y = 7, 4);
              }
            }
            u(H);
            var M = false;
            function g(p, x, j, U) {
              q(p, (b << 1) + (U ? 1 : 0), 3), function(N, Z, K, V) {
                it(N), V && (J(N, K), J(N, ~K)), a.arraySet(N.pending_buf, N.window, Z, K, N.pending), N.pending += K;
              }(p, x, j, true);
            }
            n._tr_init = function(p) {
              M || (function() {
                var x, j, U, N, Z, K = new Array(m + 1);
                for (N = U = 0; N < y - 1; N++) for (L[N] = U, x = 0; x < 1 << I[N]; x++) c[U++] = N;
                for (c[U - 1] = N, N = Z = 0; N < 16; N++) for (H[N] = Z, x = 0; x < 1 << W[N]; x++) O[Z++] = N;
                for (Z >>= 7; N < d; N++) for (H[N] = Z << 7, x = 0; x < 1 << W[N] - 7; x++) O[256 + Z++] = N;
                for (j = 0; j <= m; j++) K[j] = 0;
                for (x = 0; x <= 143; ) Q[2 * x + 1] = 8, x++, K[8]++;
                for (; x <= 255; ) Q[2 * x + 1] = 9, x++, K[9]++;
                for (; x <= 279; ) Q[2 * x + 1] = 7, x++, K[7]++;
                for (; x <= 287; ) Q[2 * x + 1] = 8, x++, K[8]++;
                for (yt(Q, w + 1, K), x = 0; x < d; x++) S[2 * x + 1] = 5, S[2 * x] = mt(x, 5);
                et = new tt(Q, I, _ + 1, w, m), $ = new tt(S, W, 0, d, m), rt = new tt(new Array(0), T, 0, k, v);
              }(), M = true), p.l_desc = new R(p.dyn_ltree, et), p.d_desc = new R(p.dyn_dtree, $), p.bl_desc = new R(p.bl_tree, rt), p.bi_buf = 0, p.bi_valid = 0, at(p);
            }, n._tr_stored_block = g, n._tr_flush_block = function(p, x, j, U) {
              var N, Z, K = 0;
              0 < p.level ? (p.strm.data_type === 2 && (p.strm.data_type = function(V) {
                var Y, lt = 4093624447;
                for (Y = 0; Y <= 31; Y++, lt >>>= 1) if (1 & lt && V.dyn_ltree[2 * Y] !== 0) return l;
                if (V.dyn_ltree[18] !== 0 || V.dyn_ltree[20] !== 0 || V.dyn_ltree[26] !== 0) return o;
                for (Y = 32; Y < _; Y++) if (V.dyn_ltree[2 * Y] !== 0) return o;
                return l;
              }(p)), xt(p, p.l_desc), xt(p, p.d_desc), K = function(V) {
                var Y;
                for (s(V, V.dyn_ltree, V.l_desc.max_code), s(V, V.dyn_dtree, V.d_desc.max_code), xt(V, V.bl_desc), Y = k - 1; 3 <= Y && V.bl_tree[2 * X[Y] + 1] === 0; Y--) ;
                return V.opt_len += 3 * (Y + 1) + 5 + 5 + 4, Y;
              }(p), N = p.opt_len + 3 + 7 >>> 3, (Z = p.static_len + 3 + 7 >>> 3) <= N && (N = Z)) : N = Z = j + 5, j + 4 <= N && x !== -1 ? g(p, x, j, U) : p.strategy === 4 || Z === N ? (q(p, 2 + (U ? 1 : 0), 3), Pt(p, Q, S)) : (q(p, 4 + (U ? 1 : 0), 3), function(V, Y, lt, st) {
                var Ct;
                for (q(V, Y - 257, 5), q(V, lt - 1, 5), q(V, st - 4, 4), Ct = 0; Ct < st; Ct++) q(V, V.bl_tree[2 * X[Ct] + 1], 3);
                F(V, V.dyn_ltree, Y - 1), F(V, V.dyn_dtree, lt - 1);
              }(p, p.l_desc.max_code + 1, p.d_desc.max_code + 1, K + 1), Pt(p, p.dyn_ltree, p.dyn_dtree)), at(p), U && it(p);
            }, n._tr_tally = function(p, x, j) {
              return p.pending_buf[p.d_buf + 2 * p.last_lit] = x >>> 8 & 255, p.pending_buf[p.d_buf + 2 * p.last_lit + 1] = 255 & x, p.pending_buf[p.l_buf + p.last_lit] = 255 & j, p.last_lit++, x === 0 ? p.dyn_ltree[2 * j]++ : (p.matches++, x--, p.dyn_ltree[2 * (c[j] + _ + 1)]++, p.dyn_dtree[2 * B(x)]++), p.last_lit === p.lit_bufsize - 1;
            }, n._tr_align = function(p) {
              q(p, 2, 3), G(p, C, Q), function(x) {
                x.bi_valid === 16 ? (J(x, x.bi_buf), x.bi_buf = 0, x.bi_valid = 0) : 8 <= x.bi_valid && (x.pending_buf[x.pending++] = 255 & x.bi_buf, x.bi_buf >>= 8, x.bi_valid -= 8);
              }(p);
            };
          }, { "../utils/common": 41 }], 53: [function(t, r, n) {
            r.exports = function() {
              this.input = null, this.next_in = 0, this.avail_in = 0, this.total_in = 0, this.output = null, this.next_out = 0, this.avail_out = 0, this.total_out = 0, this.msg = "", this.state = null, this.data_type = 2, this.adler = 0;
            };
          }, {}], 54: [function(t, r, n) {
            (function(a) {
              (function(l, o) {
                if (!l.setImmediate) {
                  var u, b, y, _, w = 1, d = {}, k = false, h = l.document, m = Object.getPrototypeOf && Object.getPrototypeOf(l);
                  m = m && m.setTimeout ? m : l, u = {}.toString.call(l.process) === "[object process]" ? function(A) {
                    process.nextTick(function() {
                      v(A);
                    });
                  } : function() {
                    if (l.postMessage && !l.importScripts) {
                      var A = true, E = l.onmessage;
                      return l.onmessage = function() {
                        A = false;
                      }, l.postMessage("", "*"), l.onmessage = E, A;
                    }
                  }() ? (_ = "setImmediate$" + Math.random() + "$", l.addEventListener ? l.addEventListener("message", C, false) : l.attachEvent("onmessage", C), function(A) {
                    l.postMessage(_ + A, "*");
                  }) : l.MessageChannel ? ((y = new MessageChannel()).port1.onmessage = function(A) {
                    v(A.data);
                  }, function(A) {
                    y.port2.postMessage(A);
                  }) : h && "onreadystatechange" in h.createElement("script") ? (b = h.documentElement, function(A) {
                    var E = h.createElement("script");
                    E.onreadystatechange = function() {
                      v(A), E.onreadystatechange = null, b.removeChild(E), E = null;
                    }, b.appendChild(E);
                  }) : function(A) {
                    setTimeout(v, 0, A);
                  }, m.setImmediate = function(A) {
                    typeof A != "function" && (A = new Function("" + A));
                    for (var E = new Array(arguments.length - 1), D = 0; D < E.length; D++) E[D] = arguments[D + 1];
                    var I = { callback: A, args: E };
                    return d[w] = I, u(w), w++;
                  }, m.clearImmediate = f;
                }
                function f(A) {
                  delete d[A];
                }
                function v(A) {
                  if (k) setTimeout(v, 0, A);
                  else {
                    var E = d[A];
                    if (E) {
                      k = true;
                      try {
                        (function(D) {
                          var I = D.callback, W = D.args;
                          switch (W.length) {
                            case 0:
                              I();
                              break;
                            case 1:
                              I(W[0]);
                              break;
                            case 2:
                              I(W[0], W[1]);
                              break;
                            case 3:
                              I(W[0], W[1], W[2]);
                              break;
                            default:
                              I.apply(o, W);
                          }
                        })(E);
                      } finally {
                        f(A), k = false;
                      }
                    }
                  }
                }
                function C(A) {
                  A.source === l && typeof A.data == "string" && A.data.indexOf(_) === 0 && v(+A.data.slice(_.length));
                }
              })(typeof self == "undefined" ? a === void 0 ? this : a : self);
            }).call(this, typeof vt != "undefined" ? vt : typeof self != "undefined" ? self : typeof window != "undefined" ? window : {});
          }, {}] }, {}, [10])(10);
        });
      })(Ht);
      var Qt = Ht.exports;
      const It = Jt(Qt);
      var ot;
      (function(i) {
        i.OfficeDocument = "http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument", i.FontTable = "http://schemas.openxmlformats.org/officeDocument/2006/relationships/fontTable", i.Image = "http://schemas.openxmlformats.org/officeDocument/2006/relationships/image", i.Numbering = "http://schemas.openxmlformats.org/officeDocument/2006/relationships/numbering", i.Styles = "http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles", i.StylesWithEffects = "http://schemas.microsoft.com/office/2007/relationships/stylesWithEffects", i.Theme = "http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme", i.Settings = "http://schemas.openxmlformats.org/officeDocument/2006/relationships/settings", i.WebSettings = "http://schemas.openxmlformats.org/officeDocument/2006/relationships/webSettings", i.Hyperlink = "http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink", i.Footnotes = "http://schemas.openxmlformats.org/officeDocument/2006/relationships/footnotes", i.Endnotes = "http://schemas.openxmlformats.org/officeDocument/2006/relationships/endnotes", i.Footer = "http://schemas.openxmlformats.org/officeDocument/2006/relationships/footer", i.Header = "http://schemas.openxmlformats.org/officeDocument/2006/relationships/header", i.ExtendedProperties = "http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties", i.CoreProperties = "http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties", i.CustomProperties = "http://schemas.openxmlformats.org/package/2006/relationships/metadata/custom-properties", i.Comments = "http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments", i.CommentsExtended = "http://schemas.microsoft.com/office/2011/relationships/commentsExtended";
      })(ot || (ot = {}));
      function Ot(i, e) {
        return e.elements(i).map((t) => ({ id: e.attr(t, "Id"), type: e.attr(t, "Type"), target: e.attr(t, "Target"), targetMode: e.attr(t, "TargetMode") }));
      }
      const oe = { wordml: "http://schemas.openxmlformats.org/wordprocessingml/2006/main", drawingml: "http://schemas.openxmlformats.org/drawingml/2006/main", picture: "http://schemas.openxmlformats.org/drawingml/2006/picture", compatibility: "http://schemas.openxmlformats.org/markup-compatibility/2006", math: "http://schemas.openxmlformats.org/officeDocument/2006/math" }, pt = { Dxa: { mul: 0.05, unit: "pt" }, Emu: { mul: 1 / 12700, unit: "pt" }, FontSize: { mul: 0.5, unit: "pt" }, Border: { mul: 0.125, unit: "pt" }, Point: { mul: 1, unit: "pt" }, Percent: { mul: 0.02, unit: "%" }, LineHeight: { mul: 1 / 240, unit: "" }, VmlEmu: { mul: 1 / 12700, unit: "" } };
      function le(i, e = pt.Dxa) {
        return i == null || /.+(p[xt]|[%])$/.test(i) ? i : `${(parseInt(i) * e.mul).toFixed(2)}${e.unit}`;
      }
      function Te(i, e = false) {
        switch (i) {
          case "1":
            return true;
          case "0":
            return false;
          case "on":
            return true;
          case "off":
            return false;
          case "true":
            return true;
          case "false":
            return false;
          default:
            return e;
        }
      }
      function ce(i, e, t) {
        if (i.namespaceURI != oe.wordml) return false;
        switch (i.localName) {
          case "color":
            e.color = t.attr(i, "val");
            break;
          case "sz":
            e.fontSize = t.lengthAttr(i, "val", pt.FontSize);
            break;
          default:
            return false;
        }
        return true;
      }
      function Ie(i, e = false) {
        e && (i = i.replace(/<[?].*[?]>/, "")), i = Fe(i);
        const t = new DOMParser().parseFromString(i, "application/xml"), r = Oe(t);
        if (r) throw new Error(r);
        return t;
      }
      function Oe(i) {
        var e;
        return (e = i.getElementsByTagName("parsererror")[0]) == null ? void 0 : e.textContent;
      }
      function Fe(i) {
        return i.charCodeAt(0) === 65279 ? i.substring(1) : i;
      }
      function De(i) {
        return new XMLSerializer().serializeToString(i);
      }
      class he {
        elements(e, t = null) {
          const r = [];
          for (let n = 0, a = e.childNodes.length; n < a; n++) {
            let l = e.childNodes.item(n);
            l.nodeType == 1 && (t == null || l.localName == t) && r.push(l);
          }
          return r;
        }
        element(e, t) {
          for (let r = 0, n = e.childNodes.length; r < n; r++) {
            let a = e.childNodes.item(r);
            if (a.nodeType == 1 && a.localName == t) return a;
          }
          return null;
        }
        elementAttr(e, t, r) {
          var n = this.element(e, t);
          return n ? this.attr(n, r) : void 0;
        }
        attrs(e) {
          return Array.from(e.attributes);
        }
        attr(e, t) {
          for (let r = 0, n = e.attributes.length; r < n; r++) {
            let a = e.attributes.item(r);
            if (a.localName == t) return a.value;
          }
          return null;
        }
        intAttr(e, t, r = null) {
          var n = this.attr(e, t);
          return n ? parseInt(n) : r;
        }
        hexAttr(e, t, r = null) {
          var n = this.attr(e, t);
          return n ? parseInt(n, 16) : r;
        }
        floatAttr(e, t, r = null) {
          var n = this.attr(e, t);
          return n ? parseFloat(n) : r;
        }
        boolAttr(e, t, r = null) {
          return Te(this.attr(e, t), r);
        }
        lengthAttr(e, t, r = pt.Dxa) {
          return le(this.attr(e, t), r);
        }
      }
      const P = new he();
      class St {
        constructor(e, t) {
          this._package = e, this.path = t;
        }
        load() {
          return wt(this, null, function* () {
            this.rels = yield this._package.loadRelationships(this.path);
            const e = yield this._package.load(this.path), t = this._package.parseXmlDocument(e);
            this._package.options.keepOrigin && (this._xmlDocument = t), this.parseXml(t.firstElementChild);
          });
        }
        save() {
          this._package.update(this.path, De(this._xmlDocument));
        }
        parseXml(e) {
        }
      }
      const Le = { embedRegular: "regular", embedBold: "bold", embedItalic: "italic", embedBoldItalic: "boldItalic" };
      function je(i, e) {
        return e.elements(i).map((t) => Ue(t, e));
      }
      function Ue(i, e) {
        let t = { name: e.attr(i, "name"), embedFontRefs: [] };
        for (let r of e.elements(i)) switch (r.localName) {
          case "family":
            t.family = e.attr(r, "val");
            break;
          case "altName":
            t.altName = e.attr(r, "val");
            break;
          case "embedRegular":
          case "embedBold":
          case "embedItalic":
          case "embedBoldItalic":
            t.embedFontRefs.push($e(r, e));
            break;
        }
        return t;
      }
      function $e(i, e) {
        return { id: e.attr(i, "id"), key: e.attr(i, "fontKey"), type: Le[i.localName] };
      }
      class We extends St {
        parseXml(e) {
          this.fonts = je(e, this._package.xmlParser);
        }
      }
      function He(i) {
        return i == null ? void 0 : i.replace(/[ .]+/g, "-").replace(/[&]+/g, "and").toLowerCase();
      }
      function Zt(i) {
        let e = i.lastIndexOf("/") + 1, t = e == 0 ? "" : i.substring(0, e), r = e == 0 ? i : i.substring(e);
        return [t, r];
      }
      function te(i, e) {
        try {
          const t = "http://docx/";
          return new URL(i, t + e).toString().substring(t.length);
        } catch (t) {
          return `${e}${i}`;
        }
      }
      function Nt(i, e) {
        return i.reduce((t, r) => (t[e(r)] = r, t), {});
      }
      function Ze(i) {
        return new Promise((e, t) => {
          const r = new FileReader();
          r.onloadend = () => e(r.result), r.onerror = () => t(), r.readAsDataURL(i);
        });
      }
      function ee(i) {
        return i && typeof i == "object" && !Array.isArray(i);
      }
      function Ve(i) {
        return typeof i == "string" || i instanceof String;
      }
      function Vt(i, ...e) {
        var r;
        if (!e.length) return i;
        const t = e.shift();
        if (ee(i) && ee(t)) for (const n in t) if (ee(t[n])) {
          const a = (r = i[n]) != null ? r : i[n] = {};
          Vt(a, t[n]);
        } else i[n] = t[n];
        return Vt(i, ...e);
      }
      function Ft(i) {
        return Array.isArray(i) ? i : [i];
      }
      class re {
        constructor(e, t) {
          this._zip = e, this.options = t, this.xmlParser = new he();
        }
        get(e) {
          var r;
          const t = Xe(e);
          return (r = this._zip.files[t]) != null ? r : this._zip.files[t.replace(/\//g, "\\")];
        }
        update(e, t) {
          this._zip.file(e, t);
        }
        static load(e, t) {
          return wt(this, null, function* () {
            const r = yield It.loadAsync(e);
            return new re(r, t);
          });
        }
        save(e = "blob") {
          return this._zip.generateAsync({ type: e });
        }
        load(e, t = "string") {
          var r, n;
          return (n = (r = this.get(e)) == null ? void 0 : r.async(t)) != null ? n : Promise.resolve(null);
        }
        loadRelationships(e = null) {
          return wt(this, null, function* () {
            let t = "_rels/.rels";
            if (e != null) {
              const [n, a] = Zt(e);
              t = `${n}_rels/${a}.rels`;
            }
            const r = yield this.load(t);
            return r ? Ot(this.parseXmlDocument(r).firstElementChild, this.xmlParser) : null;
          });
        }
        parseXmlDocument(e) {
          return Ie(e, this.options.trimXmlDeclaration);
        }
      }
      function Xe(i) {
        return i.startsWith("/") ? i.substr(1) : i;
      }
      class Ge extends St {
        constructor(e, t, r) {
          super(e, t), this._documentParser = r;
        }
        parseXml(e) {
          this.body = this._documentParser.parseDocumentFile(e);
        }
      }
      function Xt(i, e) {
        return { type: e.attr(i, "val"), color: e.attr(i, "color"), size: e.lengthAttr(i, "sz", pt.Border), offset: e.lengthAttr(i, "space", pt.Point), frame: e.boolAttr(i, "frame"), shadow: e.boolAttr(i, "shadow") };
      }
      function qe(i, e) {
        var t = {};
        for (let r of e.elements(i)) switch (r.localName) {
          case "left":
            t.left = Xt(r, e);
            break;
          case "top":
            t.top = Xt(r, e);
            break;
          case "right":
            t.right = Xt(r, e);
            break;
          case "bottom":
            t.bottom = Xt(r, e);
            break;
        }
        return t;
      }
      var ue;
      (function(i) {
        i.Continuous = "continuous", i.NextPage = "nextPage", i.NextColumn = "nextColumn", i.EvenPage = "evenPage", i.OddPage = "oddPage";
      })(ue || (ue = {}));
      function de(i, e = P) {
        var r, n;
        var t = {};
        for (let a of e.elements(i)) switch (a.localName) {
          case "pgSz":
            t.pageSize = { width: e.lengthAttr(a, "w"), height: e.lengthAttr(a, "h"), orientation: e.attr(a, "orient") };
            break;
          case "type":
            t.type = e.attr(a, "val");
            break;
          case "pgMar":
            t.pageMargins = { left: e.lengthAttr(a, "left"), right: e.lengthAttr(a, "right"), top: e.lengthAttr(a, "top"), bottom: e.lengthAttr(a, "bottom"), header: e.lengthAttr(a, "header"), footer: e.lengthAttr(a, "footer"), gutter: e.lengthAttr(a, "gutter") };
            break;
          case "cols":
            t.columns = Ke(a, e);
            break;
          case "headerReference":
            ((r = t.headerRefs) != null ? r : t.headerRefs = []).push(fe(a, e));
            break;
          case "footerReference":
            ((n = t.footerRefs) != null ? n : t.footerRefs = []).push(fe(a, e));
            break;
          case "titlePg":
            t.titlePage = e.boolAttr(a, "val", true);
            break;
          case "pgBorders":
            t.pageBorders = qe(a, e);
            break;
          case "pgNumType":
            t.pageNumber = Ye(a, e);
            break;
        }
        return t;
      }
      function Ke(i, e) {
        return { numberOfColumns: e.intAttr(i, "num"), space: e.lengthAttr(i, "space"), separator: e.boolAttr(i, "sep"), equalWidth: e.boolAttr(i, "equalWidth", true), columns: e.elements(i, "col").map((t) => ({ width: e.lengthAttr(t, "w"), space: e.lengthAttr(t, "space") })) };
      }
      function Ye(i, e) {
        return { chapSep: e.attr(i, "chapSep"), chapStyle: e.attr(i, "chapStyle"), format: e.attr(i, "fmt"), start: e.intAttr(i, "start") };
      }
      function fe(i, e) {
        return { id: e.attr(i, "id"), type: e.attr(i, "type") };
      }
      function Je(i, e) {
        return { before: e.lengthAttr(i, "before"), after: e.lengthAttr(i, "after"), line: e.intAttr(i, "line"), lineRule: e.attr(i, "lineRule") };
      }
      function ne(i, e) {
        let t = {};
        for (let r of e.elements(i)) Qe(r, t, e);
        return t;
      }
      function Qe(i, e, t) {
        return !!ce(i, e, t);
      }
      function pe(i, e) {
        let t = {};
        for (let r of e.elements(i)) me(r, t, e);
        return t;
      }
      function me(i, e, t) {
        if (i.namespaceURI != oe.wordml) return false;
        if (ce(i, e, t)) return true;
        switch (i.localName) {
          case "tabs":
            e.tabs = tr(i, t);
            break;
          case "sectPr":
            e.sectionProps = de(i, t);
            break;
          case "numPr":
            e.numbering = er(i, t);
            break;
          case "spacing":
            return e.lineSpacing = Je(i, t), false;
          case "textAlignment":
            return e.textAlignment = t.attr(i, "val"), false;
          case "keepLines":
            e.keepLines = t.boolAttr(i, "val", true);
            break;
          case "keepNext":
            e.keepNext = t.boolAttr(i, "val", true);
            break;
          case "pageBreakBefore":
            e.pageBreakBefore = t.boolAttr(i, "val", true);
            break;
          case "outlineLvl":
            e.outlineLevel = t.intAttr(i, "val");
            break;
          case "pStyle":
            e.styleName = t.attr(i, "val");
            break;
          case "rPr":
            e.runProps = ne(i, t);
            break;
          default:
            return false;
        }
        return true;
      }
      function tr(i, e) {
        return e.elements(i, "tab").map((t) => ({ position: e.lengthAttr(t, "pos"), leader: e.attr(t, "leader"), style: e.attr(t, "val") }));
      }
      function er(i, e) {
        var t = {};
        for (let r of e.elements(i)) switch (r.localName) {
          case "numId":
            t.id = e.attr(r, "val");
            break;
          case "ilvl":
            t.level = e.intAttr(r, "val");
            break;
        }
        return t;
      }
      function rr(i, e) {
        let t = { numberings: [], abstractNumberings: [], bulletPictures: [] };
        for (let r of e.elements(i)) switch (r.localName) {
          case "num":
            t.numberings.push(nr(r, e));
            break;
          case "abstractNum":
            t.abstractNumberings.push(ar(r, e));
            break;
          case "numPicBullet":
            t.bulletPictures.push(ir(r, e));
            break;
        }
        return t;
      }
      function nr(i, e) {
        let t = { id: e.attr(i, "numId"), overrides: [] };
        for (let r of e.elements(i)) switch (r.localName) {
          case "abstractNumId":
            t.abstractId = e.attr(r, "val");
            break;
          case "lvlOverride":
            t.overrides.push(sr(r, e));
            break;
        }
        return t;
      }
      function ar(i, e) {
        let t = { id: e.attr(i, "abstractNumId"), levels: [] };
        for (let r of e.elements(i)) switch (r.localName) {
          case "name":
            t.name = e.attr(r, "val");
            break;
          case "multiLevelType":
            t.multiLevelType = e.attr(r, "val");
            break;
          case "numStyleLink":
            t.numberingStyleLink = e.attr(r, "val");
            break;
          case "styleLink":
            t.styleLink = e.attr(r, "val");
            break;
          case "lvl":
            t.levels.push(ge(r, e));
            break;
        }
        return t;
      }
      function ge(i, e) {
        let t = { level: e.intAttr(i, "ilvl") };
        for (let r of e.elements(i)) switch (r.localName) {
          case "start":
            t.start = e.attr(r, "val");
            break;
          case "lvlRestart":
            t.restart = e.intAttr(r, "val");
            break;
          case "numFmt":
            t.format = e.attr(r, "val");
            break;
          case "lvlText":
            t.text = e.attr(r, "val");
            break;
          case "lvlJc":
            t.justification = e.attr(r, "val");
            break;
          case "lvlPicBulletId":
            t.bulletPictureId = e.attr(r, "val");
            break;
          case "pStyle":
            t.paragraphStyle = e.attr(r, "val");
            break;
          case "pPr":
            t.paragraphProps = pe(r, e);
            break;
          case "rPr":
            t.runProps = ne(r, e);
            break;
        }
        return t;
      }
      function sr(i, e) {
        let t = { level: e.intAttr(i, "ilvl") };
        for (let r of e.elements(i)) switch (r.localName) {
          case "startOverride":
            t.start = e.intAttr(r, "val");
            break;
          case "lvl":
            t.numberingLevel = ge(r, e);
            break;
        }
        return t;
      }
      function ir(i, e) {
        var t = e.element(i, "pict"), r = t && e.element(t, "shape"), n = r && e.element(r, "imagedata");
        return n ? { id: e.attr(i, "numPicBulletId"), referenceId: e.attr(n, "id"), style: e.attr(r, "style") } : null;
      }
      class or extends St {
        constructor(e, t, r) {
          super(e, t), this._documentParser = r;
        }
        parseXml(e) {
          Object.assign(this, rr(e, this._package.xmlParser)), this.domNumberings = this._documentParser.parseNumberingFile(e);
        }
      }
      class lr extends St {
        constructor(e, t, r) {
          super(e, t), this._documentParser = r;
        }
        parseXml(e) {
          this.styles = this._documentParser.parseStylesFile(e);
        }
      }
      var z;
      (function(i) {
        i.Document = "document", i.Paragraph = "paragraph", i.Run = "run", i.Break = "break", i.NoBreakHyphen = "noBreakHyphen", i.Table = "table", i.Row = "row", i.Cell = "cell", i.Hyperlink = "hyperlink", i.SmartTag = "smartTag", i.Drawing = "drawing", i.Image = "image", i.Text = "text", i.Tab = "tab", i.Symbol = "symbol", i.BookmarkStart = "bookmarkStart", i.BookmarkEnd = "bookmarkEnd", i.Footer = "footer", i.Header = "header", i.FootnoteReference = "footnoteReference", i.EndnoteReference = "endnoteReference", i.Footnote = "footnote", i.Endnote = "endnote", i.SimpleField = "simpleField", i.ComplexField = "complexField", i.Instruction = "instruction", i.VmlPicture = "vmlPicture", i.MmlMath = "mmlMath", i.MmlMathParagraph = "mmlMathParagraph", i.MmlFraction = "mmlFraction", i.MmlFunction = "mmlFunction", i.MmlFunctionName = "mmlFunctionName", i.MmlNumerator = "mmlNumerator", i.MmlDenominator = "mmlDenominator", i.MmlRadical = "mmlRadical", i.MmlBase = "mmlBase", i.MmlDegree = "mmlDegree", i.MmlSuperscript = "mmlSuperscript", i.MmlSubscript = "mmlSubscript", i.MmlPreSubSuper = "mmlPreSubSuper", i.MmlSubArgument = "mmlSubArgument", i.MmlSuperArgument = "mmlSuperArgument", i.MmlNary = "mmlNary", i.MmlDelimiter = "mmlDelimiter", i.MmlRun = "mmlRun", i.MmlEquationArray = "mmlEquationArray", i.MmlLimit = "mmlLimit", i.MmlLimitLower = "mmlLimitLower", i.MmlMatrix = "mmlMatrix", i.MmlMatrixRow = "mmlMatrixRow", i.MmlBox = "mmlBox", i.MmlBar = "mmlBar", i.MmlGroupChar = "mmlGroupChar", i.VmlElement = "vmlElement", i.Inserted = "inserted", i.Deleted = "deleted", i.DeletedText = "deletedText", i.Comment = "comment", i.CommentReference = "commentReference", i.CommentRangeStart = "commentRangeStart", i.CommentRangeEnd = "commentRangeEnd";
      })(z || (z = {}));
      class zt {
        constructor() {
          this.children = [], this.cssStyle = {};
        }
      }
      class cr extends zt {
        constructor() {
          super(...arguments), this.type = z.Header;
        }
      }
      class hr extends zt {
        constructor() {
          super(...arguments), this.type = z.Footer;
        }
      }
      class be extends St {
        constructor(e, t, r) {
          super(e, t), this._documentParser = r;
        }
        parseXml(e) {
          this.rootElement = this.createRootElement(), this.rootElement.children = this._documentParser.parseBodyElements(e);
        }
      }
      class ur extends be {
        createRootElement() {
          return new cr();
        }
      }
      class dr extends be {
        createRootElement() {
          return new hr();
        }
      }
      function fr(i, e) {
        const t = {};
        for (let r of e.elements(i)) switch (r.localName) {
          case "Template":
            t.template = r.textContent;
            break;
          case "Pages":
            t.pages = Dt(r.textContent);
            break;
          case "Words":
            t.words = Dt(r.textContent);
            break;
          case "Characters":
            t.characters = Dt(r.textContent);
            break;
          case "Application":
            t.application = r.textContent;
            break;
          case "Lines":
            t.lines = Dt(r.textContent);
            break;
          case "Paragraphs":
            t.paragraphs = Dt(r.textContent);
            break;
          case "Company":
            t.company = r.textContent;
            break;
          case "AppVersion":
            t.appVersion = r.textContent;
            break;
        }
        return t;
      }
      function Dt(i) {
        if (typeof i != "undefined") return parseInt(i);
      }
      class pr extends St {
        parseXml(e) {
          this.props = fr(e, this._package.xmlParser);
        }
      }
      function mr(i, e) {
        const t = {};
        for (let r of e.elements(i)) switch (r.localName) {
          case "title":
            t.title = r.textContent;
            break;
          case "description":
            t.description = r.textContent;
            break;
          case "subject":
            t.subject = r.textContent;
            break;
          case "creator":
            t.creator = r.textContent;
            break;
          case "keywords":
            t.keywords = r.textContent;
            break;
          case "language":
            t.language = r.textContent;
            break;
          case "lastModifiedBy":
            t.lastModifiedBy = r.textContent;
            break;
          case "revision":
            r.textContent && (t.revision = parseInt(r.textContent));
            break;
        }
        return t;
      }
      class gr extends St {
        parseXml(e) {
          this.props = mr(e, this._package.xmlParser);
        }
      }
      class br {
      }
      function vr(i, e) {
        var t = new br(), r = e.element(i, "themeElements");
        for (let n of e.elements(r)) switch (n.localName) {
          case "clrScheme":
            t.colorScheme = kr(n, e);
            break;
          case "fontScheme":
            t.fontScheme = yr(n, e);
            break;
        }
        return t;
      }
      function kr(i, e) {
        var t = { name: e.attr(i, "name"), colors: {} };
        for (let a of e.elements(i)) {
          var r = e.element(a, "srgbClr"), n = e.element(a, "sysClr");
          r ? t.colors[a.localName] = e.attr(r, "val") : n && (t.colors[a.localName] = e.attr(n, "lastClr"));
        }
        return t;
      }
      function yr(i, e) {
        var t = { name: e.attr(i, "name") };
        for (let r of e.elements(i)) switch (r.localName) {
          case "majorFont":
            t.majorFont = ve(r, e);
            break;
          case "minorFont":
            t.minorFont = ve(r, e);
            break;
        }
        return t;
      }
      function ve(i, e) {
        return { latinTypeface: e.elementAttr(i, "latin", "typeface"), eaTypeface: e.elementAttr(i, "ea", "typeface"), csTypeface: e.elementAttr(i, "cs", "typeface") };
      }
      class _r extends St {
        constructor(e, t) {
          super(e, t);
        }
        parseXml(e) {
          this.theme = vr(e, this._package.xmlParser);
        }
      }
      class ke {
      }
      class wr extends ke {
        constructor() {
          super(...arguments), this.type = z.Footnote;
        }
      }
      class Sr extends ke {
        constructor() {
          super(...arguments), this.type = z.Endnote;
        }
      }
      class ye extends St {
        constructor(e, t, r) {
          super(e, t), this._documentParser = r;
        }
      }
      class Cr extends ye {
        constructor(e, t, r) {
          super(e, t, r);
        }
        parseXml(e) {
          this.notes = this._documentParser.parseNotes(e, "footnote", wr);
        }
      }
      class xr extends ye {
        constructor(e, t, r) {
          super(e, t, r);
        }
        parseXml(e) {
          this.notes = this._documentParser.parseNotes(e, "endnote", Sr);
        }
      }
      function Pr(i, e) {
        var t = {};
        for (let r of e.elements(i)) switch (r.localName) {
          case "defaultTabStop":
            t.defaultTabStop = e.lengthAttr(r, "val");
            break;
          case "footnotePr":
            t.footnoteProps = _e(r, e);
            break;
          case "endnotePr":
            t.endnoteProps = _e(r, e);
            break;
          case "autoHyphenation":
            t.autoHyphenation = e.boolAttr(r, "val");
            break;
        }
        return t;
      }
      function _e(i, e) {
        var t = { defaultNoteIds: [] };
        for (let r of e.elements(i)) switch (r.localName) {
          case "numFmt":
            t.nummeringFormat = e.attr(r, "val");
            break;
          case "footnote":
          case "endnote":
            t.defaultNoteIds.push(e.attr(r, "id"));
            break;
        }
        return t;
      }
      class Er extends St {
        constructor(e, t) {
          super(e, t);
        }
        parseXml(e) {
          this.settings = Pr(e, this._package.xmlParser);
        }
      }
      function Ar(i, e) {
        return e.elements(i, "property").map((t) => {
          const r = t.firstChild;
          return { formatId: e.attr(t, "fmtid"), name: e.attr(t, "name"), type: r.nodeName, value: r.textContent };
        });
      }
      class Nr extends St {
        parseXml(e) {
          this.props = Ar(e, this._package.xmlParser);
        }
      }
      class Br extends St {
        constructor(e, t, r) {
          super(e, t), this._documentParser = r;
        }
        parseXml(e) {
          this.comments = this._documentParser.parseComments(e), this.commentMap = Nt(this.comments, (t) => t.id);
        }
      }
      class zr extends St {
        constructor(e, t) {
          super(e, t), this.comments = [];
        }
        parseXml(e) {
          const t = this._package.xmlParser;
          for (let r of t.elements(e, "commentEx")) this.comments.push({ paraId: t.attr(r, "paraId"), paraIdParent: t.attr(r, "paraIdParent"), done: t.boolAttr(r, "done") });
          this.commentMap = Nt(this.comments, (r) => r.paraId);
        }
      }
      const Rr = [{ type: ot.OfficeDocument, target: "word/document.xml" }, { type: ot.ExtendedProperties, target: "docProps/app.xml" }, { type: ot.CoreProperties, target: "docProps/core.xml" }, { type: ot.CustomProperties, target: "docProps/custom.xml" }];
      class ae {
        constructor() {
          this.parts = [], this.partsMap = {};
        }
        static load(e, t, r) {
          return wt(this, null, function* () {
            var n = new ae();
            return n._options = r, n._parser = t, n._package = yield re.load(e, r), n.rels = yield n._package.loadRelationships(), yield Promise.all(Rr.map((a) => {
              var o;
              const l = (o = n.rels.find((u) => u.type === a.type)) != null ? o : a;
              return n.loadRelationshipPart(l.target, l.type);
            })), n;
          });
        }
        save(e = "blob") {
          return this._package.save(e);
        }
        loadRelationshipPart(e, t) {
          return wt(this, null, function* () {
            var n;
            if (this.partsMap[e]) return this.partsMap[e];
            if (!this._package.get(e)) return null;
            let r = null;
            switch (t) {
              case ot.OfficeDocument:
                this.documentPart = r = new Ge(this._package, e, this._parser);
                break;
              case ot.FontTable:
                this.fontTablePart = r = new We(this._package, e);
                break;
              case ot.Numbering:
                this.numberingPart = r = new or(this._package, e, this._parser);
                break;
              case ot.Styles:
                this.stylesPart = r = new lr(this._package, e, this._parser);
                break;
              case ot.Theme:
                this.themePart = r = new _r(this._package, e);
                break;
              case ot.Footnotes:
                this.footnotesPart = r = new Cr(this._package, e, this._parser);
                break;
              case ot.Endnotes:
                this.endnotesPart = r = new xr(this._package, e, this._parser);
                break;
              case ot.Footer:
                r = new dr(this._package, e, this._parser);
                break;
              case ot.Header:
                r = new ur(this._package, e, this._parser);
                break;
              case ot.CoreProperties:
                this.corePropsPart = r = new gr(this._package, e);
                break;
              case ot.ExtendedProperties:
                this.extendedPropsPart = r = new pr(this._package, e);
                break;
              case ot.CustomProperties:
                r = new Nr(this._package, e);
                break;
              case ot.Settings:
                this.settingsPart = r = new Er(this._package, e);
                break;
              case ot.Comments:
                this.commentsPart = r = new Br(this._package, e, this._parser);
                break;
              case ot.CommentsExtended:
                this.commentsExtendedPart = r = new zr(this._package, e);
                break;
            }
            if (r == null) return Promise.resolve(null);
            if (this.partsMap[e] = r, this.parts.push(r), yield r.load(), ((n = r.rels) == null ? void 0 : n.length) > 0) {
              const [a] = Zt(r.path);
              yield Promise.all(r.rels.map((l) => this.loadRelationshipPart(te(l.target, a), l.type)));
            }
            return r;
          });
        }
        loadDocumentImage(e, t) {
          return wt(this, null, function* () {
            const r = yield this.loadResource(t != null ? t : this.documentPart, e, "blob");
            return this.blobToURL(r);
          });
        }
        loadNumberingImage(e) {
          return wt(this, null, function* () {
            const t = yield this.loadResource(this.numberingPart, e, "blob");
            return this.blobToURL(t);
          });
        }
        loadFont(e, t) {
          return wt(this, null, function* () {
            const r = yield this.loadResource(this.fontTablePart, e, "uint8array");
            return r && this.blobToURL(new Blob([Mr(r, t)]));
          });
        }
        blobToURL(e) {
          return e ? this._options.useBase64URL ? Ze(e) : URL.createObjectURL(e) : null;
        }
        findPartByRelId(e, t = null) {
          var a;
          var r = ((a = t.rels) != null ? a : this.rels).find((l) => l.id == e);
          const n = t ? Zt(t.path)[0] : "";
          return r ? this.partsMap[te(r.target, n)] : null;
        }
        getPathById(e, t) {
          const r = e.rels.find((a) => a.id == t), [n] = Zt(e.path);
          return r ? te(r.target, n) : null;
        }
        loadResource(e, t, r) {
          const n = this.getPathById(e, t);
          return n ? this._package.load(n, r) : Promise.resolve(null);
        }
      }
      function Mr(i, e) {
        const r = e.replace(/{|}|-/g, ""), n = new Array(16);
        for (let a = 0; a < 16; a++) n[16 - a - 1] = parseInt(r.substr(a * 2, 2), 16);
        for (let a = 0; a < 32; a++) i[a] = i[a] ^ n[a % 16];
        return i;
      }
      function Tr(i, e) {
        return { type: z.BookmarkStart, id: e.attr(i, "id"), name: e.attr(i, "name"), colFirst: e.intAttr(i, "colFirst"), colLast: e.intAttr(i, "colLast") };
      }
      function Ir(i, e) {
        return { type: z.BookmarkEnd, id: e.attr(i, "id") };
      }
      class Or extends zt {
        constructor() {
          super(...arguments), this.type = z.VmlElement, this.attrs = {};
        }
      }
      function we(i, e) {
        var t = new Or();
        switch (i.localName) {
          case "rect":
            t.tagName = "rect", Object.assign(t.attrs, { width: "100%", height: "100%" });
            break;
          case "oval":
            t.tagName = "ellipse", Object.assign(t.attrs, { cx: "50%", cy: "50%", rx: "50%", ry: "50%" });
            break;
          case "line":
            t.tagName = "line";
            break;
          case "shape":
            t.tagName = "g";
            break;
          case "textbox":
            t.tagName = "foreignObject", Object.assign(t.attrs, { width: "100%", height: "100%" });
            break;
          default:
            return null;
        }
        for (const r of P.attrs(i)) switch (r.localName) {
          case "style":
            t.cssStyleText = r.value;
            break;
          case "fillcolor":
            t.attrs.fill = r.value;
            break;
          case "from":
            const [n, a] = Se(r.value);
            Object.assign(t.attrs, { x1: n, y1: a });
            break;
          case "to":
            const [l, o] = Se(r.value);
            Object.assign(t.attrs, { x2: l, y2: o });
            break;
        }
        for (const r of P.elements(i)) switch (r.localName) {
          case "stroke":
            Object.assign(t.attrs, Fr(r));
            break;
          case "fill":
            Object.assign(t.attrs, Dr());
            break;
          case "imagedata":
            t.tagName = "image", Object.assign(t.attrs, { width: "100%", height: "100%" }), t.imageHref = { id: P.attr(r, "id"), title: P.attr(r, "title") };
            break;
          case "txbxContent":
            t.children.push(...e.parseBodyElements(r));
            break;
          default:
            const n = we(r, e);
            n && t.children.push(n);
            break;
        }
        return t;
      }
      function Fr(i) {
        var e;
        return { stroke: P.attr(i, "color"), "stroke-width": (e = P.lengthAttr(i, "weight", pt.Emu)) != null ? e : "1px" };
      }
      function Dr(i) {
        return {};
      }
      function Se(i) {
        return i.split(",");
      }
      class Lr extends zt {
        constructor() {
          super(...arguments), this.type = z.Comment;
        }
      }
      class jr extends zt {
        constructor(e) {
          super(), this.id = e, this.type = z.CommentReference;
        }
      }
      class Ur extends zt {
        constructor(e) {
          super(), this.id = e, this.type = z.CommentRangeStart;
        }
      }
      class $r extends zt {
        constructor(e) {
          super(), this.id = e, this.type = z.CommentRangeEnd;
        }
      }
      var Gt = { shd: "inherit", color: "black", borderColor: "black", highlight: "transparent" };
      const Wr = [], Ce = { oMath: z.MmlMath, oMathPara: z.MmlMathParagraph, f: z.MmlFraction, func: z.MmlFunction, fName: z.MmlFunctionName, num: z.MmlNumerator, den: z.MmlDenominator, rad: z.MmlRadical, deg: z.MmlDegree, e: z.MmlBase, sSup: z.MmlSuperscript, sSub: z.MmlSubscript, sPre: z.MmlPreSubSuper, sup: z.MmlSuperArgument, sub: z.MmlSubArgument, d: z.MmlDelimiter, nary: z.MmlNary, eqArr: z.MmlEquationArray, lim: z.MmlLimit, limLow: z.MmlLimitLower, m: z.MmlMatrix, mr: z.MmlMatrixRow, box: z.MmlBox, bar: z.MmlBar, groupChr: z.MmlGroupChar };
      class Hr {
        constructor(e) {
          this.options = _t({ ignoreWidth: false, debug: false }, e);
        }
        parseNotes(e, t, r) {
          var n = [];
          for (let a of P.elements(e, t)) {
            const l = new r();
            l.id = P.attr(a, "id"), l.noteType = P.attr(a, "type"), l.children = this.parseBodyElements(a), n.push(l);
          }
          return n;
        }
        parseComments(e) {
          var t = [];
          for (let r of P.elements(e, "comment")) {
            const n = new Lr();
            n.id = P.attr(r, "id"), n.author = P.attr(r, "author"), n.initials = P.attr(r, "initials"), n.date = P.attr(r, "date"), n.children = this.parseBodyElements(r), t.push(n);
          }
          return t;
        }
        parseDocumentFile(e) {
          var t = P.element(e, "body"), r = P.element(e, "background"), n = P.element(t, "sectPr");
          return { type: z.Document, children: this.parseBodyElements(t), props: n ? de(n, P) : {}, cssStyle: r ? this.parseBackground(r) : {} };
        }
        parseBackground(e) {
          var t = {}, r = ut.colorAttr(e, "color");
          return r && (t["background-color"] = r), t;
        }
        parseBodyElements(e) {
          var t = [];
          for (let r of P.elements(e)) switch (r.localName) {
            case "p":
              t.push(this.parseParagraph(r));
              break;
            case "tbl":
              t.push(this.parseTable(r));
              break;
            case "sdt":
              t.push(...this.parseSdt(r, (n) => this.parseBodyElements(n)));
              break;
          }
          return t;
        }
        parseStylesFile(e) {
          var t = [];
          return ut.foreach(e, (r) => {
            switch (r.localName) {
              case "style":
                t.push(this.parseStyle(r));
                break;
              case "docDefaults":
                t.push(this.parseDefaultStyles(r));
                break;
            }
          }), t;
        }
        parseDefaultStyles(e) {
          var t = { id: null, name: null, target: null, basedOn: null, styles: [] };
          return ut.foreach(e, (r) => {
            switch (r.localName) {
              case "rPrDefault":
                var n = P.element(r, "rPr");
                n && t.styles.push({ target: "span", values: this.parseDefaultProperties(n, {}) });
                break;
              case "pPrDefault":
                var a = P.element(r, "pPr");
                a && t.styles.push({ target: "p", values: this.parseDefaultProperties(a, {}) });
                break;
            }
          }), t;
        }
        parseStyle(e) {
          var t = { id: P.attr(e, "styleId"), isDefault: P.boolAttr(e, "default"), name: null, target: null, basedOn: null, styles: [], linked: null };
          switch (P.attr(e, "type")) {
            case "paragraph":
              t.target = "p";
              break;
            case "table":
              t.target = "table";
              break;
            case "character":
              t.target = "span";
              break;
          }
          return ut.foreach(e, (r) => {
            switch (r.localName) {
              case "basedOn":
                t.basedOn = P.attr(r, "val");
                break;
              case "name":
                t.name = P.attr(r, "val");
                break;
              case "link":
                t.linked = P.attr(r, "val");
                break;
              case "next":
                t.next = P.attr(r, "val");
                break;
              case "aliases":
                t.aliases = P.attr(r, "val").split(",");
                break;
              case "pPr":
                t.styles.push({ target: "p", values: this.parseDefaultProperties(r, {}) }), t.paragraphProps = pe(r, P);
                break;
              case "rPr":
                t.styles.push({ target: "span", values: this.parseDefaultProperties(r, {}) }), t.runProps = ne(r, P);
                break;
              case "tblPr":
              case "tcPr":
                t.styles.push({ target: "td", values: this.parseDefaultProperties(r, {}) });
                break;
              case "tblStylePr":
                for (let n of this.parseTableStyle(r)) t.styles.push(n);
                break;
              case "rsid":
              case "qFormat":
              case "hidden":
              case "semiHidden":
              case "unhideWhenUsed":
              case "autoRedefine":
              case "uiPriority":
                break;
              default:
                this.options.debug && console.warn(`DOCX: Unknown style element: ${r.localName}`);
            }
          }), t;
        }
        parseTableStyle(e) {
          var t = [], r = P.attr(e, "type"), n = "", a = "";
          switch (r) {
            case "firstRow":
              a = ".first-row", n = "tr.first-row td";
              break;
            case "lastRow":
              a = ".last-row", n = "tr.last-row td";
              break;
            case "firstCol":
              a = ".first-col", n = "td.first-col";
              break;
            case "lastCol":
              a = ".last-col", n = "td.last-col";
              break;
            case "band1Vert":
              a = ":not(.no-vband)", n = "td.odd-col";
              break;
            case "band2Vert":
              a = ":not(.no-vband)", n = "td.even-col";
              break;
            case "band1Horz":
              a = ":not(.no-hband)", n = "tr.odd-row";
              break;
            case "band2Horz":
              a = ":not(.no-hband)", n = "tr.even-row";
              break;
            default:
              return [];
          }
          return ut.foreach(e, (l) => {
            switch (l.localName) {
              case "pPr":
                t.push({ target: `${n} p`, mod: a, values: this.parseDefaultProperties(l, {}) });
                break;
              case "rPr":
                t.push({ target: `${n} span`, mod: a, values: this.parseDefaultProperties(l, {}) });
                break;
              case "tblPr":
              case "tcPr":
                t.push({ target: n, mod: a, values: this.parseDefaultProperties(l, {}) });
                break;
            }
          }), t;
        }
        parseNumberingFile(e) {
          var t = [], r = {}, n = [];
          return ut.foreach(e, (a) => {
            switch (a.localName) {
              case "abstractNum":
                this.parseAbstractNumbering(a, n).forEach((u) => t.push(u));
                break;
              case "numPicBullet":
                n.push(this.parseNumberingPicBullet(a));
                break;
              case "num":
                var l = P.attr(a, "numId"), o = P.elementAttr(a, "abstractNumId", "val");
                r[o] = l;
                break;
            }
          }), t.forEach((a) => a.id = r[a.id]), t;
        }
        parseNumberingPicBullet(e) {
          var t = P.element(e, "pict"), r = t && P.element(t, "shape"), n = r && P.element(r, "imagedata");
          return n ? { id: P.intAttr(e, "numPicBulletId"), src: P.attr(n, "id"), style: P.attr(r, "style") } : null;
        }
        parseAbstractNumbering(e, t) {
          var r = [], n = P.attr(e, "abstractNumId");
          return ut.foreach(e, (a) => {
            switch (a.localName) {
              case "lvl":
                r.push(this.parseNumberingLevel(n, a, t));
                break;
            }
          }), r;
        }
        parseNumberingLevel(e, t, r) {
          var n = { id: e, level: P.intAttr(t, "ilvl"), start: 1, pStyleName: void 0, pStyle: {}, rStyle: {}, suff: "tab" };
          return ut.foreach(t, (a) => {
            switch (a.localName) {
              case "start":
                n.start = P.intAttr(a, "val");
                break;
              case "pPr":
                this.parseDefaultProperties(a, n.pStyle);
                break;
              case "rPr":
                this.parseDefaultProperties(a, n.rStyle);
                break;
              case "lvlPicBulletId":
                var l = P.intAttr(a, "val");
                n.bullet = r.find((o) => (o == null ? void 0 : o.id) == l);
                break;
              case "lvlText":
                n.levelText = P.attr(a, "val");
                break;
              case "pStyle":
                n.pStyleName = P.attr(a, "val");
                break;
              case "numFmt":
                n.format = P.attr(a, "val");
                break;
              case "suff":
                n.suff = P.attr(a, "val");
                break;
            }
          }), n;
        }
        parseSdt(e, t) {
          const r = P.element(e, "sdtContent");
          return r ? t(r) : [];
        }
        parseInserted(e, t) {
          var r, n;
          return { type: z.Inserted, children: (n = (r = t(e)) == null ? void 0 : r.children) != null ? n : [] };
        }
        parseDeleted(e, t) {
          var r, n;
          return { type: z.Deleted, children: (n = (r = t(e)) == null ? void 0 : r.children) != null ? n : [] };
        }
        parseParagraph(e) {
          var t = { type: z.Paragraph, children: [] };
          for (let r of P.elements(e)) switch (r.localName) {
            case "pPr":
              this.parseParagraphProperties(r, t);
              break;
            case "r":
              t.children.push(this.parseRun(r, t));
              break;
            case "hyperlink":
              t.children.push(this.parseHyperlink(r, t));
              break;
            case "smartTag":
              t.children.push(this.parseSmartTag(r, t));
              break;
            case "bookmarkStart":
              t.children.push(Tr(r, P));
              break;
            case "bookmarkEnd":
              t.children.push(Ir(r, P));
              break;
            case "commentRangeStart":
              t.children.push(new Ur(P.attr(r, "id")));
              break;
            case "commentRangeEnd":
              t.children.push(new $r(P.attr(r, "id")));
              break;
            case "oMath":
            case "oMathPara":
              t.children.push(this.parseMathElement(r));
              break;
            case "sdt":
              t.children.push(...this.parseSdt(r, (n) => this.parseParagraph(n).children));
              break;
            case "ins":
              t.children.push(this.parseInserted(r, (n) => this.parseParagraph(n)));
              break;
            case "del":
              t.children.push(this.parseDeleted(r, (n) => this.parseParagraph(n)));
              break;
          }
          return t;
        }
        parseParagraphProperties(e, t) {
          this.parseDefaultProperties(e, t.cssStyle = {}, null, (r) => {
            if (me(r, t, P)) return true;
            switch (r.localName) {
              case "pStyle":
                t.styleName = P.attr(r, "val");
                break;
              case "cnfStyle":
                t.className = ct.classNameOfCnfStyle(r);
                break;
              case "framePr":
                this.parseFrame(r, t);
                break;
              case "rPr":
                break;
              default:
                return false;
            }
            return true;
          });
        }
        parseFrame(e, t) {
          var r = P.attr(e, "dropCap");
          r == "drop" && (t.cssStyle.float = "left");
        }
        parseHyperlink(e, t) {
          var r = { type: z.Hyperlink, parent: t, children: [] }, n = P.attr(e, "anchor"), a = P.attr(e, "id");
          return n && (r.href = "#" + n), a && (r.id = a), ut.foreach(e, (l) => {
            switch (l.localName) {
              case "r":
                r.children.push(this.parseRun(l, r));
                break;
            }
          }), r;
        }
        parseSmartTag(e, t) {
          var r = { type: z.SmartTag, parent: t, children: [] }, n = P.attr(e, "uri"), a = P.attr(e, "element");
          return n && (r.uri = n), a && (r.element = a), ut.foreach(e, (l) => {
            switch (l.localName) {
              case "r":
                r.children.push(this.parseRun(l, r));
                break;
            }
          }), r;
        }
        parseRun(e, t) {
          var r = { type: z.Run, parent: t, children: [] };
          return ut.foreach(e, (n) => {
            switch (n = this.checkAlternateContent(n), n.localName) {
              case "t":
                r.children.push({ type: z.Text, text: n.textContent });
                break;
              case "delText":
                r.children.push({ type: z.DeletedText, text: n.textContent });
                break;
              case "commentReference":
                r.children.push(new jr(P.attr(n, "id")));
                break;
              case "fldSimple":
                r.children.push({ type: z.SimpleField, instruction: P.attr(n, "instr"), lock: P.boolAttr(n, "lock", false), dirty: P.boolAttr(n, "dirty", false) });
                break;
              case "instrText":
                r.fieldRun = true, r.children.push({ type: z.Instruction, text: n.textContent });
                break;
              case "fldChar":
                r.fieldRun = true, r.children.push({ type: z.ComplexField, charType: P.attr(n, "fldCharType"), lock: P.boolAttr(n, "lock", false), dirty: P.boolAttr(n, "dirty", false) });
                break;
              case "noBreakHyphen":
                r.children.push({ type: z.NoBreakHyphen });
                break;
              case "br":
                r.children.push({ type: z.Break, break: P.attr(n, "type") || "textWrapping" });
                break;
              case "lastRenderedPageBreak":
                r.children.push({ type: z.Break, break: "lastRenderedPageBreak" });
                break;
              case "sym":
                r.children.push({ type: z.Symbol, font: P.attr(n, "font"), char: P.attr(n, "char") });
                break;
              case "tab":
                r.children.push({ type: z.Tab });
                break;
              case "footnoteReference":
                r.children.push({ type: z.FootnoteReference, id: P.attr(n, "id") });
                break;
              case "endnoteReference":
                r.children.push({ type: z.EndnoteReference, id: P.attr(n, "id") });
                break;
              case "drawing":
                let a = this.parseDrawing(n);
                a && (r.children = [a]);
                break;
              case "pict":
                r.children.push(this.parseVmlPicture(n));
                break;
              case "rPr":
                this.parseRunProperties(n, r);
                break;
            }
          }), r;
        }
        parseMathElement(e) {
          const t = `${e.localName}Pr`, r = { type: Ce[e.localName], children: [] };
          for (const a of P.elements(e)) if (Ce[a.localName]) r.children.push(this.parseMathElement(a));
          else if (a.localName == "r") {
            var n = this.parseRun(a);
            n.type = z.MmlRun, r.children.push(n);
          } else a.localName == t && (r.props = this.parseMathProperies(a));
          return r;
        }
        parseMathProperies(e) {
          const t = {};
          for (const r of P.elements(e)) switch (r.localName) {
            case "chr":
              t.char = P.attr(r, "val");
              break;
            case "vertJc":
              t.verticalJustification = P.attr(r, "val");
              break;
            case "pos":
              t.position = P.attr(r, "val");
              break;
            case "degHide":
              t.hideDegree = P.boolAttr(r, "val");
              break;
            case "begChr":
              t.beginChar = P.attr(r, "val");
              break;
            case "endChr":
              t.endChar = P.attr(r, "val");
              break;
          }
          return t;
        }
        parseRunProperties(e, t) {
          this.parseDefaultProperties(e, t.cssStyle = {}, null, (r) => {
            switch (r.localName) {
              case "rStyle":
                t.styleName = P.attr(r, "val");
                break;
              case "vertAlign":
                t.verticalAlign = ct.valueOfVertAlign(r, true);
                break;
              default:
                return false;
            }
            return true;
          });
        }
        parseVmlPicture(e) {
          const t = { type: z.VmlPicture, children: [] };
          for (const r of P.elements(e)) {
            const n = we(r, this);
            n && t.children.push(n);
          }
          return t;
        }
        checkAlternateContent(e) {
          var a;
          if (e.localName != "AlternateContent") return e;
          var t = P.element(e, "Choice");
          if (t) {
            var r = P.attr(t, "Requires"), n = e.lookupNamespaceURI(r);
            if (Wr.includes(n)) return t.firstElementChild;
          }
          return (a = P.element(e, "Fallback")) == null ? void 0 : a.firstElementChild;
        }
        parseDrawing(e) {
          for (var t of P.elements(e)) switch (t.localName) {
            case "inline":
            case "anchor":
              return this.parseDrawingWrapper(t);
          }
        }
        parseDrawingWrapper(e) {
          var w;
          var t = { type: z.Drawing, children: [], cssStyle: {} }, r = e.localName == "anchor";
          let n = null, a = P.boolAttr(e, "simplePos");
          P.boolAttr(e, "behindDoc");
          let l = { relative: "page", align: "left", offset: "0" }, o = { relative: "page", align: "top", offset: "0" };
          for (var u of P.elements(e)) switch (u.localName) {
            case "simplePos":
              a && (l.offset = P.lengthAttr(u, "x", pt.Emu), o.offset = P.lengthAttr(u, "y", pt.Emu));
              break;
            case "extent":
              t.cssStyle.width = P.lengthAttr(u, "cx", pt.Emu), t.cssStyle.height = P.lengthAttr(u, "cy", pt.Emu);
              break;
            case "positionH":
            case "positionV":
              if (!a) {
                let d = u.localName == "positionH" ? l : o;
                var b = P.element(u, "align"), y = P.element(u, "posOffset");
                d.relative = (w = P.attr(u, "relativeFrom")) != null ? w : d.relative, b && (d.align = b.textContent), y && (d.offset = ut.sizeValue(y, pt.Emu));
              }
              break;
            case "wrapTopAndBottom":
              n = "wrapTopAndBottom";
              break;
            case "wrapNone":
              n = "wrapNone";
              break;
            case "graphic":
              var _ = this.parseGraphic(u);
              _ && t.children.push(_);
              break;
          }
          return n == "wrapTopAndBottom" ? (t.cssStyle.display = "block", l.align && (t.cssStyle["text-align"] = l.align, t.cssStyle.width = "100%")) : n == "wrapNone" ? (t.cssStyle.display = "block", t.cssStyle.position = "relative", t.cssStyle.width = "0px", t.cssStyle.height = "0px", l.offset && (t.cssStyle.left = l.offset), o.offset && (t.cssStyle.top = o.offset)) : r && (l.align == "left" || l.align == "right") && (t.cssStyle.float = l.align), t;
        }
        parseGraphic(e) {
          var t = P.element(e, "graphicData");
          for (let r of P.elements(t)) switch (r.localName) {
            case "pic":
              return this.parsePicture(r);
          }
          return null;
        }
        parsePicture(e) {
          var t = { type: z.Image, src: "", cssStyle: {} }, r = P.element(e, "blipFill"), n = P.element(r, "blip");
          t.src = P.attr(n, "embed");
          var a = P.element(e, "spPr"), l = P.element(a, "xfrm");
          t.cssStyle.position = "relative";
          for (var o of P.elements(l)) switch (o.localName) {
            case "ext":
              t.cssStyle.width = P.lengthAttr(o, "cx", pt.Emu), t.cssStyle.height = P.lengthAttr(o, "cy", pt.Emu);
              break;
            case "off":
              t.cssStyle.left = P.lengthAttr(o, "x", pt.Emu), t.cssStyle.top = P.lengthAttr(o, "y", pt.Emu);
              break;
          }
          return t;
        }
        parseTable(e) {
          var t = { type: z.Table, children: [] };
          return ut.foreach(e, (r) => {
            switch (r.localName) {
              case "tr":
                t.children.push(this.parseTableRow(r));
                break;
              case "tblGrid":
                t.columns = this.parseTableColumns(r);
                break;
              case "tblPr":
                this.parseTableProperties(r, t);
                break;
            }
          }), t;
        }
        parseTableColumns(e) {
          var t = [];
          return ut.foreach(e, (r) => {
            switch (r.localName) {
              case "gridCol":
                t.push({ width: P.lengthAttr(r, "w") });
                break;
            }
          }), t;
        }
        parseTableProperties(e, t) {
          switch (t.cssStyle = {}, t.cellStyle = {}, this.parseDefaultProperties(e, t.cssStyle, t.cellStyle, (r) => {
            switch (r.localName) {
              case "tblStyle":
                t.styleName = P.attr(r, "val");
                break;
              case "tblLook":
                t.className = ct.classNameOftblLook(r);
                break;
              case "tblpPr":
                this.parseTablePosition(r, t);
                break;
              case "tblStyleColBandSize":
                t.colBandSize = P.intAttr(r, "val");
                break;
              case "tblStyleRowBandSize":
                t.rowBandSize = P.intAttr(r, "val");
                break;
              default:
                return false;
            }
            return true;
          }), t.cssStyle["text-align"]) {
            case "center":
              delete t.cssStyle["text-align"], t.cssStyle["margin-left"] = "auto", t.cssStyle["margin-right"] = "auto";
              break;
            case "right":
              delete t.cssStyle["text-align"], t.cssStyle["margin-left"] = "auto";
              break;
          }
        }
        parseTablePosition(e, t) {
          var r = P.lengthAttr(e, "topFromText"), n = P.lengthAttr(e, "bottomFromText"), a = P.lengthAttr(e, "rightFromText"), l = P.lengthAttr(e, "leftFromText");
          t.cssStyle.float = "left", t.cssStyle["margin-bottom"] = ct.addSize(t.cssStyle["margin-bottom"], n), t.cssStyle["margin-left"] = ct.addSize(t.cssStyle["margin-left"], l), t.cssStyle["margin-right"] = ct.addSize(t.cssStyle["margin-right"], a), t.cssStyle["margin-top"] = ct.addSize(t.cssStyle["margin-top"], r);
        }
        parseTableRow(e) {
          var t = { type: z.Row, children: [] };
          return ut.foreach(e, (r) => {
            switch (r.localName) {
              case "tc":
                t.children.push(this.parseTableCell(r));
                break;
              case "trPr":
                this.parseTableRowProperties(r, t);
                break;
            }
          }), t;
        }
        parseTableRowProperties(e, t) {
          t.cssStyle = this.parseDefaultProperties(e, {}, null, (r) => {
            switch (r.localName) {
              case "cnfStyle":
                t.className = ct.classNameOfCnfStyle(r);
                break;
              case "tblHeader":
                t.isHeader = P.boolAttr(r, "val");
                break;
              default:
                return false;
            }
            return true;
          });
        }
        parseTableCell(e) {
          var t = { type: z.Cell, children: [] };
          return ut.foreach(e, (r) => {
            switch (r.localName) {
              case "tbl":
                t.children.push(this.parseTable(r));
                break;
              case "p":
                t.children.push(this.parseParagraph(r));
                break;
              case "tcPr":
                this.parseTableCellProperties(r, t);
                break;
            }
          }), t;
        }
        parseTableCellProperties(e, t) {
          t.cssStyle = this.parseDefaultProperties(e, {}, null, (r) => {
            var n;
            switch (r.localName) {
              case "gridSpan":
                t.span = P.intAttr(r, "val", null);
                break;
              case "vMerge":
                t.verticalMerge = (n = P.attr(r, "val")) != null ? n : "continue";
                break;
              case "cnfStyle":
                t.className = ct.classNameOfCnfStyle(r);
                break;
              default:
                return false;
            }
            return true;
          });
        }
        parseDefaultProperties(e, t = null, r = null, n = null) {
          return t = t || {}, ut.foreach(e, (a) => {
            if (!(n != null && n(a))) switch (a.localName) {
              case "jc":
                t["text-align"] = ct.valueOfJc(a);
                break;
              case "textAlignment":
                t["vertical-align"] = ct.valueOfTextAlignment(a);
                break;
              case "color":
                t.color = ut.colorAttr(a, "val", null, Gt.color);
                break;
              case "sz":
                t["font-size"] = t["min-height"] = P.lengthAttr(a, "val", pt.FontSize);
                break;
              case "shd":
                t["background-color"] = ut.colorAttr(a, "fill", null, Gt.shd);
                break;
              case "highlight":
                t["background-color"] = ut.colorAttr(a, "val", null, Gt.highlight);
                break;
              case "vertAlign":
                break;
              case "position":
                t.verticalAlign = P.lengthAttr(a, "val", pt.FontSize);
                break;
              case "tcW":
                if (this.options.ignoreWidth) break;
              case "tblW":
                t.width = ct.valueOfSize(a, "w");
                break;
              case "trHeight":
                this.parseTrHeight(a, t);
                break;
              case "strike":
                t["text-decoration"] = P.boolAttr(a, "val", true) ? "line-through" : "none";
                break;
              case "b":
                t["font-weight"] = P.boolAttr(a, "val", true) ? "bold" : "normal";
                break;
              case "i":
                t["font-style"] = P.boolAttr(a, "val", true) ? "italic" : "normal";
                break;
              case "caps":
                t["text-transform"] = P.boolAttr(a, "val", true) ? "uppercase" : "none";
                break;
              case "smallCaps":
                t["font-variant"] = P.boolAttr(a, "val", true) ? "small-caps" : "none";
                break;
              case "u":
                this.parseUnderline(a, t);
                break;
              case "ind":
              case "tblInd":
                this.parseIndentation(a, t);
                break;
              case "rFonts":
                this.parseFont(a, t);
                break;
              case "tblBorders":
                this.parseBorderProperties(a, r || t);
                break;
              case "tblCellSpacing":
                t["border-spacing"] = ct.valueOfMargin(a), t["border-collapse"] = "separate";
                break;
              case "pBdr":
                this.parseBorderProperties(a, t);
                break;
              case "bdr":
                t.border = ct.valueOfBorder(a);
                break;
              case "tcBorders":
                this.parseBorderProperties(a, t);
                break;
              case "vanish":
                P.boolAttr(a, "val", true) && (t.display = "none");
                break;
              case "kern":
                break;
              case "noWrap":
                break;
              case "tblCellMar":
              case "tcMar":
                this.parseMarginProperties(a, r || t);
                break;
              case "tblLayout":
                t["table-layout"] = ct.valueOfTblLayout(a);
                break;
              case "vAlign":
                t["vertical-align"] = ct.valueOfTextAlignment(a);
                break;
              case "spacing":
                e.localName == "pPr" && this.parseSpacing(a, t);
                break;
              case "wordWrap":
                P.boolAttr(a, "val") && (t["overflow-wrap"] = "break-word");
                break;
              case "suppressAutoHyphens":
                t.hyphens = P.boolAttr(a, "val", true) ? "none" : "auto";
                break;
              case "lang":
                t.$lang = P.attr(a, "val");
                break;
              case "bCs":
              case "iCs":
              case "szCs":
              case "tabs":
              case "outlineLvl":
              case "contextualSpacing":
              case "tblStyleColBandSize":
              case "tblStyleRowBandSize":
              case "webHidden":
              case "pageBreakBefore":
              case "suppressLineNumbers":
              case "keepLines":
              case "keepNext":
              case "widowControl":
              case "bidi":
              case "rtl":
              case "noProof":
                break;
              default:
                this.options.debug && console.warn(`DOCX: Unknown document element: ${e.localName}.${a.localName}`);
                break;
            }
          }), t;
        }
        parseUnderline(e, t) {
          var r = P.attr(e, "val");
          if (r != null) {
            switch (r) {
              case "dash":
              case "dashDotDotHeavy":
              case "dashDotHeavy":
              case "dashedHeavy":
              case "dashLong":
              case "dashLongHeavy":
              case "dotDash":
              case "dotDotDash":
                t["text-decoration"] = "underline dashed";
                break;
              case "dotted":
              case "dottedHeavy":
                t["text-decoration"] = "underline dotted";
                break;
              case "double":
                t["text-decoration"] = "underline double";
                break;
              case "single":
              case "thick":
                t["text-decoration"] = "underline";
                break;
              case "wave":
              case "wavyDouble":
              case "wavyHeavy":
                t["text-decoration"] = "underline wavy";
                break;
              case "words":
                t["text-decoration"] = "underline";
                break;
              case "none":
                t["text-decoration"] = "none";
                break;
            }
            var n = ut.colorAttr(e, "color");
            n && (t["text-decoration-color"] = n);
          }
        }
        parseFont(e, t) {
          var r = P.attr(e, "ascii"), n = ct.themeValue(e, "asciiTheme"), a = [r, n].filter((l) => l).join(", ");
          a.length > 0 && (t["font-family"] = a);
        }
        parseIndentation(e, t) {
          var r = P.lengthAttr(e, "firstLine"), n = P.lengthAttr(e, "hanging"), a = P.lengthAttr(e, "left"), l = P.lengthAttr(e, "start"), o = P.lengthAttr(e, "right"), u = P.lengthAttr(e, "end");
          r && (t["text-indent"] = r), n && (t["text-indent"] = `-${n}`), (a || l) && (t["margin-left"] = a || l), (o || u) && (t["margin-right"] = o || u);
        }
        parseSpacing(e, t) {
          var r = P.lengthAttr(e, "before"), n = P.lengthAttr(e, "after"), a = P.intAttr(e, "line", null), l = P.attr(e, "lineRule");
          if (r && (t["margin-top"] = r), n && (t["margin-bottom"] = n), a !== null) switch (l) {
            case "auto":
              t["line-height"] = `${(a / 240).toFixed(2)}`;
              break;
            case "atLeast":
              t["line-height"] = `calc(100% + ${a / 20}pt)`;
              break;
            default:
              t["line-height"] = t["min-height"] = `${a / 20}pt`;
              break;
          }
        }
        parseMarginProperties(e, t) {
          ut.foreach(e, (r) => {
            switch (r.localName) {
              case "left":
                t["padding-left"] = ct.valueOfMargin(r);
                break;
              case "right":
                t["padding-right"] = ct.valueOfMargin(r);
                break;
              case "top":
                t["padding-top"] = ct.valueOfMargin(r);
                break;
              case "bottom":
                t["padding-bottom"] = ct.valueOfMargin(r);
                break;
            }
          });
        }
        parseTrHeight(e, t) {
          switch (P.attr(e, "hRule")) {
            case "exact":
              t.height = P.lengthAttr(e, "val");
              break;
            case "atLeast":
            default:
              t.height = P.lengthAttr(e, "val");
              break;
          }
        }
        parseBorderProperties(e, t) {
          ut.foreach(e, (r) => {
            switch (r.localName) {
              case "start":
              case "left":
                t["border-left"] = ct.valueOfBorder(r);
                break;
              case "end":
              case "right":
                t["border-right"] = ct.valueOfBorder(r);
                break;
              case "top":
                t["border-top"] = ct.valueOfBorder(r);
                break;
              case "bottom":
                t["border-bottom"] = ct.valueOfBorder(r);
                break;
            }
          });
        }
      }
      const Zr = ["black", "blue", "cyan", "darkBlue", "darkCyan", "darkGray", "darkGreen", "darkMagenta", "darkRed", "darkYellow", "green", "lightGray", "magenta", "none", "red", "white", "yellow"];
      class ut {
        static foreach(e, t) {
          for (var r = 0; r < e.childNodes.length; r++) {
            let n = e.childNodes[r];
            n.nodeType == Node.ELEMENT_NODE && t(n);
          }
        }
        static colorAttr(e, t, r = null, n = "black") {
          var a = P.attr(e, t);
          if (a) return a == "auto" ? n : Zr.includes(a) ? a : `#${a}`;
          var l = P.attr(e, "themeColor");
          return l ? `var(--docx-${l}-color)` : r;
        }
        static sizeValue(e, t = pt.Dxa) {
          return le(e.textContent, t);
        }
      }
      class ct {
        static themeValue(e, t) {
          var r = P.attr(e, t);
          return r ? `var(--docx-${r}-font)` : null;
        }
        static valueOfSize(e, t) {
          var r = pt.Dxa;
          switch (P.attr(e, "type")) {
            case "dxa":
              break;
            case "pct":
              r = pt.Percent;
              break;
            case "auto":
              return "auto";
          }
          return P.lengthAttr(e, t, r);
        }
        static valueOfMargin(e) {
          return P.lengthAttr(e, "w");
        }
        static valueOfBorder(e) {
          var t = P.attr(e, "val");
          if (t == "nil") return "none";
          var r = ut.colorAttr(e, "color"), n = P.lengthAttr(e, "sz", pt.Border);
          return `${n} solid ${r == "auto" ? Gt.borderColor : r}`;
        }
        static valueOfTblLayout(e) {
          var t = P.attr(e, "val");
          return t == "fixed" ? "fixed" : "auto";
        }
        static classNameOfCnfStyle(e) {
          const t = P.attr(e, "val");
          return ["first-row", "last-row", "first-col", "last-col", "odd-col", "even-col", "odd-row", "even-row", "ne-cell", "nw-cell", "se-cell", "sw-cell"].filter((n, a) => t[a] == "1").join(" ");
        }
        static valueOfJc(e) {
          var t = P.attr(e, "val");
          switch (t) {
            case "start":
            case "left":
              return "left";
            case "center":
              return "center";
            case "end":
            case "right":
              return "right";
            case "both":
              return "justify";
          }
          return t;
        }
        static valueOfVertAlign(e, t = false) {
          var r = P.attr(e, "val");
          switch (r) {
            case "subscript":
              return "sub";
            case "superscript":
              return t ? "sup" : "super";
          }
          return t ? null : r;
        }
        static valueOfTextAlignment(e) {
          var t = P.attr(e, "val");
          switch (t) {
            case "auto":
            case "baseline":
              return "baseline";
            case "top":
              return "top";
            case "center":
              return "middle";
            case "bottom":
              return "bottom";
          }
          return t;
        }
        static addSize(e, t) {
          return e == null ? t : t == null ? e : `calc(${e} + ${t})`;
        }
        static classNameOftblLook(e) {
          const t = P.hexAttr(e, "val", 0);
          let r = "";
          return (P.boolAttr(e, "firstRow") || t & 32) && (r += " first-row"), (P.boolAttr(e, "lastRow") || t & 64) && (r += " last-row"), (P.boolAttr(e, "firstColumn") || t & 128) && (r += " first-col"), (P.boolAttr(e, "lastColumn") || t & 256) && (r += " last-col"), (P.boolAttr(e, "noHBand") || t & 512) && (r += " no-hband"), (P.boolAttr(e, "noVBand") || t & 1024) && (r += " no-vband"), r.trim();
        }
      }
      const xe = { pos: 0, leader: "none", style: "left" }, Vr = 50;
      function Xr(i = document.body) {
        const e = document.createElement("div");
        e.style.width = "100pt", i.appendChild(e);
        const t = 100 / e.offsetWidth;
        return i.removeChild(e), t;
      }
      function Gr(i, e, t, r = 72 / 96) {
        const n = i.closest("p"), a = i.getBoundingClientRect(), l = n.getBoundingClientRect(), o = getComputedStyle(n), u = (e == null ? void 0 : e.length) > 0 ? e.map((v) => ({ pos: Pe(v.position), leader: v.leader, style: v.style })).sort((v, C) => v.pos - C.pos) : [xe], b = u[u.length - 1], y = l.width * r, _ = Pe(t);
        let w = b.pos + _;
        if (w < y) for (; w < y && u.length < Vr; w += _) u.push(Wt(_t({}, xe), { pos: w }));
        const d = parseFloat(o.marginLeft), k = l.left + d, h = (a.left - k) * r, m = u.find((v) => v.style != "clear" && v.pos > h);
        if (m == null) return;
        let f = 1;
        if (m.style == "right" || m.style == "center") {
          const v = Array.from(n.querySelectorAll(`.${i.className}`)), C = v.indexOf(i) + 1, A = document.createRange();
          A.setStart(i, 1), C < v.length ? A.setEndBefore(v[C]) : A.setEndAfter(n);
          const E = m.style == "center" ? 0.5 : 1, D = A.getBoundingClientRect(), I = D.left + E * D.width - (l.left - d);
          f = m.pos - I * r;
        } else f = m.pos - h;
        switch (i.innerHTML = "&nbsp;", i.style.textDecoration = "inherit", i.style.wordSpacing = `${f.toFixed(0)}pt`, m.leader) {
          case "dot":
          case "middleDot":
            i.style.textDecoration = "underline", i.style.textDecorationStyle = "dotted";
            break;
          case "hyphen":
          case "heavy":
          case "underscore":
            i.style.textDecoration = "underline";
            break;
        }
      }
      function Pe(i) {
        return parseFloat(i);
      }
      const nt = { svg: "http://www.w3.org/2000/svg", mathML: "http://www.w3.org/1998/Math/MathML" };
      class qr {
        constructor(e) {
          this.htmlDocument = e, this.className = "docx", this.styleMap = {}, this.currentPart = null, this.tableVerticalMerges = [], this.currentVerticalMerge = null, this.tableCellPositions = [], this.currentCellPosition = null, this.footnoteMap = {}, this.endnoteMap = {}, this.currentEndnoteIds = [], this.usedHederFooterParts = [], this.currentTabs = [], this.tabsTimeout = 0, this.commentMap = {}, this.tasks = [], this.postRenderTasks = [], this.createElement = Rt;
        }
        render(e, t, r = null, n) {
          var l;
          this.document = e, this.options = n, this.className = n.className, this.rootSelector = n.inWrapper ? `.${this.className}-wrapper` : ":root", this.styleMap = null, this.tasks = [], this.options.renderComments && globalThis.Highlight && (this.commentHighlight = new Highlight()), r = r || t, Ae(r), Ae(t), Lt(r, "docxjs library predefined styles"), r.appendChild(this.renderDefaultStyle()), e.themePart && (Lt(r, "docxjs document theme values"), this.renderTheme(e.themePart, r)), e.stylesPart != null && (this.styleMap = this.processStyles(e.stylesPart.styles), Lt(r, "docxjs document styles"), r.appendChild(this.renderStyles(e.stylesPart.styles))), e.numberingPart && (this.prodessNumberings(e.numberingPart.domNumberings), Lt(r, "docxjs document numbering styles"), r.appendChild(this.renderNumbering(e.numberingPart.domNumberings, r))), e.footnotesPart && (this.footnoteMap = Nt(e.footnotesPart.notes, (o) => o.id)), e.endnotesPart && (this.endnoteMap = Nt(e.endnotesPart.notes, (o) => o.id)), e.settingsPart && (this.defaultTabSize = (l = e.settingsPart.settings) == null ? void 0 : l.defaultTabStop), !n.ignoreFonts && e.fontTablePart && this.renderFontTable(e.fontTablePart, r);
          var a = this.renderSections(e.documentPart.body);
          this.options.inWrapper ? t.appendChild(this.renderWrapper(a)) : se(t, a), this.commentHighlight && n.renderComments && CSS.highlights.set(`${this.className}-comments`, this.commentHighlight), this.refreshTabStops(), this.postRenderTasks.forEach((o) => o());
        }
        renderTheme(e, t) {
          var o, u;
          const r = {}, n = (o = e.theme) == null ? void 0 : o.fontScheme;
          n && (n.majorFont && (r["--docx-majorHAnsi-font"] = n.majorFont.latinTypeface), n.minorFont && (r["--docx-minorHAnsi-font"] = n.minorFont.latinTypeface));
          const a = (u = e.theme) == null ? void 0 : u.colorScheme;
          if (a) for (let [b, y] of Object.entries(a.colors)) r[`--docx-${b}-color`] = `#${y}`;
          const l = this.styleToString(`.${this.className}`, r);
          t.appendChild(Mt(l));
        }
        renderFontTable(e, t) {
          for (let r of e.fonts) for (let n of r.embedFontRefs) this.tasks.push(this.document.loadFont(n.id, n.key).then((a) => {
            const l = { "font-family": r.name, src: `url(${a})` };
            (n.type == "bold" || n.type == "boldItalic") && (l["font-weight"] = "bold"), (n.type == "italic" || n.type == "boldItalic") && (l["font-style"] = "italic"), Lt(t, `docxjs ${r.name} font`);
            const o = this.styleToString("@font-face", l);
            t.appendChild(Mt(o)), this.refreshTabStops();
          }));
        }
        processStyleName(e) {
          return e ? `${this.className}_${He(e)}` : this.className;
        }
        processStyles(e) {
          const t = Nt(e.filter((n) => n.id != null), (n) => n.id);
          for (const n of e.filter((a) => a.basedOn)) {
            var r = t[n.basedOn];
            if (r) {
              n.paragraphProps = Vt(n.paragraphProps, r.paragraphProps), n.runProps = Vt(n.runProps, r.runProps);
              for (const a of r.styles) {
                const l = n.styles.find((o) => o.target == a.target);
                l ? this.copyStyleProperties(a.values, l.values) : n.styles.push(Wt(_t({}, a), { values: _t({}, a.values) }));
              }
            } else this.options.debug && console.warn(`Can't find base style ${n.basedOn}`);
          }
          for (let n of e) n.cssName = this.processStyleName(n.id);
          return t;
        }
        prodessNumberings(e) {
          var t;
          for (let r of e.filter((n) => n.pStyleName)) {
            const n = this.findStyle(r.pStyleName);
            (t = n == null ? void 0 : n.paragraphProps) != null && t.numbering && (n.paragraphProps.numbering.level = r.level);
          }
        }
        processElement(e) {
          if (e.children) for (var t of e.children) t.parent = e, t.type == z.Table ? this.processTable(t) : this.processElement(t);
        }
        processTable(e) {
          for (var t of e.children) for (var r of t.children) r.cssStyle = this.copyStyleProperties(e.cellStyle, r.cssStyle, ["border-left", "border-right", "border-top", "border-bottom", "padding-left", "padding-right", "padding-top", "padding-bottom"]), this.processElement(r);
        }
        copyStyleProperties(e, t, r = null) {
          if (!e) return t;
          t == null && (t = {}), r == null && (r = Object.getOwnPropertyNames(e));
          for (var n of r) e.hasOwnProperty(n) && !t.hasOwnProperty(n) && (t[n] = e[n]);
          return t;
        }
        createPageElement(e, t) {
          var r = this.createElement("section", { className: e });
          return t && (t.pageMargins && (r.style.paddingLeft = t.pageMargins.left, r.style.paddingRight = t.pageMargins.right, r.style.paddingTop = t.pageMargins.top, r.style.paddingBottom = t.pageMargins.bottom), t.pageSize && (this.options.ignoreWidth || (r.style.width = t.pageSize.width), this.options.ignoreHeight || (r.style.minHeight = t.pageSize.height))), r;
        }
        createSectionContent(e) {
          var t = this.createElement("article");
          return e.columns && e.columns.numberOfColumns && (t.style.columnCount = `${e.columns.numberOfColumns}`, t.style.columnGap = e.columns.space, e.columns.separator && (t.style.columnRule = "1px solid black")), t;
        }
        renderSections(e) {
          const t = [];
          this.processElement(e);
          const r = this.splitBySection(e.children, e.props), n = this.groupByPageBreaks(r);
          let a = null;
          for (let o = 0, u = n.length; o < u; o++) {
            this.currentFootnoteIds = [];
            let y = n[o][0].sectProps;
            const _ = this.createPageElement(this.className, y);
            this.renderStyleValues(e.cssStyle, _), this.options.renderHeaders && this.renderHeaderFooter(y.headerRefs, y, t.length, a != y, _);
            for (const w of n[o]) {
              var l = this.createSectionContent(w.sectProps);
              this.renderElements(w.elements, l), _.appendChild(l), y = w.sectProps;
            }
            this.options.renderFootnotes && this.renderNotes(this.currentFootnoteIds, this.footnoteMap, _), this.options.renderEndnotes && o == u - 1 && this.renderNotes(this.currentEndnoteIds, this.endnoteMap, _), this.options.renderFooters && this.renderHeaderFooter(y.footerRefs, y, t.length, a != y, _), t.push(_), a = y;
          }
          return t;
        }
        renderHeaderFooter(e, t, r, n, a) {
          var u, b;
          if (e) {
            var l = (b = (u = t.titlePage && n ? e.find((y) => y.type == "first") : null) != null ? u : r % 2 == 1 ? e.find((y) => y.type == "even") : null) != null ? b : e.find((y) => y.type == "default"), o = l && this.document.findPartByRelId(l.id, this.document.documentPart);
            if (o) {
              this.currentPart = o, this.usedHederFooterParts.includes(o.path) || (this.processElement(o.rootElement), this.usedHederFooterParts.push(o.path));
              const [y] = this.renderElements([o.rootElement], a);
              t != null && t.pageMargins && (o.rootElement.type === z.Header ? (y.style.marginTop = `calc(${t.pageMargins.header} - ${t.pageMargins.top})`, y.style.minHeight = `calc(${t.pageMargins.top} - ${t.pageMargins.header})`) : o.rootElement.type === z.Footer && (y.style.marginBottom = `calc(${t.pageMargins.footer} - ${t.pageMargins.bottom})`, y.style.minHeight = `calc(${t.pageMargins.bottom} - ${t.pageMargins.footer})`)), this.currentPart = null;
            }
          }
        }
        isPageBreakElement(e) {
          return e.type != z.Break ? false : e.break == "lastRenderedPageBreak" ? !this.options.ignoreLastRenderedPageBreak : e.break == "page";
        }
        isPageBreakSection(e, t) {
          var r, n, a, l, o, u;
          return !e || !t ? false : ((r = e.pageSize) == null ? void 0 : r.orientation) != ((n = t.pageSize) == null ? void 0 : n.orientation) || ((a = e.pageSize) == null ? void 0 : a.width) != ((l = t.pageSize) == null ? void 0 : l.width) || ((o = e.pageSize) == null ? void 0 : o.height) != ((u = t.pageSize) == null ? void 0 : u.height);
        }
        splitBySection(e, t) {
          var _;
          var r = { sectProps: null, elements: [], pageBreak: false }, n = [r];
          for (let w of e) {
            if (w.type == z.Paragraph) {
              const d = this.findStyle(w.styleName);
              (_ = d == null ? void 0 : d.paragraphProps) != null && _.pageBreakBefore && (r.sectProps = a, r.pageBreak = true, r = { sectProps: null, elements: [], pageBreak: false }, n.push(r));
            }
            if (r.elements.push(w), w.type == z.Paragraph) {
              const d = w;
              var a = d.sectionProps, l = -1, o = -1;
              if (this.options.breakPages && d.children && (l = d.children.findIndex((k) => {
                var h, m;
                return o = (m = (h = k.children) == null ? void 0 : h.findIndex(this.isPageBreakElement.bind(this))) != null ? m : -1, o != -1;
              })), (a || l != -1) && (r.sectProps = a, r.pageBreak = l != -1, r = { sectProps: null, elements: [], pageBreak: false }, n.push(r)), l != -1) {
                let k = d.children[l], h = o < k.children.length - 1;
                if (l < d.children.length - 1 || h) {
                  var u = w.children, b = Wt(_t({}, w), { children: u.slice(l) });
                  if (w.children = u.slice(0, l), r.elements.push(b), h) {
                    let m = k.children, f = Wt(_t({}, k), { children: m.slice(0, o) });
                    w.children.push(f), k.children = m.slice(o);
                  }
                }
              }
            }
          }
          let y = null;
          for (let w = n.length - 1; w >= 0; w--) n[w].sectProps == null ? n[w].sectProps = y != null ? y : t : y = n[w].sectProps;
          return n;
        }
        groupByPageBreaks(e) {
          let t = [], r;
          const n = [t];
          for (let a of e) t.push(a), (this.options.ignoreLastRenderedPageBreak || a.pageBreak || this.isPageBreakSection(r, a.sectProps)) && n.push(t = []), r = a.sectProps;
          return n.filter((a) => a.length > 0);
        }
        renderWrapper(e) {
          return this.createElement("div", { className: `${this.className}-wrapper` }, e);
        }
        renderDefaultStyle() {
          var e = this.className, t = `
.${e}-wrapper { background: gray; padding: 30px; padding-bottom: 0px; display: flex; flex-flow: column; align-items: center; } 
.${e}-wrapper>section.${e} { background: white; box-shadow: 0 0 10px rgba(0, 0, 0, 0.5); margin-bottom: 30px; }
.${e} { color: black; hyphens: auto; text-underline-position: from-font; }
section.${e} { box-sizing: border-box; display: flex; flex-flow: column nowrap; position: relative; overflow: hidden; }
section.${e}>article { margin-bottom: auto; z-index: 1; }
section.${e}>footer { z-index: 1; }
.${e} table { border-collapse: collapse; }
.${e} table td, .${e} table th { vertical-align: top; }
.${e} p { margin: 0pt; min-height: 1em; }
.${e} span { white-space: pre-wrap; overflow-wrap: break-word; }
.${e} a { color: inherit; text-decoration: inherit; }
.${e} svg { fill: transparent; }
`;
          return this.options.renderComments && (t += `
.${e}-comment-ref { cursor: default; }
.${e}-comment-popover { display: none; z-index: 1000; padding: 0.5rem; background: white; position: absolute; box-shadow: 0 0 0.25rem rgba(0, 0, 0, 0.25); width: 30ch; }
.${e}-comment-ref:hover~.${e}-comment-popover { display: block; }
.${e}-comment-author,.${e}-comment-date { font-size: 0.875rem; color: #888; }
`), Mt(t);
        }
        renderNumbering(e, t) {
          var r = "", n = [];
          for (var a of e) {
            var l = `p.${this.numberingClass(a.id, a.level)}`, o = "none";
            if (a.bullet) {
              let u = `--${this.className}-${a.bullet.src}`.toLowerCase();
              r += this.styleToString(`${l}:before`, { content: "' '", display: "inline-block", background: `var(${u})` }, a.bullet.style), this.tasks.push(this.document.loadNumberingImage(a.bullet.src).then((b) => {
                var y = `${this.rootSelector} { ${u}: url(${b}) }`;
                t.appendChild(Mt(y));
              }));
            } else if (a.levelText) {
              let u = this.numberingCounter(a.id, a.level);
              const b = u + " " + (a.start - 1);
              a.level > 0 && (r += this.styleToString(`p.${this.numberingClass(a.id, a.level - 1)}`, { "counter-reset": b })), n.push(b), r += this.styleToString(`${l}:before`, _t({ content: this.levelTextToContent(a.levelText, a.suff, a.id, this.numFormatToCssValue(a.format)), "counter-increment": u }, a.rStyle));
            } else o = this.numFormatToCssValue(a.format);
            r += this.styleToString(l, _t({ display: "list-item", "list-style-position": "inside", "list-style-type": o }, a.pStyle));
          }
          return n.length > 0 && (r += this.styleToString(this.rootSelector, { "counter-reset": n.join(" ") })), Mt(r);
        }
        renderStyles(e) {
          var u;
          var t = "";
          const r = this.styleMap, n = Nt(e.filter((b) => b.isDefault), (b) => b.target);
          for (const b of e) {
            var a = b.styles;
            if (b.linked) {
              var l = b.linked && r[b.linked];
              l ? a = a.concat(l.styles) : this.options.debug && console.warn(`Can't find linked style ${b.linked}`);
            }
            for (const y of a) {
              var o = `${(u = b.target) != null ? u : ""}.${b.cssName}`;
              b.target != y.target && (o += ` ${y.target}`), n[b.target] == b && (o = `.${this.className} ${b.target}, ` + o), t += this.styleToString(o, y.values);
            }
          }
          return Mt(t);
        }
        renderNotes(e, t, r) {
          var n = e.map((l) => t[l]).filter((l) => l);
          if (n.length > 0) {
            var a = this.createElement("ol", null, this.renderElements(n));
            r.appendChild(a);
          }
        }
        renderElement(e) {
          switch (e.type) {
            case z.Paragraph:
              return this.renderParagraph(e);
            case z.BookmarkStart:
              return this.renderBookmarkStart(e);
            case z.BookmarkEnd:
              return null;
            case z.Run:
              return this.renderRun(e);
            case z.Table:
              return this.renderTable(e);
            case z.Row:
              return this.renderTableRow(e);
            case z.Cell:
              return this.renderTableCell(e);
            case z.Hyperlink:
              return this.renderHyperlink(e);
            case z.SmartTag:
              return this.renderSmartTag(e);
            case z.Drawing:
              return this.renderDrawing(e);
            case z.Image:
              return this.renderImage(e);
            case z.Text:
              return this.renderText(e);
            case z.Text:
              return this.renderText(e);
            case z.DeletedText:
              return this.renderDeletedText(e);
            case z.Tab:
              return this.renderTab(e);
            case z.Symbol:
              return this.renderSymbol(e);
            case z.Break:
              return this.renderBreak(e);
            case z.Footer:
              return this.renderContainer(e, "footer");
            case z.Header:
              return this.renderContainer(e, "header");
            case z.Footnote:
            case z.Endnote:
              return this.renderContainer(e, "li");
            case z.FootnoteReference:
              return this.renderFootnoteReference(e);
            case z.EndnoteReference:
              return this.renderEndnoteReference(e);
            case z.NoBreakHyphen:
              return this.createElement("wbr");
            case z.VmlPicture:
              return this.renderVmlPicture(e);
            case z.VmlElement:
              return this.renderVmlElement(e);
            case z.MmlMath:
              return this.renderContainerNS(e, nt.mathML, "math", { xmlns: nt.mathML });
            case z.MmlMathParagraph:
              return this.renderContainer(e, "span");
            case z.MmlFraction:
              return this.renderContainerNS(e, nt.mathML, "mfrac");
            case z.MmlBase:
              return this.renderContainerNS(e, nt.mathML, e.parent.type == z.MmlMatrixRow ? "mtd" : "mrow");
            case z.MmlNumerator:
            case z.MmlDenominator:
            case z.MmlFunction:
            case z.MmlLimit:
            case z.MmlBox:
              return this.renderContainerNS(e, nt.mathML, "mrow");
            case z.MmlGroupChar:
              return this.renderMmlGroupChar(e);
            case z.MmlLimitLower:
              return this.renderContainerNS(e, nt.mathML, "munder");
            case z.MmlMatrix:
              return this.renderContainerNS(e, nt.mathML, "mtable");
            case z.MmlMatrixRow:
              return this.renderContainerNS(e, nt.mathML, "mtr");
            case z.MmlRadical:
              return this.renderMmlRadical(e);
            case z.MmlSuperscript:
              return this.renderContainerNS(e, nt.mathML, "msup");
            case z.MmlSubscript:
              return this.renderContainerNS(e, nt.mathML, "msub");
            case z.MmlDegree:
            case z.MmlSuperArgument:
            case z.MmlSubArgument:
              return this.renderContainerNS(e, nt.mathML, "mn");
            case z.MmlFunctionName:
              return this.renderContainerNS(e, nt.mathML, "ms");
            case z.MmlDelimiter:
              return this.renderMmlDelimiter(e);
            case z.MmlRun:
              return this.renderMmlRun(e);
            case z.MmlNary:
              return this.renderMmlNary(e);
            case z.MmlPreSubSuper:
              return this.renderMmlPreSubSuper(e);
            case z.MmlBar:
              return this.renderMmlBar(e);
            case z.MmlEquationArray:
              return this.renderMllList(e);
            case z.Inserted:
              return this.renderInserted(e);
            case z.Deleted:
              return this.renderDeleted(e);
            case z.CommentRangeStart:
              return this.renderCommentRangeStart(e);
            case z.CommentRangeEnd:
              return this.renderCommentRangeEnd(e);
            case z.CommentReference:
              return this.renderCommentReference(e);
          }
          return null;
        }
        renderChildren(e, t) {
          return this.renderElements(e.children, t);
        }
        renderElements(e, t) {
          if (e == null) return null;
          var r = e.flatMap((n) => this.renderElement(n)).filter((n) => n != null);
          return t && se(t, r), r;
        }
        renderContainer(e, t, r) {
          return this.createElement(t, r, this.renderChildren(e));
        }
        renderContainerNS(e, t, r, n) {
          return ht(t, r, n, this.renderChildren(e));
        }
        renderParagraph(e) {
          var a, l, o, u;
          var t = this.createElement("p");
          const r = this.findStyle(e.styleName);
          (l = e.tabs) != null || (e.tabs = (a = r == null ? void 0 : r.paragraphProps) == null ? void 0 : a.tabs), this.renderClass(e, t), this.renderChildren(e, t), this.renderStyleValues(e.cssStyle, t), this.renderCommonProperties(t.style, e);
          const n = (u = e.numbering) != null ? u : (o = r == null ? void 0 : r.paragraphProps) == null ? void 0 : o.numbering;
          return n && t.classList.add(this.numberingClass(n.id, n.level)), t;
        }
        renderRunProperties(e, t) {
          this.renderCommonProperties(e, t);
        }
        renderCommonProperties(e, t) {
          t != null && (t.color && (e.color = t.color), t.fontSize && (e["font-size"] = t.fontSize));
        }
        renderHyperlink(e) {
          var t = this.createElement("a");
          if (this.renderChildren(e, t), this.renderStyleValues(e.cssStyle, t), e.href) t.href = e.href;
          else if (e.id) {
            const r = this.document.documentPart.rels.find((n) => n.id == e.id && n.targetMode === "External");
            t.href = r == null ? void 0 : r.target;
          }
          return t;
        }
        renderSmartTag(e) {
          var t = this.createElement("span");
          return this.renderChildren(e, t), t;
        }
        renderCommentRangeStart(e) {
          var n;
          if (!this.options.renderComments) return null;
          const t = new Range();
          (n = this.commentHighlight) == null || n.add(t);
          const r = this.htmlDocument.createComment(`start of comment #${e.id}`);
          return this.later(() => t.setStart(r, 0)), this.commentMap[e.id] = t, r;
        }
        renderCommentRangeEnd(e) {
          if (!this.options.renderComments) return null;
          const t = this.commentMap[e.id], r = this.htmlDocument.createComment(`end of comment #${e.id}`);
          return this.later(() => t == null ? void 0 : t.setEnd(r, 0)), r;
        }
        renderCommentReference(e) {
          var l;
          if (!this.options.renderComments) return null;
          var t = (l = this.document.commentsPart) == null ? void 0 : l.commentMap[e.id];
          if (!t) return null;
          const r = new DocumentFragment(), n = Rt("span", { className: `${this.className}-comment-ref` }, ["💬"]), a = Rt("div", { className: `${this.className}-comment-popover` });
          return this.renderCommentContent(t, a), r.appendChild(this.htmlDocument.createComment(`comment #${t.id} by ${t.author} on ${t.date}`)), r.appendChild(n), r.appendChild(a), r;
        }
        renderCommentContent(e, t) {
          t.appendChild(Rt("div", { className: `${this.className}-comment-author` }, [e.author])), t.appendChild(Rt("div", { className: `${this.className}-comment-date` }, [new Date(e.date).toLocaleString()])), this.renderChildren(e, t);
        }
        renderDrawing(e) {
          var t = this.createElement("div");
          return t.style.display = "inline-block", t.style.position = "relative", t.style.textIndent = "0px", this.renderChildren(e, t), this.renderStyleValues(e.cssStyle, t), t;
        }
        renderImage(e) {
          let t = this.createElement("img");
          return this.renderStyleValues(e.cssStyle, t), this.document && this.tasks.push(this.document.loadDocumentImage(e.src, this.currentPart).then((r) => {
            t.src = r;
          })), t;
        }
        renderText(e) {
          return this.htmlDocument.createTextNode(e.text);
        }
        renderDeletedText(e) {
          return this.options.renderEndnotes ? this.htmlDocument.createTextNode(e.text) : null;
        }
        renderBreak(e) {
          return e.break == "textWrapping" ? this.createElement("br") : null;
        }
        renderInserted(e) {
          return this.options.renderChanges ? this.renderContainer(e, "ins") : this.renderChildren(e);
        }
        renderDeleted(e) {
          return this.options.renderChanges ? this.renderContainer(e, "del") : null;
        }
        renderSymbol(e) {
          var t = this.createElement("span");
          return t.style.fontFamily = e.font, t.innerHTML = `&#x${e.char};`, t;
        }
        renderFootnoteReference(e) {
          var t = this.createElement("sup");
          return this.currentFootnoteIds.push(e.id), t.textContent = `${this.currentFootnoteIds.length}`, t;
        }
        renderEndnoteReference(e) {
          var t = this.createElement("sup");
          return this.currentEndnoteIds.push(e.id), t.textContent = `${this.currentEndnoteIds.length}`, t;
        }
        renderTab(e) {
          var n;
          var t = this.createElement("span");
          if (t.innerHTML = "&emsp;", this.options.experimental) {
            t.className = this.tabStopClass();
            var r = (n = Kr(e, z.Paragraph)) == null ? void 0 : n.tabs;
            this.currentTabs.push({ stops: r, span: t });
          }
          return t;
        }
        renderBookmarkStart(e) {
          var t = this.createElement("span");
          return t.id = e.name, t;
        }
        renderRun(e) {
          if (e.fieldRun) return null;
          const t = this.createElement("span");
          if (e.id && (t.id = e.id), this.renderClass(e, t), this.renderStyleValues(e.cssStyle, t), e.verticalAlign) {
            const r = this.createElement(e.verticalAlign);
            this.renderChildren(e, r), t.appendChild(r);
          } else this.renderChildren(e, t);
          return t;
        }
        renderTable(e) {
          let t = this.createElement("table");
          return this.tableCellPositions.push(this.currentCellPosition), this.tableVerticalMerges.push(this.currentVerticalMerge), this.currentVerticalMerge = {}, this.currentCellPosition = { col: 0, row: 0 }, e.columns && t.appendChild(this.renderTableColumns(e.columns)), this.renderClass(e, t), this.renderChildren(e, t), this.renderStyleValues(e.cssStyle, t), this.currentVerticalMerge = this.tableVerticalMerges.pop(), this.currentCellPosition = this.tableCellPositions.pop(), t;
        }
        renderTableColumns(e) {
          let t = this.createElement("colgroup");
          for (let r of e) {
            let n = this.createElement("col");
            r.width && (n.style.width = r.width), t.appendChild(n);
          }
          return t;
        }
        renderTableRow(e) {
          let t = this.createElement("tr");
          return this.currentCellPosition.col = 0, this.renderClass(e, t), this.renderChildren(e, t), this.renderStyleValues(e.cssStyle, t), this.currentCellPosition.row++, t;
        }
        renderTableCell(e) {
          let t = this.createElement("td");
          const r = this.currentCellPosition.col;
          return e.verticalMerge ? e.verticalMerge == "restart" ? (this.currentVerticalMerge[r] = t, t.rowSpan = 1) : this.currentVerticalMerge[r] && (this.currentVerticalMerge[r].rowSpan += 1, t.style.display = "none") : this.currentVerticalMerge[r] = null, this.renderClass(e, t), this.renderChildren(e, t), this.renderStyleValues(e.cssStyle, t), e.span && (t.colSpan = e.span), this.currentCellPosition.col += t.colSpan, t;
        }
        renderVmlPicture(e) {
          var t = Rt("div");
          return this.renderChildren(e, t), t;
        }
        renderVmlElement(e) {
          var n, a;
          var t = Ee("svg");
          t.setAttribute("style", e.cssStyleText);
          const r = this.renderVmlChildElement(e);
          return (n = e.imageHref) != null && n.id && this.tasks.push((a = this.document) == null ? void 0 : a.loadDocumentImage(e.imageHref.id, this.currentPart).then((l) => r.setAttribute("href", l))), t.appendChild(r), requestAnimationFrame(() => {
            const l = t.firstElementChild.getBBox();
            t.setAttribute("width", `${Math.ceil(l.x + l.width)}`), t.setAttribute("height", `${Math.ceil(l.y + l.height)}`);
          }), t;
        }
        renderVmlChildElement(e) {
          const t = Ee(e.tagName);
          Object.entries(e.attrs).forEach(([r, n]) => t.setAttribute(r, n));
          for (let r of e.children) r.type == z.VmlElement ? t.appendChild(this.renderVmlChildElement(r)) : t.appendChild(...Ft(this.renderElement(r)));
          return t;
        }
        renderMmlRadical(e) {
          var n;
          const t = e.children.find((a) => a.type == z.MmlBase);
          if ((n = e.props) != null && n.hideDegree) return ht(nt.mathML, "msqrt", null, this.renderElements([t]));
          const r = e.children.find((a) => a.type == z.MmlDegree);
          return ht(nt.mathML, "mroot", null, this.renderElements([t, r]));
        }
        renderMmlDelimiter(e) {
          var r, n;
          const t = [];
          return t.push(ht(nt.mathML, "mo", null, [(r = e.props.beginChar) != null ? r : "("])), t.push(...this.renderElements(e.children)), t.push(ht(nt.mathML, "mo", null, [(n = e.props.endChar) != null ? n : ")"])), ht(nt.mathML, "mrow", null, t);
        }
        renderMmlNary(e) {
          var b, y;
          const t = [], r = Nt(e.children, (_) => _.type), n = r[z.MmlSuperArgument], a = r[z.MmlSubArgument], l = n ? ht(nt.mathML, "mo", null, Ft(this.renderElement(n))) : null, o = a ? ht(nt.mathML, "mo", null, Ft(this.renderElement(a))) : null, u = ht(nt.mathML, "mo", null, [(y = (b = e.props) == null ? void 0 : b.char) != null ? y : "∫"]);
          return l || o ? t.push(ht(nt.mathML, "munderover", null, [u, o, l])) : l ? t.push(ht(nt.mathML, "mover", null, [u, l])) : o ? t.push(ht(nt.mathML, "munder", null, [u, o])) : t.push(u), t.push(...this.renderElements(r[z.MmlBase].children)), ht(nt.mathML, "mrow", null, t);
        }
        renderMmlPreSubSuper(e) {
          const t = [], r = Nt(e.children, (b) => b.type), n = r[z.MmlSuperArgument], a = r[z.MmlSubArgument], l = n ? ht(nt.mathML, "mo", null, Ft(this.renderElement(n))) : null, o = a ? ht(nt.mathML, "mo", null, Ft(this.renderElement(a))) : null, u = ht(nt.mathML, "mo", null);
          return t.push(ht(nt.mathML, "msubsup", null, [u, o, l])), t.push(...this.renderElements(r[z.MmlBase].children)), ht(nt.mathML, "mrow", null, t);
        }
        renderMmlGroupChar(e) {
          const t = e.props.verticalJustification === "bot" ? "mover" : "munder", r = this.renderContainerNS(e, nt.mathML, t);
          return e.props.char && r.appendChild(ht(nt.mathML, "mo", null, [e.props.char])), r;
        }
        renderMmlBar(e) {
          const t = this.renderContainerNS(e, nt.mathML, "mrow");
          switch (e.props.position) {
            case "top":
              t.style.textDecoration = "overline";
              break;
            case "bottom":
              t.style.textDecoration = "underline";
              break;
          }
          return t;
        }
        renderMmlRun(e) {
          const t = ht(nt.mathML, "ms");
          return this.renderClass(e, t), this.renderStyleValues(e.cssStyle, t), this.renderChildren(e, t), t;
        }
        renderMllList(e) {
          const t = ht(nt.mathML, "mtable");
          this.renderClass(e, t), this.renderStyleValues(e.cssStyle, t), this.renderChildren(e);
          for (let r of this.renderChildren(e)) t.appendChild(ht(nt.mathML, "mtr", null, [ht(nt.mathML, "mtd", null, [r])]));
          return t;
        }
        renderStyleValues(e, t) {
          for (let r in e) r.startsWith("$") ? t.setAttribute(r.slice(1), e[r]) : t.style[r] = e[r];
        }
        renderClass(e, t) {
          e.className && (t.className = e.className), e.styleName && t.classList.add(this.processStyleName(e.styleName));
        }
        findStyle(e) {
          var t;
          return e && ((t = this.styleMap) == null ? void 0 : t[e]);
        }
        numberingClass(e, t) {
          return `${this.className}-num-${e}-${t}`;
        }
        tabStopClass() {
          return `${this.className}-tab-stop`;
        }
        styleToString(e, t, r = null) {
          let n = `${e} {\r
`;
          for (const a in t) a.startsWith("$") || (n += `  ${a}: ${t[a]};\r
`);
          return r && (n += r), n + `}\r
`;
        }
        numberingCounter(e, t) {
          return `${this.className}-num-${e}-${t}`;
        }
        levelTextToContent(e, t, r, n) {
          var o;
          const a = { tab: "\\9", space: "\\a0" };
          var l = e.replace(/%\d*/g, (u) => {
            let b = parseInt(u.substring(1), 10) - 1;
            return `"counter(${this.numberingCounter(r, b)}, ${n})"`;
          });
          return `"${l}${(o = a[t]) != null ? o : ""}"`;
        }
        numFormatToCssValue(e) {
          var r;
          var t = { none: "none", bullet: "disc", decimal: "decimal", lowerLetter: "lower-alpha", upperLetter: "upper-alpha", lowerRoman: "lower-roman", upperRoman: "upper-roman", decimalZero: "decimal-leading-zero", aiueo: "katakana", aiueoFullWidth: "katakana", chineseCounting: "simp-chinese-informal", chineseCountingThousand: "simp-chinese-informal", chineseLegalSimplified: "simp-chinese-formal", chosung: "hangul-consonant", ideographDigital: "cjk-ideographic", ideographTraditional: "cjk-heavenly-stem", ideographLegalTraditional: "trad-chinese-formal", ideographZodiac: "cjk-earthly-branch", iroha: "katakana-iroha", irohaFullWidth: "katakana-iroha", japaneseCounting: "japanese-informal", japaneseDigitalTenThousand: "cjk-decimal", japaneseLegal: "japanese-formal", thaiNumbers: "thai", koreanCounting: "korean-hangul-formal", koreanDigital: "korean-hangul-formal", koreanDigital2: "korean-hanja-informal", hebrew1: "hebrew", hebrew2: "hebrew", hindiNumbers: "devanagari", ganada: "hangul", taiwaneseCounting: "cjk-ideographic", taiwaneseCountingThousand: "cjk-ideographic", taiwaneseDigital: "cjk-decimal" };
          return (r = t[e]) != null ? r : e;
        }
        refreshTabStops() {
          this.options.experimental && (clearTimeout(this.tabsTimeout), this.tabsTimeout = setTimeout(() => {
            const e = Xr();
            for (let t of this.currentTabs) Gr(t.span, t.stops, this.defaultTabSize, e);
          }, 500));
        }
        later(e) {
          this.postRenderTasks.push(e);
        }
      }
      function Rt(i, e, t) {
        return ht(void 0, i, e, t);
      }
      function Ee(i, e, t) {
        return ht(nt.svg, i, e, t);
      }
      function ht(i, e, t, r) {
        var n = i ? document.createElementNS(i, e) : document.createElement(e);
        return Object.assign(n, t), r && se(n, r), n;
      }
      function Ae(i) {
        i.innerHTML = "";
      }
      function se(i, e) {
        e.forEach((t) => i.appendChild(Ve(t) ? document.createTextNode(t) : t));
      }
      function Mt(i) {
        return Rt("style", { innerHTML: i });
      }
      function Lt(i, e) {
        i.appendChild(document.createComment(e));
      }
      function Kr(i, e) {
        for (var t = i.parent; t != null && t.type != e; ) t = t.parent;
        return t;
      }
      const Ne = { ignoreHeight: false, ignoreWidth: false, ignoreFonts: false, breakPages: true, debug: false, experimental: false, className: "docx", inWrapper: true, trimXmlDeclaration: true, ignoreLastRenderedPageBreak: true, renderHeaders: true, renderFooters: true, renderFootnotes: true, renderEndnotes: true, useBase64URL: false, renderChanges: false, renderComments: false };
      function Yr(i, e) {
        const t = _t(_t({}, Ne), e);
        return ae.load(i, new Hr(t), t);
      }
      function Jr(i, e, t, r) {
        return wt(this, null, function* () {
          const n = _t(_t({}, Ne), r), a = new qr(window.document);
          return a.render(i, e, t, n), Promise.allSettled(a.tasks);
        });
      }
      function Qr(i, e, t, r) {
        return wt(this, null, function* () {
          const n = yield Yr(i, r);
          return yield Jr(n, e, t, r), n;
        });
      }
      const tn = { ignoreLastRenderedPageBreak: false };
      function en(i, e = {}) {
        return typeof i == "string" ? rn(i, e) : Promise.resolve(i);
      }
      function rn(i, e) {
        return fetch(i, e).then((t) => t.status !== 200 ? Promise.reject(t) : t);
      }
      function nn(i) {
        return wt(this, null, function* () {
          let e;
          return i instanceof Blob ? e = i : i instanceof Response ? e = yield i.blob() : i instanceof ArrayBuffer && (e = new Blob([i])), e;
        });
      }
      function an(i, e, t = {}) {
        if (!i) return e.innerHTML = "", Promise.resolve();
        let r;
        return i instanceof Blob ? r = i : i instanceof Response ? r = i.blob() : i instanceof ArrayBuffer && (r = new Blob([i])), Qr(r, e, e, _t(_t({}, tn), t));
      }
      const Tt = { getData: en, render: an, getBlob: nn };
      function sn(i, e) {
        return wt(this, null, function* () {
          e && (e instanceof ArrayBuffer && (e = new Blob([e])), on(i, URL.createObjectURL(e)));
        });
      }
      function on(i, e) {
        let t = document.createElement("a");
        t.download = i, t.style.display = "none", t.href = e, document.body.appendChild(t), t.click(), document.body.removeChild(t);
      }
      const _n = "", ln = (i, e) => {
        const t = i.__vccOpts || i;
        for (const [r, n] of e) t[r] = n;
        return t;
      }, cn = dt.defineComponent({ name: "VueOfficeDocx", props: { src: [String, ArrayBuffer, Blob], requestOptions: { type: Object, default: () => ({}) }, options: { type: Object, default: () => ({}) } }, emits: ["rendered", "error"], setup(i, { emit: e }) {
        const t = dt.ref(null);
        let r = null;
        function n() {
          let l = t.value;
          Tt.getData(i.src, i.requestOptions).then((o) => wt(this, null, function* () {
            r = yield Tt.getBlob(o), Tt.render(r, l, i.options).then(() => {
              e("rendered");
            }).catch((u) => {
              Tt.render("", l, i.options), e("error", u);
            });
          })).catch((o) => {
            Tt.render("", l, i.options), e("error", o);
          });
        }
        dt.onMounted(() => {
          i.src && n();
        }), dt.watch(() => i.src, () => {
          i.src ? n() : Tt.render("", t.value, i.options).then(() => {
            e("rendered");
          });
        });
        function a(l) {
          sn(l || `vue-office-docx-${(/* @__PURE__ */ new Date()).getTime()}.docx`, r);
        }
        return { rootRef: t, save: a };
      } }), hn = { class: "vue-office-docx" }, un = { class: "vue-office-docx-main", ref: "rootRef" };
      function dn(i, e, t, r, n, a) {
        return ft.openBlock(), ft.createElementBlock("div", hn, [ft.createElementVNode("div", un, null, 512)]);
      }
      const qt = ln(cn, [["render", dn]]);
      return qt.install = function(i) {
        i.component(qt.name, qt);
      }, qt;
    });
  }
});
export default require_lib2();
/*! Bundled license information:

@vue-office/docx/lib/index.js:
  (*!
  
      JSZip v3.10.1 - A JavaScript class for generating and reading zip files
      <http://stuartk.com/jszip>
  
      (c) 2009-2016 Stuart Knightley <stuart [at] stuartk.com>
      Dual licenced under the MIT license or GPLv3. See https://raw.github.com/Stuk/jszip/main/LICENSE.markdown.
  
      JSZip uses the library pako released under the MIT license :
      https://github.com/nodeca/pako/blob/main/LICENSE
      *)
*/
//# sourceMappingURL=@vue-office_docx.js.map
