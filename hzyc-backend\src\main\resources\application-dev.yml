#数据源配置
spring:
  data:
    redis:
      ##redis 单机环境配置
      host: 127.0.0.1
      port: 6379
      password:
      database: 0
      ssl:
        enabled: false
      ##redis 集群环境配置
      #cluster:
      #  nodes: 127.0.0.1:7001,127.0.0.1:7002,127.0.0.1:7003
      #  commandTimeout: 5000
  datasource:
    # MySql
    url: ************************************************************************************************************************************************************************************************************************************************************************************************************
    username: mlops
    password: Yueshutech@20240210
    # PostgreSQL
    #url: ********************************************
    #username: postgres
    #password: 123456
    # Oracle
    #url: ******************************************
    #username: BLADEX_BOOT
    #password: BLADEX_BOOT
    # SqlServer
    #url: ********************************************************
    #username: bladex_boot
    #password: bladex_boot
    # DaMeng
    #url: jdbc:dm://127.0.0.1:5236/BLADEX_BOOT?zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8
    #username: BLADEX_BOOT
    #password: BLADEX_BOOT
    # YashanDB
    #url: ***************************************
    #username: BLADEX_BOOT
    #password: BLADEX_BOOT
    # KingbaseES
    #url: **************************************
    #username: kingbase
    #password: 123456

#第三方登陆
social:
  enabled: true
  domain: http://127.0.0.1:2888

#oss默认配置
oss:
  #开启oss配置
  enabled: true
  #开启oss类型
  #minio、s3、qiniu、alioss、huaweiobs、tencentcos
  name: minio
  #租户模式
  tenant-mode: true
  #oss服务地址
  endpoint: http://**************:9000
  #oss转换服务地址，用于内网上传后将返回地址改为转换的外网地址
  #transform-endpoint: http://***********:9000
  #访问key
  access-key: vx8UHwzstj0Ls9qaCwlr
  #密钥key
  secret-key: ArP5P1YuNN4iC0XU10EYaHlKFyWgvTrzVZuklYxS
  #存储桶
  bucket-name: hzyc

#blade配置
blade:
  #分布式锁配置
  lock:
    ##是否启用分布式锁
    enabled: false
    ##redis服务地址
    address: redis://127.0.0.1:6379

#网关配置
gateway:
  #网关服务地址
  url: http://*********** 8443/GatewayMsg/http/api/proxy/invoke
dify:
  api:
    # Dify API服务地址
    url: http://localhost/v1
    # Dify API密钥
    key: app-DrhHZu6RvdPluvcuKhznYCZI
    # 连接超时时间（毫秒）
    connect-timeout: 30000
    # 读取超时时间（毫秒）
    read-timeout: 60000
