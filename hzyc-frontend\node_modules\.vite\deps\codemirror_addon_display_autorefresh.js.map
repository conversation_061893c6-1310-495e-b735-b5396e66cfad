{"version": 3, "sources": ["../../codemirror/addon/display/autorefresh.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"))\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod)\n  else // Plain browser env\n    mod(CodeMirror)\n})(function(CodeMirror) {\n  \"use strict\"\n\n  CodeMirror.defineOption(\"autoRefresh\", false, function(cm, val) {\n    if (cm.state.autoRefresh) {\n      stopListening(cm, cm.state.autoRefresh)\n      cm.state.autoRefresh = null\n    }\n    if (val && cm.display.wrapper.offsetHeight == 0)\n      startListening(cm, cm.state.autoRefresh = {delay: val.delay || 250})\n  })\n\n  function startListening(cm, state) {\n    function check() {\n      if (cm.display.wrapper.offsetHeight) {\n        stopListening(cm, state)\n        if (cm.display.lastWrapHeight != cm.display.wrapper.clientHeight)\n          cm.refresh()\n      } else {\n        state.timeout = setTimeout(check, state.delay)\n      }\n    }\n    state.timeout = setTimeout(check, state.delay)\n    state.hurry = function() {\n      clearTimeout(state.timeout)\n      state.timeout = setTimeout(check, 50)\n    }\n    CodeMirror.on(window, \"mouseup\", state.hurry)\n    CodeMirror.on(window, \"keyup\", state.hurry)\n  }\n\n  function stopListening(_cm, state) {\n    clearTimeout(state.timeout)\n    CodeMirror.off(window, \"mouseup\", state.hurry)\n    CodeMirror.off(window, \"keyup\", state.hurry)\n  }\n});\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACtB;AAEA,MAAAA,YAAW,aAAa,eAAe,OAAO,SAAS,IAAI,KAAK;AAC9D,YAAI,GAAG,MAAM,aAAa;AACxB,wBAAc,IAAI,GAAG,MAAM,WAAW;AACtC,aAAG,MAAM,cAAc;AAAA,QACzB;AACA,YAAI,OAAO,GAAG,QAAQ,QAAQ,gBAAgB;AAC5C,yBAAe,IAAI,GAAG,MAAM,cAAc,EAAC,OAAO,IAAI,SAAS,IAAG,CAAC;AAAA,MACvE,CAAC;AAED,eAAS,eAAe,IAAI,OAAO;AACjC,iBAAS,QAAQ;AACf,cAAI,GAAG,QAAQ,QAAQ,cAAc;AACnC,0BAAc,IAAI,KAAK;AACvB,gBAAI,GAAG,QAAQ,kBAAkB,GAAG,QAAQ,QAAQ;AAClD,iBAAG,QAAQ;AAAA,UACf,OAAO;AACL,kBAAM,UAAU,WAAW,OAAO,MAAM,KAAK;AAAA,UAC/C;AAAA,QACF;AACA,cAAM,UAAU,WAAW,OAAO,MAAM,KAAK;AAC7C,cAAM,QAAQ,WAAW;AACvB,uBAAa,MAAM,OAAO;AAC1B,gBAAM,UAAU,WAAW,OAAO,EAAE;AAAA,QACtC;AACA,QAAAA,YAAW,GAAG,QAAQ,WAAW,MAAM,KAAK;AAC5C,QAAAA,YAAW,GAAG,QAAQ,SAAS,MAAM,KAAK;AAAA,MAC5C;AAEA,eAAS,cAAc,KAAK,OAAO;AACjC,qBAAa,MAAM,OAAO;AAC1B,QAAAA,YAAW,IAAI,QAAQ,WAAW,MAAM,KAAK;AAC7C,QAAAA,YAAW,IAAI,QAAQ,SAAS,MAAM,KAAK;AAAA,MAC7C;AAAA,IACF,CAAC;AAAA;AAAA;", "names": ["CodeMirror"]}