{"version": 3, "sources": ["../../codemirror/addon/fold/foldcode.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  function doFold(cm, pos, options, force) {\n    if (options && options.call) {\n      var finder = options;\n      options = null;\n    } else {\n      var finder = getOption(cm, options, \"rangeFinder\");\n    }\n    if (typeof pos == \"number\") pos = CodeMirror.Pos(pos, 0);\n    var minSize = getOption(cm, options, \"minFoldSize\");\n\n    function getRange(allowFolded) {\n      var range = finder(cm, pos);\n      if (!range || range.to.line - range.from.line < minSize) return null;\n      if (force === \"fold\") return range;\n\n      var marks = cm.findMarksAt(range.from);\n      for (var i = 0; i < marks.length; ++i) {\n        if (marks[i].__isFold) {\n          if (!allowFolded) return null;\n          range.cleared = true;\n          marks[i].clear();\n        }\n      }\n      return range;\n    }\n\n    var range = getRange(true);\n    if (getOption(cm, options, \"scanUp\")) while (!range && pos.line > cm.firstLine()) {\n      pos = CodeMirror.Pos(pos.line - 1, 0);\n      range = getRange(false);\n    }\n    if (!range || range.cleared || force === \"unfold\") return;\n\n    var myWidget = makeWidget(cm, options, range);\n    CodeMirror.on(myWidget, \"mousedown\", function(e) {\n      myRange.clear();\n      CodeMirror.e_preventDefault(e);\n    });\n    var myRange = cm.markText(range.from, range.to, {\n      replacedWith: myWidget,\n      clearOnEnter: getOption(cm, options, \"clearOnEnter\"),\n      __isFold: true\n    });\n    myRange.on(\"clear\", function(from, to) {\n      CodeMirror.signal(cm, \"unfold\", cm, from, to);\n    });\n    CodeMirror.signal(cm, \"fold\", cm, range.from, range.to);\n  }\n\n  function makeWidget(cm, options, range) {\n    var widget = getOption(cm, options, \"widget\");\n\n    if (typeof widget == \"function\") {\n      widget = widget(range.from, range.to);\n    }\n\n    if (typeof widget == \"string\") {\n      var text = document.createTextNode(widget);\n      widget = document.createElement(\"span\");\n      widget.appendChild(text);\n      widget.className = \"CodeMirror-foldmarker\";\n    } else if (widget) {\n      widget = widget.cloneNode(true)\n    }\n    return widget;\n  }\n\n  // Clumsy backwards-compatible interface\n  CodeMirror.newFoldFunction = function(rangeFinder, widget) {\n    return function(cm, pos) { doFold(cm, pos, {rangeFinder: rangeFinder, widget: widget}); };\n  };\n\n  // New-style interface\n  CodeMirror.defineExtension(\"foldCode\", function(pos, options, force) {\n    doFold(this, pos, options, force);\n  });\n\n  CodeMirror.defineExtension(\"isFolded\", function(pos) {\n    var marks = this.findMarksAt(pos);\n    for (var i = 0; i < marks.length; ++i)\n      if (marks[i].__isFold) return true;\n  });\n\n  CodeMirror.commands.toggleFold = function(cm) {\n    cm.foldCode(cm.getCursor());\n  };\n  CodeMirror.commands.fold = function(cm) {\n    cm.foldCode(cm.getCursor(), null, \"fold\");\n  };\n  CodeMirror.commands.unfold = function(cm) {\n    cm.foldCode(cm.getCursor(), { scanUp: false }, \"unfold\");\n  };\n  CodeMirror.commands.foldAll = function(cm) {\n    cm.operation(function() {\n      for (var i = cm.firstLine(), e = cm.lastLine(); i <= e; i++)\n        cm.foldCode(CodeMirror.Pos(i, 0), { scanUp: false }, \"fold\");\n    });\n  };\n  CodeMirror.commands.unfoldAll = function(cm) {\n    cm.operation(function() {\n      for (var i = cm.firstLine(), e = cm.lastLine(); i <= e; i++)\n        cm.foldCode(CodeMirror.Pos(i, 0), { scanUp: false }, \"unfold\");\n    });\n  };\n\n  CodeMirror.registerHelper(\"fold\", \"combine\", function() {\n    var funcs = Array.prototype.slice.call(arguments, 0);\n    return function(cm, start) {\n      for (var i = 0; i < funcs.length; ++i) {\n        var found = funcs[i](cm, start);\n        if (found) return found;\n      }\n    };\n  });\n\n  CodeMirror.registerHelper(\"fold\", \"auto\", function(cm, start) {\n    var helpers = cm.getHelpers(start, \"fold\");\n    for (var i = 0; i < helpers.length; i++) {\n      var cur = helpers[i](cm, start);\n      if (cur) return cur;\n    }\n  });\n\n  var defaultOptions = {\n    rangeFinder: CodeMirror.fold.auto,\n    widget: \"\\u2194\",\n    minFoldSize: 0,\n    scanUp: false,\n    clearOnEnter: true\n  };\n\n  CodeMirror.defineOption(\"foldOptions\", null);\n\n  function getOption(cm, options, name) {\n    if (options && options[name] !== undefined)\n      return options[name];\n    var editorOptions = cm.options.foldOptions;\n    if (editorOptions && editorOptions[name] !== undefined)\n      return editorOptions[name];\n    return defaultOptions[name];\n  }\n\n  CodeMirror.defineExtension(\"foldOption\", function(options, name) {\n    return getOption(this, options, name);\n  });\n});\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACtB;AAEA,eAAS,OAAO,IAAI,KAAK,SAAS,OAAO;AACvC,YAAI,WAAW,QAAQ,MAAM;AAC3B,cAAI,SAAS;AACb,oBAAU;AAAA,QACZ,OAAO;AACL,cAAI,SAAS,UAAU,IAAI,SAAS,aAAa;AAAA,QACnD;AACA,YAAI,OAAO,OAAO,SAAU,OAAMA,YAAW,IAAI,KAAK,CAAC;AACvD,YAAI,UAAU,UAAU,IAAI,SAAS,aAAa;AAElD,iBAAS,SAAS,aAAa;AAC7B,cAAIC,SAAQ,OAAO,IAAI,GAAG;AAC1B,cAAI,CAACA,UAASA,OAAM,GAAG,OAAOA,OAAM,KAAK,OAAO,QAAS,QAAO;AAChE,cAAI,UAAU,OAAQ,QAAOA;AAE7B,cAAI,QAAQ,GAAG,YAAYA,OAAM,IAAI;AACrC,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACrC,gBAAI,MAAM,CAAC,EAAE,UAAU;AACrB,kBAAI,CAAC,YAAa,QAAO;AACzB,cAAAA,OAAM,UAAU;AAChB,oBAAM,CAAC,EAAE,MAAM;AAAA,YACjB;AAAA,UACF;AACA,iBAAOA;AAAA,QACT;AAEA,YAAI,QAAQ,SAAS,IAAI;AACzB,YAAI,UAAU,IAAI,SAAS,QAAQ,EAAG,QAAO,CAAC,SAAS,IAAI,OAAO,GAAG,UAAU,GAAG;AAChF,gBAAMD,YAAW,IAAI,IAAI,OAAO,GAAG,CAAC;AACpC,kBAAQ,SAAS,KAAK;AAAA,QACxB;AACA,YAAI,CAAC,SAAS,MAAM,WAAW,UAAU,SAAU;AAEnD,YAAI,WAAW,WAAW,IAAI,SAAS,KAAK;AAC5C,QAAAA,YAAW,GAAG,UAAU,aAAa,SAAS,GAAG;AAC/C,kBAAQ,MAAM;AACd,UAAAA,YAAW,iBAAiB,CAAC;AAAA,QAC/B,CAAC;AACD,YAAI,UAAU,GAAG,SAAS,MAAM,MAAM,MAAM,IAAI;AAAA,UAC9C,cAAc;AAAA,UACd,cAAc,UAAU,IAAI,SAAS,cAAc;AAAA,UACnD,UAAU;AAAA,QACZ,CAAC;AACD,gBAAQ,GAAG,SAAS,SAAS,MAAM,IAAI;AACrC,UAAAA,YAAW,OAAO,IAAI,UAAU,IAAI,MAAM,EAAE;AAAA,QAC9C,CAAC;AACD,QAAAA,YAAW,OAAO,IAAI,QAAQ,IAAI,MAAM,MAAM,MAAM,EAAE;AAAA,MACxD;AAEA,eAAS,WAAW,IAAI,SAAS,OAAO;AACtC,YAAI,SAAS,UAAU,IAAI,SAAS,QAAQ;AAE5C,YAAI,OAAO,UAAU,YAAY;AAC/B,mBAAS,OAAO,MAAM,MAAM,MAAM,EAAE;AAAA,QACtC;AAEA,YAAI,OAAO,UAAU,UAAU;AAC7B,cAAI,OAAO,SAAS,eAAe,MAAM;AACzC,mBAAS,SAAS,cAAc,MAAM;AACtC,iBAAO,YAAY,IAAI;AACvB,iBAAO,YAAY;AAAA,QACrB,WAAW,QAAQ;AACjB,mBAAS,OAAO,UAAU,IAAI;AAAA,QAChC;AACA,eAAO;AAAA,MACT;AAGA,MAAAA,YAAW,kBAAkB,SAAS,aAAa,QAAQ;AACzD,eAAO,SAAS,IAAI,KAAK;AAAE,iBAAO,IAAI,KAAK,EAAC,aAA0B,OAAc,CAAC;AAAA,QAAG;AAAA,MAC1F;AAGA,MAAAA,YAAW,gBAAgB,YAAY,SAAS,KAAK,SAAS,OAAO;AACnE,eAAO,MAAM,KAAK,SAAS,KAAK;AAAA,MAClC,CAAC;AAED,MAAAA,YAAW,gBAAgB,YAAY,SAAS,KAAK;AACnD,YAAI,QAAQ,KAAK,YAAY,GAAG;AAChC,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE;AAClC,cAAI,MAAM,CAAC,EAAE,SAAU,QAAO;AAAA,MAClC,CAAC;AAED,MAAAA,YAAW,SAAS,aAAa,SAAS,IAAI;AAC5C,WAAG,SAAS,GAAG,UAAU,CAAC;AAAA,MAC5B;AACA,MAAAA,YAAW,SAAS,OAAO,SAAS,IAAI;AACtC,WAAG,SAAS,GAAG,UAAU,GAAG,MAAM,MAAM;AAAA,MAC1C;AACA,MAAAA,YAAW,SAAS,SAAS,SAAS,IAAI;AACxC,WAAG,SAAS,GAAG,UAAU,GAAG,EAAE,QAAQ,MAAM,GAAG,QAAQ;AAAA,MACzD;AACA,MAAAA,YAAW,SAAS,UAAU,SAAS,IAAI;AACzC,WAAG,UAAU,WAAW;AACtB,mBAAS,IAAI,GAAG,UAAU,GAAG,IAAI,GAAG,SAAS,GAAG,KAAK,GAAG;AACtD,eAAG,SAASA,YAAW,IAAI,GAAG,CAAC,GAAG,EAAE,QAAQ,MAAM,GAAG,MAAM;AAAA,QAC/D,CAAC;AAAA,MACH;AACA,MAAAA,YAAW,SAAS,YAAY,SAAS,IAAI;AAC3C,WAAG,UAAU,WAAW;AACtB,mBAAS,IAAI,GAAG,UAAU,GAAG,IAAI,GAAG,SAAS,GAAG,KAAK,GAAG;AACtD,eAAG,SAASA,YAAW,IAAI,GAAG,CAAC,GAAG,EAAE,QAAQ,MAAM,GAAG,QAAQ;AAAA,QACjE,CAAC;AAAA,MACH;AAEA,MAAAA,YAAW,eAAe,QAAQ,WAAW,WAAW;AACtD,YAAI,QAAQ,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AACnD,eAAO,SAAS,IAAI,OAAO;AACzB,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACrC,gBAAI,QAAQ,MAAM,CAAC,EAAE,IAAI,KAAK;AAC9B,gBAAI,MAAO,QAAO;AAAA,UACpB;AAAA,QACF;AAAA,MACF,CAAC;AAED,MAAAA,YAAW,eAAe,QAAQ,QAAQ,SAAS,IAAI,OAAO;AAC5D,YAAI,UAAU,GAAG,WAAW,OAAO,MAAM;AACzC,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,cAAI,MAAM,QAAQ,CAAC,EAAE,IAAI,KAAK;AAC9B,cAAI,IAAK,QAAO;AAAA,QAClB;AAAA,MACF,CAAC;AAED,UAAI,iBAAiB;AAAA,QACnB,aAAaA,YAAW,KAAK;AAAA,QAC7B,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,cAAc;AAAA,MAChB;AAEA,MAAAA,YAAW,aAAa,eAAe,IAAI;AAE3C,eAAS,UAAU,IAAI,SAAS,MAAM;AACpC,YAAI,WAAW,QAAQ,IAAI,MAAM;AAC/B,iBAAO,QAAQ,IAAI;AACrB,YAAI,gBAAgB,GAAG,QAAQ;AAC/B,YAAI,iBAAiB,cAAc,IAAI,MAAM;AAC3C,iBAAO,cAAc,IAAI;AAC3B,eAAO,eAAe,IAAI;AAAA,MAC5B;AAEA,MAAAA,YAAW,gBAAgB,cAAc,SAAS,SAAS,MAAM;AAC/D,eAAO,UAAU,MAAM,SAAS,IAAI;AAAA,MACtC,CAAC;AAAA,IACH,CAAC;AAAA;AAAA;", "names": ["CodeMirror", "range"]}