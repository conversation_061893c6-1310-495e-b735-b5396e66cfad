<template>
  <div class="inquiry-record-container">
    <!-- 文档头部操作栏 -->
    <div class="document-header">
      <div class="header-left">
        <h3>询问笔录</h3>
      </div>
      <div class="header-right">
        <el-button
          v-if="!isEditing"
          size="small"
          type="primary"
          @click="toggleEdit"
          icon="Edit">
          编辑
        </el-button>
        <template v-else>
          <el-button
            size="small"
            type="success"
            @click="saveEdit"
            icon="Check">
            保存
          </el-button>
          <el-button
            size="small"
            type="info"
            @click="cancelEdit"
            icon="Close">
            取消
          </el-button>
        </template>
        <el-button
          size="small"
          @click="downloadOriginalFile"
          icon="Download">
          导出
        </el-button>
      </div>
    </div>

    <!-- 直接显示DOCX预览，不使用弹窗 -->
    <div class="document-preview" v-if="props.fileUrl&&!isEditing">
      <div class="preview-content" v-loading="previewLoading">
        <VueOfficeDocx
          :src="props.fileUrl"
          style="width: 100%;"
          @rendered="onDocxRendered"
          @error="onPreviewError"
        />
      </div>
    </div>

    <!-- 如果没有文件URL或不是DOCX，显示表单编辑模式 -->
    <div v-else class="document-body">
      <template v-if="isEditing">
        <div class="document-layout">

          <!-- 文档头部 -->
          <div class="document-header-section">
            <div class="org-name">
              <el-input
                v-model="formData.bureauName"
                placeholder="机构名称"
                class="org-input"
              />
              <span>烟草专卖局</span>
            </div>

            <div class="document-title">
              <h2>询问笔录</h2>
            </div>
          </div>

          <!-- 询问时间 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.inquiry_time_info"
                type="textarea"
                :autosize="{ minRows: 1 }"
                placeholder="询问时间：XXXX年XX月XX日XX时XX分至XXXX年XX月XX日XX时XX分。"
                class="inquiry-time auto-resize-textarea"
                maxlength="500"
                show-word-limit
              />
            </div>
          </div>

          <!-- 询问地点 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.inquiry_location"
                type="textarea"
                :autosize="{ minRows: 1 }"
                placeholder="询问地点：XXXXXXX"
                class="inquiry-location auto-resize-textarea"
                maxlength="300"
                show-word-limit
              />
            </div>
          </div>

          <!-- 询问人员信息 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.inquiry_personnel"
                type="textarea"
                :autosize="{ minRows: 1 }"
                placeholder="询问人：XXX  记录人：XXX"
                class="inquiry-personnel auto-resize-textarea"
                maxlength="500"
                show-word-limit
              />
            </div>
          </div>

          <!-- 被询问人基本信息 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.interviewee_basic"
                type="textarea"
                :autosize="{ minRows: 1 }"
                placeholder="被询问人：XXX  性别：X"
                class="interviewee-basic auto-resize-textarea"
                maxlength="200"
                show-word-limit
              />
            </div>
          </div>

          <!-- 年龄和民族 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.by_ask_age_nation"
                type="textarea"
                :autosize="{ minRows: 1 }"
                placeholder="年龄：XX  民族：XX"
                class="age-nation auto-resize-textarea"
                maxlength="100"
                show-word-limit
              />
            </div>
          </div>

          <!-- 证件类型及号码 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.by_ask_id_info"
                type="textarea"
                :autosize="{ minRows: 1 }"
                placeholder="证件类型及号码：身份证 XXXXXXXXXXXXXXXX"
                class="id-info auto-resize-textarea"
                maxlength="200"
                show-word-limit
              />
            </div>
          </div>

          <!-- 住址 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.by_ask_home_addr"
                type="textarea"
                :autosize="{ minRows: 1 }"
                placeholder="住址：XXXXXXXXXXXXXXXX"
                class="home-addr auto-resize-textarea"
                maxlength="300"
                show-word-limit
              />
            </div>
          </div>

          <!-- 经营地址和联系电话 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.by_ask_business_contact"
                type="textarea"
                :autosize="{ minRows: 1 }"
                placeholder="经营地址：XXXXXXX  联系电话：XXXXXXX"
                class="business-contact auto-resize-textarea"
                maxlength="300"
                show-word-limit
              />
            </div>
          </div>

          <!-- 执法人员表明身份记录 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.id_record_desc"
                type="textarea"
                :autosize="{ minRows: 2 }"
                placeholder="执法人员表明身份、出示证件及被询问人确认的记录："
                class="id-record auto-resize-textarea"
                maxlength="1000"
                show-word-limit
              />
            </div>
          </div>

          <!-- 告知陈述权利 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.stmt"
                type="textarea"
                :autosize="{ minRows: 2 }"
                placeholder="告知陈述（申辩）和申请回避的权利："
                class="statement auto-resize-textarea"
                maxlength="1000"
                show-word-limit
              />
            </div>
          </div>

          <!-- 询问说明 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.inquiry_intro"
                type="textarea"
                :autosize="{ minRows: 3 }"
                placeholder="根据人民政府的举报，出示证件后被询问人确认的身份，我们依法对你进行询问，你应当如实回答我们的询问，这是法定的义务。这是询问笔录，询问结束后，你可以查阅，如果记录有误，你可以要求补正。"
                class="inquiry-intro auto-resize-textarea"
                maxlength="1000"
                show-word-limit
              />
            </div>
          </div>

          <!-- 问答内容 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.question_content"
                type="textarea"
                :autosize="{ minRows: 10 }"
                placeholder="问询内容（问答）和被询问的答复：&#10;问：请输入问题...&#10;答：请输入回答..."
                class="question-content auto-resize-textarea"
                maxlength="5000"
                show-word-limit
              />
            </div>
          </div>

          <!-- 其他信息 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.other_info"
                type="textarea"
                :autosize="{ minRows: 2 }"
                placeholder="其他信息"
                class="other-info auto-resize-textarea"
                maxlength="1000"
                show-word-limit
              />
            </div>
          </div>

          <!-- 签名区域 -->
          <div class="signature-section">
            <div class="signature-line">
              <span>以上笔录经被询问人核对无误。</span>
            </div>
            <div class="signature-line">
              <el-input
                v-model="formData.signature_info"
                type="textarea"
                :autosize="{ minRows: 3 }"
                placeholder="询问人（签名）：XXX  执法证号：XXX  年 月 日&#10;询问人（签名）：XXX  执法证号：XXX  年 月 日"
                class="signature-info auto-resize-textarea"
                maxlength="1000"
                show-word-limit
              />
            </div>
            <div class="date-line">
              <el-input
                v-model="formData.sys_modify_time"
                placeholder="日期"
                style="width: 200px;"
              />
            </div>
          </div>
        </div>
      </template>

    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Check, View, Download, FullScreen, ScaleToOriginal, Close } from '@element-plus/icons-vue'
import VueOfficeDocx from '@vue-office/docx'
// 在组件中导入样式
import '@/styles/vue-office-docx.css'
import '@/styles/document-common.scss';

const props = defineProps({
  documentData: {
    type: Object,
    default: () => ({})
  },
  fileUrl: {
    type: String,
    default: ''
  },
  fileType: {
    type: String,
    default: ''
  },
  fileName: {
    type: String,
    default: ''
  }
})

// 添加调试日志
watch(() => props.fileUrl, (newVal, oldVal) => {
  console.log('fileUrl changed:', { newVal, oldVal })
}, { immediate: true })

watch(() => props, (newVal) => {
  console.log('所有props:', newVal)
}, { immediate: true, deep: true })

const emit = defineEmits(['save'])

// 编辑状态
const isEditing = ref(false)

// 表单数据
const formData = ref({
  bureauName: '',
  // 询问基本信息（合并格式）
  inquiry_time_info: '',
  inquiry_location: '',
  inquiry_personnel: '',
  // 被询问人信息（拆分格式）
  interviewee_basic: '',
  by_ask_age_nation: '',
  by_ask_id_info: '',
  by_ask_home_addr: '',
  by_ask_business_contact: '',
  // 执法记录
  id_record_desc: '',
  stmt: '',
  // 询问说明
  inquiry_intro: '',
  // 问答内容
  question_content: '',
  // 其他信息
  other_info: '',
  // 签名信息
  signature_info: '',
  sys_modify_time: ''
})

// 预览相关状态
const previewLoading = ref(false)

// 保存原始数据的备份
const originalFormData = ref({})

// 监听传入的文档数据
watch(() => props.documentData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 从documentContent中提取属性，先解析JSON字符串
    let docContent = {}
    try {
      if (typeof newVal.documentContent === 'string') {
        docContent = JSON.parse(newVal.documentContent)
      } else {
        docContent = newVal.documentContent || {}
      }
    } catch (error) {
      docContent = {}
    }

    // 合并数据，优先使用documentContent中的数据
    formData.value = {
      ...formData.value,
      ...newVal,
      // 从documentContent中提取具体字段，支持后端实际字段名
      inquiry_time_info: docContent.inquiry_time_info || docContent.inquiryTimeInfo ||
        (docContent.ask_time_e ? `询问时间：${new Date(docContent.ask_time_e).toLocaleString()}` : '') ||
        newVal.inquiry_time_info || '',
      inquiry_location: docContent.inquiry_location || docContent.inquiryLocation ||
        (docContent.handling_addr ? `询问地点：${docContent.handling_addr}` : '') ||
        newVal.inquiry_location || '',
      inquiry_personnel: docContent.inquiry_personnel || docContent.inquiryPersonnel ||
        (docContent.ask_person ? `询问人：${docContent.ask_person}` : '') ||
        newVal.inquiry_personnel || '',
      // 被询问人信息（拆分字段）- 组合后端字段
      interviewee_basic: docContent.interviewee_basic || docContent.intervieweeBasic ||
        (docContent.by_ask_psn && docContent.by_ask_sex ?
          `被询问人：${docContent.by_ask_psn}  性别：${docContent.by_ask_sex === '01' ? '男' : '女'}` : '') ||
        newVal.interviewee_basic || '',
      by_ask_age_nation: docContent.by_ask_age_nation || docContent.byAskAgeNation ||
        (docContent.by_ask_age ? `年龄：${docContent.by_ask_age}  民族：汉族` : '') ||
        newVal.by_ask_age_nation || '',
      by_ask_id_info: docContent.by_ask_id_info || docContent.byAskIdInfo ||
        (docContent.by_ask_id_no ? `证件类型及号码：身份证 ${docContent.by_ask_id_no}` : '') ||
        newVal.by_ask_id_info || '',
      by_ask_home_addr: docContent.by_ask_home_addr || docContent.byAskHomeAddr ||
        (docContent.by_ask_addr ? `住址：${docContent.by_ask_addr}` : '') ||
        newVal.by_ask_home_addr || '',
      by_ask_business_contact: docContent.by_ask_business_contact || docContent.byAskBusinessContact ||
        (docContent.by_ask_phone ? `经营地址：${docContent.handling_addr || ''}  联系电话：${docContent.by_ask_phone}` : '') ||
        newVal.by_ask_business_contact || '',
      // 其他字段
      id_record_desc: docContent.id_record_desc || docContent.idRecordDesc || newVal.id_record_desc || '',
      stmt: docContent.stmt || newVal.stmt || '',
      inquiry_intro: docContent.inquiry_intro || docContent.inquiryIntro || newVal.inquiry_intro || '',
      question_content: docContent.question_content || docContent.questionContent || docContent.ask_content || newVal.question_content || '',
      other_info: docContent.other_info || docContent.otherInfo || newVal.other_info || '',
      signature_info: docContent.signature_info || docContent.signatureInfo || newVal.signature_info || '',
      bureauName: docContent.bureauName || docContent.org_short_name || newVal.bureauName || '',
      sys_modify_time: docContent.sys_modify_time || docContent.sysModifyTime || newVal.sys_modify_time || ''
    }

    // 保存原始数据的备份
    originalFormData.value = { ...formData.value }
  }
}, { immediate: true, deep: true })

// 修改切换编辑状态函数
const toggleEdit = () => {
  if (!isEditing.value) {
    // 进入编辑模式时保存当前数据作为备份
    originalFormData.value = { ...formData.value }
  }
  isEditing.value = !isEditing.value
}

// 新增保存编辑函数
const saveEdit = () => {
  // 保存时只传递需要的数据字段，避免传递整个formData
  const saveData = {
    inquiry_time_info: formData.value.inquiry_time_info,
    inquiry_location: formData.value.inquiry_location,
    inquiry_personnel: formData.value.inquiry_personnel,
    // 被询问人信息（拆分字段）
    interviewee_basic: formData.value.interviewee_basic,
    by_ask_age_nation: formData.value.by_ask_age_nation,
    by_ask_id_info: formData.value.by_ask_id_info,
    by_ask_home_addr: formData.value.by_ask_home_addr,
    by_ask_business_contact: formData.value.by_ask_business_contact,
    // 其他字段
    id_record_desc: formData.value.id_record_desc,
    stmt: formData.value.stmt,
    inquiry_intro: formData.value.inquiry_intro,
    question_content: formData.value.question_content,
    other_info: formData.value.other_info,
    signature_info: formData.value.signature_info,
    bureauName: formData.value.bureauName,
    sys_modify_time: formData.value.sys_modify_time
  }

  emit('save', saveData)
  isEditing.value = false
}

// 新增取消编辑函数
const cancelEdit = () => {
  // 恢复原始数据
  formData.value = { ...originalFormData.value }
  isEditing.value = false
  ElMessage.info('已取消编辑，数据已恢复')
}

// 文档渲染完成回调
const onDocxRendered = () => {
  previewLoading.value = false
}

// 预览错误处理
const onPreviewError = (error) => {
  previewLoading.value = false
  console.error('文档预览失败:', error)
  ElMessage.error('文档预览失败，请检查文件格式或网络连接')
}

// 下载原文件
const downloadOriginalFile = () => {
  if (props.fileUrl) {
    const link = document.createElement('a')
    link.href = props.fileUrl
    link.download = props.fileName || '文档'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('开始下载原文件')
  } else {
    ElMessage.warning('原文件下载链接不存在')
  }
}

// 在 onMounted 中添加事件监听
onMounted(() => {
  // 监听优化事件
  const handleOptimizeEvent = (event) => {
    const { action } = event.detail
    // 根据不同的优化动作进入编辑模式
    if (action === 'partyInfo' || action === 'lawBasis' || action === 'penalty') {
      isEditing.value = true
      ElMessage.info('已进入编辑模式，请完善相关信息')
    }
  }

  document.addEventListener('document-optimize', handleOptimizeEvent)

  // 组件卸载时移除监听器
  onUnmounted(() => {
    document.removeEventListener('document-optimize', handleOptimizeEvent)
  })
})
</script>

<style scoped>
/* 组件特有样式已移至 @/styles/document-common.scss */
.inquiry-record-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.org-name {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.org-input {
  width: 200px;
}

.document-header-section {
  text-align: center;
  margin-bottom: 30px;
}

.auto-resize-textarea {
  width: 100%;
}

.textarea-wrapper {
  margin-bottom: 15px;
}
</style>


