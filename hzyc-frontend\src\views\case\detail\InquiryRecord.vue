<template>
  <div class="inquiry-record-container">
    <!-- 文档头部操作栏 -->
    <div class="document-header">
      <div class="header-left">
        <h3>询问笔录</h3>
      </div>
      <div class="header-right">
        <el-button
          v-if="!isEditing"
          size="small"
          type="primary"
          @click="toggleEdit"
          icon="Edit">
          编辑
        </el-button>
        <template v-else>
          <el-button
            size="small"
            type="success"
            @click="saveEdit"
            icon="Check">
            保存
          </el-button>
          <el-button
            size="small"
            type="info"
            @click="cancelEdit"
            icon="Close">
            取消
          </el-button>
        </template>
        <el-button
          size="small"
          @click="downloadOriginalFile"
          icon="Download">
          导出
        </el-button>
      </div>
    </div>

    <!-- 直接显示DOCX预览，不使用弹窗 -->
    <div class="document-preview" v-if="props.fileUrl&&!isEditing">
      <div class="preview-content" v-loading="previewLoading">
        <VueOfficeDocx
          :src="props.fileUrl"
          style="width: 100%;"
          @rendered="onDocxRendered"
          @error="onPreviewError"
        />
      </div>
    </div>

    <!-- 如果没有文件URL或不是DOCX，显示表单编辑模式 -->
    <div v-else class="document-body">
      <template v-if="isEditing">
        <div class="document-layout">

          <!-- 文档头部 -->
          <div class="document-header-section">
            <div class="org-name">
              <el-input
                v-model="formData.bureauName"
                placeholder="机构名称"
                class="org-input"
              />
              <span>烟草专卖局</span>
            </div>

            <div class="document-title">
              <h2>询问笔录</h2>
            </div>
          </div>

          <!-- 询问时间 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.inquiry_time_info"
                type="textarea"
                :autosize="{ minRows: 1 }"
                placeholder="询问时间：XXXX年XX月XX日XX时XX分至XXXX年XX月XX日XX时XX分。"
                class="inquiry-time auto-resize-textarea"
                maxlength="500"
                show-word-limit
              />
            </div>
          </div>

          <!-- 询问地点 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.inquiry_location"
                type="textarea"
                :autosize="{ minRows: 1 }"
                placeholder="询问地点：XXXXXXX"
                class="inquiry-location auto-resize-textarea"
                maxlength="300"
                show-word-limit
              />
            </div>
          </div>

          <!-- 询问人员信息 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.inquiry_personnel"
                type="textarea"
                :autosize="{ minRows: 1 }"
                placeholder="询问人：XXX  记录人：XXX"
                class="inquiry-personnel auto-resize-textarea"
                maxlength="500"
                show-word-limit
              />
            </div>
          </div>

          <!-- 被询问人基本信息 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.interviewee_basic"
                type="textarea"
                :autosize="{ minRows: 1 }"
                placeholder="被询问人：XXX  性别：X"
                class="interviewee-basic auto-resize-textarea"
                maxlength="200"
                show-word-limit
              />
            </div>
          </div>

          <!-- 年龄和民族 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.by_ask_age_nation"
                type="textarea"
                :autosize="{ minRows: 1 }"
                placeholder="年龄：XX  民族：XX"
                class="age-nation auto-resize-textarea"
                maxlength="100"
                show-word-limit
              />
            </div>
          </div>

          <!-- 证件类型及号码 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.by_ask_id_info"
                type="textarea"
                :autosize="{ minRows: 1 }"
                placeholder="证件类型及号码：身份证 XXXXXXXXXXXXXXXX"
                class="id-info auto-resize-textarea"
                maxlength="200"
                show-word-limit
              />
            </div>
          </div>

          <!-- 住址 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.by_ask_home_addr"
                type="textarea"
                :autosize="{ minRows: 1 }"
                placeholder="住址：XXXXXXXXXXXXXXXX"
                class="home-addr auto-resize-textarea"
                maxlength="300"
                show-word-limit
              />
            </div>
          </div>

          <!-- 经营地址和联系电话 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.by_ask_business_contact"
                type="textarea"
                :autosize="{ minRows: 1 }"
                placeholder="经营地址：XXXXXXX  联系电话：XXXXXXX"
                class="business-contact auto-resize-textarea"
                maxlength="300"
                show-word-limit
              />
            </div>
          </div>

          <!-- 执法人员表明身份记录 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.id_record_desc"
                type="textarea"
                :autosize="{ minRows: 2 }"
                placeholder="执法人员表明身份、出示证件及被询问人确认的记录："
                class="id-record auto-resize-textarea"
                maxlength="1000"
                show-word-limit
              />
            </div>
          </div>

          <!-- 告知陈述权利 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.stmt"
                type="textarea"
                :autosize="{ minRows: 2 }"
                placeholder="告知陈述（申辩）和申请回避的权利："
                class="statement auto-resize-textarea"
                maxlength="1000"
                show-word-limit
              />
            </div>
          </div>

          <!-- 询问说明 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.inquiry_intro"
                type="textarea"
                :autosize="{ minRows: 3 }"
                placeholder="根据人民政府的举报，出示证件后被询问人确认的身份，我们依法对你进行询问，你应当如实回答我们的询问，这是法定的义务。这是询问笔录，询问结束后，你可以查阅，如果记录有误，你可以要求补正。"
                class="inquiry-intro auto-resize-textarea"
                maxlength="1000"
                show-word-limit
              />
            </div>
          </div>

          <!-- 问答内容 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.question_content"
                type="textarea"
                :autosize="{ minRows: 10 }"
                placeholder="问询内容（问答）和被询问的答复：&#10;问：请输入问题...&#10;答：请输入回答..."
                class="question-content auto-resize-textarea"
                maxlength="5000"
                show-word-limit
              />
            </div>
          </div>

          <!-- 其他信息 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <el-input
                v-model="formData.other_info"
                type="textarea"
                :autosize="{ minRows: 2 }"
                placeholder="其他信息"
                class="other-info auto-resize-textarea"
                maxlength="1000"
                show-word-limit
              />
            </div>
          </div>

          <!-- 签名区域 -->
          <div class="signature-section">
            <div class="signature-line">
              <span>以上笔录经被询问人核对无误。</span>
            </div>
            <div class="signature-line">
              <el-input
                v-model="formData.signature_info"
                type="textarea"
                :autosize="{ minRows: 3 }"
                placeholder="询问人（签名）：XXX  执法证号：XXX  年 月 日&#10;询问人（签名）：XXX  执法证号：XXX  年 月 日"
                class="signature-info auto-resize-textarea"
                maxlength="1000"
                show-word-limit
              />
            </div>
            <div class="date-line">
              <el-input
                v-model="formData.sys_modify_time"
                placeholder="日期"
                style="width: 200px;"
              />
            </div>
          </div>
        </div>
      </template>

    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Check, View, Download, FullScreen, ScaleToOriginal, Close } from '@element-plus/icons-vue'
import VueOfficeDocx from '@vue-office/docx'
// 在组件中导入样式
import '@/styles/vue-office-docx.css'
import '@/styles/document-common.scss';

const props = defineProps({
  documentData: {
    type: Object,
    default: () => ({})
  },
  fileUrl: {
    type: String,
    default: ''
  },
  fileType: {
    type: String,
    default: ''
  },
  fileName: {
    type: String,
    default: ''
  }
})

// 添加调试日志
watch(() => props.fileUrl, (newVal, oldVal) => {
  console.log('fileUrl changed:', { newVal, oldVal })
}, { immediate: true })

watch(() => props, (newVal) => {
  console.log('所有props:', newVal)
}, { immediate: true, deep: true })

const emit = defineEmits(['save'])

// 编辑状态
const isEditing = ref(false)

// 表单数据
const formData = ref({
  bureauName: '惠阳区',
  // 询问基本信息（合并格式）
  inquiry_time_info: '询问时间：2024年1月15日14时30分至2024年1月15日16时30分。',
  inquiry_location: '询问地点：惠阳区烟草专卖局办公楼三楼询问室',
  inquiry_personnel: '询问人：王执法  记录人：李执法',
  // 被询问人信息（拆分格式）
  interviewee_basic: '被询问人：张某某  性别：男',
  by_ask_age_nation: '年龄：35  民族：汉族',
  by_ask_id_info: '证件类型及号码：身份证 440123198901010001',
  by_ask_home_addr: '住址：惠阳区某某街道某某小区1号楼101室',
  by_ask_business_contact: '经营地址：某某路某某号烟酒店  联系电话：***********',
  // 执法记录
  id_record_desc: '执法人员表明身份、出示证件及被询问人确认的记录：执法人员表明身份、出示证件及被询问人确认的身份，我们依法对你进行询问，你应当如实回答我们的询问，这是法定的义务。',
  stmt: '告知陈述（申辩）和申请回避的权利：已告知被询问人有陈述、申辩和申请回避的权利。',
  // 询问说明
  inquiry_intro: '根据人民政府的举报，出示证件后被询问人确认的身份，我们依法对你进行询问，你应当如实回答我们的询问，这是法定的义务。这是询问笔录，询问结束后，你可以查阅，如果记录有误，你可以要求补正。',
  // 问答内容
  question_content: `问询内容（问答）和被询问的答复：

问：请说明你的基本情况。
答：我叫张某某，男，35岁，汉族，身份证号码440123198901010001，住址惠阳区某某街道某某小区1号楼101室，联系电话***********。我在某某路经营一家烟酒店，营业执照号码为91441300MA4XXXXX。

问：你店里销售的卷烟是从哪里进货的？
答：我店里的卷烟都是从正规渠道进货的，主要是从惠州区烟草专卖局指定的批发商那里进货。

问：你是否销售过假冒卷烟？
答：我没有故意销售假冒卷烟。如果有的话，可能是我不知情的情况下进的货。

问：你对我们今天检查发现的问题有什么要说的？
答：如果确实有问题，我愿意配合调查，并承担相应的责任。我以后会更加注意进货渠道，确保不再出现类似问题。`,
  // 其他信息
  other_info: '以上询问内容属实，我已如实回答。',
  // 签名信息
  signature_info: `询问人（签名）：王执法  执法证号：ZF001  年 月 日
询问人（签名）：李执法  执法证号：ZF002  年 月 日`,
  sys_modify_time: ''
})

// 预览相关状态
const previewDialogVisible = ref(false)
const previewLoading = ref(false)
const isFullscreen = ref(false)

// 保存原始数据的备份
const originalFormData = ref({})

// 监听传入的文档数据
watch(() => props.documentData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 从documentContent中提取属性，先解析JSON字符串
    let docContent = {}
    try {
      if (typeof newVal.documentContent === 'string') {
        docContent = JSON.parse(newVal.documentContent)
      } else {
        docContent = newVal.documentContent || {}
      }
    } catch (error) {
      docContent = {}
    }

    // 合并数据，优先使用documentContent中的数据
    formData.value = {
      ...formData.value,
      ...newVal,
      // 从documentContent中提取具体字段
      inquiry_time_info: docContent.inquiry_time_info || docContent.inquiryTimeInfo || newVal.inquiry_time_info || formData.value.inquiry_time_info,
      inquiry_location: docContent.inquiry_location || docContent.inquiryLocation || newVal.inquiry_location || formData.value.inquiry_location,
      inquiry_personnel: docContent.inquiry_personnel || docContent.inquiryPersonnel || newVal.inquiry_personnel || formData.value.inquiry_personnel,
      // 被询问人信息（拆分字段）
      interviewee_basic: docContent.interviewee_basic || docContent.intervieweeBasic || newVal.interviewee_basic || formData.value.interviewee_basic,
      by_ask_age_nation: docContent.by_ask_age_nation || docContent.byAskAgeNation || newVal.by_ask_age_nation || formData.value.by_ask_age_nation,
      by_ask_id_info: docContent.by_ask_id_info || docContent.byAskIdInfo || newVal.by_ask_id_info || formData.value.by_ask_id_info,
      by_ask_home_addr: docContent.by_ask_home_addr || docContent.byAskHomeAddr || newVal.by_ask_home_addr || formData.value.by_ask_home_addr,
      by_ask_business_contact: docContent.by_ask_business_contact || docContent.byAskBusinessContact || newVal.by_ask_business_contact || formData.value.by_ask_business_contact,
      // 其他字段
      id_record_desc: docContent.id_record_desc || docContent.idRecordDesc || newVal.id_record_desc || formData.value.id_record_desc,
      stmt: docContent.stmt || newVal.stmt || formData.value.stmt,
      inquiry_intro: docContent.inquiry_intro || docContent.inquiryIntro || newVal.inquiry_intro || formData.value.inquiry_intro,
      question_content: docContent.question_content || docContent.questionContent || newVal.question_content || formData.value.question_content,
      other_info: docContent.other_info || docContent.otherInfo || newVal.other_info || formData.value.other_info,
      signature_info: docContent.signature_info || docContent.signatureInfo || newVal.signature_info || formData.value.signature_info,
      bureauName: docContent.bureauName || newVal.bureauName || formData.value.bureauName,
      sys_modify_time: docContent.sys_modify_time || docContent.sysModifyTime || newVal.sys_modify_time || formData.value.sys_modify_time
    }

    // 保存原始数据的备份
    originalFormData.value = { ...formData.value }
  }
}, { immediate: true, deep: true })

// 修改切换编辑状态函数
const toggleEdit = () => {
  if (!isEditing.value) {
    // 进入编辑模式时保存当前数据作为备份
    originalFormData.value = { ...formData.value }
  }
  isEditing.value = !isEditing.value
}

// 新增保存编辑函数
const saveEdit = () => {
  // 保存时只传递需要的数据字段，避免传递整个formData
  const saveData = {
    inquiry_time_info: formData.value.inquiry_time_info,
    inquiry_location: formData.value.inquiry_location,
    inquiry_personnel: formData.value.inquiry_personnel,
    // 被询问人信息（拆分字段）
    interviewee_basic: formData.value.interviewee_basic,
    by_ask_age_nation: formData.value.by_ask_age_nation,
    by_ask_id_info: formData.value.by_ask_id_info,
    by_ask_home_addr: formData.value.by_ask_home_addr,
    by_ask_business_contact: formData.value.by_ask_business_contact,
    // 其他字段
    id_record_desc: formData.value.id_record_desc,
    stmt: formData.value.stmt,
    inquiry_intro: formData.value.inquiry_intro,
    question_content: formData.value.question_content,
    other_info: formData.value.other_info,
    signature_info: formData.value.signature_info,
    bureauName: formData.value.bureauName,
    sys_modify_time: formData.value.sys_modify_time
  }

  emit('save', saveData)
  isEditing.value = false
}

// 新增取消编辑函数
const cancelEdit = () => {
  // 恢复原始数据
  formData.value = { ...originalFormData.value }
  isEditing.value = false
  ElMessage.info('已取消编辑，数据已恢复')
}

// 文档渲染完成回调
const onDocxRendered = () => {
  previewLoading.value = false
}

// 预览错误处理
const onPreviewError = (error) => {
  previewLoading.value = false
  console.error('文档预览失败:', error)
  ElMessage.error('文档预览失败，请检查文件格式或网络连接')
}

// 下载原文件
const downloadOriginalFile = () => {
  if (props.fileUrl) {
    const link = document.createElement('a')
    link.href = props.fileUrl
    link.download = props.fileName || '文档'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('开始下载原文件')
  } else {
    ElMessage.warning('原文件下载链接不存在')
  }
}

// 在 onMounted 中添加事件监听
onMounted(() => {
  // 监听优化事件
  const handleOptimizeEvent = (event) => {
    const { action } = event.detail
    // 根据不同的优化动作进入编辑模式
    if (action === 'partyInfo' || action === 'lawBasis' || action === 'penalty') {
      isEditing.value = true
      ElMessage.info('已进入编辑模式，请完善相关信息')
    }
  }

  document.addEventListener('document-optimize', handleOptimizeEvent)

  // 组件卸载时移除监听器
  onUnmounted(() => {
    document.removeEventListener('document-optimize', handleOptimizeEvent)
  })
})
</script>

<style scoped>
/* 组件特有样式已移至 @/styles/document-common.scss */
.inquiry-record-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.org-name {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.org-input {
  width: 200px;
}

.document-header-section {
  text-align: center;
  margin-bottom: 30px;
}

.auto-resize-textarea {
  width: 100%;
}

.textarea-wrapper {
  margin-bottom: 15px;
}
</style>


