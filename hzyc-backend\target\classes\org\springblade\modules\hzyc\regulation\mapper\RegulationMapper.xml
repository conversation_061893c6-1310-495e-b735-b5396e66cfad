<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.hzyc.regulation.mapper.RegulationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="regulationResultMap" type="org.springblade.modules.hzyc.regulation.pojo.entity.RegulationEntity">
        <result column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="category" property="category"/>
        <result column="date" property="date"/>
        <result column="type" property="type"/>
        <result column="document_number" property="documentNumber"/>
        <result column="source" property="source"/>
        <result column="summary" property="summary"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectRegulationPage" resultMap="regulationResultMap">
        select * from ca_regulation where is_deleted = 0
    </select>


    <select id="exportRegulation" resultType="org.springblade.modules.hzyc.regulation.excel.RegulationExcel">
        SELECT * FROM ca_regulation ${ew.customSqlSegment}
    </select>

</mapper>
