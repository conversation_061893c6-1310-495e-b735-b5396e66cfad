<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.hzyc.document.mapper.DocumentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="documentResultMap" type="org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity">
        <result column="id" property="id"/>
        <result column="case_uuid" property="caseUuid"/>
        <result column="document_type" property="documentType"/>
        <result column="document_content" property="documentContent"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectDocumentPage" resultMap="documentResultMap">
        select * from ca_document where is_deleted = 0
    </select>


    <select id="exportDocument" resultType="org.springblade.modules.hzyc.document.excel.DocumentExcel">
        SELECT * FROM ca_document ${ew.customSqlSegment}
    </select>

</mapper>
