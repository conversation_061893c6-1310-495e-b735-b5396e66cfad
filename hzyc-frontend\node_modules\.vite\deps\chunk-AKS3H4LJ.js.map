{"version": 3, "sources": ["../../normalize-wheel-es/src/UserAgent_DEPRECATED.js", "../../normalize-wheel-es/src/ExecutionEnvironment.js", "../../normalize-wheel-es/src/isEventSupported.js", "../../normalize-wheel-es/src/normalizeWheel.js", "../../dayjs/plugin/localeData.js", "../../dayjs/plugin/customParseFormat.js", "../../dayjs/plugin/advancedFormat.js", "../../dayjs/plugin/weekOfYear.js", "../../dayjs/plugin/weekYear.js", "../../dayjs/plugin/dayOfYear.js", "../../dayjs/plugin/isSameOrAfter.js", "../../dayjs/plugin/isSameOrBefore.js", "../../src/util.ts", "../../src/rule/required.ts", "../../src/rule/whitespace.ts", "../../src/rule/url.ts", "../../src/rule/type.ts", "../../src/rule/range.ts", "../../src/rule/enum.ts", "../../src/rule/pattern.ts", "../../src/rule/index.ts", "../../src/validator/string.ts", "../../src/validator/method.ts", "../../src/validator/number.ts", "../../src/validator/boolean.ts", "../../src/validator/regexp.ts", "../../src/validator/integer.ts", "../../src/validator/float.ts", "../../src/validator/array.ts", "../../src/validator/object.ts", "../../src/validator/enum.ts", "../../src/validator/pattern.ts", "../../src/validator/date.ts", "../../src/validator/required.ts", "../../src/validator/type.ts", "../../src/validator/any.ts", "../../src/validator/index.ts", "../../src/messages.ts", "../../src/index.ts", "../../@floating-ui/utils/dist/floating-ui.utils.mjs", "../../@floating-ui/core/dist/floating-ui.core.mjs", "../../@floating-ui/utils/dist/floating-ui.utils.dom.mjs", "../../@ctrl/tinycolor/dist/module/util.js", "../../@ctrl/tinycolor/dist/module/conversion.js", "../../@ctrl/tinycolor/dist/module/css-color-names.js", "../../@ctrl/tinycolor/dist/module/format-input.js", "../../@ctrl/tinycolor/dist/module/index.js", "../../@ctrl/tinycolor/dist/module/readability.js", "../../@ctrl/tinycolor/dist/module/to-ms-filter.js", "../../@ctrl/tinycolor/dist/module/from-ratio.js", "../../@ctrl/tinycolor/dist/module/random.js", "../../@ctrl/tinycolor/dist/module/interfaces.js", "../../@ctrl/tinycolor/dist/module/public_api.js", "../../memoize-one/dist/memoize-one.esm.js"], "sourcesContent": ["/**\n * Copyright 2004-present Facebook. All Rights Reserved.\n *\n * @providesModule UserAgent_DEPRECATED\n */\n\n/**\n *  Provides entirely client-side User Agent and OS detection. You should prefer\n *  the non-deprecated UserAgent module when possible, which exposes our\n *  authoritative server-side PHP-based detection to the client.\n *\n *  Usage is straightforward:\n *\n *    if (UserAgent_DEPRECATED.ie()) {\n *      //  IE\n *    }\n *\n *  You can also do version checks:\n *\n *    if (UserAgent_DEPRECATED.ie() >= 7) {\n *      //  IE7 or better\n *    }\n *\n *  The browser functions will return NaN if the browser does not match, so\n *  you can also do version compares the other way:\n *\n *    if (UserAgent_DEPRECATED.ie() < 7) {\n *      //  IE6 or worse\n *    }\n *\n *  Note that the version is a float and may include a minor version number,\n *  so you should always use range operators to perform comparisons, not\n *  strict equality.\n *\n *  **Note:** You should **strongly** prefer capability detection to browser\n *  version detection where it's reasonable:\n *\n *    http://www.quirksmode.org/js/support.html\n *\n *  Further, we have a large number of mature wrapper functions and classes\n *  which abstract away many browser irregularities. Check the documentation,\n *  grep for things, or <NAME_EMAIL> before writing yet\n *  another copy of \"event || window.event\".\n *\n */\n\nvar _populated = false;\n\n// Browsers\nvar _ie, _firefox, _opera, _webkit, _chrome;\n\n// Actual IE browser for compatibility mode\nvar _ie_real_version;\n\n// Platforms\nvar _osx, _windows, _linux, _android;\n\n// Architectures\nvar _win64;\n\n// Devices\nvar _iphone, _ipad, _native;\n\nvar _mobile;\n\nfunction _populate() {\n  if (_populated) {\n    return;\n  }\n\n  _populated = true;\n\n  // To work around buggy JS libraries that can't handle multi-digit\n  // version numbers, Opera 10's user agent string claims it's Opera\n  // 9, then later includes a Version/X.Y field:\n  //\n  // Opera/9.80 (foo) Presto/2.2.15 Version/10.10\n  var uas = navigator.userAgent;\n  var agent =\n    /(?:MSIE.(\\d+\\.\\d+))|(?:(?:Firefox|GranParadiso|Iceweasel).(\\d+\\.\\d+))|(?:Opera(?:.+Version.|.)(\\d+\\.\\d+))|(?:AppleWebKit.(\\d+(?:\\.\\d+)?))|(?:Trident\\/\\d+\\.\\d+.*rv:(\\d+\\.\\d+))/.exec(\n      uas\n    );\n  var os = /(Mac OS X)|(Windows)|(Linux)/.exec(uas);\n\n  _iphone = /\\b(iPhone|iP[ao]d)/.exec(uas);\n  _ipad = /\\b(iP[ao]d)/.exec(uas);\n  _android = /Android/i.exec(uas);\n  _native = /FBAN\\/\\w+;/i.exec(uas);\n  _mobile = /Mobile/i.exec(uas);\n\n  // Note that the IE team blog would have you believe you should be checking\n  // for 'Win64; x64'.  But MSDN then reveals that you can actually be coming\n  // from either x64 or ia64;  so ultimately, you should just check for Win64\n  // as in indicator of whether you're in 64-bit IE.  32-bit IE on 64-bit\n  // Windows will send 'WOW64' instead.\n  _win64 = !!/Win64/.exec(uas);\n\n  if (agent) {\n    _ie = agent[1]\n      ? parseFloat(agent[1])\n      : agent[5]\n      ? parseFloat(agent[5])\n      : NaN;\n    // IE compatibility mode\n    if (_ie && document && document.documentMode) {\n      _ie = document.documentMode;\n    }\n    // grab the \"true\" ie version from the trident token if available\n    var trident = /(?:Trident\\/(\\d+.\\d+))/.exec(uas);\n    _ie_real_version = trident ? parseFloat(trident[1]) + 4 : _ie;\n\n    _firefox = agent[2] ? parseFloat(agent[2]) : NaN;\n    _opera = agent[3] ? parseFloat(agent[3]) : NaN;\n    _webkit = agent[4] ? parseFloat(agent[4]) : NaN;\n    if (_webkit) {\n      // We do not add the regexp to the above test, because it will always\n      // match 'safari' only since 'AppleWebKit' appears before 'Chrome' in\n      // the userAgent string.\n      agent = /(?:Chrome\\/(\\d+\\.\\d+))/.exec(uas);\n      _chrome = agent && agent[1] ? parseFloat(agent[1]) : NaN;\n    } else {\n      _chrome = NaN;\n    }\n  } else {\n    _ie = _firefox = _opera = _chrome = _webkit = NaN;\n  }\n\n  if (os) {\n    if (os[1]) {\n      // Detect OS X version.  If no version number matches, set _osx to true.\n      // Version examples:  10, 10_6_1, 10.7\n      // Parses version number as a float, taking only first two sets of\n      // digits.  If only one set of digits is found, returns just the major\n      // version number.\n      var ver = /(?:Mac OS X (\\d+(?:[._]\\d+)?))/.exec(uas);\n\n      _osx = ver ? parseFloat(ver[1].replace('_', '.')) : true;\n    } else {\n      _osx = false;\n    }\n    _windows = !!os[2];\n    _linux = !!os[3];\n  } else {\n    _osx = _windows = _linux = false;\n  }\n}\n\nvar UserAgent_DEPRECATED = {\n  /**\n   *  Check if the UA is Internet Explorer.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  ie: function () {\n    return _populate() || _ie;\n  },\n\n  /**\n   * Check if we're in Internet Explorer compatibility mode.\n   *\n   * @return bool true if in compatibility mode, false if\n   * not compatibility mode or not ie\n   */\n  ieCompatibilityMode: function () {\n    return _populate() || _ie_real_version > _ie;\n  },\n\n  /**\n   * Whether the browser is 64-bit IE.  Really, this is kind of weak sauce;  we\n   * only need this because Skype can't handle 64-bit IE yet.  We need to remove\n   * this when we don't need it -- tracked by #601957.\n   */\n  ie64: function () {\n    return UserAgent_DEPRECATED.ie() && _win64;\n  },\n\n  /**\n   *  Check if the UA is Firefox.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  firefox: function () {\n    return _populate() || _firefox;\n  },\n\n  /**\n   *  Check if the UA is Opera.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  opera: function () {\n    return _populate() || _opera;\n  },\n\n  /**\n   *  Check if the UA is WebKit.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  webkit: function () {\n    return _populate() || _webkit;\n  },\n\n  /**\n   *  For Push\n   *  WILL BE REMOVED VERY SOON. Use UserAgent_DEPRECATED.webkit\n   */\n  safari: function () {\n    return UserAgent_DEPRECATED.webkit();\n  },\n\n  /**\n   *  Check if the UA is a Chrome browser.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  chrome: function () {\n    return _populate() || _chrome;\n  },\n\n  /**\n   *  Check if the user is running Windows.\n   *\n   *  @return bool `true' if the user's OS is Windows.\n   */\n  windows: function () {\n    return _populate() || _windows;\n  },\n\n  /**\n   *  Check if the user is running Mac OS X.\n   *\n   *  @return float|bool   Returns a float if a version number is detected,\n   *                       otherwise true/false.\n   */\n  osx: function () {\n    return _populate() || _osx;\n  },\n\n  /**\n   * Check if the user is running Linux.\n   *\n   * @return bool `true' if the user's OS is some flavor of Linux.\n   */\n  linux: function () {\n    return _populate() || _linux;\n  },\n\n  /**\n   * Check if the user is running on an iPhone or iPod platform.\n   *\n   * @return bool `true' if the user is running some flavor of the\n   *    iPhone OS.\n   */\n  iphone: function () {\n    return _populate() || _iphone;\n  },\n\n  mobile: function () {\n    return _populate() || _iphone || _ipad || _android || _mobile;\n  },\n\n  nativeApp: function () {\n    // webviews inside of the native apps\n    return _populate() || _native;\n  },\n\n  android: function () {\n    return _populate() || _android;\n  },\n\n  ipad: function () {\n    return _populate() || _ipad;\n  },\n};\n\nexport default UserAgent_DEPRECATED;\n", "/**\n * Copyright (c) 2015, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n *\n * @providesModule ExecutionEnvironment\n */\n\n/*jslint evil: true */\n\nvar canUseDOM = !!(\n  typeof window !== 'undefined' &&\n  window.document &&\n  window.document.createElement\n);\n\n/**\n * Simple, lightweight module assisting with the detection and context of\n * Worker. Helps avoid circular dependencies and allows code to reason about\n * whether or not they are in a Worker, even if they never include the main\n * `ReactWorker` dependency.\n */\nvar ExecutionEnvironment = {\n  canUseDOM: canUseDOM,\n\n  canUseWorkers: typeof Worker !== 'undefined',\n\n  canUseEventListeners:\n    canUseDOM && !!(window.addEventListener || window.attachEvent),\n\n  canUseViewport: canUseDOM && !!window.screen,\n\n  isInWorker: !canUseDOM, // For now, this is true - might change in the future.\n};\n\nexport default ExecutionEnvironment;\n", "/**\n * Copyright 2013-2015, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n *\n * @providesModule isEventSupported\n */\n\nimport ExecutionEnvironment from './ExecutionEnvironment';\n\nvar useHasFeature;\nif (ExecutionEnvironment.canUseDOM) {\n  useHasFeature =\n    document.implementation &&\n    document.implementation.hasFeature &&\n    // always returns true in newer browsers as per the standard.\n    // @see http://dom.spec.whatwg.org/#dom-domimplementation-hasfeature\n    document.implementation.hasFeature('', '') !== true;\n}\n\n/**\n * Checks if an event is supported in the current execution environment.\n *\n * NOTE: This will not work correctly for non-generic events such as `change`,\n * `reset`, `load`, `error`, and `select`.\n *\n * Borrows from Modernizr.\n *\n * @param {string} eventNameSuffix Event name, e.g. \"click\".\n * @param {?boolean} capture Check if the capture phase is supported.\n * @return {boolean} True if the event is supported.\n * @internal\n * @license Modernizr 3.0.0pre (Custom Build) | MIT\n */\nfunction isEventSupported(eventNameSuffix, capture) {\n  if (\n    !ExecutionEnvironment.canUseDOM ||\n    (capture && !('addEventListener' in document))\n  ) {\n    return false;\n  }\n\n  var eventName = 'on' + eventNameSuffix;\n  var isSupported = eventName in document;\n\n  if (!isSupported) {\n    var element = document.createElement('div');\n    element.setAttribute(eventName, 'return;');\n    isSupported = typeof element[eventName] === 'function';\n  }\n\n  if (!isSupported && useHasFeature && eventNameSuffix === 'wheel') {\n    // This is the only way to test support for the `wheel` event in IE9+.\n    isSupported = document.implementation.hasFeature('Events.wheel', '3.0');\n  }\n\n  return isSupported;\n}\n\nexport default isEventSupported;\n", "/**\n * Copyright (c) 2015, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n *\n * @providesModule normalizeWheel\n * @typechecks\n */\n\nimport UserAgent_DEPRECATED from './UserAgent_DEPRECATED';\n\nimport isEventSupported from './isEventSupported';\n\n// Reasonable defaults\nvar PIXEL_STEP = 10;\nvar LINE_HEIGHT = 40;\nvar PAGE_HEIGHT = 800;\n\n/**\n * Mouse wheel (and 2-finger trackpad) support on the web sucks.  It is\n * complicated, thus this doc is long and (hopefully) detailed enough to answer\n * your questions.\n *\n * If you need to react to the mouse wheel in a predictable way, this code is\n * like your bestest friend. * hugs *\n *\n * As of today, there are 4 DOM event types you can listen to:\n *\n *   'wheel'                -- Chrome(31+), FF(17+), IE(9+)\n *   'mousewheel'           -- Chrome, IE(6+), Opera, Safari\n *   'MozMousePixelScroll'  -- FF(3.5 only!) (2010-2013) -- don't bother!\n *   'DOMMouseScroll'       -- FF(0.9.7+) since 2003\n *\n * So what to do?  The is the best:\n *\n *   normalizeWheel.getEventType();\n *\n * In your event callback, use this code to get sane interpretation of the\n * deltas.  This code will return an object with properties:\n *\n *   spinX   -- normalized spin speed (use for zoom) - x plane\n *   spinY   -- \" - y plane\n *   pixelX  -- normalized distance (to pixels) - x plane\n *   pixelY  -- \" - y plane\n *\n * Wheel values are provided by the browser assuming you are using the wheel to\n * scroll a web page by a number of lines or pixels (or pages).  Values can vary\n * significantly on different platforms and browsers, forgetting that you can\n * scroll at different speeds.  Some devices (like trackpads) emit more events\n * at smaller increments with fine granularity, and some emit massive jumps with\n * linear speed or acceleration.\n *\n * This code does its best to normalize the deltas for you:\n *\n *   - spin is trying to normalize how far the wheel was spun (or trackpad\n *     dragged).  This is super useful for zoom support where you want to\n *     throw away the chunky scroll steps on the PC and make those equal to\n *     the slow and smooth tiny steps on the Mac. Key data: This code tries to\n *     resolve a single slow step on a wheel to 1.\n *\n *   - pixel is normalizing the desired scroll delta in pixel units.  You'll\n *     get the crazy differences between browsers, but at least it'll be in\n *     pixels!\n *\n *   - positive value indicates scrolling DOWN/RIGHT, negative UP/LEFT.  This\n *     should translate to positive value zooming IN, negative zooming OUT.\n *     This matches the newer 'wheel' event.\n *\n * Why are there spinX, spinY (or pixels)?\n *\n *   - spinX is a 2-finger side drag on the trackpad, and a shift + wheel turn\n *     with a mouse.  It results in side-scrolling in the browser by default.\n *\n *   - spinY is what you expect -- it's the classic axis of a mouse wheel.\n *\n *   - I dropped spinZ/pixelZ.  It is supported by the DOM 3 'wheel' event and\n *     probably is by browsers in conjunction with fancy 3D controllers .. but\n *     you know.\n *\n * Implementation info:\n *\n * Examples of 'wheel' event if you scroll slowly (down) by one step with an\n * average mouse:\n *\n *   OS X + Chrome  (mouse)     -    4   pixel delta  (wheelDelta -120)\n *   OS X + Safari  (mouse)     -  N/A   pixel delta  (wheelDelta  -12)\n *   OS X + Firefox (mouse)     -    0.1 line  delta  (wheelDelta  N/A)\n *   Win8 + Chrome  (mouse)     -  100   pixel delta  (wheelDelta -120)\n *   Win8 + Firefox (mouse)     -    3   line  delta  (wheelDelta -120)\n *\n * On the trackpad:\n *\n *   OS X + Chrome  (trackpad)  -    2   pixel delta  (wheelDelta   -6)\n *   OS X + Firefox (trackpad)  -    1   pixel delta  (wheelDelta  N/A)\n *\n * On other/older browsers.. it's more complicated as there can be multiple and\n * also missing delta values.\n *\n * The 'wheel' event is more standard:\n *\n * http://www.w3.org/TR/DOM-Level-3-Events/#events-wheelevents\n *\n * The basics is that it includes a unit, deltaMode (pixels, lines, pages), and\n * deltaX, deltaY and deltaZ.  Some browsers provide other values to maintain\n * backward compatibility with older events.  Those other values help us\n * better normalize spin speed.  Example of what the browsers provide:\n *\n *                          | event.wheelDelta | event.detail\n *        ------------------+------------------+--------------\n *          Safari v5/OS X  |       -120       |       0\n *          Safari v5/Win7  |       -120       |       0\n *         Chrome v17/OS X  |       -120       |       0\n *         Chrome v17/Win7  |       -120       |       0\n *                IE9/Win7  |       -120       |   undefined\n *         Firefox v4/OS X  |     undefined    |       1\n *         Firefox v4/Win7  |     undefined    |       3\n *\n */\nfunction normalizeWheel(/*object*/ event) /*object*/ {\n  var sX = 0,\n    sY = 0, // spinX, spinY\n    pX = 0,\n    pY = 0; // pixelX, pixelY\n\n  // Legacy\n  if ('detail' in event) {\n    sY = event.detail;\n  }\n  if ('wheelDelta' in event) {\n    sY = -event.wheelDelta / 120;\n  }\n  if ('wheelDeltaY' in event) {\n    sY = -event.wheelDeltaY / 120;\n  }\n  if ('wheelDeltaX' in event) {\n    sX = -event.wheelDeltaX / 120;\n  }\n\n  // side scrolling on FF with DOMMouseScroll\n  if ('axis' in event && event.axis === event.HORIZONTAL_AXIS) {\n    sX = sY;\n    sY = 0;\n  }\n\n  pX = sX * PIXEL_STEP;\n  pY = sY * PIXEL_STEP;\n\n  if ('deltaY' in event) {\n    pY = event.deltaY;\n  }\n  if ('deltaX' in event) {\n    pX = event.deltaX;\n  }\n\n  if ((pX || pY) && event.deltaMode) {\n    if (event.deltaMode == 1) {\n      // delta in LINE units\n      pX *= LINE_HEIGHT;\n      pY *= LINE_HEIGHT;\n    } else {\n      // delta in PAGE units\n      pX *= PAGE_HEIGHT;\n      pY *= PAGE_HEIGHT;\n    }\n  }\n\n  // Fall-back if spin cannot be determined\n  if (pX && !sX) {\n    sX = pX < 1 ? -1 : 1;\n  }\n  if (pY && !sY) {\n    sY = pY < 1 ? -1 : 1;\n  }\n\n  return { spinX: sX, spinY: sY, pixelX: pX, pixelY: pY };\n}\n\n/**\n * The best combination if you prefer spinX + spinY normalization.  It favors\n * the older DOMMouseScroll for Firefox, as FF does not include wheelDelta with\n * 'wheel' event, making spin speed determination impossible.\n */\nnormalizeWheel.getEventType = function () /*string*/ {\n  return UserAgent_DEPRECATED.firefox()\n    ? 'DOMMouseScroll'\n    : isEventSupported('wheel')\n    ? 'wheel'\n    : 'mousewheel';\n};\n\nexport default normalizeWheel;\n", "!function(n,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=e():\"function\"==typeof define&&define.amd?define(e):(n=\"undefined\"!=typeof globalThis?globalThis:n||self).dayjs_plugin_localeData=e()}(this,(function(){\"use strict\";return function(n,e,t){var r=e.prototype,o=function(n){return n&&(n.indexOf?n:n.s)},u=function(n,e,t,r,u){var i=n.name?n:n.$locale(),a=o(i[e]),s=o(i[t]),f=a||s.map((function(n){return n.slice(0,r)}));if(!u)return f;var d=i.weekStart;return f.map((function(n,e){return f[(e+(d||0))%7]}))},i=function(){return t.Ls[t.locale()]},a=function(n,e){return n.formats[e]||function(n){return n.replace(/(\\[[^\\]]+])|(MMMM|MM|DD|dddd)/g,(function(n,e,t){return e||t.slice(1)}))}(n.formats[e.toUpperCase()])},s=function(){var n=this;return{months:function(e){return e?e.format(\"MMMM\"):u(n,\"months\")},monthsShort:function(e){return e?e.format(\"MMM\"):u(n,\"monthsShort\",\"months\",3)},firstDayOfWeek:function(){return n.$locale().weekStart||0},weekdays:function(e){return e?e.format(\"dddd\"):u(n,\"weekdays\")},weekdaysMin:function(e){return e?e.format(\"dd\"):u(n,\"weekdaysMin\",\"weekdays\",2)},weekdaysShort:function(e){return e?e.format(\"ddd\"):u(n,\"weekdaysShort\",\"weekdays\",3)},longDateFormat:function(e){return a(n.$locale(),e)},meridiem:this.$locale().meridiem,ordinal:this.$locale().ordinal}};r.localeData=function(){return s.bind(this)()},t.localeData=function(){var n=i();return{firstDayOfWeek:function(){return n.weekStart||0},weekdays:function(){return t.weekdays()},weekdaysShort:function(){return t.weekdaysShort()},weekdaysMin:function(){return t.weekdaysMin()},months:function(){return t.months()},monthsShort:function(){return t.monthsShort()},longDateFormat:function(e){return a(n,e)},meridiem:n.meridiem,ordinal:n.ordinal}},t.months=function(){return u(i(),\"months\")},t.monthsShort=function(){return u(i(),\"monthsShort\",\"months\",3)},t.weekdays=function(n){return u(i(),\"weekdays\",null,null,n)},t.weekdaysShort=function(n){return u(i(),\"weekdaysShort\",\"weekdays\",3,n)},t.weekdaysMin=function(n){return u(i(),\"weekdaysMin\",\"weekdays\",2,n)}}}));", "!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define(t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_customParseFormat=t()}(this,(function(){\"use strict\";var e={LTS:\"h:mm:ss A\",LT:\"h:mm A\",L:\"MM/DD/YYYY\",LL:\"MMMM D, YYYY\",LLL:\"MMMM D, YYYY h:mm A\",LLLL:\"dddd, MMMM D, YYYY h:mm A\"},t=/(\\[[^[]*\\])|([-_:/.,()\\s]+)|(A|a|Q|YYYY|YY?|ww?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g,n=/\\d/,r=/\\d\\d/,i=/\\d\\d?/,o=/\\d*[^-_:/,()\\s\\d]+/,s={},a=function(e){return(e=+e)+(e>68?1900:2e3)};var f=function(e){return function(t){this[e]=+t}},h=[/[+-]\\d\\d:?(\\d\\d)?|Z/,function(e){(this.zone||(this.zone={})).offset=function(e){if(!e)return 0;if(\"Z\"===e)return 0;var t=e.match(/([+-]|\\d\\d)/g),n=60*t[1]+(+t[2]||0);return 0===n?0:\"+\"===t[0]?-n:n}(e)}],u=function(e){var t=s[e];return t&&(t.indexOf?t:t.s.concat(t.f))},d=function(e,t){var n,r=s.meridiem;if(r){for(var i=1;i<=24;i+=1)if(e.indexOf(r(i,0,t))>-1){n=i>12;break}}else n=e===(t?\"pm\":\"PM\");return n},c={A:[o,function(e){this.afternoon=d(e,!1)}],a:[o,function(e){this.afternoon=d(e,!0)}],Q:[n,function(e){this.month=3*(e-1)+1}],S:[n,function(e){this.milliseconds=100*+e}],SS:[r,function(e){this.milliseconds=10*+e}],SSS:[/\\d{3}/,function(e){this.milliseconds=+e}],s:[i,f(\"seconds\")],ss:[i,f(\"seconds\")],m:[i,f(\"minutes\")],mm:[i,f(\"minutes\")],H:[i,f(\"hours\")],h:[i,f(\"hours\")],HH:[i,f(\"hours\")],hh:[i,f(\"hours\")],D:[i,f(\"day\")],DD:[r,f(\"day\")],Do:[o,function(e){var t=s.ordinal,n=e.match(/\\d+/);if(this.day=n[0],t)for(var r=1;r<=31;r+=1)t(r).replace(/\\[|\\]/g,\"\")===e&&(this.day=r)}],w:[i,f(\"week\")],ww:[r,f(\"week\")],M:[i,f(\"month\")],MM:[r,f(\"month\")],MMM:[o,function(e){var t=u(\"months\"),n=(u(\"monthsShort\")||t.map((function(e){return e.slice(0,3)}))).indexOf(e)+1;if(n<1)throw new Error;this.month=n%12||n}],MMMM:[o,function(e){var t=u(\"months\").indexOf(e)+1;if(t<1)throw new Error;this.month=t%12||t}],Y:[/[+-]?\\d+/,f(\"year\")],YY:[r,function(e){this.year=a(e)}],YYYY:[/\\d{4}/,f(\"year\")],Z:h,ZZ:h};function l(n){var r,i;r=n,i=s&&s.formats;for(var o=(n=r.replace(/(\\[[^\\]]+])|(LTS?|l{1,4}|L{1,4})/g,(function(t,n,r){var o=r&&r.toUpperCase();return n||i[r]||e[r]||i[o].replace(/(\\[[^\\]]+])|(MMMM|MM|DD|dddd)/g,(function(e,t,n){return t||n.slice(1)}))}))).match(t),a=o.length,f=0;f<a;f+=1){var h=o[f],u=c[h],d=u&&u[0],l=u&&u[1];o[f]=l?{regex:d,parser:l}:h.replace(/^\\[|\\]$/g,\"\")}return function(e){for(var t={},n=0,r=0;n<a;n+=1){var i=o[n];if(\"string\"==typeof i)r+=i.length;else{var s=i.regex,f=i.parser,h=e.slice(r),u=s.exec(h)[0];f.call(t,u),e=e.replace(u,\"\")}}return function(e){var t=e.afternoon;if(void 0!==t){var n=e.hours;t?n<12&&(e.hours+=12):12===n&&(e.hours=0),delete e.afternoon}}(t),t}}return function(e,t,n){n.p.customParseFormat=!0,e&&e.parseTwoDigitYear&&(a=e.parseTwoDigitYear);var r=t.prototype,i=r.parse;r.parse=function(e){var t=e.date,r=e.utc,o=e.args;this.$u=r;var a=o[1];if(\"string\"==typeof a){var f=!0===o[2],h=!0===o[3],u=f||h,d=o[2];h&&(d=o[2]),s=this.$locale(),!f&&d&&(s=n.Ls[d]),this.$d=function(e,t,n,r){try{if([\"x\",\"X\"].indexOf(t)>-1)return new Date((\"X\"===t?1e3:1)*e);var i=l(t)(e),o=i.year,s=i.month,a=i.day,f=i.hours,h=i.minutes,u=i.seconds,d=i.milliseconds,c=i.zone,m=i.week,M=new Date,Y=a||(o||s?1:M.getDate()),p=o||M.getFullYear(),v=0;o&&!s||(v=s>0?s-1:M.getMonth());var D,w=f||0,g=h||0,y=u||0,L=d||0;return c?new Date(Date.UTC(p,v,Y,w,g,y,L+60*c.offset*1e3)):n?new Date(Date.UTC(p,v,Y,w,g,y,L)):(D=new Date(p,v,Y,w,g,y,L),m&&(D=r(D).week(m).toDate()),D)}catch(e){return new Date(\"\")}}(t,a,r,n),this.init(),d&&!0!==d&&(this.$L=this.locale(d).$L),u&&t!=this.format(a)&&(this.$d=new Date(\"\")),s={}}else if(a instanceof Array)for(var c=a.length,m=1;m<=c;m+=1){o[1]=a[m-1];var M=n.apply(this,o);if(M.isValid()){this.$d=M.$d,this.$L=M.$L,this.init();break}m===c&&(this.$d=new Date(\"\"))}else i.call(this,e)}}}));", "!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define(t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_advancedFormat=t()}(this,(function(){\"use strict\";return function(e,t){var r=t.prototype,n=r.format;r.format=function(e){var t=this,r=this.$locale();if(!this.isValid())return n.bind(this)(e);var s=this.$utils(),a=(e||\"YYYY-MM-DDTHH:mm:ssZ\").replace(/\\[([^\\]]+)]|Q|wo|ww|w|WW|W|zzz|z|gggg|GGGG|Do|X|x|k{1,2}|S/g,(function(e){switch(e){case\"Q\":return Math.ceil((t.$M+1)/3);case\"Do\":return r.ordinal(t.$D);case\"gggg\":return t.weekYear();case\"GGGG\":return t.isoWeekYear();case\"wo\":return r.ordinal(t.week(),\"W\");case\"w\":case\"ww\":return s.s(t.week(),\"w\"===e?1:2,\"0\");case\"W\":case\"WW\":return s.s(t.isoWeek(),\"W\"===e?1:2,\"0\");case\"k\":case\"kk\":return s.s(String(0===t.$H?24:t.$H),\"k\"===e?1:2,\"0\");case\"X\":return Math.floor(t.$d.getTime()/1e3);case\"x\":return t.$d.getTime();case\"z\":return\"[\"+t.offsetName()+\"]\";case\"zzz\":return\"[\"+t.offsetName(\"long\")+\"]\";default:return e}}));return n.bind(this)(a)}}}));", "!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define(t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_weekOfYear=t()}(this,(function(){\"use strict\";var e=\"week\",t=\"year\";return function(i,n,r){var f=n.prototype;f.week=function(i){if(void 0===i&&(i=null),null!==i)return this.add(7*(i-this.week()),\"day\");var n=this.$locale().yearStart||1;if(11===this.month()&&this.date()>25){var f=r(this).startOf(t).add(1,t).date(n),s=r(this).endOf(e);if(f.isBefore(s))return 1}var a=r(this).startOf(t).date(n).startOf(e).subtract(1,\"millisecond\"),o=this.diff(a,e,!0);return o<0?r(this).startOf(\"week\").week():Math.ceil(o)},f.weeks=function(e){return void 0===e&&(e=null),this.week(e)}}}));", "!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define(t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_weekYear=t()}(this,(function(){\"use strict\";return function(e,t){t.prototype.weekYear=function(){var e=this.month(),t=this.week(),n=this.year();return 1===t&&11===e?n+1:0===e&&t>=52?n-1:n}}}));", "!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define(t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_dayOfYear=t()}(this,(function(){\"use strict\";return function(e,t,n){t.prototype.dayOfYear=function(e){var t=Math.round((n(this).startOf(\"day\")-n(this).startOf(\"year\"))/864e5)+1;return null==e?t:this.add(e-t,\"day\")}}}));", "!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define(t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_isSameOrAfter=t()}(this,(function(){\"use strict\";return function(e,t){t.prototype.isSameOrAfter=function(e,t){return this.isSame(e,t)||this.isAfter(e,t)}}}));", "!function(e,i){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=i():\"function\"==typeof define&&define.amd?define(i):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_isSameOrBefore=i()}(this,(function(){\"use strict\";return function(e,i){i.prototype.isSameOrBefore=function(e,i){return this.isSame(e,i)||this.isBefore(e,i)}}}));", "/* eslint no-console:0 */\n\nimport {\n  ValidateError,\n  ValidateOption,\n  RuleValuePackage,\n  InternalRuleItem,\n  SyncErrorType,\n  RuleType,\n  Value,\n  Values,\n} from './interface';\n\nconst formatRegExp = /%[sdj%]/g;\n\ndeclare var ASYNC_VALIDATOR_NO_WARNING;\n\nexport let warning: (type: string, errors: SyncErrorType[]) => void = () => {};\n\n// don't print warning message when in production env or node runtime\nif (\n  typeof process !== 'undefined' &&\n  process.env &&\n  process.env.NODE_ENV !== 'production' &&\n  typeof window !== 'undefined' &&\n  typeof document !== 'undefined'\n) {\n  warning = (type, errors) => {\n    if (\n      typeof console !== 'undefined' &&\n      console.warn &&\n      typeof ASYNC_VALIDATOR_NO_WARNING === 'undefined'\n    ) {\n      if (errors.every(e => typeof e === 'string')) {\n        console.warn(type, errors);\n      }\n    }\n  };\n}\n\nexport function convertFieldsError(\n  errors: ValidateError[],\n): Record<string, ValidateError[]> {\n  if (!errors || !errors.length) return null;\n  const fields = {};\n  errors.forEach(error => {\n    const field = error.field;\n    fields[field] = fields[field] || [];\n    fields[field].push(error);\n  });\n  return fields;\n}\n\nexport function format(\n  template: ((...args: any[]) => string) | string,\n  ...args: any[]\n): string {\n  let i = 0;\n  const len = args.length;\n  if (typeof template === 'function') {\n    return template.apply(null, args);\n  }\n  if (typeof template === 'string') {\n    let str = template.replace(formatRegExp, x => {\n      if (x === '%%') {\n        return '%';\n      }\n      if (i >= len) {\n        return x;\n      }\n      switch (x) {\n        case '%s':\n          return String(args[i++]);\n        case '%d':\n          return (Number(args[i++]) as unknown) as string;\n        case '%j':\n          try {\n            return JSON.stringify(args[i++]);\n          } catch (_) {\n            return '[Circular]';\n          }\n          break;\n        default:\n          return x;\n      }\n    });\n    return str;\n  }\n  return template;\n}\n\nfunction isNativeStringType(type: string) {\n  return (\n    type === 'string' ||\n    type === 'url' ||\n    type === 'hex' ||\n    type === 'email' ||\n    type === 'date' ||\n    type === 'pattern'\n  );\n}\n\nexport function isEmptyValue(value: Value, type?: string) {\n  if (value === undefined || value === null) {\n    return true;\n  }\n  if (type === 'array' && Array.isArray(value) && !value.length) {\n    return true;\n  }\n  if (isNativeStringType(type) && typeof value === 'string' && !value) {\n    return true;\n  }\n  return false;\n}\n\nexport function isEmptyObject(obj: object) {\n  return Object.keys(obj).length === 0;\n}\n\nfunction asyncParallelArray(\n  arr: RuleValuePackage[],\n  func: ValidateFunc,\n  callback: (errors: ValidateError[]) => void,\n) {\n  const results: ValidateError[] = [];\n  let total = 0;\n  const arrLength = arr.length;\n\n  function count(errors: ValidateError[]) {\n    results.push(...(errors || []));\n    total++;\n    if (total === arrLength) {\n      callback(results);\n    }\n  }\n\n  arr.forEach(a => {\n    func(a, count);\n  });\n}\n\nfunction asyncSerialArray(\n  arr: RuleValuePackage[],\n  func: ValidateFunc,\n  callback: (errors: ValidateError[]) => void,\n) {\n  let index = 0;\n  const arrLength = arr.length;\n\n  function next(errors: ValidateError[]) {\n    if (errors && errors.length) {\n      callback(errors);\n      return;\n    }\n    const original = index;\n    index = index + 1;\n    if (original < arrLength) {\n      func(arr[original], next);\n    } else {\n      callback([]);\n    }\n  }\n\n  next([]);\n}\n\nfunction flattenObjArr(objArr: Record<string, RuleValuePackage[]>) {\n  const ret: RuleValuePackage[] = [];\n  Object.keys(objArr).forEach(k => {\n    ret.push(...(objArr[k] || []));\n  });\n  return ret;\n}\n\nexport class AsyncValidationError extends Error {\n  errors: ValidateError[];\n  fields: Record<string, ValidateError[]>;\n\n  constructor(\n    errors: ValidateError[],\n    fields: Record<string, ValidateError[]>,\n  ) {\n    super('Async Validation Error');\n    this.errors = errors;\n    this.fields = fields;\n  }\n}\n\ntype ValidateFunc = (\n  data: RuleValuePackage,\n  doIt: (errors: ValidateError[]) => void,\n) => void;\n\nexport function asyncMap(\n  objArr: Record<string, RuleValuePackage[]>,\n  option: ValidateOption,\n  func: ValidateFunc,\n  callback: (errors: ValidateError[]) => void,\n  source: Values,\n): Promise<Values> {\n  if (option.first) {\n    const pending = new Promise<Values>((resolve, reject) => {\n      const next = (errors: ValidateError[]) => {\n        callback(errors);\n        return errors.length\n          ? reject(new AsyncValidationError(errors, convertFieldsError(errors)))\n          : resolve(source);\n      };\n      const flattenArr = flattenObjArr(objArr);\n      asyncSerialArray(flattenArr, func, next);\n    });\n    pending.catch(e => e);\n    return pending;\n  }\n  const firstFields =\n    option.firstFields === true\n      ? Object.keys(objArr)\n      : option.firstFields || [];\n\n  const objArrKeys = Object.keys(objArr);\n  const objArrLength = objArrKeys.length;\n  let total = 0;\n  const results: ValidateError[] = [];\n  const pending = new Promise<Values>((resolve, reject) => {\n    const next = (errors: ValidateError[]) => {\n      results.push.apply(results, errors);\n      total++;\n      if (total === objArrLength) {\n        callback(results);\n        return results.length\n          ? reject(\n              new AsyncValidationError(results, convertFieldsError(results)),\n            )\n          : resolve(source);\n      }\n    };\n    if (!objArrKeys.length) {\n      callback(results);\n      resolve(source);\n    }\n    objArrKeys.forEach(key => {\n      const arr = objArr[key];\n      if (firstFields.indexOf(key) !== -1) {\n        asyncSerialArray(arr, func, next);\n      } else {\n        asyncParallelArray(arr, func, next);\n      }\n    });\n  });\n  pending.catch(e => e);\n  return pending;\n}\n\nfunction isErrorObj(\n  obj: ValidateError | string | (() => string),\n): obj is ValidateError {\n  return !!(obj && (obj as ValidateError).message !== undefined);\n}\n\nfunction getValue(value: Values, path: string[]) {\n  let v = value;\n  for (let i = 0; i < path.length; i++) {\n    if (v == undefined) {\n      return v;\n    }\n    v = v[path[i]];\n  }\n  return v;\n}\n\nexport function complementError(rule: InternalRuleItem, source: Values) {\n  return (oe: ValidateError | (() => string) | string): ValidateError => {\n    let fieldValue;\n    if (rule.fullFields) {\n      fieldValue = getValue(source, rule.fullFields);\n    } else {\n      fieldValue = source[(oe as any).field || rule.fullField];\n    }\n    if (isErrorObj(oe)) {\n      oe.field = oe.field || rule.fullField;\n      oe.fieldValue = fieldValue;\n      return oe;\n    }\n    return {\n      message: typeof oe === 'function' ? oe() : oe,\n      fieldValue,\n      field: ((oe as unknown) as ValidateError).field || rule.fullField,\n    };\n  };\n}\n\nexport function deepMerge<T extends object>(target: T, source: Partial<T>): T {\n  if (source) {\n    for (const s in source) {\n      if (source.hasOwnProperty(s)) {\n        const value = source[s];\n        if (typeof value === 'object' && typeof target[s] === 'object') {\n          target[s] = {\n            ...target[s],\n            ...value,\n          };\n        } else {\n          target[s] = value;\n        }\n      }\n    }\n  }\n  return target;\n}\n", "import { ExecuteRule } from '../interface';\nimport { format, isEmptyValue } from '../util';\n\nconst required: ExecuteRule = (rule, value, source, errors, options, type) => {\n  if (\n    rule.required &&\n    (!source.hasOwnProperty(rule.field) ||\n      isEmptyValue(value, type || rule.type))\n  ) {\n    errors.push(format(options.messages.required, rule.fullField));\n  }\n};\n\nexport default required;\n", "import { ExecuteRule } from '../interface';\nimport { format } from '../util';\n\n/**\n *  Rule for validating whitespace.\n *\n *  @param rule The validation rule.\n *  @param value The value of the field on the source object.\n *  @param source The source object being validated.\n *  @param errors An array of errors that this rule may add\n *  validation errors to.\n *  @param options The validation options.\n *  @param options.messages The validation messages.\n */\nconst whitespace: ExecuteRule = (rule, value, source, errors, options) => {\n  if (/^\\s+$/.test(value) || value === '') {\n    errors.push(format(options.messages.whitespace, rule.fullField));\n  }\n};\n\nexport default whitespace;\n", "// https://github.com/kevva/url-regex/blob/master/index.js\nlet urlReg: RegExp;\n\nexport default () => {\n  if (urlReg) {\n    return urlReg;\n  }\n\n  const word = '[a-fA-F\\\\d:]';\n  const b = options =>\n    options && options.includeBoundaries\n      ? `(?:(?<=\\\\s|^)(?=${word})|(?<=${word})(?=\\\\s|$))`\n      : '';\n\n  const v4 =\n    '(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}';\n\n  const v6seg = '[a-fA-F\\\\d]{1,4}';\n  const v6 = `\n(?:\n(?:${v6seg}:){7}(?:${v6seg}|:)|                                    // 1:2:3:4:5:6:7::  1:2:3:4:5:6:7:8\n(?:${v6seg}:){6}(?:${v4}|:${v6seg}|:)|                             // 1:2:3:4:5:6::    1:2:3:4:5:6::8   1:2:3:4:5:6::8  1:2:3:4:5:6::*******\n(?:${v6seg}:){5}(?::${v4}|(?::${v6seg}){1,2}|:)|                   // 1:2:3:4:5::      1:2:3:4:5::7:8   1:2:3:4:5::8    1:2:3:4:5::7:*******\n(?:${v6seg}:){4}(?:(?::${v6seg}){0,1}:${v4}|(?::${v6seg}){1,3}|:)| // 1:2:3:4::        1:2:3:4::6:7:8   1:2:3:4::8      1:2:3:4::6:7:*******\n(?:${v6seg}:){3}(?:(?::${v6seg}){0,2}:${v4}|(?::${v6seg}){1,4}|:)| // 1:2:3::          1:2:3::5:6:7:8   1:2:3::8        1:2:3::5:6:7:*******\n(?:${v6seg}:){2}(?:(?::${v6seg}){0,3}:${v4}|(?::${v6seg}){1,5}|:)| // 1:2::            1:2::4:5:6:7:8   1:2::8          1:2::4:5:6:7:*******\n(?:${v6seg}:){1}(?:(?::${v6seg}){0,4}:${v4}|(?::${v6seg}){1,6}|:)| // 1::              1::3:4:5:6:7:8   1::8            1::3:4:5:6:7:*******\n(?::(?:(?::${v6seg}){0,5}:${v4}|(?::${v6seg}){1,7}|:))             // ::2:3:4:5:6:7:8  ::2:3:4:5:6:7:8  ::8             ::*******\n)(?:%[0-9a-zA-Z]{1,})?                                             // %eth0            %1\n`\n    .replace(/\\s*\\/\\/.*$/gm, '')\n    .replace(/\\n/g, '')\n    .trim();\n\n  // Pre-compile only the exact regexes because adding a global flag make regexes stateful\n  const v46Exact = new RegExp(`(?:^${v4}$)|(?:^${v6}$)`);\n  const v4exact = new RegExp(`^${v4}$`);\n  const v6exact = new RegExp(`^${v6}$`);\n\n  const ip = options =>\n    options && options.exact\n      ? v46Exact\n      : new RegExp(\n          `(?:${b(options)}${v4}${b(options)})|(?:${b(options)}${v6}${b(\n            options,\n          )})`,\n          'g',\n        );\n\n  ip.v4 = (options?) =>\n    options && options.exact\n      ? v4exact\n      : new RegExp(`${b(options)}${v4}${b(options)}`, 'g');\n  ip.v6 = (options?) =>\n    options && options.exact\n      ? v6exact\n      : new RegExp(`${b(options)}${v6}${b(options)}`, 'g');\n\n  const protocol = `(?:(?:[a-z]+:)?//)`;\n  const auth = '(?:\\\\S+(?::\\\\S*)?@)?';\n  const ipv4 = ip.v4().source;\n  const ipv6 = ip.v6().source;\n  const host = '(?:(?:[a-z\\\\u00a1-\\\\uffff0-9][-_]*)*[a-z\\\\u00a1-\\\\uffff0-9]+)';\n  const domain =\n    '(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff0-9]-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)*';\n  const tld = `(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff]{2,}))`;\n  const port = '(?::\\\\d{2,5})?';\n  const path = '(?:[/?#][^\\\\s\"]*)?';\n  const regex = `(?:${protocol}|www\\\\.)${auth}(?:localhost|${ipv4}|${ipv6}|${host}${domain}${tld})${port}${path}`;\n  urlReg = new RegExp(`(?:^${regex}$)`, 'i');\n  return urlReg;\n};\n", "import { ExecuteRule, Value } from '../interface';\nimport { format } from '../util';\nimport required from './required';\nimport getUrlRegex from './url';\n/* eslint max-len:0 */\n\nconst pattern = {\n  // http://emailregex.com/\n  email: /^(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}])|(([a-zA-Z\\-0-9\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]+\\.)+[a-zA-Z\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]{2,}))$/,\n  // url: new RegExp(\n  //   '^(?!mailto:)(?:(?:http|https|ftp)://|//)(?:\\\\S+(?::\\\\S*)?@)?(?:(?:(?:[1-9]\\\\d?|1\\\\d\\\\d|2[01]\\\\d|22[0-3])(?:\\\\.(?:1?\\\\d{1,2}|2[0-4]\\\\d|25[0-5])){2}(?:\\\\.(?:[0-9]\\\\d?|1\\\\d\\\\d|2[0-4]\\\\d|25[0-4]))|(?:(?:[a-z\\\\u00a1-\\\\uffff0-9]+-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff0-9]+-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)*(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff]{2,})))|localhost)(?::\\\\d{2,5})?(?:(/|\\\\?|#)[^\\\\s]*)?$',\n  //   'i',\n  // ),\n  hex: /^#?([a-f0-9]{6}|[a-f0-9]{3})$/i,\n};\n\nconst types = {\n  integer(value: Value) {\n    return types.number(value) && parseInt(value, 10) === value;\n  },\n  float(value: Value) {\n    return types.number(value) && !types.integer(value);\n  },\n  array(value: Value) {\n    return Array.isArray(value);\n  },\n  regexp(value: Value) {\n    if (value instanceof RegExp) {\n      return true;\n    }\n    try {\n      return !!new RegExp(value);\n    } catch (e) {\n      return false;\n    }\n  },\n  date(value: Value) {\n    return (\n      typeof value.getTime === 'function' &&\n      typeof value.getMonth === 'function' &&\n      typeof value.getYear === 'function' &&\n      !isNaN(value.getTime())\n    );\n  },\n  number(value: Value) {\n    if (isNaN(value)) {\n      return false;\n    }\n    return typeof value === 'number';\n  },\n  object(value: Value) {\n    return typeof value === 'object' && !types.array(value);\n  },\n  method(value: Value) {\n    return typeof value === 'function';\n  },\n  email(value: Value) {\n    return (\n      typeof value === 'string' &&\n      value.length <= 320 &&\n      !!value.match(pattern.email)\n    );\n  },\n  url(value: Value) {\n    return (\n      typeof value === 'string' &&\n      value.length <= 2048 &&\n      !!value.match(getUrlRegex())\n    );\n  },\n  hex(value: Value) {\n    return typeof value === 'string' && !!value.match(pattern.hex);\n  },\n};\n\nconst type: ExecuteRule = (rule, value, source, errors, options) => {\n  if (rule.required && value === undefined) {\n    required(rule, value, source, errors, options);\n    return;\n  }\n  const custom = [\n    'integer',\n    'float',\n    'array',\n    'regexp',\n    'object',\n    'method',\n    'email',\n    'number',\n    'date',\n    'url',\n    'hex',\n  ];\n  const ruleType = rule.type;\n  if (custom.indexOf(ruleType) > -1) {\n    if (!types[ruleType](value)) {\n      errors.push(\n        format(options.messages.types[ruleType], rule.fullField, rule.type),\n      );\n    }\n    // straight typeof check\n  } else if (ruleType && typeof value !== rule.type) {\n    errors.push(\n      format(options.messages.types[ruleType], rule.fullField, rule.type),\n    );\n  }\n};\n\nexport default type;\n", "import { ExecuteRule } from '../interface';\nimport { format } from '../util';\n\nconst range: ExecuteRule = (rule, value, source, errors, options) => {\n  const len = typeof rule.len === 'number';\n  const min = typeof rule.min === 'number';\n  const max = typeof rule.max === 'number';\n  // 正则匹配码点范围从U+010000一直到U+10FFFF的文字（补充平面Supplementary Plane）\n  const spRegexp = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/g;\n  let val = value;\n  let key = null;\n  const num = typeof value === 'number';\n  const str = typeof value === 'string';\n  const arr = Array.isArray(value);\n  if (num) {\n    key = 'number';\n  } else if (str) {\n    key = 'string';\n  } else if (arr) {\n    key = 'array';\n  }\n  // if the value is not of a supported type for range validation\n  // the validation rule rule should use the\n  // type property to also test for a particular type\n  if (!key) {\n    return false;\n  }\n  if (arr) {\n    val = value.length;\n  }\n  if (str) {\n    // 处理码点大于U+010000的文字length属性不准确的bug，如\"𠮷𠮷𠮷\".lenght !== 3\n    val = value.replace(spRegexp, '_').length;\n  }\n  if (len) {\n    if (val !== rule.len) {\n      errors.push(format(options.messages[key].len, rule.fullField, rule.len));\n    }\n  } else if (min && !max && val < rule.min) {\n    errors.push(format(options.messages[key].min, rule.fullField, rule.min));\n  } else if (max && !min && val > rule.max) {\n    errors.push(format(options.messages[key].max, rule.fullField, rule.max));\n  } else if (min && max && (val < rule.min || val > rule.max)) {\n    errors.push(\n      format(options.messages[key].range, rule.fullField, rule.min, rule.max),\n    );\n  }\n};\n\nexport default range;\n", "import { ExecuteRule } from '../interface';\nimport { format } from '../util';\n\nconst ENUM = 'enum' as const;\n\nconst enumerable: ExecuteRule = (rule, value, source, errors, options) => {\n  rule[ENUM] = Array.isArray(rule[ENUM]) ? rule[ENUM] : [];\n  if (rule[ENUM].indexOf(value) === -1) {\n    errors.push(\n      format(options.messages[ENUM], rule.fullField, rule[ENUM].join(', ')),\n    );\n  }\n};\n\nexport default enumerable;\n", "import { ExecuteRule } from '../interface';\nimport { format } from '../util';\n\nconst pattern: ExecuteRule = (rule, value, source, errors, options) => {\n  if (rule.pattern) {\n    if (rule.pattern instanceof RegExp) {\n      // if a RegExp instance is passed, reset `lastIndex` in case its `global`\n      // flag is accidentally set to `true`, which in a validation scenario\n      // is not necessary and the result might be misleading\n      rule.pattern.lastIndex = 0;\n      if (!rule.pattern.test(value)) {\n        errors.push(\n          format(\n            options.messages.pattern.mismatch,\n            rule.fullField,\n            value,\n            rule.pattern,\n          ),\n        );\n      }\n    } else if (typeof rule.pattern === 'string') {\n      const _pattern = new RegExp(rule.pattern);\n      if (!_pattern.test(value)) {\n        errors.push(\n          format(\n            options.messages.pattern.mismatch,\n            rule.fullField,\n            value,\n            rule.pattern,\n          ),\n        );\n      }\n    }\n  }\n};\n\nexport default pattern;\n", "import required from './required';\nimport whitespace from './whitespace';\nimport type from './type';\nimport range from './range';\nimport enumRule from './enum';\nimport pattern from './pattern';\n\nexport default {\n  required,\n  whitespace,\n  type,\n  range,\n  enum: enumRule,\n  pattern,\n};\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst string: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value, 'string') && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options, 'string');\n    if (!isEmptyValue(value, 'string')) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n      rules.pattern(rule, value, source, errors, options);\n      if (rule.whitespace === true) {\n        rules.whitespace(rule, value, source, errors, options);\n      }\n    }\n  }\n  callback(errors);\n};\n\nexport default string;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst method: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default method;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst number: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (value === '') {\n      value = undefined;\n    }\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default number;\n", "import { isEmptyValue } from '../util';\nimport rules from '../rule';\nimport { ExecuteValidator } from '../interface';\n\nconst boolean: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default boolean;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst regexp: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (!isEmptyValue(value)) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default regexp;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst integer: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default integer;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst floatFn: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default floatFn;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule/index';\n\nconst array: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if ((value === undefined || value === null) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options, 'array');\n    if (value !== undefined && value !== null) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default array;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst object: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default object;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst ENUM = 'enum' as const;\n\nconst enumerable: ExecuteValidator = (\n  rule,\n  value,\n  callback,\n  source,\n  options,\n) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules[ENUM](rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default enumerable;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst pattern: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value, 'string') && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (!isEmptyValue(value, 'string')) {\n      rules.pattern(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default pattern;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst date: ExecuteValidator = (rule, value, callback, source, options) => {\n  // console.log('integer rule called %j', rule);\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  // console.log('validate on %s value', value);\n  if (validate) {\n    if (isEmptyValue(value, 'date') && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (!isEmptyValue(value, 'date')) {\n      let dateObject;\n\n      if (value instanceof Date) {\n        dateObject = value;\n      } else {\n        dateObject = new Date(value);\n      }\n\n      rules.type(rule, dateObject, source, errors, options);\n      if (dateObject) {\n        rules.range(rule, dateObject.getTime(), source, errors, options);\n      }\n    }\n  }\n  callback(errors);\n};\n\nexport default date;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\n\nconst required: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const type = Array.isArray(value) ? 'array' : typeof value;\n  rules.required(rule, value, source, errors, options, type);\n  callback(errors);\n};\n\nexport default required;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst type: ExecuteValidator = (rule, value, callback, source, options) => {\n  const ruleType = rule.type;\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value, ruleType) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options, ruleType);\n    if (!isEmptyValue(value, ruleType)) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\n\nexport default type;\n", "import { ExecuteValidator } from '../interface';\nimport rules from '../rule';\nimport { isEmptyValue } from '../util';\n\nconst any: ExecuteValidator = (rule, value, callback, source, options) => {\n  const errors: string[] = [];\n  const validate =\n    rule.required || (!rule.required && source.hasOwnProperty(rule.field));\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n  }\n  callback(errors);\n};\n\nexport default any;\n", "import string from './string';\nimport method from './method';\nimport number from './number';\nimport boolean from './boolean';\nimport regexp from './regexp';\nimport integer from './integer';\nimport float from './float';\nimport array from './array';\nimport object from './object';\nimport enumValidator from './enum';\nimport pattern from './pattern';\nimport date from './date';\nimport required from './required';\nimport type from './type';\nimport any from './any';\n\nexport default {\n  string,\n  method,\n  number,\n  boolean,\n  regexp,\n  integer,\n  float,\n  array,\n  object,\n  enum: enumValidator,\n  pattern,\n  date,\n  url: type,\n  hex: type,\n  email: type,\n  required,\n  any,\n};\n", "import { InternalValidateMessages } from './interface';\n\nexport function newMessages(): InternalValidateMessages {\n  return {\n    default: 'Validation error on field %s',\n    required: '%s is required',\n    enum: '%s must be one of %s',\n    whitespace: '%s cannot be empty',\n    date: {\n      format: '%s date %s is invalid for format %s',\n      parse: '%s date could not be parsed, %s is invalid ',\n      invalid: '%s date %s is invalid',\n    },\n    types: {\n      string: '%s is not a %s',\n      method: '%s is not a %s (function)',\n      array: '%s is not an %s',\n      object: '%s is not an %s',\n      number: '%s is not a %s',\n      date: '%s is not a %s',\n      boolean: '%s is not a %s',\n      integer: '%s is not an %s',\n      float: '%s is not a %s',\n      regexp: '%s is not a valid %s',\n      email: '%s is not a valid %s',\n      url: '%s is not a valid %s',\n      hex: '%s is not a valid %s',\n    },\n    string: {\n      len: '%s must be exactly %s characters',\n      min: '%s must be at least %s characters',\n      max: '%s cannot be longer than %s characters',\n      range: '%s must be between %s and %s characters',\n    },\n    number: {\n      len: '%s must equal %s',\n      min: '%s cannot be less than %s',\n      max: '%s cannot be greater than %s',\n      range: '%s must be between %s and %s',\n    },\n    array: {\n      len: '%s must be exactly %s in length',\n      min: '%s cannot be less than %s in length',\n      max: '%s cannot be greater than %s in length',\n      range: '%s must be between %s and %s in length',\n    },\n    pattern: {\n      mismatch: '%s value %s does not match pattern %s',\n    },\n    clone() {\n      const cloned = JSON.parse(JSON.stringify(this));\n      cloned.clone = this.clone;\n      return cloned;\n    },\n  };\n}\n\nexport const messages = newMessages();\n", "import {\n  format,\n  complementError,\n  asyncMap,\n  warning,\n  deepMerge,\n  convertFieldsError,\n} from './util';\nimport validators from './validator/index';\nimport { messages as defaultMessages, newMessages } from './messages';\nimport {\n  InternalRuleItem,\n  InternalValidateMessages,\n  Rule,\n  RuleItem,\n  Rules,\n  ValidateCallback,\n  ValidateMessages,\n  ValidateOption,\n  Values,\n  RuleValuePackage,\n  ValidateError,\n  ValidateFieldsError,\n  SyncErrorType,\n  ValidateResult,\n} from './interface';\n\nexport * from './interface';\n\n/**\n *  Encapsulates a validation schema.\n *\n *  @param descriptor An object declaring validation rules\n *  for this schema.\n */\nclass Schema {\n  // ========================= Static =========================\n  static register = function register(type: string, validator) {\n    if (typeof validator !== 'function') {\n      throw new Error(\n        'Cannot register a validator by type, validator is not a function',\n      );\n    }\n    validators[type] = validator;\n  };\n\n  static warning = warning;\n\n  static messages = defaultMessages;\n\n  static validators = validators;\n\n  // ======================== Instance ========================\n  rules: Record<string, RuleItem[]> = null;\n  _messages: InternalValidateMessages = defaultMessages;\n\n  constructor(descriptor: Rules) {\n    this.define(descriptor);\n  }\n\n  define(rules: Rules) {\n    if (!rules) {\n      throw new Error('Cannot configure a schema with no rules');\n    }\n    if (typeof rules !== 'object' || Array.isArray(rules)) {\n      throw new Error('Rules must be an object');\n    }\n    this.rules = {};\n\n    Object.keys(rules).forEach(name => {\n      const item: Rule = rules[name];\n      this.rules[name] = Array.isArray(item) ? item : [item];\n    });\n  }\n\n  messages(messages?: ValidateMessages) {\n    if (messages) {\n      this._messages = deepMerge(newMessages(), messages);\n    }\n    return this._messages;\n  }\n\n  validate(\n    source: Values,\n    option?: ValidateOption,\n    callback?: ValidateCallback,\n  ): Promise<Values>;\n  validate(source: Values, callback: ValidateCallback): Promise<Values>;\n  validate(source: Values): Promise<Values>;\n\n  validate(source_: Values, o: any = {}, oc: any = () => {}): Promise<Values> {\n    let source: Values = source_;\n    let options: ValidateOption = o;\n    let callback: ValidateCallback = oc;\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n    if (!this.rules || Object.keys(this.rules).length === 0) {\n      if (callback) {\n        callback(null, source);\n      }\n      return Promise.resolve(source);\n    }\n\n    function complete(results: (ValidateError | ValidateError[])[]) {\n      let errors: ValidateError[] = [];\n      let fields: ValidateFieldsError = {};\n\n      function add(e: ValidateError | ValidateError[]) {\n        if (Array.isArray(e)) {\n          errors = errors.concat(...e);\n        } else {\n          errors.push(e);\n        }\n      }\n\n      for (let i = 0; i < results.length; i++) {\n        add(results[i]);\n      }\n      if (!errors.length) {\n        callback(null, source);\n      } else {\n        fields = convertFieldsError(errors);\n        (callback as (\n          errors: ValidateError[],\n          fields: ValidateFieldsError,\n        ) => void)(errors, fields);\n      }\n    }\n\n    if (options.messages) {\n      let messages = this.messages();\n      if (messages === defaultMessages) {\n        messages = newMessages();\n      }\n      deepMerge(messages, options.messages);\n      options.messages = messages;\n    } else {\n      options.messages = this.messages();\n    }\n\n    const series: Record<string, RuleValuePackage[]> = {};\n    const keys = options.keys || Object.keys(this.rules);\n    keys.forEach(z => {\n      const arr = this.rules[z];\n      let value = source[z];\n      arr.forEach(r => {\n        let rule: InternalRuleItem = r;\n        if (typeof rule.transform === 'function') {\n          if (source === source_) {\n            source = { ...source };\n          }\n          value = source[z] = rule.transform(value);\n        }\n        if (typeof rule === 'function') {\n          rule = {\n            validator: rule,\n          };\n        } else {\n          rule = { ...rule };\n        }\n\n        // Fill validator. Skip if nothing need to validate\n        rule.validator = this.getValidationMethod(rule);\n        if (!rule.validator) {\n          return;\n        }\n\n        rule.field = z;\n        rule.fullField = rule.fullField || z;\n        rule.type = this.getType(rule);\n        series[z] = series[z] || [];\n        series[z].push({\n          rule,\n          value,\n          source,\n          field: z,\n        });\n      });\n    });\n    const errorFields = {};\n    return asyncMap(\n      series,\n      options,\n      (data, doIt) => {\n        const rule = data.rule;\n        let deep =\n          (rule.type === 'object' || rule.type === 'array') &&\n          (typeof rule.fields === 'object' ||\n            typeof rule.defaultField === 'object');\n        deep = deep && (rule.required || (!rule.required && data.value));\n        rule.field = data.field;\n\n        function addFullField(key: string, schema: RuleItem) {\n          return {\n            ...schema,\n            fullField: `${rule.fullField}.${key}`,\n            fullFields: rule.fullFields ? [...rule.fullFields, key] : [key],\n          };\n        }\n\n        function cb(e: SyncErrorType | SyncErrorType[] = []) {\n          let errorList = Array.isArray(e) ? e : [e];\n          if (!options.suppressWarning && errorList.length) {\n            Schema.warning('async-validator:', errorList);\n          }\n          if (errorList.length && rule.message !== undefined) {\n            errorList = [].concat(rule.message);\n          }\n\n          // Fill error info\n          let filledErrors = errorList.map(complementError(rule, source));\n\n          if (options.first && filledErrors.length) {\n            errorFields[rule.field] = 1;\n            return doIt(filledErrors);\n          }\n          if (!deep) {\n            doIt(filledErrors);\n          } else {\n            // if rule is required but the target object\n            // does not exist fail at the rule level and don't\n            // go deeper\n            if (rule.required && !data.value) {\n              if (rule.message !== undefined) {\n                filledErrors = []\n                  .concat(rule.message)\n                  .map(complementError(rule, source));\n              } else if (options.error) {\n                filledErrors = [\n                  options.error(\n                    rule,\n                    format(options.messages.required, rule.field),\n                  ),\n                ];\n              }\n              return doIt(filledErrors);\n            }\n\n            let fieldsSchema: Record<string, Rule> = {};\n            if (rule.defaultField) {\n              Object.keys(data.value).map(key => {\n                fieldsSchema[key] = rule.defaultField;\n              });\n            }\n            fieldsSchema = {\n              ...fieldsSchema,\n              ...data.rule.fields,\n            };\n\n            const paredFieldsSchema: Record<string, RuleItem[]> = {};\n\n            Object.keys(fieldsSchema).forEach(field => {\n              const fieldSchema = fieldsSchema[field];\n              const fieldSchemaList = Array.isArray(fieldSchema)\n                ? fieldSchema\n                : [fieldSchema];\n              paredFieldsSchema[field] = fieldSchemaList.map(\n                addFullField.bind(null, field),\n              );\n            });\n            const schema = new Schema(paredFieldsSchema);\n            schema.messages(options.messages);\n            if (data.rule.options) {\n              data.rule.options.messages = options.messages;\n              data.rule.options.error = options.error;\n            }\n            schema.validate(data.value, data.rule.options || options, errs => {\n              const finalErrors = [];\n              if (filledErrors && filledErrors.length) {\n                finalErrors.push(...filledErrors);\n              }\n              if (errs && errs.length) {\n                finalErrors.push(...errs);\n              }\n              doIt(finalErrors.length ? finalErrors : null);\n            });\n          }\n        }\n\n        let res: ValidateResult;\n        if (rule.asyncValidator) {\n          res = rule.asyncValidator(rule, data.value, cb, data.source, options);\n        } else if (rule.validator) {\n          try {\n            res = rule.validator(rule, data.value, cb, data.source, options);\n          } catch (error) {\n            console.error?.(error);\n            // rethrow to report error\n            if (!options.suppressValidatorError) {\n              setTimeout(() => {\n                throw error;\n              }, 0);\n            }\n            cb(error.message);\n          }\n          if (res === true) {\n            cb();\n          } else if (res === false) {\n            cb(\n              typeof rule.message === 'function'\n                ? rule.message(rule.fullField || rule.field)\n                : rule.message || `${rule.fullField || rule.field} fails`,\n            );\n          } else if (res instanceof Array) {\n            cb(res);\n          } else if (res instanceof Error) {\n            cb(res.message);\n          }\n        }\n        if (res && (res as Promise<void>).then) {\n          (res as Promise<void>).then(\n            () => cb(),\n            e => cb(e),\n          );\n        }\n      },\n      results => {\n        complete(results);\n      },\n      source,\n    );\n  }\n\n  getType(rule: InternalRuleItem) {\n    if (rule.type === undefined && rule.pattern instanceof RegExp) {\n      rule.type = 'pattern';\n    }\n    if (\n      typeof rule.validator !== 'function' &&\n      rule.type &&\n      !validators.hasOwnProperty(rule.type)\n    ) {\n      throw new Error(format('Unknown rule type %s', rule.type));\n    }\n    return rule.type || 'string';\n  }\n\n  getValidationMethod(rule: InternalRuleItem) {\n    if (typeof rule.validator === 'function') {\n      return rule.validator;\n    }\n    const keys = Object.keys(rule);\n    const messageIndex = keys.indexOf('message');\n    if (messageIndex !== -1) {\n      keys.splice(messageIndex, 1);\n    }\n    if (keys.length === 1 && keys[0] === 'required') {\n      return validators.required;\n    }\n    return validators[this.getType(rule)] || undefined;\n  }\n}\n\nexport default Schema;\n", "/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */\n\nconst sides = ['top', 'right', 'bottom', 'left'];\nconst alignments = ['start', 'end'];\nconst placements = /*#__PURE__*/sides.reduce((acc, side) => acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nfunction getSideAxis(placement) {\n  return ['top', 'bottom'].includes(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nfunction getSideList(side, isStart, rtl) {\n  const lr = ['left', 'right'];\n  const rl = ['right', 'left'];\n  const tb = ['top', 'bottom'];\n  const bt = ['bottom', 'top'];\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rl : lr;\n      return isStart ? lr : rl;\n    case 'left':\n    case 'right':\n      return isStart ? tb : bt;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    ...padding\n  };\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  const {\n    x,\n    y,\n    width,\n    height\n  } = rect;\n  return {\n    width,\n    height,\n    top: y,\n    left: x,\n    right: x + width,\n    bottom: y + height,\n    x,\n    y\n  };\n}\n\nexport { alignments, clamp, createCoords, evaluate, expandPaddingObject, floor, getAlignment, getAlignmentAxis, getAlignmentSides, getAxisLength, getExpandedPlacements, getOppositeAlignmentPlacement, getOppositeAxis, getOppositeAxisPlacements, getOppositePlacement, getPaddingObject, getSide, getSideAxis, max, min, placements, rectToClientRect, round, sides };\n", "import { getSideAxis, getAlignmentAxis, getAxisLength, getSide, getAlignment, evaluate, getPaddingObject, rectToClientRect, min, clamp, placements, getAlignmentSides, getOppositeAlignmentPlacement, getOppositePlacement, getExpandedPlacements, getOppositeAxisPlacements, sides, max, getOppositeAxis } from '@floating-ui/utils';\nexport { rectToClientRect } from '@floating-ui/utils';\n\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = getSideAxis(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const alignLength = getAxisLength(alignmentAxis);\n  const side = getSide(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = {\n      ...middlewareData,\n      [name]: {\n        ...middlewareData[name],\n        ...data\n      }\n    };\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = evaluate(options, state);\n  const paddingObject = getPaddingObject(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = rectToClientRect(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? {\n    x,\n    y,\n    width: rects.floating.width,\n    height: rects.floating.height\n  } : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements,\n      middlewareData\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = evaluate(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = getPaddingObject(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = getAlignmentAxis(placement);\n    const length = getAxisLength(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = min(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = clamp(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. To ensure `shift()` continues to take action,\n    // a single reset is performed when this is true.\n    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n    return {\n      [axis]: coords[axis] + alignmentOffset,\n      data: {\n        [axis]: offset,\n        centerOffset: center - offset - alignmentOffset,\n        ...(shouldAddOffset && {\n          alignmentOffset\n        })\n      },\n      reset: shouldAddOffset\n    };\n  }\n});\n\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => getAlignment(placement) === alignment), ...allowedPlacements.filter(placement => getAlignment(placement) !== alignment)] : allowedPlacements.filter(placement => getSide(placement) === placement);\n  return allowedPlacementsSortedByAlignment.filter(placement => {\n    if (alignment) {\n      return getAlignment(placement) === alignment || (autoAlignment ? getOppositeAlignmentPlacement(placement) !== placement : false);\n    }\n    return true;\n  });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'autoPlacement',\n    options,\n    async fn(state) {\n      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n      const {\n        rects,\n        middlewareData,\n        placement,\n        platform,\n        elements\n      } = state;\n      const {\n        crossAxis = false,\n        alignment,\n        allowedPlacements = placements,\n        autoAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const placements$1 = alignment !== undefined || allowedPlacements === placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n      const currentPlacement = placements$1[currentIndex];\n      if (currentPlacement == null) {\n        return {};\n      }\n      const alignmentSides = getAlignmentSides(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n\n      // Make `computeCoords` start from the right place.\n      if (placement !== currentPlacement) {\n        return {\n          reset: {\n            placement: placements$1[0]\n          }\n        };\n      }\n      const currentOverflows = [overflow[getSide(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\n      const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {\n        placement: currentPlacement,\n        overflows: currentOverflows\n      }];\n      const nextPlacement = placements$1[currentIndex + 1];\n\n      // There are more placements to check.\n      if (nextPlacement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: nextPlacement\n          }\n        };\n      }\n      const placementsSortedByMostSpace = allOverflows.map(d => {\n        const alignment = getAlignment(d.placement);\n        return [d.placement, alignment && crossAxis ?\n        // Check along the mainAxis and main crossAxis side.\n        d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :\n        // Check only the mainAxis.\n        d.overflows[0], d.overflows];\n      }).sort((a, b) => a[1] - b[1]);\n      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,\n      // Aligned placements should not check their opposite crossAxis\n      // side.\n      getAlignment(d[0]) ? 2 : 3).every(v => v <= 0));\n      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n      if (resetPlacement !== placement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: resetPlacement\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$arrow, _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true,\n        fallbackPlacements: specifiedFallbackPlacements,\n        fallbackStrategy = 'bestFit',\n        fallbackAxisSideDirection = 'none',\n        flipAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n\n      // If a reset by the arrow was caused due to an alignment offset being\n      // added, we should skip any logic now since `flip()` has already done its\n      // work.\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      const side = getSide(placement);\n      const initialSideAxis = getSideAxis(initialPlacement);\n      const isBasePlacement = getSide(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== 'none';\n      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = getAlignmentSides(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          // Try next placement and re-run the lifecycle.\n          return {\n            data: {\n              index: nextIndex,\n              overflows: overflowsData\n            },\n            reset: {\n              placement: nextPlacement\n            }\n          };\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$filter2;\n                const placement = (_overflowsData$filter2 = overflowsData.filter(d => {\n                  if (hasFallbackAxisSideDirection) {\n                    const currentSideAxis = getSideAxis(d.placement);\n                    return currentSideAxis === initialSideAxis ||\n                    // Create a bias to the `y` side axis due to horizontal\n                    // reading directions favoring greater width.\n                    currentSideAxis === 'y';\n                  }\n                  return true;\n                }).map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\n\nfunction getSideOffsets(overflow, rect) {\n  return {\n    top: overflow.top - rect.height,\n    right: overflow.right - rect.width,\n    bottom: overflow.bottom - rect.height,\n    left: overflow.left - rect.width\n  };\n}\nfunction isAnySideFullyClipped(overflow) {\n  return sides.some(side => overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'hide',\n    options,\n    async fn(state) {\n      const {\n        rects\n      } = state;\n      const {\n        strategy = 'referenceHidden',\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      switch (strategy) {\n        case 'referenceHidden':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              elementContext: 'reference'\n            });\n            const offsets = getSideOffsets(overflow, rects.reference);\n            return {\n              data: {\n                referenceHiddenOffsets: offsets,\n                referenceHidden: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        case 'escaped':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              altBoundary: true\n            });\n            const offsets = getSideOffsets(overflow, rects.floating);\n            return {\n              data: {\n                escapedOffsets: offsets,\n                escaped: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        default:\n          {\n            return {};\n          }\n      }\n    }\n  };\n};\n\nfunction getBoundingRect(rects) {\n  const minX = min(...rects.map(rect => rect.left));\n  const minY = min(...rects.map(rect => rect.top));\n  const maxX = max(...rects.map(rect => rect.right));\n  const maxY = max(...rects.map(rect => rect.bottom));\n  return {\n    x: minX,\n    y: minY,\n    width: maxX - minX,\n    height: maxY - minY\n  };\n}\nfunction getRectsByLine(rects) {\n  const sortedRects = rects.slice().sort((a, b) => a.y - b.y);\n  const groups = [];\n  let prevRect = null;\n  for (let i = 0; i < sortedRects.length; i++) {\n    const rect = sortedRects[i];\n    if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n      groups.push([rect]);\n    } else {\n      groups[groups.length - 1].push(rect);\n    }\n    prevRect = rect;\n  }\n  return groups.map(rect => rectToClientRect(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'inline',\n    options,\n    async fn(state) {\n      const {\n        placement,\n        elements,\n        rects,\n        platform,\n        strategy\n      } = state;\n      // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n      // ClientRect's bounds, despite the event listener being triggered. A\n      // padding of 2 seems to handle this issue.\n      const {\n        padding = 2,\n        x,\n        y\n      } = evaluate(options, state);\n      const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);\n      const clientRects = getRectsByLine(nativeClientRects);\n      const fallback = rectToClientRect(getBoundingRect(nativeClientRects));\n      const paddingObject = getPaddingObject(padding);\n      function getBoundingClientRect() {\n        // There are two rects and they are disjoined.\n        if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n          // Find the first rect in which the point is fully inside.\n          return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n        }\n\n        // There are 2 or more connected rects.\n        if (clientRects.length >= 2) {\n          if (getSideAxis(placement) === 'y') {\n            const firstRect = clientRects[0];\n            const lastRect = clientRects[clientRects.length - 1];\n            const isTop = getSide(placement) === 'top';\n            const top = firstRect.top;\n            const bottom = lastRect.bottom;\n            const left = isTop ? firstRect.left : lastRect.left;\n            const right = isTop ? firstRect.right : lastRect.right;\n            const width = right - left;\n            const height = bottom - top;\n            return {\n              top,\n              bottom,\n              left,\n              right,\n              width,\n              height,\n              x: left,\n              y: top\n            };\n          }\n          const isLeftSide = getSide(placement) === 'left';\n          const maxRight = max(...clientRects.map(rect => rect.right));\n          const minLeft = min(...clientRects.map(rect => rect.left));\n          const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n          const top = measureRects[0].top;\n          const bottom = measureRects[measureRects.length - 1].bottom;\n          const left = minLeft;\n          const right = maxRight;\n          const width = right - left;\n          const height = bottom - top;\n          return {\n            top,\n            bottom,\n            left,\n            right,\n            width,\n            height,\n            x: left,\n            y: top\n          };\n        }\n        return fallback;\n      }\n      const resetRects = await platform.getElementRects({\n        reference: {\n          getBoundingClientRect\n        },\n        floating: elements.floating,\n        strategy\n      });\n      if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n        return {\n          reset: {\n            rects: resetRects\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\n\nasync function convertValueToCoords(state, options) {\n  const {\n    placement,\n    platform,\n    elements\n  } = state;\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n  const side = getSide(placement);\n  const alignment = getAlignment(placement);\n  const isVertical = getSideAxis(placement) === 'y';\n  const mainAxisMulti = ['left', 'top'].includes(side) ? -1 : 1;\n  const crossAxisMulti = rtl && isVertical ? -1 : 1;\n  const rawValue = evaluate(options, state);\n\n  // eslint-disable-next-line prefer-const\n  let {\n    mainAxis,\n    crossAxis,\n    alignmentAxis\n  } = typeof rawValue === 'number' ? {\n    mainAxis: rawValue,\n    crossAxis: 0,\n    alignmentAxis: null\n  } : {\n    mainAxis: rawValue.mainAxis || 0,\n    crossAxis: rawValue.crossAxis || 0,\n    alignmentAxis: rawValue.alignmentAxis\n  };\n  if (alignment && typeof alignmentAxis === 'number') {\n    crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\n  }\n  return isVertical ? {\n    x: crossAxis * crossAxisMulti,\n    y: mainAxis * mainAxisMulti\n  } : {\n    x: mainAxis * mainAxisMulti,\n    y: crossAxis * crossAxisMulti\n  };\n}\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = function (options) {\n  if (options === void 0) {\n    options = 0;\n  }\n  return {\n    name: 'offset',\n    options,\n    async fn(state) {\n      var _middlewareData$offse, _middlewareData$arrow;\n      const {\n        x,\n        y,\n        placement,\n        middlewareData\n      } = state;\n      const diffCoords = await convertValueToCoords(state, options);\n\n      // If the placement is the same and the arrow caused an alignment offset\n      // then we don't need to change the positioning coordinates.\n      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      return {\n        x: x + diffCoords.x,\n        y: y + diffCoords.y,\n        data: {\n          ...diffCoords,\n          placement\n        }\n      };\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = false,\n        limiter = {\n          fn: _ref => {\n            let {\n              x,\n              y\n            } = _ref;\n            return {\n              x,\n              y\n            };\n          }\n        },\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = getSideAxis(getSide(placement));\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = clamp(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = clamp(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn({\n        ...state,\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      });\n      return {\n        ...limitedCoords,\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y,\n          enabled: {\n            [mainAxis]: checkMainAxis,\n            [crossAxis]: checkCrossAxis\n          }\n        }\n      };\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = getSideAxis(placement);\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = evaluate(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : {\n        mainAxis: 0,\n        crossAxis: 0,\n        ...rawOffset\n      };\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = ['top', 'left'].includes(getSide(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'size',\n    options,\n    async fn(state) {\n      var _state$middlewareData, _state$middlewareData2;\n      const {\n        placement,\n        rects,\n        platform,\n        elements\n      } = state;\n      const {\n        apply = () => {},\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const side = getSide(placement);\n      const alignment = getAlignment(placement);\n      const isYAxis = getSideAxis(placement) === 'y';\n      const {\n        width,\n        height\n      } = rects.floating;\n      let heightSide;\n      let widthSide;\n      if (side === 'top' || side === 'bottom') {\n        heightSide = side;\n        widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';\n      } else {\n        widthSide = side;\n        heightSide = alignment === 'end' ? 'top' : 'bottom';\n      }\n      const maximumClippingHeight = height - overflow.top - overflow.bottom;\n      const maximumClippingWidth = width - overflow.left - overflow.right;\n      const overflowAvailableHeight = min(height - overflow[heightSide], maximumClippingHeight);\n      const overflowAvailableWidth = min(width - overflow[widthSide], maximumClippingWidth);\n      const noShift = !state.middlewareData.shift;\n      let availableHeight = overflowAvailableHeight;\n      let availableWidth = overflowAvailableWidth;\n      if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n        availableWidth = maximumClippingWidth;\n      }\n      if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n        availableHeight = maximumClippingHeight;\n      }\n      if (noShift && !alignment) {\n        const xMin = max(overflow.left, 0);\n        const xMax = max(overflow.right, 0);\n        const yMin = max(overflow.top, 0);\n        const yMax = max(overflow.bottom, 0);\n        if (isYAxis) {\n          availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : max(overflow.left, overflow.right));\n        } else {\n          availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : max(overflow.top, overflow.bottom));\n        }\n      }\n      await apply({\n        ...state,\n        availableWidth,\n        availableHeight\n      });\n      const nextDimensions = await platform.getDimensions(elements.floating);\n      if (width !== nextDimensions.width || height !== nextDimensions.height) {\n        return {\n          reset: {\n            rects: true\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\nexport { arrow, autoPlacement, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, shift, size };\n", "function hasWindow() {\n  return typeof window !== 'undefined';\n}\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  if (!hasWindow() || typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isTopLayer(element) {\n  return [':popover-open', ':modal'].some(selector => {\n    try {\n      return element.matches(selector);\n    } catch (e) {\n      return false;\n    }\n  });\n}\nfunction isContainingBlock(elementOrCss) {\n  const webkit = isWebKit();\n  const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  // https://drafts.csswg.org/css-transforms-2/#individual-transforms\n  return ['transform', 'translate', 'scale', 'rotate', 'perspective'].some(value => css[value] ? css[value] !== 'none' : false) || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'translate', 'scale', 'rotate', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else if (isTopLayer(currentNode)) {\n      return null;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.scrollX,\n    scrollTop: element.scrollY\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    const frameElement = getFrameElement(win);\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\n\nexport { getComputedStyle, getContainingBlock, getDocumentElement, getFrameElement, getNearestOverflowAncestor, getNodeName, getNodeScroll, getOverflowAncestors, getParentNode, getWindow, isContainingBlock, isElement, isHTMLElement, isLastTraversableNode, isNode, isOverflowElement, isShadowRoot, isTableElement, isTopLayer, isWebKit };\n", "/**\n * Take input from [0, n] and return it as [0, 1]\n * @hidden\n */\nexport function bound01(n, max) {\n    if (isOnePointZero(n)) {\n        n = '100%';\n    }\n    var isPercent = isPercentage(n);\n    n = max === 360 ? n : Math.min(max, Math.max(0, parseFloat(n)));\n    // Automatically convert percentage into number\n    if (isPercent) {\n        n = parseInt(String(n * max), 10) / 100;\n    }\n    // Handle floating point rounding errors\n    if (Math.abs(n - max) < 0.000001) {\n        return 1;\n    }\n    // Convert into [0, 1] range if it isn't already\n    if (max === 360) {\n        // If n is a hue given in degrees,\n        // wrap around out-of-range values into [0, 360] range\n        // then convert into [0, 1].\n        n = (n < 0 ? (n % max) + max : n % max) / parseFloat(String(max));\n    }\n    else {\n        // If n not a hue given in degrees\n        // Convert into [0, 1] range if it isn't already.\n        n = (n % max) / parseFloat(String(max));\n    }\n    return n;\n}\n/**\n * Force a number between 0 and 1\n * @hidden\n */\nexport function clamp01(val) {\n    return Math.min(1, Math.max(0, val));\n}\n/**\n * Need to handle 1.0 as 100%, since once it is a number, there is no difference between it and 1\n * <http://stackoverflow.com/questions/7422072/javascript-how-to-detect-number-as-a-decimal-including-1-0>\n * @hidden\n */\nexport function isOnePointZero(n) {\n    return typeof n === 'string' && n.indexOf('.') !== -1 && parseFloat(n) === 1;\n}\n/**\n * Check to see if string passed in is a percentage\n * @hidden\n */\nexport function isPercentage(n) {\n    return typeof n === 'string' && n.indexOf('%') !== -1;\n}\n/**\n * Return a valid alpha value [0,1] with all invalid values being set to 1\n * @hidden\n */\nexport function boundAlpha(a) {\n    a = parseFloat(a);\n    if (isNaN(a) || a < 0 || a > 1) {\n        a = 1;\n    }\n    return a;\n}\n/**\n * Replace a decimal with it's percentage value\n * @hidden\n */\nexport function convertToPercentage(n) {\n    if (n <= 1) {\n        return \"\".concat(Number(n) * 100, \"%\");\n    }\n    return n;\n}\n/**\n * Force a hex value to have 2 characters\n * @hidden\n */\nexport function pad2(c) {\n    return c.length === 1 ? '0' + c : String(c);\n}\n", "import { bound01, pad2 } from './util.js';\n// `rgbToHsl`, `rgbToHsv`, `hslToRgb`, `hsvToRgb` modified from:\n// <http://mjijackson.com/2008/02/rgb-to-hsl-and-rgb-to-hsv-color-model-conversion-algorithms-in-javascript>\n/**\n * Handle bounds / percentage checking to conform to CSS color spec\n * <http://www.w3.org/TR/css3-color/>\n * *Assumes:* r, g, b in [0, 255] or [0, 1]\n * *Returns:* { r, g, b } in [0, 255]\n */\nexport function rgbToRgb(r, g, b) {\n    return {\n        r: bound01(r, 255) * 255,\n        g: bound01(g, 255) * 255,\n        b: bound01(b, 255) * 255,\n    };\n}\n/**\n * Converts an RGB color value to HSL.\n * *Assumes:* r, g, and b are contained in [0, 255] or [0, 1]\n * *Returns:* { h, s, l } in [0,1]\n */\nexport function rgbToHsl(r, g, b) {\n    r = bound01(r, 255);\n    g = bound01(g, 255);\n    b = bound01(b, 255);\n    var max = Math.max(r, g, b);\n    var min = Math.min(r, g, b);\n    var h = 0;\n    var s = 0;\n    var l = (max + min) / 2;\n    if (max === min) {\n        s = 0;\n        h = 0; // achromatic\n    }\n    else {\n        var d = max - min;\n        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n        switch (max) {\n            case r:\n                h = (g - b) / d + (g < b ? 6 : 0);\n                break;\n            case g:\n                h = (b - r) / d + 2;\n                break;\n            case b:\n                h = (r - g) / d + 4;\n                break;\n            default:\n                break;\n        }\n        h /= 6;\n    }\n    return { h: h, s: s, l: l };\n}\nfunction hue2rgb(p, q, t) {\n    if (t < 0) {\n        t += 1;\n    }\n    if (t > 1) {\n        t -= 1;\n    }\n    if (t < 1 / 6) {\n        return p + (q - p) * (6 * t);\n    }\n    if (t < 1 / 2) {\n        return q;\n    }\n    if (t < 2 / 3) {\n        return p + (q - p) * (2 / 3 - t) * 6;\n    }\n    return p;\n}\n/**\n * Converts an HSL color value to RGB.\n *\n * *Assumes:* h is contained in [0, 1] or [0, 360] and s and l are contained [0, 1] or [0, 100]\n * *Returns:* { r, g, b } in the set [0, 255]\n */\nexport function hslToRgb(h, s, l) {\n    var r;\n    var g;\n    var b;\n    h = bound01(h, 360);\n    s = bound01(s, 100);\n    l = bound01(l, 100);\n    if (s === 0) {\n        // achromatic\n        g = l;\n        b = l;\n        r = l;\n    }\n    else {\n        var q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n        var p = 2 * l - q;\n        r = hue2rgb(p, q, h + 1 / 3);\n        g = hue2rgb(p, q, h);\n        b = hue2rgb(p, q, h - 1 / 3);\n    }\n    return { r: r * 255, g: g * 255, b: b * 255 };\n}\n/**\n * Converts an RGB color value to HSV\n *\n * *Assumes:* r, g, and b are contained in the set [0, 255] or [0, 1]\n * *Returns:* { h, s, v } in [0,1]\n */\nexport function rgbToHsv(r, g, b) {\n    r = bound01(r, 255);\n    g = bound01(g, 255);\n    b = bound01(b, 255);\n    var max = Math.max(r, g, b);\n    var min = Math.min(r, g, b);\n    var h = 0;\n    var v = max;\n    var d = max - min;\n    var s = max === 0 ? 0 : d / max;\n    if (max === min) {\n        h = 0; // achromatic\n    }\n    else {\n        switch (max) {\n            case r:\n                h = (g - b) / d + (g < b ? 6 : 0);\n                break;\n            case g:\n                h = (b - r) / d + 2;\n                break;\n            case b:\n                h = (r - g) / d + 4;\n                break;\n            default:\n                break;\n        }\n        h /= 6;\n    }\n    return { h: h, s: s, v: v };\n}\n/**\n * Converts an HSV color value to RGB.\n *\n * *Assumes:* h is contained in [0, 1] or [0, 360] and s and v are contained in [0, 1] or [0, 100]\n * *Returns:* { r, g, b } in the set [0, 255]\n */\nexport function hsvToRgb(h, s, v) {\n    h = bound01(h, 360) * 6;\n    s = bound01(s, 100);\n    v = bound01(v, 100);\n    var i = Math.floor(h);\n    var f = h - i;\n    var p = v * (1 - s);\n    var q = v * (1 - f * s);\n    var t = v * (1 - (1 - f) * s);\n    var mod = i % 6;\n    var r = [v, q, p, p, t, v][mod];\n    var g = [t, v, v, q, p, p][mod];\n    var b = [p, p, t, v, v, q][mod];\n    return { r: r * 255, g: g * 255, b: b * 255 };\n}\n/**\n * Converts an RGB color to hex\n *\n * Assumes r, g, and b are contained in the set [0, 255]\n * Returns a 3 or 6 character hex\n */\nexport function rgbToHex(r, g, b, allow3Char) {\n    var hex = [\n        pad2(Math.round(r).toString(16)),\n        pad2(Math.round(g).toString(16)),\n        pad2(Math.round(b).toString(16)),\n    ];\n    // Return a 3 character hex if possible\n    if (allow3Char &&\n        hex[0].startsWith(hex[0].charAt(1)) &&\n        hex[1].startsWith(hex[1].charAt(1)) &&\n        hex[2].startsWith(hex[2].charAt(1))) {\n        return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0);\n    }\n    return hex.join('');\n}\n/**\n * Converts an RGBA color plus alpha transparency to hex\n *\n * Assumes r, g, b are contained in the set [0, 255] and\n * a in [0, 1]. Returns a 4 or 8 character rgba hex\n */\n// eslint-disable-next-line max-params\nexport function rgbaToHex(r, g, b, a, allow4Char) {\n    var hex = [\n        pad2(Math.round(r).toString(16)),\n        pad2(Math.round(g).toString(16)),\n        pad2(Math.round(b).toString(16)),\n        pad2(convertDecimalToHex(a)),\n    ];\n    // Return a 4 character hex if possible\n    if (allow4Char &&\n        hex[0].startsWith(hex[0].charAt(1)) &&\n        hex[1].startsWith(hex[1].charAt(1)) &&\n        hex[2].startsWith(hex[2].charAt(1)) &&\n        hex[3].startsWith(hex[3].charAt(1))) {\n        return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0) + hex[3].charAt(0);\n    }\n    return hex.join('');\n}\n/**\n * Converts an RGBA color to an ARGB Hex8 string\n * Rarely used, but required for \"toFilter()\"\n */\nexport function rgbaToArgbHex(r, g, b, a) {\n    var hex = [\n        pad2(convertDecimalToHex(a)),\n        pad2(Math.round(r).toString(16)),\n        pad2(Math.round(g).toString(16)),\n        pad2(Math.round(b).toString(16)),\n    ];\n    return hex.join('');\n}\n/** Converts a decimal to a hex value */\nexport function convertDecimalToHex(d) {\n    return Math.round(parseFloat(d) * 255).toString(16);\n}\n/** Converts a hex value to a decimal */\nexport function convertHexToDecimal(h) {\n    return parseIntFromHex(h) / 255;\n}\n/** Parse a base-16 hex value into a base-10 integer */\nexport function parseIntFromHex(val) {\n    return parseInt(val, 16);\n}\nexport function numberInputToObject(color) {\n    return {\n        r: color >> 16,\n        g: (color & 0xff00) >> 8,\n        b: color & 0xff,\n    };\n}\n", "// https://github.com/bahamas10/css-color-names/blob/master/css-color-names.json\n/**\n * @hidden\n */\nexport var names = {\n    aliceblue: '#f0f8ff',\n    antiquewhite: '#faebd7',\n    aqua: '#00ffff',\n    aquamarine: '#7fffd4',\n    azure: '#f0ffff',\n    beige: '#f5f5dc',\n    bisque: '#ffe4c4',\n    black: '#000000',\n    blanchedalmond: '#ffebcd',\n    blue: '#0000ff',\n    blueviolet: '#8a2be2',\n    brown: '#a52a2a',\n    burlywood: '#deb887',\n    cadetblue: '#5f9ea0',\n    chartreuse: '#7fff00',\n    chocolate: '#d2691e',\n    coral: '#ff7f50',\n    cornflowerblue: '#6495ed',\n    cornsilk: '#fff8dc',\n    crimson: '#dc143c',\n    cyan: '#00ffff',\n    darkblue: '#00008b',\n    darkcyan: '#008b8b',\n    darkgoldenrod: '#b8860b',\n    darkgray: '#a9a9a9',\n    darkgreen: '#006400',\n    darkgrey: '#a9a9a9',\n    darkkhaki: '#bdb76b',\n    darkmagenta: '#8b008b',\n    darkolivegreen: '#556b2f',\n    darkorange: '#ff8c00',\n    darkorchid: '#9932cc',\n    darkred: '#8b0000',\n    darksalmon: '#e9967a',\n    darkseagreen: '#8fbc8f',\n    darkslateblue: '#483d8b',\n    darkslategray: '#2f4f4f',\n    darkslategrey: '#2f4f4f',\n    darkturquoise: '#00ced1',\n    darkviolet: '#9400d3',\n    deeppink: '#ff1493',\n    deepskyblue: '#00bfff',\n    dimgray: '#696969',\n    dimgrey: '#696969',\n    dodgerblue: '#1e90ff',\n    firebrick: '#b22222',\n    floralwhite: '#fffaf0',\n    forestgreen: '#228b22',\n    fuchsia: '#ff00ff',\n    gainsboro: '#dcdcdc',\n    ghostwhite: '#f8f8ff',\n    goldenrod: '#daa520',\n    gold: '#ffd700',\n    gray: '#808080',\n    green: '#008000',\n    greenyellow: '#adff2f',\n    grey: '#808080',\n    honeydew: '#f0fff0',\n    hotpink: '#ff69b4',\n    indianred: '#cd5c5c',\n    indigo: '#4b0082',\n    ivory: '#fffff0',\n    khaki: '#f0e68c',\n    lavenderblush: '#fff0f5',\n    lavender: '#e6e6fa',\n    lawngreen: '#7cfc00',\n    lemonchiffon: '#fffacd',\n    lightblue: '#add8e6',\n    lightcoral: '#f08080',\n    lightcyan: '#e0ffff',\n    lightgoldenrodyellow: '#fafad2',\n    lightgray: '#d3d3d3',\n    lightgreen: '#90ee90',\n    lightgrey: '#d3d3d3',\n    lightpink: '#ffb6c1',\n    lightsalmon: '#ffa07a',\n    lightseagreen: '#20b2aa',\n    lightskyblue: '#87cefa',\n    lightslategray: '#778899',\n    lightslategrey: '#778899',\n    lightsteelblue: '#b0c4de',\n    lightyellow: '#ffffe0',\n    lime: '#00ff00',\n    limegreen: '#32cd32',\n    linen: '#faf0e6',\n    magenta: '#ff00ff',\n    maroon: '#800000',\n    mediumaquamarine: '#66cdaa',\n    mediumblue: '#0000cd',\n    mediumorchid: '#ba55d3',\n    mediumpurple: '#9370db',\n    mediumseagreen: '#3cb371',\n    mediumslateblue: '#7b68ee',\n    mediumspringgreen: '#00fa9a',\n    mediumturquoise: '#48d1cc',\n    mediumvioletred: '#c71585',\n    midnightblue: '#191970',\n    mintcream: '#f5fffa',\n    mistyrose: '#ffe4e1',\n    moccasin: '#ffe4b5',\n    navajowhite: '#ffdead',\n    navy: '#000080',\n    oldlace: '#fdf5e6',\n    olive: '#808000',\n    olivedrab: '#6b8e23',\n    orange: '#ffa500',\n    orangered: '#ff4500',\n    orchid: '#da70d6',\n    palegoldenrod: '#eee8aa',\n    palegreen: '#98fb98',\n    paleturquoise: '#afeeee',\n    palevioletred: '#db7093',\n    papayawhip: '#ffefd5',\n    peachpuff: '#ffdab9',\n    peru: '#cd853f',\n    pink: '#ffc0cb',\n    plum: '#dda0dd',\n    powderblue: '#b0e0e6',\n    purple: '#800080',\n    rebeccapurple: '#663399',\n    red: '#ff0000',\n    rosybrown: '#bc8f8f',\n    royalblue: '#4169e1',\n    saddlebrown: '#8b4513',\n    salmon: '#fa8072',\n    sandybrown: '#f4a460',\n    seagreen: '#2e8b57',\n    seashell: '#fff5ee',\n    sienna: '#a0522d',\n    silver: '#c0c0c0',\n    skyblue: '#87ceeb',\n    slateblue: '#6a5acd',\n    slategray: '#708090',\n    slategrey: '#708090',\n    snow: '#fffafa',\n    springgreen: '#00ff7f',\n    steelblue: '#4682b4',\n    tan: '#d2b48c',\n    teal: '#008080',\n    thistle: '#d8bfd8',\n    tomato: '#ff6347',\n    turquoise: '#40e0d0',\n    violet: '#ee82ee',\n    wheat: '#f5deb3',\n    white: '#ffffff',\n    whitesmoke: '#f5f5f5',\n    yellow: '#ffff00',\n    yellowgreen: '#9acd32',\n};\n", "/* eslint-disable @typescript-eslint/no-redundant-type-constituents */\nimport { convertHexToDecimal, hslToRgb, hsvToRgb, parseIntFromHex, rgbToRgb, } from './conversion.js';\nimport { names } from './css-color-names.js';\nimport { boundAlpha, convertToPercentage } from './util.js';\n/**\n * Given a string or object, convert that input to RGB\n *\n * Possible string inputs:\n * ```\n * \"red\"\n * \"#f00\" or \"f00\"\n * \"#ff0000\" or \"ff0000\"\n * \"#ff000000\" or \"ff000000\"\n * \"rgb 255 0 0\" or \"rgb (255, 0, 0)\"\n * \"rgb 1.0 0 0\" or \"rgb (1, 0, 0)\"\n * \"rgba (255, 0, 0, 1)\" or \"rgba 255, 0, 0, 1\"\n * \"rgba (1.0, 0, 0, 1)\" or \"rgba 1.0, 0, 0, 1\"\n * \"hsl(0, 100%, 50%)\" or \"hsl 0 100% 50%\"\n * \"hsla(0, 100%, 50%, 1)\" or \"hsla 0 100% 50%, 1\"\n * \"hsv(0, 100%, 100%)\" or \"hsv 0 100% 100%\"\n * ```\n */\nexport function inputToRGB(color) {\n    var rgb = { r: 0, g: 0, b: 0 };\n    var a = 1;\n    var s = null;\n    var v = null;\n    var l = null;\n    var ok = false;\n    var format = false;\n    if (typeof color === 'string') {\n        color = stringInputToObject(color);\n    }\n    if (typeof color === 'object') {\n        if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {\n            rgb = rgbToRgb(color.r, color.g, color.b);\n            ok = true;\n            format = String(color.r).substr(-1) === '%' ? 'prgb' : 'rgb';\n        }\n        else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {\n            s = convertToPercentage(color.s);\n            v = convertToPercentage(color.v);\n            rgb = hsvToRgb(color.h, s, v);\n            ok = true;\n            format = 'hsv';\n        }\n        else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {\n            s = convertToPercentage(color.s);\n            l = convertToPercentage(color.l);\n            rgb = hslToRgb(color.h, s, l);\n            ok = true;\n            format = 'hsl';\n        }\n        if (Object.prototype.hasOwnProperty.call(color, 'a')) {\n            a = color.a;\n        }\n    }\n    a = boundAlpha(a);\n    return {\n        ok: ok,\n        format: color.format || format,\n        r: Math.min(255, Math.max(rgb.r, 0)),\n        g: Math.min(255, Math.max(rgb.g, 0)),\n        b: Math.min(255, Math.max(rgb.b, 0)),\n        a: a,\n    };\n}\n// <http://www.w3.org/TR/css3-values/#integers>\nvar CSS_INTEGER = '[-\\\\+]?\\\\d+%?';\n// <http://www.w3.org/TR/css3-values/#number-value>\nvar CSS_NUMBER = '[-\\\\+]?\\\\d*\\\\.\\\\d+%?';\n// Allow positive/negative integer/number.  Don't capture the either/or, just the entire outcome.\nvar CSS_UNIT = \"(?:\".concat(CSS_NUMBER, \")|(?:\").concat(CSS_INTEGER, \")\");\n// Actual matching.\n// Parentheses and commas are optional, but not required.\n// Whitespace can take the place of commas or opening paren\nvar PERMISSIVE_MATCH3 = \"[\\\\s|\\\\(]+(\".concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")\\\\s*\\\\)?\");\nvar PERMISSIVE_MATCH4 = \"[\\\\s|\\\\(]+(\".concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")\\\\s*\\\\)?\");\nvar matchers = {\n    CSS_UNIT: new RegExp(CSS_UNIT),\n    rgb: new RegExp('rgb' + PERMISSIVE_MATCH3),\n    rgba: new RegExp('rgba' + PERMISSIVE_MATCH4),\n    hsl: new RegExp('hsl' + PERMISSIVE_MATCH3),\n    hsla: new RegExp('hsla' + PERMISSIVE_MATCH4),\n    hsv: new RegExp('hsv' + PERMISSIVE_MATCH3),\n    hsva: new RegExp('hsva' + PERMISSIVE_MATCH4),\n    hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n    hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n};\n/**\n * Permissive string parsing.  Take in a number of formats, and output an object\n * based on detected format.  Returns `{ r, g, b }` or `{ h, s, l }` or `{ h, s, v}`\n */\nexport function stringInputToObject(color) {\n    color = color.trim().toLowerCase();\n    if (color.length === 0) {\n        return false;\n    }\n    var named = false;\n    if (names[color]) {\n        color = names[color];\n        named = true;\n    }\n    else if (color === 'transparent') {\n        return { r: 0, g: 0, b: 0, a: 0, format: 'name' };\n    }\n    // Try to match string input using regular expressions.\n    // Keep most of the number bounding out of this function - don't worry about [0,1] or [0,100] or [0,360]\n    // Just return an object and let the conversion functions handle that.\n    // This way the result will be the same whether the tinycolor is initialized with string or object.\n    var match = matchers.rgb.exec(color);\n    if (match) {\n        return { r: match[1], g: match[2], b: match[3] };\n    }\n    match = matchers.rgba.exec(color);\n    if (match) {\n        return { r: match[1], g: match[2], b: match[3], a: match[4] };\n    }\n    match = matchers.hsl.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], l: match[3] };\n    }\n    match = matchers.hsla.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], l: match[3], a: match[4] };\n    }\n    match = matchers.hsv.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], v: match[3] };\n    }\n    match = matchers.hsva.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], v: match[3], a: match[4] };\n    }\n    match = matchers.hex8.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1]),\n            g: parseIntFromHex(match[2]),\n            b: parseIntFromHex(match[3]),\n            a: convertHexToDecimal(match[4]),\n            format: named ? 'name' : 'hex8',\n        };\n    }\n    match = matchers.hex6.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1]),\n            g: parseIntFromHex(match[2]),\n            b: parseIntFromHex(match[3]),\n            format: named ? 'name' : 'hex',\n        };\n    }\n    match = matchers.hex4.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1] + match[1]),\n            g: parseIntFromHex(match[2] + match[2]),\n            b: parseIntFromHex(match[3] + match[3]),\n            a: convertHexToDecimal(match[4] + match[4]),\n            format: named ? 'name' : 'hex8',\n        };\n    }\n    match = matchers.hex3.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1] + match[1]),\n            g: parseIntFromHex(match[2] + match[2]),\n            b: parseIntFromHex(match[3] + match[3]),\n            format: named ? 'name' : 'hex',\n        };\n    }\n    return false;\n}\n/**\n * Check to see if it looks like a CSS unit\n * (see `matchers` above for definition).\n */\nexport function isValidCSSUnit(color) {\n    return Boolean(matchers.CSS_UNIT.exec(String(color)));\n}\n", "import { numberInputToObject, rgbaToHex, rgbToHex, rgbToHsl, rgbToHsv } from './conversion.js';\nimport { names } from './css-color-names.js';\nimport { inputToRGB } from './format-input';\nimport { bound01, boundAlpha, clamp01 } from './util.js';\nvar TinyColor = /** @class */ (function () {\n    function TinyColor(color, opts) {\n        if (color === void 0) { color = ''; }\n        if (opts === void 0) { opts = {}; }\n        var _a;\n        // If input is already a tinycolor, return itself\n        if (color instanceof TinyColor) {\n            // eslint-disable-next-line no-constructor-return\n            return color;\n        }\n        if (typeof color === 'number') {\n            color = numberInputToObject(color);\n        }\n        this.originalInput = color;\n        var rgb = inputToRGB(color);\n        this.originalInput = color;\n        this.r = rgb.r;\n        this.g = rgb.g;\n        this.b = rgb.b;\n        this.a = rgb.a;\n        this.roundA = Math.round(100 * this.a) / 100;\n        this.format = (_a = opts.format) !== null && _a !== void 0 ? _a : rgb.format;\n        this.gradientType = opts.gradientType;\n        // Don't let the range of [0,255] come back in [0,1].\n        // Potentially lose a little bit of precision here, but will fix issues where\n        // .5 gets interpreted as half of the total, instead of half of 1\n        // If it was supposed to be 128, this was already taken care of by `inputToRgb`\n        if (this.r < 1) {\n            this.r = Math.round(this.r);\n        }\n        if (this.g < 1) {\n            this.g = Math.round(this.g);\n        }\n        if (this.b < 1) {\n            this.b = Math.round(this.b);\n        }\n        this.isValid = rgb.ok;\n    }\n    TinyColor.prototype.isDark = function () {\n        return this.getBrightness() < 128;\n    };\n    TinyColor.prototype.isLight = function () {\n        return !this.isDark();\n    };\n    /**\n     * Returns the perceived brightness of the color, from 0-255.\n     */\n    TinyColor.prototype.getBrightness = function () {\n        // http://www.w3.org/TR/AERT#color-contrast\n        var rgb = this.toRgb();\n        return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;\n    };\n    /**\n     * Returns the perceived luminance of a color, from 0-1.\n     */\n    TinyColor.prototype.getLuminance = function () {\n        // http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n        var rgb = this.toRgb();\n        var R;\n        var G;\n        var B;\n        var RsRGB = rgb.r / 255;\n        var GsRGB = rgb.g / 255;\n        var BsRGB = rgb.b / 255;\n        if (RsRGB <= 0.03928) {\n            R = RsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            R = Math.pow((RsRGB + 0.055) / 1.055, 2.4);\n        }\n        if (GsRGB <= 0.03928) {\n            G = GsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            G = Math.pow((GsRGB + 0.055) / 1.055, 2.4);\n        }\n        if (BsRGB <= 0.03928) {\n            B = BsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            B = Math.pow((BsRGB + 0.055) / 1.055, 2.4);\n        }\n        return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n    };\n    /**\n     * Returns the alpha value of a color, from 0-1.\n     */\n    TinyColor.prototype.getAlpha = function () {\n        return this.a;\n    };\n    /**\n     * Sets the alpha value on the current color.\n     *\n     * @param alpha - The new alpha value. The accepted range is 0-1.\n     */\n    TinyColor.prototype.setAlpha = function (alpha) {\n        this.a = boundAlpha(alpha);\n        this.roundA = Math.round(100 * this.a) / 100;\n        return this;\n    };\n    /**\n     * Returns whether the color is monochrome.\n     */\n    TinyColor.prototype.isMonochrome = function () {\n        var s = this.toHsl().s;\n        return s === 0;\n    };\n    /**\n     * Returns the object as a HSVA object.\n     */\n    TinyColor.prototype.toHsv = function () {\n        var hsv = rgbToHsv(this.r, this.g, this.b);\n        return { h: hsv.h * 360, s: hsv.s, v: hsv.v, a: this.a };\n    };\n    /**\n     * Returns the hsva values interpolated into a string with the following format:\n     * \"hsva(xxx, xxx, xxx, xx)\".\n     */\n    TinyColor.prototype.toHsvString = function () {\n        var hsv = rgbToHsv(this.r, this.g, this.b);\n        var h = Math.round(hsv.h * 360);\n        var s = Math.round(hsv.s * 100);\n        var v = Math.round(hsv.v * 100);\n        return this.a === 1 ? \"hsv(\".concat(h, \", \").concat(s, \"%, \").concat(v, \"%)\") : \"hsva(\".concat(h, \", \").concat(s, \"%, \").concat(v, \"%, \").concat(this.roundA, \")\");\n    };\n    /**\n     * Returns the object as a HSLA object.\n     */\n    TinyColor.prototype.toHsl = function () {\n        var hsl = rgbToHsl(this.r, this.g, this.b);\n        return { h: hsl.h * 360, s: hsl.s, l: hsl.l, a: this.a };\n    };\n    /**\n     * Returns the hsla values interpolated into a string with the following format:\n     * \"hsla(xxx, xxx, xxx, xx)\".\n     */\n    TinyColor.prototype.toHslString = function () {\n        var hsl = rgbToHsl(this.r, this.g, this.b);\n        var h = Math.round(hsl.h * 360);\n        var s = Math.round(hsl.s * 100);\n        var l = Math.round(hsl.l * 100);\n        return this.a === 1 ? \"hsl(\".concat(h, \", \").concat(s, \"%, \").concat(l, \"%)\") : \"hsla(\".concat(h, \", \").concat(s, \"%, \").concat(l, \"%, \").concat(this.roundA, \")\");\n    };\n    /**\n     * Returns the hex value of the color.\n     * @param allow3Char will shorten hex value to 3 char if possible\n     */\n    TinyColor.prototype.toHex = function (allow3Char) {\n        if (allow3Char === void 0) { allow3Char = false; }\n        return rgbToHex(this.r, this.g, this.b, allow3Char);\n    };\n    /**\n     * Returns the hex value of the color -with a # prefixed.\n     * @param allow3Char will shorten hex value to 3 char if possible\n     */\n    TinyColor.prototype.toHexString = function (allow3Char) {\n        if (allow3Char === void 0) { allow3Char = false; }\n        return '#' + this.toHex(allow3Char);\n    };\n    /**\n     * Returns the hex 8 value of the color.\n     * @param allow4Char will shorten hex value to 4 char if possible\n     */\n    TinyColor.prototype.toHex8 = function (allow4Char) {\n        if (allow4Char === void 0) { allow4Char = false; }\n        return rgbaToHex(this.r, this.g, this.b, this.a, allow4Char);\n    };\n    /**\n     * Returns the hex 8 value of the color -with a # prefixed.\n     * @param allow4Char will shorten hex value to 4 char if possible\n     */\n    TinyColor.prototype.toHex8String = function (allow4Char) {\n        if (allow4Char === void 0) { allow4Char = false; }\n        return '#' + this.toHex8(allow4Char);\n    };\n    /**\n     * Returns the shorter hex value of the color depends on its alpha -with a # prefixed.\n     * @param allowShortChar will shorten hex value to 3 or 4 char if possible\n     */\n    TinyColor.prototype.toHexShortString = function (allowShortChar) {\n        if (allowShortChar === void 0) { allowShortChar = false; }\n        return this.a === 1 ? this.toHexString(allowShortChar) : this.toHex8String(allowShortChar);\n    };\n    /**\n     * Returns the object as a RGBA object.\n     */\n    TinyColor.prototype.toRgb = function () {\n        return {\n            r: Math.round(this.r),\n            g: Math.round(this.g),\n            b: Math.round(this.b),\n            a: this.a,\n        };\n    };\n    /**\n     * Returns the RGBA values interpolated into a string with the following format:\n     * \"RGBA(xxx, xxx, xxx, xx)\".\n     */\n    TinyColor.prototype.toRgbString = function () {\n        var r = Math.round(this.r);\n        var g = Math.round(this.g);\n        var b = Math.round(this.b);\n        return this.a === 1 ? \"rgb(\".concat(r, \", \").concat(g, \", \").concat(b, \")\") : \"rgba(\".concat(r, \", \").concat(g, \", \").concat(b, \", \").concat(this.roundA, \")\");\n    };\n    /**\n     * Returns the object as a RGBA object.\n     */\n    TinyColor.prototype.toPercentageRgb = function () {\n        var fmt = function (x) { return \"\".concat(Math.round(bound01(x, 255) * 100), \"%\"); };\n        return {\n            r: fmt(this.r),\n            g: fmt(this.g),\n            b: fmt(this.b),\n            a: this.a,\n        };\n    };\n    /**\n     * Returns the RGBA relative values interpolated into a string\n     */\n    TinyColor.prototype.toPercentageRgbString = function () {\n        var rnd = function (x) { return Math.round(bound01(x, 255) * 100); };\n        return this.a === 1\n            ? \"rgb(\".concat(rnd(this.r), \"%, \").concat(rnd(this.g), \"%, \").concat(rnd(this.b), \"%)\")\n            : \"rgba(\".concat(rnd(this.r), \"%, \").concat(rnd(this.g), \"%, \").concat(rnd(this.b), \"%, \").concat(this.roundA, \")\");\n    };\n    /**\n     * The 'real' name of the color -if there is one.\n     */\n    TinyColor.prototype.toName = function () {\n        if (this.a === 0) {\n            return 'transparent';\n        }\n        if (this.a < 1) {\n            return false;\n        }\n        var hex = '#' + rgbToHex(this.r, this.g, this.b, false);\n        for (var _i = 0, _a = Object.entries(names); _i < _a.length; _i++) {\n            var _b = _a[_i], key = _b[0], value = _b[1];\n            if (hex === value) {\n                return key;\n            }\n        }\n        return false;\n    };\n    TinyColor.prototype.toString = function (format) {\n        var formatSet = Boolean(format);\n        format = format !== null && format !== void 0 ? format : this.format;\n        var formattedString = false;\n        var hasAlpha = this.a < 1 && this.a >= 0;\n        var needsAlphaFormat = !formatSet && hasAlpha && (format.startsWith('hex') || format === 'name');\n        if (needsAlphaFormat) {\n            // Special case for \"transparent\", all other non-alpha formats\n            // will return rgba when there is transparency.\n            if (format === 'name' && this.a === 0) {\n                return this.toName();\n            }\n            return this.toRgbString();\n        }\n        if (format === 'rgb') {\n            formattedString = this.toRgbString();\n        }\n        if (format === 'prgb') {\n            formattedString = this.toPercentageRgbString();\n        }\n        if (format === 'hex' || format === 'hex6') {\n            formattedString = this.toHexString();\n        }\n        if (format === 'hex3') {\n            formattedString = this.toHexString(true);\n        }\n        if (format === 'hex4') {\n            formattedString = this.toHex8String(true);\n        }\n        if (format === 'hex8') {\n            formattedString = this.toHex8String();\n        }\n        if (format === 'name') {\n            formattedString = this.toName();\n        }\n        if (format === 'hsl') {\n            formattedString = this.toHslString();\n        }\n        if (format === 'hsv') {\n            formattedString = this.toHsvString();\n        }\n        return formattedString || this.toHexString();\n    };\n    TinyColor.prototype.toNumber = function () {\n        return (Math.round(this.r) << 16) + (Math.round(this.g) << 8) + Math.round(this.b);\n    };\n    TinyColor.prototype.clone = function () {\n        return new TinyColor(this.toString());\n    };\n    /**\n     * Lighten the color a given amount. Providing 100 will always return white.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.lighten = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.l += amount / 100;\n        hsl.l = clamp01(hsl.l);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Brighten the color a given amount, from 0 to 100.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.brighten = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var rgb = this.toRgb();\n        rgb.r = Math.max(0, Math.min(255, rgb.r - Math.round(255 * -(amount / 100))));\n        rgb.g = Math.max(0, Math.min(255, rgb.g - Math.round(255 * -(amount / 100))));\n        rgb.b = Math.max(0, Math.min(255, rgb.b - Math.round(255 * -(amount / 100))));\n        return new TinyColor(rgb);\n    };\n    /**\n     * Darken the color a given amount, from 0 to 100.\n     * Providing 100 will always return black.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.darken = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.l -= amount / 100;\n        hsl.l = clamp01(hsl.l);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Mix the color with pure white, from 0 to 100.\n     * Providing 0 will do nothing, providing 100 will always return white.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.tint = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        return this.mix('white', amount);\n    };\n    /**\n     * Mix the color with pure black, from 0 to 100.\n     * Providing 0 will do nothing, providing 100 will always return black.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.shade = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        return this.mix('black', amount);\n    };\n    /**\n     * Desaturate the color a given amount, from 0 to 100.\n     * Providing 100 will is the same as calling greyscale\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.desaturate = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.s -= amount / 100;\n        hsl.s = clamp01(hsl.s);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Saturate the color a given amount, from 0 to 100.\n     * @param amount - valid between 1-100\n     */\n    TinyColor.prototype.saturate = function (amount) {\n        if (amount === void 0) { amount = 10; }\n        var hsl = this.toHsl();\n        hsl.s += amount / 100;\n        hsl.s = clamp01(hsl.s);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Completely desaturates a color into greyscale.\n     * Same as calling `desaturate(100)`\n     */\n    TinyColor.prototype.greyscale = function () {\n        return this.desaturate(100);\n    };\n    /**\n     * Spin takes a positive or negative amount within [-360, 360] indicating the change of hue.\n     * Values outside of this range will be wrapped into this range.\n     */\n    TinyColor.prototype.spin = function (amount) {\n        var hsl = this.toHsl();\n        var hue = (hsl.h + amount) % 360;\n        hsl.h = hue < 0 ? 360 + hue : hue;\n        return new TinyColor(hsl);\n    };\n    /**\n     * Mix the current color a given amount with another color, from 0 to 100.\n     * 0 means no mixing (return current color).\n     */\n    TinyColor.prototype.mix = function (color, amount) {\n        if (amount === void 0) { amount = 50; }\n        var rgb1 = this.toRgb();\n        var rgb2 = new TinyColor(color).toRgb();\n        var p = amount / 100;\n        var rgba = {\n            r: (rgb2.r - rgb1.r) * p + rgb1.r,\n            g: (rgb2.g - rgb1.g) * p + rgb1.g,\n            b: (rgb2.b - rgb1.b) * p + rgb1.b,\n            a: (rgb2.a - rgb1.a) * p + rgb1.a,\n        };\n        return new TinyColor(rgba);\n    };\n    TinyColor.prototype.analogous = function (results, slices) {\n        if (results === void 0) { results = 6; }\n        if (slices === void 0) { slices = 30; }\n        var hsl = this.toHsl();\n        var part = 360 / slices;\n        var ret = [this];\n        for (hsl.h = (hsl.h - ((part * results) >> 1) + 720) % 360; --results;) {\n            hsl.h = (hsl.h + part) % 360;\n            ret.push(new TinyColor(hsl));\n        }\n        return ret;\n    };\n    /**\n     * taken from https://github.com/infusion/jQuery-xcolor/blob/master/jquery.xcolor.js\n     */\n    TinyColor.prototype.complement = function () {\n        var hsl = this.toHsl();\n        hsl.h = (hsl.h + 180) % 360;\n        return new TinyColor(hsl);\n    };\n    TinyColor.prototype.monochromatic = function (results) {\n        if (results === void 0) { results = 6; }\n        var hsv = this.toHsv();\n        var h = hsv.h;\n        var s = hsv.s;\n        var v = hsv.v;\n        var res = [];\n        var modification = 1 / results;\n        while (results--) {\n            res.push(new TinyColor({ h: h, s: s, v: v }));\n            v = (v + modification) % 1;\n        }\n        return res;\n    };\n    TinyColor.prototype.splitcomplement = function () {\n        var hsl = this.toHsl();\n        var h = hsl.h;\n        return [\n            this,\n            new TinyColor({ h: (h + 72) % 360, s: hsl.s, l: hsl.l }),\n            new TinyColor({ h: (h + 216) % 360, s: hsl.s, l: hsl.l }),\n        ];\n    };\n    /**\n     * Compute how the color would appear on a background\n     */\n    TinyColor.prototype.onBackground = function (background) {\n        var fg = this.toRgb();\n        var bg = new TinyColor(background).toRgb();\n        var alpha = fg.a + bg.a * (1 - fg.a);\n        return new TinyColor({\n            r: (fg.r * fg.a + bg.r * bg.a * (1 - fg.a)) / alpha,\n            g: (fg.g * fg.a + bg.g * bg.a * (1 - fg.a)) / alpha,\n            b: (fg.b * fg.a + bg.b * bg.a * (1 - fg.a)) / alpha,\n            a: alpha,\n        });\n    };\n    /**\n     * Alias for `polyad(3)`\n     */\n    TinyColor.prototype.triad = function () {\n        return this.polyad(3);\n    };\n    /**\n     * Alias for `polyad(4)`\n     */\n    TinyColor.prototype.tetrad = function () {\n        return this.polyad(4);\n    };\n    /**\n     * Get polyad colors, like (for 1, 2, 3, 4, 5, 6, 7, 8, etc...)\n     * monad, dyad, triad, tetrad, pentad, hexad, heptad, octad, etc...\n     */\n    TinyColor.prototype.polyad = function (n) {\n        var hsl = this.toHsl();\n        var h = hsl.h;\n        var result = [this];\n        var increment = 360 / n;\n        for (var i = 1; i < n; i++) {\n            result.push(new TinyColor({ h: (h + i * increment) % 360, s: hsl.s, l: hsl.l }));\n        }\n        return result;\n    };\n    /**\n     * compare color vs current color\n     */\n    TinyColor.prototype.equals = function (color) {\n        return this.toRgbString() === new TinyColor(color).toRgbString();\n    };\n    return TinyColor;\n}());\nexport { TinyColor };\n// kept for backwards compatability with v1\nexport function tinycolor(color, opts) {\n    if (color === void 0) { color = ''; }\n    if (opts === void 0) { opts = {}; }\n    return new TinyColor(color, opts);\n}\n", "import { TinyColor } from './index.js';\n// Readability Functions\n// ---------------------\n// <http://www.w3.org/TR/2008/REC-WCAG20-20081211/#contrast-ratiodef (WCAG Version 2)\n/**\n * AKA `contrast`\n *\n * Analyze the 2 colors and returns the color contrast defined by (WCAG Version 2)\n */\nexport function readability(color1, color2) {\n    var c1 = new TinyColor(color1);\n    var c2 = new TinyColor(color2);\n    return ((Math.max(c1.getLuminance(), c2.getLuminance()) + 0.05) /\n        (Math.min(c1.getLuminance(), c2.getLuminance()) + 0.05));\n}\n/**\n * Ensure that foreground and background color combinations meet WCAG2 guidelines.\n * The third argument is an object.\n *      the 'level' property states 'AA' or 'AAA' - if missing or invalid, it defaults to 'AA';\n *      the 'size' property states 'large' or 'small' - if missing or invalid, it defaults to 'small'.\n * If the entire object is absent, isReadable defaults to {level:\"AA\",size:\"small\"}.\n *\n * Example\n * ```ts\n * new TinyColor().isReadable('#000', '#111') => false\n * new TinyColor().isReadable('#000', '#111', { level: 'AA', size: 'large' }) => false\n * ```\n */\nexport function isReadable(color1, color2, wcag2) {\n    var _a, _b;\n    if (wcag2 === void 0) { wcag2 = { level: 'AA', size: 'small' }; }\n    var readabilityLevel = readability(color1, color2);\n    switch (((_a = wcag2.level) !== null && _a !== void 0 ? _a : 'AA') + ((_b = wcag2.size) !== null && _b !== void 0 ? _b : 'small')) {\n        case 'AAsmall':\n        case 'AAAlarge':\n            return readabilityLevel >= 4.5;\n        case 'AAlarge':\n            return readabilityLevel >= 3;\n        case 'AAAsmall':\n            return readabilityLevel >= 7;\n        default:\n            return false;\n    }\n}\n/**\n * Given a base color and a list of possible foreground or background\n * colors for that base, returns the most readable color.\n * Optionally returns Black or White if the most readable color is unreadable.\n *\n * @param baseColor - the base color.\n * @param colorList - array of colors to pick the most readable one from.\n * @param args - and object with extra arguments\n *\n * Example\n * ```ts\n * new TinyColor().mostReadable('#123', ['#124\", \"#125'], { includeFallbackColors: false }).toHexString(); // \"#112255\"\n * new TinyColor().mostReadable('#123', ['#124\", \"#125'],{ includeFallbackColors: true }).toHexString();  // \"#ffffff\"\n * new TinyColor().mostReadable('#a8015a', [\"#faf3f3\"], { includeFallbackColors:true, level: 'AAA', size: 'large' }).toHexString(); // \"#faf3f3\"\n * new TinyColor().mostReadable('#a8015a', [\"#faf3f3\"], { includeFallbackColors:true, level: 'AAA', size: 'small' }).toHexString(); // \"#ffffff\"\n * ```\n */\nexport function mostReadable(baseColor, colorList, args) {\n    if (args === void 0) { args = { includeFallbackColors: false, level: 'AA', size: 'small' }; }\n    var bestColor = null;\n    var bestScore = 0;\n    var includeFallbackColors = args.includeFallbackColors, level = args.level, size = args.size;\n    for (var _i = 0, colorList_1 = colorList; _i < colorList_1.length; _i++) {\n        var color = colorList_1[_i];\n        var score = readability(baseColor, color);\n        if (score > bestScore) {\n            bestScore = score;\n            bestColor = new TinyColor(color);\n        }\n    }\n    if (isReadable(baseColor, bestColor, { level: level, size: size }) || !includeFallbackColors) {\n        return bestColor;\n    }\n    args.includeFallbackColors = false;\n    return mostReadable(baseColor, ['#fff', '#000'], args);\n}\n", "import { rgbaToArgbHex } from './conversion.js';\nimport { TinyColor } from './index.js';\n/**\n * Returns the color represented as a Microsoft filter for use in old versions of IE.\n */\nexport function toMsFilter(firstColor, secondColor) {\n    var color = new TinyColor(firstColor);\n    var hex8String = '#' + rgbaToArgbHex(color.r, color.g, color.b, color.a);\n    var secondHex8String = hex8String;\n    var gradientType = color.gradientType ? 'GradientType = 1, ' : '';\n    if (secondColor) {\n        var s = new TinyColor(secondColor);\n        secondHex8String = '#' + rgbaToArgbHex(s.r, s.g, s.b, s.a);\n    }\n    return \"progid:DXImageTransform.Microsoft.gradient(\".concat(gradientType, \"startColorstr=\").concat(hex8String, \",endColorstr=\").concat(secondHex8String, \")\");\n}\n", "import { TinyColor } from './index.js';\nimport { convertToPercentage } from './util.js';\n/**\n * If input is an object, force 1 into \"1.0\" to handle ratios properly\n * String input requires \"1.0\" as input, so 1 will be treated as 1\n */\nexport function fromRatio(ratio, opts) {\n    var newColor = {\n        r: convertToPercentage(ratio.r),\n        g: convertToPercentage(ratio.g),\n        b: convertToPercentage(ratio.b),\n    };\n    if (ratio.a !== undefined) {\n        newColor.a = Number(ratio.a);\n    }\n    return new TinyColor(newColor, opts);\n}\n/** old random function */\nexport function legacyRandom() {\n    return new TinyColor({\n        r: Math.random(),\n        g: Math.random(),\n        b: Math.random(),\n    });\n}\n", "/* eslint-disable @typescript-eslint/no-redundant-type-constituents */\n// randomColor by <PERSON> under the CC0 license\n// https://github.com/davidmerfield/randomColor/\nimport { TinyColor } from './index.js';\nexport function random(options) {\n    if (options === void 0) { options = {}; }\n    // Check if we need to generate multiple colors\n    if (options.count !== undefined &&\n        options.count !== null) {\n        var totalColors = options.count;\n        var colors = [];\n        options.count = undefined;\n        while (totalColors > colors.length) {\n            // Since we're generating multiple colors,\n            // incremement the seed. Otherwise we'd just\n            // generate the same color each time...\n            options.count = null;\n            if (options.seed) {\n                options.seed += 1;\n            }\n            colors.push(random(options));\n        }\n        options.count = totalColors;\n        return colors;\n    }\n    // First we pick a hue (H)\n    var h = pickHue(options.hue, options.seed);\n    // Then use H to determine saturation (S)\n    var s = pickSaturation(h, options);\n    // Then use S and H to determine brightness (B).\n    var v = pickBrightness(h, s, options);\n    var res = { h: h, s: s, v: v };\n    if (options.alpha !== undefined) {\n        res.a = options.alpha;\n    }\n    // Then we return the HSB color in the desired format\n    return new TinyColor(res);\n}\nfunction pickHue(hue, seed) {\n    var hueRange = getHueRange(hue);\n    var res = randomWithin(hueRange, seed);\n    // Instead of storing red as two seperate ranges,\n    // we group them, using negative numbers\n    if (res < 0) {\n        res = 360 + res;\n    }\n    return res;\n}\nfunction pickSaturation(hue, options) {\n    if (options.hue === 'monochrome') {\n        return 0;\n    }\n    if (options.luminosity === 'random') {\n        return randomWithin([0, 100], options.seed);\n    }\n    var saturationRange = getColorInfo(hue).saturationRange;\n    var sMin = saturationRange[0];\n    var sMax = saturationRange[1];\n    switch (options.luminosity) {\n        case 'bright':\n            sMin = 55;\n            break;\n        case 'dark':\n            sMin = sMax - 10;\n            break;\n        case 'light':\n            sMax = 55;\n            break;\n        default:\n            break;\n    }\n    return randomWithin([sMin, sMax], options.seed);\n}\nfunction pickBrightness(H, S, options) {\n    var bMin = getMinimumBrightness(H, S);\n    var bMax = 100;\n    switch (options.luminosity) {\n        case 'dark':\n            bMax = bMin + 20;\n            break;\n        case 'light':\n            bMin = (bMax + bMin) / 2;\n            break;\n        case 'random':\n            bMin = 0;\n            bMax = 100;\n            break;\n        default:\n            break;\n    }\n    return randomWithin([bMin, bMax], options.seed);\n}\nfunction getMinimumBrightness(H, S) {\n    var lowerBounds = getColorInfo(H).lowerBounds;\n    for (var i = 0; i < lowerBounds.length - 1; i++) {\n        var s1 = lowerBounds[i][0];\n        var v1 = lowerBounds[i][1];\n        var s2 = lowerBounds[i + 1][0];\n        var v2 = lowerBounds[i + 1][1];\n        if (S >= s1 && S <= s2) {\n            var m = (v2 - v1) / (s2 - s1);\n            var b = v1 - m * s1;\n            return m * S + b;\n        }\n    }\n    return 0;\n}\nfunction getHueRange(colorInput) {\n    var num = parseInt(colorInput, 10);\n    if (!Number.isNaN(num) && num < 360 && num > 0) {\n        return [num, num];\n    }\n    if (typeof colorInput === 'string') {\n        var namedColor = bounds.find(function (n) { return n.name === colorInput; });\n        if (namedColor) {\n            var color = defineColor(namedColor);\n            if (color.hueRange) {\n                return color.hueRange;\n            }\n        }\n        var parsed = new TinyColor(colorInput);\n        if (parsed.isValid) {\n            var hue = parsed.toHsv().h;\n            return [hue, hue];\n        }\n    }\n    return [0, 360];\n}\nfunction getColorInfo(hue) {\n    // Maps red colors to make picking hue easier\n    if (hue >= 334 && hue <= 360) {\n        hue -= 360;\n    }\n    for (var _i = 0, bounds_1 = bounds; _i < bounds_1.length; _i++) {\n        var bound = bounds_1[_i];\n        var color = defineColor(bound);\n        if (color.hueRange && hue >= color.hueRange[0] && hue <= color.hueRange[1]) {\n            return color;\n        }\n    }\n    throw Error('Color not found');\n}\nfunction randomWithin(range, seed) {\n    if (seed === undefined) {\n        return Math.floor(range[0] + Math.random() * (range[1] + 1 - range[0]));\n    }\n    // Seeded random algorithm from http://indiegamr.com/generate-repeatable-random-numbers-in-js/\n    var max = range[1] || 1;\n    var min = range[0] || 0;\n    seed = (seed * 9301 + 49297) % 233280;\n    var rnd = seed / 233280.0;\n    return Math.floor(min + rnd * (max - min));\n}\nfunction defineColor(bound) {\n    var sMin = bound.lowerBounds[0][0];\n    var sMax = bound.lowerBounds[bound.lowerBounds.length - 1][0];\n    var bMin = bound.lowerBounds[bound.lowerBounds.length - 1][1];\n    var bMax = bound.lowerBounds[0][1];\n    return {\n        name: bound.name,\n        hueRange: bound.hueRange,\n        lowerBounds: bound.lowerBounds,\n        saturationRange: [sMin, sMax],\n        brightnessRange: [bMin, bMax],\n    };\n}\n/**\n * @hidden\n */\nexport var bounds = [\n    {\n        name: 'monochrome',\n        hueRange: null,\n        lowerBounds: [\n            [0, 0],\n            [100, 0],\n        ],\n    },\n    {\n        name: 'red',\n        hueRange: [-26, 18],\n        lowerBounds: [\n            [20, 100],\n            [30, 92],\n            [40, 89],\n            [50, 85],\n            [60, 78],\n            [70, 70],\n            [80, 60],\n            [90, 55],\n            [100, 50],\n        ],\n    },\n    {\n        name: 'orange',\n        hueRange: [19, 46],\n        lowerBounds: [\n            [20, 100],\n            [30, 93],\n            [40, 88],\n            [50, 86],\n            [60, 85],\n            [70, 70],\n            [100, 70],\n        ],\n    },\n    {\n        name: 'yellow',\n        hueRange: [47, 62],\n        lowerBounds: [\n            [25, 100],\n            [40, 94],\n            [50, 89],\n            [60, 86],\n            [70, 84],\n            [80, 82],\n            [90, 80],\n            [100, 75],\n        ],\n    },\n    {\n        name: 'green',\n        hueRange: [63, 178],\n        lowerBounds: [\n            [30, 100],\n            [40, 90],\n            [50, 85],\n            [60, 81],\n            [70, 74],\n            [80, 64],\n            [90, 50],\n            [100, 40],\n        ],\n    },\n    {\n        name: 'blue',\n        hueRange: [179, 257],\n        lowerBounds: [\n            [20, 100],\n            [30, 86],\n            [40, 80],\n            [50, 74],\n            [60, 60],\n            [70, 52],\n            [80, 44],\n            [90, 39],\n            [100, 35],\n        ],\n    },\n    {\n        name: 'purple',\n        hueRange: [258, 282],\n        lowerBounds: [\n            [20, 100],\n            [30, 87],\n            [40, 79],\n            [50, 70],\n            [60, 65],\n            [70, 59],\n            [80, 52],\n            [90, 45],\n            [100, 42],\n        ],\n    },\n    {\n        name: 'pink',\n        hueRange: [283, 334],\n        lowerBounds: [\n            [20, 100],\n            [30, 90],\n            [40, 86],\n            [60, 84],\n            [80, 80],\n            [90, 75],\n            [100, 73],\n        ],\n    },\n];\n", "export {};\n", "import { tinycolor } from './index.js';\nexport * from './index.js';\nexport * from './css-color-names.js';\nexport * from './readability.js';\nexport * from './to-ms-filter.js';\nexport * from './from-ratio.js';\nexport * from './format-input.js';\nexport * from './random.js';\nexport * from './interfaces.js';\nexport * from './conversion.js';\n// kept for backwards compatability with v1\nexport default tinycolor;\n", "var safeIsNaN = Number.isNaN ||\n    function ponyfill(value) {\n        return typeof value === 'number' && value !== value;\n    };\nfunction isEqual(first, second) {\n    if (first === second) {\n        return true;\n    }\n    if (safeIsNaN(first) && safeIsNaN(second)) {\n        return true;\n    }\n    return false;\n}\nfunction areInputsEqual(newInputs, lastInputs) {\n    if (newInputs.length !== lastInputs.length) {\n        return false;\n    }\n    for (var i = 0; i < newInputs.length; i++) {\n        if (!isEqual(newInputs[i], lastInputs[i])) {\n            return false;\n        }\n    }\n    return true;\n}\n\nfunction memoizeOne(resultFn, isEqual) {\n    if (isEqual === void 0) { isEqual = areInputsEqual; }\n    var cache = null;\n    function memoized() {\n        var newArgs = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            newArgs[_i] = arguments[_i];\n        }\n        if (cache && cache.lastThis === this && isEqual(newArgs, cache.lastArgs)) {\n            return cache.lastResult;\n        }\n        var lastResult = resultFn.apply(this, newArgs);\n        cache = {\n            lastResult: lastResult,\n            lastArgs: newArgs,\n            lastThis: this,\n        };\n        return lastResult;\n    }\n    memoized.clear = function clear() {\n        cache = null;\n    };\n    return memoized;\n}\n\nexport { memoizeOne as default };\n"], "mappings": ";;;;;;;AA8CA;;;;AAmBA,SAAA,IAAqB;AACnB,MAAI,CAAA,GAIJ;AAAA,QAAa;AAOb,QAAI,IAAM,UAAU,WAChB,IACF,iLAAiL,KAC/K,CACF,GACE,IAAK,+BAA+B,KAAK,CAAG;AAehD,QAbA,IAAU,qBAAqB,KAAK,CAAG,GACvC,IAAQ,cAAc,KAAK,CAAG,GAC9B,IAAW,WAAW,KAAK,CAAG,GAC9B,IAAU,cAAc,KAAK,CAAG,GAChC,IAAU,UAAU,KAAK,CAAG,GAO5B,IAAS,CAAC,CAAC,QAAQ,KAAK,CAAG,GAEvB,GAAO;AACT,UAAM,EAAM,CAAA,IACR,WAAW,EAAM,CAAA,CAAE,IACnB,EAAM,CAAA,IACN,WAAW,EAAM,CAAA,CAAE,IACnB,KAEA,KAAO,YAAY,SAAS,iBAC9B,IAAM,SAAS;AAGjB,UAAI,IAAU,yBAAyB,KAAK,CAAG;AAC/C,UAAmB,IAAU,WAAW,EAAQ,CAAA,CAAE,IAAI,IAAI,GAE1D,IAAW,EAAM,CAAA,IAAK,WAAW,EAAM,CAAA,CAAE,IAAI,KAC7C,IAAS,EAAM,CAAA,IAAK,WAAW,EAAM,CAAA,CAAE,IAAI,KAC3C,IAAU,EAAM,CAAA,IAAK,WAAW,EAAM,CAAA,CAAE,IAAI,KACxC,KAIF,IAAQ,yBAAyB,KAAK,CAAG,GACzC,IAAU,KAAS,EAAM,CAAA,IAAK,WAAW,EAAM,CAAA,CAAE,IAAI,OAErD,IAAU;IAEd,MACE,KAAM,IAAW,IAAS,IAAU,IAAU;AAGhD,QAAI,GAAI;AACN,UAAI,EAAG,CAAA,GAAI;AAMT,YAAI,IAAM,iCAAiC,KAAK,CAAG;AAEnD,YAAO,IAAM,WAAW,EAAI,CAAA,EAAG,QAAQ,KAAK,GAAG,CAAC,IAAI;MACtD,MACE,KAAO;AAET,UAAW,CAAC,CAAC,EAAG,CAAA,GAChB,IAAS,CAAC,CAAC,EAAG,CAAA;IAChB,MACE,KAAO,IAAW,IAAS;EAAA;AAE/B;AE5GA,SAAA,EAA0B,GAAiB,GAAS;AAClD,MACE,CAAC,EAAqB,aACrB,KAAW,EAAE,sBAAsB,UAEpC,QAAO;AAGT,MAAI,IAAY,OAAO,GACnB,IAAc,KAAa;AAE/B,MAAI,CAAC,GAAa;AAChB,QAAI,IAAU,SAAS,cAAc,KAAK;AAC1C,MAAQ,aAAa,GAAW,SAAS,GACzC,IAAc,OAAO,EAAQ,CAAA,KAAe;EAC9C;AAEA,SAAI,CAAC,KAAe,KAAiB,MAAoB,YAEvD,IAAc,SAAS,eAAe,WAAW,gBAAgB,KAAK,IAGjE;AACT;AC6DA,SAAA,EAAmC,GAAkB;AACnD,MAAI,IAAK,GACP,IAAK,GACL,IAAK,GACL,IAAK;AAGP,SAAI,YAAY,MACd,IAAK,EAAM,SAET,gBAAgB,MAClB,IAAK,CAAC,EAAM,aAAa,MAEvB,iBAAiB,MACnB,IAAK,CAAC,EAAM,cAAc,MAExB,iBAAiB,MACnB,IAAK,CAAC,EAAM,cAAc,MAIxB,UAAU,KAAS,EAAM,SAAS,EAAM,oBAC1C,IAAK,GACL,IAAK,IAGP,IAAK,IAAK,GACV,IAAK,IAAK,GAEN,YAAY,MACd,IAAK,EAAM,SAET,YAAY,MACd,IAAK,EAAM,UAGR,KAAM,MAAO,EAAM,cAClB,EAAM,aAAa,KAErB,KAAM,GACN,KAAM,MAGN,KAAM,GACN,KAAM,KAKN,KAAM,CAAC,MACT,IAAK,IAAK,IAAI,KAAK,IAEjB,KAAM,CAAC,MACT,IAAK,IAAK,IAAI,KAAK,IAGd,EAAE,OAAO,GAAI,OAAO,GAAI,QAAQ,GAAI,QAAQ,EAAG;AACxD;AHpIA,IAAI,GAGA,GAAK,GAAU,GAAQ,GAAS,GAGhC,GAGA,GAAM,GAAU,GAAQ,GAGxB,GAGA,GAAS,GAAO,GAEhB,GAoFA,GAsIG,GC5QH,GAYA,GAaG,GCzBH,GAiDG,GC7CH,GACA,GACA,GA8KG;AHnJP;;IAAI,IAAa;AAqGjB,IAAI,IAAuB,EAOzB,IAAI,WAAY;AACd,aAAO,EAAU,KAAK;IACxB,GAQA,qBAAqB,WAAY;AAC/B,aAAO,EAAU,KAAK,IAAmB;IAC3C,GAOA,MAAM,WAAY;AAChB,aAAO,EAAqB,GAAG,KAAK;IACtC,GAQA,SAAS,WAAY;AACnB,aAAO,EAAU,KAAK;IACxB,GAQA,OAAO,WAAY;AACjB,aAAO,EAAU,KAAK;IACxB,GAQA,QAAQ,WAAY;AAClB,aAAO,EAAU,KAAK;IACxB,GAMA,QAAQ,WAAY;AAClB,aAAO,EAAqB,OAAO;IACrC,GAQA,QAAQ,WAAY;AAClB,aAAO,EAAU,KAAK;IACxB,GAOA,SAAS,WAAY;AACnB,aAAO,EAAU,KAAK;IACxB,GAQA,KAAK,WAAY;AACf,aAAO,EAAU,KAAK;IACxB,GAOA,OAAO,WAAY;AACjB,aAAO,EAAU,KAAK;IACxB,GAQA,QAAQ,WAAY;AAClB,aAAO,EAAU,KAAK;IACxB,GAEA,QAAQ,WAAY;AAClB,aAAO,EAAU,KAAK,KAAW,KAAS,KAAY;IACxD,GAEA,WAAW,WAAY;AAErB,aAAO,EAAU,KAAK;IACxB,GAEA,SAAS,WAAY;AACnB,aAAO,EAAU,KAAK;IACxB,GAEA,MAAM,WAAY;AAChB,aAAO,EAAU,KAAK;IACxB,EACF;AApIA,IAsIO,IAAQ;AC5Qf,IAAI,IAAY,CAAC,EACf,OAAO,SAAW,OAClB,OAAO,YACP,OAAO,SAAS;AAHlB,IAYI,IAAuB,EACzB,WAAW,GAEX,eAAe,OAAO,SAAW,KAEjC,sBACE,KAAa,CAAC,EAAE,OAAO,oBAAoB,OAAO,cAEpD,gBAAgB,KAAa,CAAC,CAAC,OAAO,QAEtC,YAAY,CAAC,EACf;AAvBA,IAyBO,IAAQ;ACxBX,MAAqB,cACvB,IACE,SAAS,kBACT,SAAS,eAAe,cAGxB,SAAS,eAAe,WAAW,IAAI,EAAE,MAAM;AA0CnD,IAAO,IAAQ;AC7Cf,IAAI,IAAa;AAAjB,IACI,IAAc;AADlB,IAEI,IAAc;AAsKlB,MAAe,eAAe,WAAuB;AACnD,aAAO,EAAqB,QAAQ,IAChC,mBACA,EAAiB,OAAO,IACxB,UACA;IACN;AAEA,IAAO,IAAQ;;;;;ACjMf;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,0BAAwB,EAAE;AAAA,IAAC,EAAE,SAAM,WAAU;AAAC;AAAa,aAAO,SAAS,GAAE,GAAE,GAAE;AAAC,YAAI,IAAE,EAAE,WAAUA,KAAE,SAASC,IAAE;AAAC,iBAAOA,OAAIA,GAAE,UAAQA,KAAEA,GAAE;AAAA,QAAE,GAAEC,KAAE,SAASD,IAAEE,IAAEC,IAAEC,IAAEH,IAAE;AAAC,cAAII,KAAEL,GAAE,OAAKA,KAAEA,GAAE,QAAQ,GAAEM,KAAEP,GAAEM,GAAEH,EAAC,CAAC,GAAEK,KAAER,GAAEM,GAAEF,EAAC,CAAC,GAAEK,KAAEF,MAAGC,GAAE,IAAK,SAASP,IAAE;AAAC,mBAAOA,GAAE,MAAM,GAAEI,EAAC;AAAA,UAAC,CAAE;AAAE,cAAG,CAACH,GAAE,QAAOO;AAAE,cAAIC,KAAEJ,GAAE;AAAU,iBAAOG,GAAE,IAAK,SAASR,IAAEE,IAAE;AAAC,mBAAOM,IAAGN,MAAGO,MAAG,MAAI,CAAC;AAAA,UAAC,CAAE;AAAA,QAAC,GAAE,IAAE,WAAU;AAAC,iBAAO,EAAE,GAAG,EAAE,OAAO,CAAC;AAAA,QAAC,GAAEH,KAAE,SAASN,IAAEE,IAAE;AAAC,iBAAOF,GAAE,QAAQE,EAAC,KAAG,SAASF,IAAE;AAAC,mBAAOA,GAAE,QAAQ,kCAAkC,SAASA,IAAEE,IAAEC,IAAE;AAAC,qBAAOD,MAAGC,GAAE,MAAM,CAAC;AAAA,YAAC,CAAE;AAAA,UAAC,EAAEH,GAAE,QAAQE,GAAE,YAAY,CAAC,CAAC;AAAA,QAAC,GAAEK,KAAE,WAAU;AAAC,cAAIP,KAAE;AAAK,iBAAM,EAAC,QAAO,SAASE,IAAE;AAAC,mBAAOA,KAAEA,GAAE,OAAO,MAAM,IAAED,GAAED,IAAE,QAAQ;AAAA,UAAC,GAAE,aAAY,SAASE,IAAE;AAAC,mBAAOA,KAAEA,GAAE,OAAO,KAAK,IAAED,GAAED,IAAE,eAAc,UAAS,CAAC;AAAA,UAAC,GAAE,gBAAe,WAAU;AAAC,mBAAOA,GAAE,QAAQ,EAAE,aAAW;AAAA,UAAC,GAAE,UAAS,SAASE,IAAE;AAAC,mBAAOA,KAAEA,GAAE,OAAO,MAAM,IAAED,GAAED,IAAE,UAAU;AAAA,UAAC,GAAE,aAAY,SAASE,IAAE;AAAC,mBAAOA,KAAEA,GAAE,OAAO,IAAI,IAAED,GAAED,IAAE,eAAc,YAAW,CAAC;AAAA,UAAC,GAAE,eAAc,SAASE,IAAE;AAAC,mBAAOA,KAAEA,GAAE,OAAO,KAAK,IAAED,GAAED,IAAE,iBAAgB,YAAW,CAAC;AAAA,UAAC,GAAE,gBAAe,SAASE,IAAE;AAAC,mBAAOI,GAAEN,GAAE,QAAQ,GAAEE,EAAC;AAAA,UAAC,GAAE,UAAS,KAAK,QAAQ,EAAE,UAAS,SAAQ,KAAK,QAAQ,EAAE,QAAO;AAAA,QAAC;AAAE,UAAE,aAAW,WAAU;AAAC,iBAAOK,GAAE,KAAK,IAAI,EAAE;AAAA,QAAC,GAAE,EAAE,aAAW,WAAU;AAAC,cAAIP,KAAE,EAAE;AAAE,iBAAM,EAAC,gBAAe,WAAU;AAAC,mBAAOA,GAAE,aAAW;AAAA,UAAC,GAAE,UAAS,WAAU;AAAC,mBAAO,EAAE,SAAS;AAAA,UAAC,GAAE,eAAc,WAAU;AAAC,mBAAO,EAAE,cAAc;AAAA,UAAC,GAAE,aAAY,WAAU;AAAC,mBAAO,EAAE,YAAY;AAAA,UAAC,GAAE,QAAO,WAAU;AAAC,mBAAO,EAAE,OAAO;AAAA,UAAC,GAAE,aAAY,WAAU;AAAC,mBAAO,EAAE,YAAY;AAAA,UAAC,GAAE,gBAAe,SAASE,IAAE;AAAC,mBAAOI,GAAEN,IAAEE,EAAC;AAAA,UAAC,GAAE,UAASF,GAAE,UAAS,SAAQA,GAAE,QAAO;AAAA,QAAC,GAAE,EAAE,SAAO,WAAU;AAAC,iBAAOC,GAAE,EAAE,GAAE,QAAQ;AAAA,QAAC,GAAE,EAAE,cAAY,WAAU;AAAC,iBAAOA,GAAE,EAAE,GAAE,eAAc,UAAS,CAAC;AAAA,QAAC,GAAE,EAAE,WAAS,SAASD,IAAE;AAAC,iBAAOC,GAAE,EAAE,GAAE,YAAW,MAAK,MAAKD,EAAC;AAAA,QAAC,GAAE,EAAE,gBAAc,SAASA,IAAE;AAAC,iBAAOC,GAAE,EAAE,GAAE,iBAAgB,YAAW,GAAED,EAAC;AAAA,QAAC,GAAE,EAAE,cAAY,SAASA,IAAE;AAAC,iBAAOC,GAAE,EAAE,GAAE,eAAc,YAAW,GAAED,EAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA;AAAA;;;ACAjiE;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,iCAA+B,EAAE;AAAA,IAAC,EAAE,SAAM,WAAU;AAAC;AAAa,UAAI,IAAE,EAAC,KAAI,aAAY,IAAG,UAAS,GAAE,cAAa,IAAG,gBAAe,KAAI,uBAAsB,MAAK,4BAA2B,GAAE,IAAE,iGAAgG,IAAE,MAAK,IAAE,QAAO,IAAE,SAAQU,KAAE,sBAAqBC,KAAE,CAAC,GAAEC,KAAE,SAASC,IAAE;AAAC,gBAAOA,KAAE,CAACA,OAAIA,KAAE,KAAG,OAAK;AAAA,MAAI;AAAE,UAAIC,KAAE,SAASD,IAAE;AAAC,eAAO,SAASE,IAAE;AAAC,eAAKF,EAAC,IAAE,CAACE;AAAA,QAAC;AAAA,MAAC,GAAEC,KAAE,CAAC,uBAAsB,SAASH,IAAE;AAAC,SAAC,KAAK,SAAO,KAAK,OAAK,CAAC,IAAI,SAAO,SAASA,IAAE;AAAC,cAAG,CAACA,GAAE,QAAO;AAAE,cAAG,QAAMA,GAAE,QAAO;AAAE,cAAIE,KAAEF,GAAE,MAAM,cAAc,GAAEI,KAAE,KAAGF,GAAE,CAAC,KAAG,CAACA,GAAE,CAAC,KAAG;AAAG,iBAAO,MAAIE,KAAE,IAAE,QAAMF,GAAE,CAAC,IAAE,CAACE,KAAEA;AAAA,QAAC,EAAEJ,EAAC;AAAA,MAAC,CAAC,GAAEK,KAAE,SAASL,IAAE;AAAC,YAAIE,KAAEJ,GAAEE,EAAC;AAAE,eAAOE,OAAIA,GAAE,UAAQA,KAAEA,GAAE,EAAE,OAAOA,GAAE,CAAC;AAAA,MAAE,GAAEI,KAAE,SAASN,IAAEE,IAAE;AAAC,YAAIE,IAAEG,KAAET,GAAE;AAAS,YAAGS,IAAE;AAAC,mBAAQC,KAAE,GAAEA,MAAG,IAAGA,MAAG,EAAE,KAAGR,GAAE,QAAQO,GAAEC,IAAE,GAAEN,EAAC,CAAC,IAAE,IAAG;AAAC,YAAAE,KAAEI,KAAE;AAAG;AAAA,UAAK;AAAA,QAAC,MAAM,CAAAJ,KAAEJ,QAAKE,KAAE,OAAK;AAAM,eAAOE;AAAA,MAAC,GAAEK,KAAE,EAAC,GAAE,CAACZ,IAAE,SAASG,IAAE;AAAC,aAAK,YAAUM,GAAEN,IAAE,KAAE;AAAA,MAAC,CAAC,GAAE,GAAE,CAACH,IAAE,SAASG,IAAE;AAAC,aAAK,YAAUM,GAAEN,IAAE,IAAE;AAAA,MAAC,CAAC,GAAE,GAAE,CAAC,GAAE,SAASA,IAAE;AAAC,aAAK,QAAM,KAAGA,KAAE,KAAG;AAAA,MAAC,CAAC,GAAE,GAAE,CAAC,GAAE,SAASA,IAAE;AAAC,aAAK,eAAa,MAAI,CAACA;AAAA,MAAC,CAAC,GAAE,IAAG,CAAC,GAAE,SAASA,IAAE;AAAC,aAAK,eAAa,KAAG,CAACA;AAAA,MAAC,CAAC,GAAE,KAAI,CAAC,SAAQ,SAASA,IAAE;AAAC,aAAK,eAAa,CAACA;AAAA,MAAC,CAAC,GAAE,GAAE,CAAC,GAAEC,GAAE,SAAS,CAAC,GAAE,IAAG,CAAC,GAAEA,GAAE,SAAS,CAAC,GAAE,GAAE,CAAC,GAAEA,GAAE,SAAS,CAAC,GAAE,IAAG,CAAC,GAAEA,GAAE,SAAS,CAAC,GAAE,GAAE,CAAC,GAAEA,GAAE,OAAO,CAAC,GAAE,GAAE,CAAC,GAAEA,GAAE,OAAO,CAAC,GAAE,IAAG,CAAC,GAAEA,GAAE,OAAO,CAAC,GAAE,IAAG,CAAC,GAAEA,GAAE,OAAO,CAAC,GAAE,GAAE,CAAC,GAAEA,GAAE,KAAK,CAAC,GAAE,IAAG,CAAC,GAAEA,GAAE,KAAK,CAAC,GAAE,IAAG,CAACJ,IAAE,SAASG,IAAE;AAAC,YAAIE,KAAEJ,GAAE,SAAQM,KAAEJ,GAAE,MAAM,KAAK;AAAE,YAAG,KAAK,MAAII,GAAE,CAAC,GAAEF,GAAE,UAAQK,KAAE,GAAEA,MAAG,IAAGA,MAAG,EAAE,CAAAL,GAAEK,EAAC,EAAE,QAAQ,UAAS,EAAE,MAAIP,OAAI,KAAK,MAAIO;AAAA,MAAE,CAAC,GAAE,GAAE,CAAC,GAAEN,GAAE,MAAM,CAAC,GAAE,IAAG,CAAC,GAAEA,GAAE,MAAM,CAAC,GAAE,GAAE,CAAC,GAAEA,GAAE,OAAO,CAAC,GAAE,IAAG,CAAC,GAAEA,GAAE,OAAO,CAAC,GAAE,KAAI,CAACJ,IAAE,SAASG,IAAE;AAAC,YAAIE,KAAEG,GAAE,QAAQ,GAAED,MAAGC,GAAE,aAAa,KAAGH,GAAE,IAAK,SAASF,IAAE;AAAC,iBAAOA,GAAE,MAAM,GAAE,CAAC;AAAA,QAAC,CAAE,GAAG,QAAQA,EAAC,IAAE;AAAE,YAAGI,KAAE,EAAE,OAAM,IAAI;AAAM,aAAK,QAAMA,KAAE,MAAIA;AAAA,MAAC,CAAC,GAAE,MAAK,CAACP,IAAE,SAASG,IAAE;AAAC,YAAIE,KAAEG,GAAE,QAAQ,EAAE,QAAQL,EAAC,IAAE;AAAE,YAAGE,KAAE,EAAE,OAAM,IAAI;AAAM,aAAK,QAAMA,KAAE,MAAIA;AAAA,MAAC,CAAC,GAAE,GAAE,CAAC,YAAWD,GAAE,MAAM,CAAC,GAAE,IAAG,CAAC,GAAE,SAASD,IAAE;AAAC,aAAK,OAAKD,GAAEC,EAAC;AAAA,MAAC,CAAC,GAAE,MAAK,CAAC,SAAQC,GAAE,MAAM,CAAC,GAAE,GAAEE,IAAE,IAAGA,GAAC;AAAE,eAASO,GAAEN,IAAE;AAAC,YAAIG,IAAEC;AAAE,QAAAD,KAAEH,IAAEI,KAAEV,MAAGA,GAAE;AAAQ,iBAAQD,MAAGO,KAAEG,GAAE,QAAQ,qCAAqC,SAASL,IAAEE,IAAEG,IAAE;AAAC,cAAIV,KAAEU,MAAGA,GAAE,YAAY;AAAE,iBAAOH,MAAGI,GAAED,EAAC,KAAG,EAAEA,EAAC,KAAGC,GAAEX,EAAC,EAAE,QAAQ,kCAAkC,SAASG,IAAEE,IAAEE,IAAE;AAAC,mBAAOF,MAAGE,GAAE,MAAM,CAAC;AAAA,UAAC,CAAE;AAAA,QAAC,CAAE,GAAG,MAAM,CAAC,GAAEL,KAAEF,GAAE,QAAOI,KAAE,GAAEA,KAAEF,IAAEE,MAAG,GAAE;AAAC,cAAIE,KAAEN,GAAEI,EAAC,GAAEI,KAAEI,GAAEN,EAAC,GAAEG,KAAED,MAAGA,GAAE,CAAC,GAAEK,KAAEL,MAAGA,GAAE,CAAC;AAAE,UAAAR,GAAEI,EAAC,IAAES,KAAE,EAAC,OAAMJ,IAAE,QAAOI,GAAC,IAAEP,GAAE,QAAQ,YAAW,EAAE;AAAA,QAAC;AAAC,eAAO,SAASH,IAAE;AAAC,mBAAQE,KAAE,CAAC,GAAEE,KAAE,GAAEG,KAAE,GAAEH,KAAEL,IAAEK,MAAG,GAAE;AAAC,gBAAII,KAAEX,GAAEO,EAAC;AAAE,gBAAG,YAAU,OAAOI,GAAE,CAAAD,MAAGC,GAAE;AAAA,iBAAW;AAAC,kBAAIV,KAAEU,GAAE,OAAMP,KAAEO,GAAE,QAAOL,KAAEH,GAAE,MAAMO,EAAC,GAAEF,KAAEP,GAAE,KAAKK,EAAC,EAAE,CAAC;AAAE,cAAAF,GAAE,KAAKC,IAAEG,EAAC,GAAEL,KAAEA,GAAE,QAAQK,IAAE,EAAE;AAAA,YAAC;AAAA,UAAC;AAAC,iBAAO,SAASL,IAAE;AAAC,gBAAIE,KAAEF,GAAE;AAAU,gBAAG,WAASE,IAAE;AAAC,kBAAIE,KAAEJ,GAAE;AAAM,cAAAE,KAAEE,KAAE,OAAKJ,GAAE,SAAO,MAAI,OAAKI,OAAIJ,GAAE,QAAM,IAAG,OAAOA,GAAE;AAAA,YAAS;AAAA,UAAC,EAAEE,EAAC,GAAEA;AAAA,QAAC;AAAA,MAAC;AAAC,aAAO,SAASF,IAAEE,IAAEE,IAAE;AAAC,QAAAA,GAAE,EAAE,oBAAkB,MAAGJ,MAAGA,GAAE,sBAAoBD,KAAEC,GAAE;AAAmB,YAAIO,KAAEL,GAAE,WAAUM,KAAED,GAAE;AAAM,QAAAA,GAAE,QAAM,SAASP,IAAE;AAAC,cAAIE,KAAEF,GAAE,MAAKO,KAAEP,GAAE,KAAIH,KAAEG,GAAE;AAAK,eAAK,KAAGO;AAAE,cAAIR,KAAEF,GAAE,CAAC;AAAE,cAAG,YAAU,OAAOE,IAAE;AAAC,gBAAIE,KAAE,SAAKJ,GAAE,CAAC,GAAEM,KAAE,SAAKN,GAAE,CAAC,GAAEQ,KAAEJ,MAAGE,IAAEG,KAAET,GAAE,CAAC;AAAE,YAAAM,OAAIG,KAAET,GAAE,CAAC,IAAGC,KAAE,KAAK,QAAQ,GAAE,CAACG,MAAGK,OAAIR,KAAEM,GAAE,GAAGE,EAAC,IAAG,KAAK,KAAG,SAASN,IAAEE,IAAEE,IAAEG,IAAE;AAAC,kBAAG;AAAC,oBAAG,CAAC,KAAI,GAAG,EAAE,QAAQL,EAAC,IAAE,GAAG,QAAO,IAAI,MAAM,QAAMA,KAAE,MAAI,KAAGF,EAAC;AAAE,oBAAIQ,KAAEE,GAAER,EAAC,EAAEF,EAAC,GAAEH,KAAEW,GAAE,MAAKV,KAAEU,GAAE,OAAMT,KAAES,GAAE,KAAIP,KAAEO,GAAE,OAAML,KAAEK,GAAE,SAAQH,KAAEG,GAAE,SAAQF,KAAEE,GAAE,cAAaC,KAAED,GAAE,MAAKG,KAAEH,GAAE,MAAKI,KAAE,oBAAI,QAAKC,KAAEd,OAAIF,MAAGC,KAAE,IAAEc,GAAE,QAAQ,IAAGE,KAAEjB,MAAGe,GAAE,YAAY,GAAEG,KAAE;AAAE,gBAAAlB,MAAG,CAACC,OAAIiB,KAAEjB,KAAE,IAAEA,KAAE,IAAEc,GAAE,SAAS;AAAG,oBAAII,IAAEC,KAAEhB,MAAG,GAAE,IAAEE,MAAG,GAAE,IAAEE,MAAG,GAAE,IAAEC,MAAG;AAAE,uBAAOG,KAAE,IAAI,KAAK,KAAK,IAAIK,IAAEC,IAAEF,IAAEI,IAAE,GAAE,GAAE,IAAE,KAAGR,GAAE,SAAO,GAAG,CAAC,IAAEL,KAAE,IAAI,KAAK,KAAK,IAAIU,IAAEC,IAAEF,IAAEI,IAAE,GAAE,GAAE,CAAC,CAAC,KAAGD,KAAE,IAAI,KAAKF,IAAEC,IAAEF,IAAEI,IAAE,GAAE,GAAE,CAAC,GAAEN,OAAIK,KAAET,GAAES,EAAC,EAAE,KAAKL,EAAC,EAAE,OAAO,IAAGK;AAAA,cAAE,SAAOhB,IAAE;AAAC,uBAAO,oBAAI,KAAK,EAAE;AAAA,cAAC;AAAA,YAAC,EAAEE,IAAEH,IAAEQ,IAAEH,EAAC,GAAE,KAAK,KAAK,GAAEE,MAAG,SAAKA,OAAI,KAAK,KAAG,KAAK,OAAOA,EAAC,EAAE,KAAID,MAAGH,MAAG,KAAK,OAAOH,EAAC,MAAI,KAAK,KAAG,oBAAI,KAAK,EAAE,IAAGD,KAAE,CAAC;AAAA,UAAC,WAASC,cAAa,MAAM,UAAQU,KAAEV,GAAE,QAAOY,KAAE,GAAEA,MAAGF,IAAEE,MAAG,GAAE;AAAC,YAAAd,GAAE,CAAC,IAAEE,GAAEY,KAAE,CAAC;AAAE,gBAAIC,KAAER,GAAE,MAAM,MAAKP,EAAC;AAAE,gBAAGe,GAAE,QAAQ,GAAE;AAAC,mBAAK,KAAGA,GAAE,IAAG,KAAK,KAAGA,GAAE,IAAG,KAAK,KAAK;AAAE;AAAA,YAAK;AAAC,YAAAD,OAAIF,OAAI,KAAK,KAAG,oBAAI,KAAK,EAAE;AAAA,UAAE;AAAA,cAAM,CAAAD,GAAE,KAAK,MAAKR,EAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA;AAAA;;;ACAryH;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,8BAA4B,EAAE;AAAA,IAAC,EAAE,SAAM,WAAU;AAAC;AAAa,aAAO,SAAS,GAAE,GAAE;AAAC,YAAI,IAAE,EAAE,WAAU,IAAE,EAAE;AAAO,UAAE,SAAO,SAASkB,IAAE;AAAC,cAAIC,KAAE,MAAKC,KAAE,KAAK,QAAQ;AAAE,cAAG,CAAC,KAAK,QAAQ,EAAE,QAAO,EAAE,KAAK,IAAI,EAAEF,EAAC;AAAE,cAAIG,KAAE,KAAK,OAAO,GAAEC,MAAGJ,MAAG,wBAAwB,QAAQ,+DAA+D,SAASA,IAAE;AAAC,oBAAOA,IAAE;AAAA,cAAC,KAAI;AAAI,uBAAO,KAAK,MAAMC,GAAE,KAAG,KAAG,CAAC;AAAA,cAAE,KAAI;AAAK,uBAAOC,GAAE,QAAQD,GAAE,EAAE;AAAA,cAAE,KAAI;AAAO,uBAAOA,GAAE,SAAS;AAAA,cAAE,KAAI;AAAO,uBAAOA,GAAE,YAAY;AAAA,cAAE,KAAI;AAAK,uBAAOC,GAAE,QAAQD,GAAE,KAAK,GAAE,GAAG;AAAA,cAAE,KAAI;AAAA,cAAI,KAAI;AAAK,uBAAOE,GAAE,EAAEF,GAAE,KAAK,GAAE,QAAMD,KAAE,IAAE,GAAE,GAAG;AAAA,cAAE,KAAI;AAAA,cAAI,KAAI;AAAK,uBAAOG,GAAE,EAAEF,GAAE,QAAQ,GAAE,QAAMD,KAAE,IAAE,GAAE,GAAG;AAAA,cAAE,KAAI;AAAA,cAAI,KAAI;AAAK,uBAAOG,GAAE,EAAE,OAAO,MAAIF,GAAE,KAAG,KAAGA,GAAE,EAAE,GAAE,QAAMD,KAAE,IAAE,GAAE,GAAG;AAAA,cAAE,KAAI;AAAI,uBAAO,KAAK,MAAMC,GAAE,GAAG,QAAQ,IAAE,GAAG;AAAA,cAAE,KAAI;AAAI,uBAAOA,GAAE,GAAG,QAAQ;AAAA,cAAE,KAAI;AAAI,uBAAM,MAAIA,GAAE,WAAW,IAAE;AAAA,cAAI,KAAI;AAAM,uBAAM,MAAIA,GAAE,WAAW,MAAM,IAAE;AAAA,cAAI;AAAQ,uBAAOD;AAAA,YAAC;AAAA,UAAC,CAAE;AAAE,iBAAO,EAAE,KAAK,IAAI,EAAEI,EAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA;AAAA;;;ACAxkC;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,0BAAwB,EAAE;AAAA,IAAC,EAAE,SAAM,WAAU;AAAC;AAAa,UAAI,IAAE,QAAO,IAAE;AAAO,aAAO,SAAS,GAAE,GAAE,GAAE;AAAC,YAAIC,KAAE,EAAE;AAAU,QAAAA,GAAE,OAAK,SAASC,IAAE;AAAC,cAAG,WAASA,OAAIA,KAAE,OAAM,SAAOA,GAAE,QAAO,KAAK,IAAI,KAAGA,KAAE,KAAK,KAAK,IAAG,KAAK;AAAE,cAAIC,KAAE,KAAK,QAAQ,EAAE,aAAW;AAAE,cAAG,OAAK,KAAK,MAAM,KAAG,KAAK,KAAK,IAAE,IAAG;AAAC,gBAAIF,KAAE,EAAE,IAAI,EAAE,QAAQ,CAAC,EAAE,IAAI,GAAE,CAAC,EAAE,KAAKE,EAAC,GAAEC,KAAE,EAAE,IAAI,EAAE,MAAM,CAAC;AAAE,gBAAGH,GAAE,SAASG,EAAC,EAAE,QAAO;AAAA,UAAC;AAAC,cAAIC,KAAE,EAAE,IAAI,EAAE,QAAQ,CAAC,EAAE,KAAKF,EAAC,EAAE,QAAQ,CAAC,EAAE,SAAS,GAAE,aAAa,GAAEG,KAAE,KAAK,KAAKD,IAAE,GAAE,IAAE;AAAE,iBAAOC,KAAE,IAAE,EAAE,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,IAAE,KAAK,KAAKA,EAAC;AAAA,QAAC,GAAEL,GAAE,QAAM,SAASM,IAAE;AAAC,iBAAO,WAASA,OAAIA,KAAE,OAAM,KAAK,KAAKA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA;AAAA;;;ACArwB;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,wBAAsB,EAAE;AAAA,IAAC,EAAE,SAAM,WAAU;AAAC;AAAa,aAAO,SAAS,GAAE,GAAE;AAAC,UAAE,UAAU,WAAS,WAAU;AAAC,cAAIC,KAAE,KAAK,MAAM,GAAEC,KAAE,KAAK,KAAK,GAAE,IAAE,KAAK,KAAK;AAAE,iBAAO,MAAIA,MAAG,OAAKD,KAAE,IAAE,IAAE,MAAIA,MAAGC,MAAG,KAAG,IAAE,IAAE;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA;AAAA;;;ACAzY;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,yBAAuB,EAAE;AAAA,IAAC,EAAE,SAAM,WAAU;AAAC;AAAa,aAAO,SAAS,GAAE,GAAE,GAAE;AAAC,UAAE,UAAU,YAAU,SAASC,IAAE;AAAC,cAAIC,KAAE,KAAK,OAAO,EAAE,IAAI,EAAE,QAAQ,KAAK,IAAE,EAAE,IAAI,EAAE,QAAQ,MAAM,KAAG,KAAK,IAAE;AAAE,iBAAO,QAAMD,KAAEC,KAAE,KAAK,IAAID,KAAEC,IAAE,KAAK;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA;AAAA;;;ACAna;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,6BAA2B,EAAE;AAAA,IAAC,EAAE,SAAM,WAAU;AAAC;AAAa,aAAO,SAAS,GAAE,GAAE;AAAC,UAAE,UAAU,gBAAc,SAASC,IAAEC,IAAE;AAAC,iBAAO,KAAK,OAAOD,IAAEC,EAAC,KAAG,KAAK,QAAQD,IAAEC,EAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA;AAAA;;;ACAtW;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,8BAA4B,EAAE;AAAA,IAAC,EAAE,SAAM,WAAU;AAAC;AAAa,aAAO,SAAS,GAAE,GAAE;AAAC,UAAE,UAAU,iBAAe,SAASC,IAAEC,IAAE;AAAC,iBAAO,KAAK,OAAOD,IAAEC,EAAC,KAAG,KAAK,SAASD,IAAEC,EAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwClW,SAASC,mBACdC,QACiC;AACjC,MAAI,CAACA,UAAU,CAACA,OAAOC,OAAQ,QAAO;AACtC,MAAMC,SAAS,CAAA;AACfF,SAAOG,QAAQ,SAAAC,OAAS;AACtB,QAAMC,QAAQD,MAAMC;AACpBH,WAAOG,KAAD,IAAUH,OAAOG,KAAD,KAAW,CAAA;AACjCH,WAAOG,KAAD,EAAQC,KAAKF,KAAnB;GAHF;AAKA,SAAOF;AACR;AAEM,SAASK,OACdC,UAEQ;AAAA,WAAA,OAAA,UAAA,QADLC,OACK,IAAA,MAAA,OAAA,IAAA,OAAA,IAAA,CAAA,GAAA,OAAA,GAAA,OAAA,MAAA,QAAA;AADLA,SACK,OAAA,CAAA,IAAA,UAAA,IAAA;EAAA;AACR,MAAIC,IAAI;AACR,MAAMC,MAAMF,KAAKR;AACjB,MAAI,OAAOO,aAAa,YAAY;AAClC,WAAOA,SAASI,MAAM,MAAMH,IAArB;EACR;AACD,MAAI,OAAOD,aAAa,UAAU;AAChC,QAAIK,MAAML,SAASM,QAAQC,cAAc,SAAAC,IAAK;AAC5C,UAAIA,OAAM,MAAM;AACd,eAAO;MACR;AACD,UAAIN,KAAKC,KAAK;AACZ,eAAOK;MACR;AACD,cAAQA,IAAR;QACE,KAAK;AACH,iBAAOC,OAAOR,KAAKC,GAAD,CAAL;QACf,KAAK;AACH,iBAAQQ,OAAOT,KAAKC,GAAD,CAAL;QAChB,KAAK;AACH,cAAI;AACF,mBAAOS,KAAKC,UAAUX,KAAKC,GAAD,CAAnB;mBACAW,IAAG;AACV,mBAAO;UACR;AACD;QACF;AACE,iBAAOL;MAbX;IAeD,CAtBS;AAuBV,WAAOH;EACR;AACD,SAAOL;AACR;AAED,SAASc,mBAAmBC,OAAc;AACxC,SACEA,UAAS,YACTA,UAAS,SACTA,UAAS,SACTA,UAAS,WACTA,UAAS,UACTA,UAAS;AAEZ;AAEM,SAASC,aAAaC,OAAcF,OAAe;AACxD,MAAIE,UAAUC,UAAaD,UAAU,MAAM;AACzC,WAAO;EACR;AACD,MAAIF,UAAS,WAAWI,MAAMC,QAAQH,KAAd,KAAwB,CAACA,MAAMxB,QAAQ;AAC7D,WAAO;EACR;AACD,MAAIqB,mBAAmBC,KAAD,KAAU,OAAOE,UAAU,YAAY,CAACA,OAAO;AACnE,WAAO;EACR;AACD,SAAO;AACR;AAMD,SAASI,mBACPC,KACAC,MACAC,UACA;AACA,MAAMC,UAA2B,CAAA;AACjC,MAAIC,QAAQ;AACZ,MAAMC,YAAYL,IAAI7B;AAEtB,WAASmC,MAAMpC,QAAyB;AACtCiC,YAAQ3B,KAAR2B,MAAAA,SAAiBjC,UAAU,CAAA,CAApB;AACPkC;AACA,QAAIA,UAAUC,WAAW;AACvBH,eAASC,OAAD;IACT;EACF;AAEDH,MAAI3B,QAAQ,SAAAkC,IAAK;AACfN,SAAKM,IAAGD,KAAJ;GADN;AAGD;AAED,SAASE,iBACPR,KACAC,MACAC,UACA;AACA,MAAIO,QAAQ;AACZ,MAAMJ,YAAYL,IAAI7B;AAEtB,WAASuC,KAAKxC,QAAyB;AACrC,QAAIA,UAAUA,OAAOC,QAAQ;AAC3B+B,eAAShC,MAAD;AACR;IACD;AACD,QAAMyC,WAAWF;AACjBA,YAAQA,QAAQ;AAChB,QAAIE,WAAWN,WAAW;AACxBJ,WAAKD,IAAIW,QAAD,GAAYD,IAAhB;IACL,OAAM;AACLR,eAAS,CAAA,CAAD;IACT;EACF;AAEDQ,OAAK,CAAA,CAAD;AACL;AAED,SAASE,cAAcC,QAA4C;AACjE,MAAMC,MAA0B,CAAA;AAChCC,SAAOC,KAAKH,MAAZ,EAAoBxC,QAAQ,SAAA4C,GAAK;AAC/BH,QAAItC,KAAJ,MAAAsC,KAAaD,OAAOI,CAAD,KAAO,CAAA,CAAvB;GADL;AAGA,SAAOH;AACR;AAqBM,SAASI,SACdL,QACAM,QACAlB,MACAC,UACAkB,QACiB;AACjB,MAAID,OAAOE,OAAO;AAChB,QAAMC,WAAU,IAAIC,QAAgB,SAACC,SAASC,QAAW;AACvD,UAAMf,OAAO,SAAPA,MAAQxC,QAA4B;AACxCgC,iBAAShC,MAAD;AACR,eAAOA,OAAOC,SACVsD,OAAO,IAAIC,qBAAqBxD,QAAQD,mBAAmBC,MAAD,CAAnD,CAAD,IACNsD,QAAQJ,MAAD;;AAEb,UAAMO,aAAaf,cAAcC,MAAD;AAChCL,uBAAiBmB,YAAY1B,MAAMS,IAAnB;IACjB,CATe;AAUhBY,aAAO,OAAA,EAAO,SAAAM,GAAC;AAAA,aAAIA;KAAnB;AACA,WAAON;EACR;AACD,MAAMO,cACJV,OAAOU,gBAAgB,OACnBd,OAAOC,KAAKH,MAAZ,IACAM,OAAOU,eAAe,CAAA;AAE5B,MAAMC,aAAaf,OAAOC,KAAKH,MAAZ;AACnB,MAAMkB,eAAeD,WAAW3D;AAChC,MAAIiC,QAAQ;AACZ,MAAMD,UAA2B,CAAA;AACjC,MAAMmB,UAAU,IAAIC,QAAgB,SAACC,SAASC,QAAW;AACvD,QAAMf,OAAO,SAAPA,MAAQxC,QAA4B;AACxCiC,cAAQ3B,KAAKM,MAAMqB,SAASjC,MAA5B;AACAkC;AACA,UAAIA,UAAU2B,cAAc;AAC1B7B,iBAASC,OAAD;AACR,eAAOA,QAAQhC,SACXsD,OACE,IAAIC,qBAAqBvB,SAASlC,mBAAmBkC,OAAD,CAApD,CADI,IAGNqB,QAAQJ,MAAD;MACZ;;AAEH,QAAI,CAACU,WAAW3D,QAAQ;AACtB+B,eAASC,OAAD;AACRqB,cAAQJ,MAAD;IACR;AACDU,eAAWzD,QAAQ,SAAA2D,KAAO;AACxB,UAAMhC,MAAMa,OAAOmB,GAAD;AAClB,UAAIH,YAAYI,QAAQD,GAApB,MAA6B,IAAI;AACnCxB,yBAAiBR,KAAKC,MAAMS,IAAZ;MACjB,OAAM;AACLX,2BAAmBC,KAAKC,MAAMS,IAAZ;MACnB;KANH;EAQD,CAzBe;AA0BhBY,UAAO,OAAA,EAAO,SAAAM,GAAC;AAAA,WAAIA;GAAnB;AACA,SAAON;AACR;AAED,SAASY,WACPC,KACsB;AACtB,SAAO,CAAC,EAAEA,OAAQA,IAAsBC,YAAYxC;AACrD;AAED,SAASyC,SAAS1C,OAAe2C,MAAgB;AAC/C,MAAIC,KAAI5C;AACR,WAASf,IAAI,GAAGA,IAAI0D,KAAKnE,QAAQS,KAAK;AACpC,QAAI2D,MAAK3C,QAAW;AAClB,aAAO2C;IACR;AACDA,IAAAA,KAAIA,GAAED,KAAK1D,CAAD,CAAL;EACN;AACD,SAAO2D;AACR;AAEM,SAASC,gBAAgBC,MAAwBrB,QAAgB;AACtE,SAAO,SAACsB,IAA+D;AACrE,QAAIC;AACJ,QAAIF,KAAKG,YAAY;AACnBD,mBAAaN,SAASjB,QAAQqB,KAAKG,UAAd;IACtB,OAAM;AACLD,mBAAavB,OAAQsB,GAAWnE,SAASkE,KAAKI,SAA3B;IACpB;AACD,QAAIX,WAAWQ,EAAD,GAAM;AAClBA,SAAGnE,QAAQmE,GAAGnE,SAASkE,KAAKI;AAC5BH,SAAGC,aAAaA;AAChB,aAAOD;IACR;AACD,WAAO;MACLN,SAAS,OAAOM,OAAO,aAAaA,GAAE,IAAKA;MAC3CC;MACApE,OAASmE,GAAiCnE,SAASkE,KAAKI;;;AAG7D;AAEM,SAASC,UAA4BC,QAAW3B,QAAuB;AAC5E,MAAIA,QAAQ;AACV,aAAW4B,MAAK5B,QAAQ;AACtB,UAAIA,OAAO6B,eAAeD,EAAtB,GAA0B;AAC5B,YAAMrD,QAAQyB,OAAO4B,EAAD;AACpB,YAAI,OAAOrD,UAAU,YAAY,OAAOoD,OAAOC,EAAD,MAAQ,UAAU;AAC9DD,iBAAOC,EAAD,IAAN,SAAA,CAAA,GACKD,OAAOC,EAAD,GACNrD,KAFL;QAID,OAAM;AACLoD,iBAAOC,EAAD,IAAMrD;QACb;MACF;IACF;EACF;AACD,SAAOoD;AACR;AyBlTM,SAASG,cAAwC;AACtD,SAAO;IACL,WAAS;IACTC,UAAU;IACV,QAAM;IACNC,YAAY;IACZC,MAAM;MACJ5E,QAAQ;MACR6E,OAAO;MACPC,SAAS;;IAEXC,OAAO;MACLC,QAAQ;MACRC,QAAQ;MACRC,OAAO;MACPC,QAAQ;MACRC,QAAQ;MACRR,MAAM;MACN,WAAS;MACTS,SAAS;MACT,SAAO;MACPC,QAAQ;MACRC,OAAO;MACPC,KAAK;MACLC,KAAK;;IAEPT,QAAQ;MACN5E,KAAK;MACLsF,KAAK;MACLC,KAAK;MACLC,OAAO;;IAETR,QAAQ;MACNhF,KAAK;MACLsF,KAAK;MACLC,KAAK;MACLC,OAAO;;IAETV,OAAO;MACL9E,KAAK;MACLsF,KAAK;MACLC,KAAK;MACLC,OAAO;;IAETC,SAAS;MACPC,UAAU;;IAEZC,OAAQ,SAAA,QAAA;AACN,UAAMC,SAASpF,KAAKiE,MAAMjE,KAAKC,UAAU,IAAf,CAAX;AACfmF,aAAOD,QAAQ,KAAKA;AACpB,aAAOC;IACR;;AAEJ;IzB1CKxF,cAIKyF,SA6JEhD,sBC3KPyB,YCWAC,YCbFuB,QAEJ,aCGML,WAUAd,OA2DA/D,QCxEA4E,OCAAO,QAEAC,cCFAP,WCIN,OCHMb,QCAAC,SCAAG,SCAAiB,UCAAf,SCAAD,UCAAiB,SCDApB,QCCAC,SCAAgB,MAEAC,aCFAP,UCAAjB,OCDAF,WCCA1D,OCAAuF,KCYN,YCyCaC,UCtBPC;;;A1BtBN,IAAMjG,eAAe;AAId,IAAIyF,UAA2D,SAAAA,WAAM;IAAA;AAG5E,QACE,OAAOS,YAAY,eACnBA,QAAQC,OACRD,QACA,OAAOE,WAAW,eAClB,OAAOC,aAAa,aACpB;AACAZ,gBAAU,SAAAA,SAACjF,OAAMvB,QAAW;AAC1B,YACE,OAAOqH,YAAY,eACnBA,QAAQC,QACR,OAAOC,+BAA+B,aACtC;AACA,cAAIvH,OAAOwH,MAAM,SAAA9D,GAAC;AAAA,mBAAI,OAAOA,MAAM;UAAjB,CAAd,GAA0C;AAC5C2D,oBAAQC,KAAK/F,OAAMvB,MAAnB;UACD;QACF;;IAEJ;AAwID,IAAawD,uBAAb,SAAA,QAAA;AAAA,qBAAAA,uBAAA,MAAA;AAIE,eACExD,sBAAAA,QACAE,QACA;AAAA,YAAA;AACA,gBAAA,OAAA,KAAA,MAAM,wBAAN,KAAA;AACA,cAAKF,SAASA;AACd,cAAKE,SAASA;AAHd,eAAA;MAID;AAXH,aAAAsD;IAAA,EAAA,iBAA0CiE,KAA1C,CAAA;AC3KA,IAAMxC,aAAwB,SAAxBA,SAAyBV,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,SAASnG,OAAS;AAC5E,UACEgD,KAAKU,aACJ,CAAC/B,OAAO6B,eAAeR,KAAKlE,KAA3B,KACAmB,aAAaC,OAAOF,SAAQgD,KAAKhD,IAArB,IACd;AACAvB,eAAOM,KAAKC,OAAOmH,QAAQX,SAAS9B,UAAUV,KAAKI,SAAjC,CAAlB;MACD;IACF;ACGD,IAAMO,aAA0B,SAA1BA,YAA2BX,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,SAAY;AACxE,UAAI,QAAQC,KAAKlG,KAAb,KAAuBA,UAAU,IAAI;AACvCzB,eAAOM,KAAKC,OAAOmH,QAAQX,SAAS7B,YAAYX,KAAKI,SAAnC,CAAlB;MACD;IACF;ACfD,IAAA,cAAe,WAAM;AACnB,UAAI8B,QAAQ;AACV,eAAOA;MACR;AAED,UAAMmB,OAAO;AACb,UAAMC,KAAI,SAAJA,GAAIH,SAAO;AAAA,eACfA,WAAWA,QAAQI,oBAAnB,qBACuBF,OADvB,WACoCA,OADpC,gBAEI;;AAEN,UAAMG,KACJ;AAEF,UAAMC,QAAQ;AACd,UAAMC,MAEHD,eAAAA,QAFQ,aAEQA,QAFR,qFAGRA,QAHQ,aAGQD,KAAOC,OAAAA,QACvBA,oHAAAA,QAJQ,cAISD,KAJT,UAImBC,QAJnB,gHAKRA,QALQ,iBAKYA,QALZ,YAK2BD,KAAUC,UAAAA,QAC7CA,8FAAAA,QANQ,iBAMYA,QANZ,YAM2BD,KAN3B,UAMqCC,QAC7CA,8FAAAA,QAAoBA,iBAAAA,QAAeD,YAAAA,KAAUC,UAAAA,QAPrC,8FAQRA,QARQ,iBAQYA,QARZ,YAQ2BD,KAAUC,UAAAA,QACrCA,sGAAAA,QATA,YASeD,KATf,UASyBC,QATzB,sLAYRlH,QAAQ,gBAAgB,EAZhB,EAaRA,QAAQ,OAAO,EAbP,EAcRoH,KAdQ;AAiBX,UAAMC,WAAW,IAAIC,OAAJ,SAAkBL,KAAlB,YAA8BE,KAA/C,IAAA;AACA,UAAMI,UAAU,IAAID,OAAJ,MAAeL,KAA/B,GAAA;AACA,UAAMO,UAAU,IAAIF,OAAJ,MAAeH,KAA/B,GAAA;AAEA,UAAMM,KAAK,SAALA,IAAKb,SAAO;AAAA,eAChBA,WAAWA,QAAQc,QACfL,WACA,IAAIC,OAAJ,QACQP,GAAEH,OAAD,IAAYK,KAAKF,GAAEH,OAAD,IAD3B,UAC4CG,GAAEH,OAAD,IAAYO,KAAKJ,GAC1DH,OAD2D,IAD/D,KAIE,GAJF;;AAONa,SAAGR,KAAK,SAACL,SAAD;AAAA,eACNA,WAAWA,QAAQc,QACfH,UACA,IAAID,OAAUP,KAAAA,GAAEH,OAAD,IAAYK,KAAKF,GAAEH,OAAD,GAAa,GAA9C;;AACNa,SAAGN,KAAK,SAACP,SAAD;AAAA,eACNA,WAAWA,QAAQc,QACfF,UACA,IAAIF,OAAUP,KAAAA,GAAEH,OAAD,IAAYO,KAAKJ,GAAEH,OAAD,GAAa,GAA9C;;AAEN,UAAMe,WAAN;AACA,UAAMC,OAAO;AACb,UAAMC,OAAOJ,GAAGR,GAAH,EAAQ7E;AACrB,UAAM0F,OAAOL,GAAGN,GAAH,EAAQ/E;AACrB,UAAM2F,OAAO;AACb,UAAMC,SACJ;AACF,UAAMC,MAAN;AACA,UAAMC,OAAO;AACb,UAAM5E,OAAO;AACb,UAAM6E,QAAcR,QAAAA,WAAT,aAA4BC,OAA5B,kBAAgDC,OAAQC,MAAAA,OAAQC,MAAAA,OAAOC,SAASC,MAAOC,MAAAA,OAAO5E;AACzGqC,eAAS,IAAI2B,OAAJ,SAAkBa,QAAlB,MAA6B,GAA7B;AACT,aAAOxC;IACR;ACjED,IAAML,YAAU;;MAEdN,OAAO;;;;;MAKPE,KAAK;IAPS;AAUhB,IAAMV,QAAQ;MACZM,SADY,SAAA,QACJnE,OAAc;AACpB,eAAO6D,MAAMK,OAAOlE,KAAb,KAAuByH,SAASzH,OAAO,EAAR,MAAgBA;;MAF5C,SAAA,SAAA,MAINA,OAAc;AAClB,eAAO6D,MAAMK,OAAOlE,KAAb,KAAuB,CAAC6D,MAAMM,QAAQnE,KAAd;;MAEjCgE,OAPY,SAAA,MAONhE,OAAc;AAClB,eAAOE,MAAMC,QAAQH,KAAd;;MAEToE,QAVY,SAAA,OAULpE,OAAc;AACnB,YAAIA,iBAAiB2G,QAAQ;AAC3B,iBAAO;QACR;AACD,YAAI;AACF,iBAAO,CAAC,CAAC,IAAIA,OAAO3G,KAAX;iBACFiC,GAAG;AACV,iBAAO;QACR;;MAEHyB,MApBY,SAAA,KAoBP1D,OAAc;AACjB,eACE,OAAOA,MAAM0H,YAAY,cACzB,OAAO1H,MAAM2H,aAAa,cAC1B,OAAO3H,MAAM4H,YAAY,cACzB,CAACC,MAAM7H,MAAM0H,QAAN,CAAD;;MAGVxD,QA5BY,SAAA,OA4BLlE,OAAc;AACnB,YAAI6H,MAAM7H,KAAD,GAAS;AAChB,iBAAO;QACR;AACD,eAAO,OAAOA,UAAU;;MAE1BiE,QAlCY,SAAA,OAkCLjE,OAAc;AACnB,eAAO,OAAOA,UAAU,YAAY,CAAC6D,MAAMG,MAAMhE,KAAZ;;MAEvC+D,QArCY,SAAA,OAqCL/D,OAAc;AACnB,eAAO,OAAOA,UAAU;;MAE1BqE,OAxCY,SAAA,MAwCNrE,OAAc;AAClB,eACE,OAAOA,UAAU,YACjBA,MAAMxB,UAAU,OAChB,CAAC,CAACwB,MAAM8H,MAAMnD,UAAQN,KAApB;;MAGNC,KA/CY,SAAA,IA+CRtE,OAAc;AAChB,eACE,OAAOA,UAAU,YACjBA,MAAMxB,UAAU,QAChB,CAAC,CAACwB,MAAM8H,MAAMC,YAAW,CAAvB;;MAGNxD,KAtDY,SAAA,IAsDRvE,OAAc;AAChB,eAAO,OAAOA,UAAU,YAAY,CAAC,CAACA,MAAM8H,MAAMnD,UAAQJ,GAApB;MACvC;IAxDW;AA2Dd,IAAMzE,SAAoB,SAApBA,KAAqBgD,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,SAAY;AAClE,UAAInD,KAAKU,YAAYxD,UAAUC,QAAW;AACxCuD,mBAASV,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAA9B;AACR;MACD;AACD,UAAM+B,SAAS,CACb,WACA,SACA,SACA,UACA,UACA,UACA,SACA,UACA,QACA,OACA,KAXa;AAaf,UAAMC,WAAWnF,KAAKhD;AACtB,UAAIkI,OAAO1F,QAAQ2F,QAAf,IAA2B,IAAI;AACjC,YAAI,CAACpE,MAAMoE,QAAD,EAAWjI,KAAhB,GAAwB;AAC3BzB,iBAAOM,KACLC,OAAOmH,QAAQX,SAASzB,MAAMoE,QAAvB,GAAkCnF,KAAKI,WAAWJ,KAAKhD,IAAxD,CADR;QAGD;iBAEQmI,YAAY,OAAOjI,UAAU8C,KAAKhD,MAAM;AACjDvB,eAAOM,KACLC,OAAOmH,QAAQX,SAASzB,MAAMoE,QAAvB,GAAkCnF,KAAKI,WAAWJ,KAAKhD,IAAxD,CADR;MAGD;IACF;ACvGD,IAAM4E,QAAqB,SAArBA,OAAsB5B,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,SAAY;AACnE,UAAM/G,MAAM,OAAO4D,KAAK5D,QAAQ;AAChC,UAAMsF,OAAM,OAAO1B,KAAK0B,QAAQ;AAChC,UAAMC,OAAM,OAAO3B,KAAK2B,QAAQ;AAEhC,UAAMyD,WAAW;AACjB,UAAIC,MAAMnI;AACV,UAAIqC,MAAM;AACV,UAAM+F,MAAM,OAAOpI,UAAU;AAC7B,UAAMZ,MAAM,OAAOY,UAAU;AAC7B,UAAMK,MAAMH,MAAMC,QAAQH,KAAd;AACZ,UAAIoI,KAAK;AACP/F,cAAM;iBACGjD,KAAK;AACdiD,cAAM;iBACGhC,KAAK;AACdgC,cAAM;MACP;AAID,UAAI,CAACA,KAAK;AACR,eAAO;MACR;AACD,UAAIhC,KAAK;AACP8H,cAAMnI,MAAMxB;MACb;AACD,UAAIY,KAAK;AAEP+I,cAAMnI,MAAMX,QAAQ6I,UAAU,GAAxB,EAA6B1J;MACpC;AACD,UAAIU,KAAK;AACP,YAAIiJ,QAAQrF,KAAK5D,KAAK;AACpBX,iBAAOM,KAAKC,OAAOmH,QAAQX,SAASjD,GAAjB,EAAsBnD,KAAK4D,KAAKI,WAAWJ,KAAK5D,GAAjD,CAAlB;QACD;MACF,WAAUsF,QAAO,CAACC,QAAO0D,MAAMrF,KAAK0B,KAAK;AACxCjG,eAAOM,KAAKC,OAAOmH,QAAQX,SAASjD,GAAjB,EAAsBmC,KAAK1B,KAAKI,WAAWJ,KAAK0B,GAAjD,CAAlB;MACD,WAAUC,QAAO,CAACD,QAAO2D,MAAMrF,KAAK2B,KAAK;AACxClG,eAAOM,KAAKC,OAAOmH,QAAQX,SAASjD,GAAjB,EAAsBoC,KAAK3B,KAAKI,WAAWJ,KAAK2B,GAAjD,CAAlB;MACD,WAAUD,QAAOC,SAAQ0D,MAAMrF,KAAK0B,OAAO2D,MAAMrF,KAAK2B,MAAM;AAC3DlG,eAAOM,KACLC,OAAOmH,QAAQX,SAASjD,GAAjB,EAAsBqC,OAAO5B,KAAKI,WAAWJ,KAAK0B,KAAK1B,KAAK2B,GAA7D,CADR;MAGD;IACF;AC5CD,IAAMQ,SAAO;AAEb,IAAMC,eAA0B,SAA1BA,WAA2BpC,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,SAAY;AACxEnD,WAAKmC,MAAD,IAAS/E,MAAMC,QAAQ2C,KAAKmC,MAAD,CAAlB,IAA4BnC,KAAKmC,MAAD,IAAS,CAAA;AACtD,UAAInC,KAAKmC,MAAD,EAAO3C,QAAQtC,KAAnB,MAA8B,IAAI;AACpCzB,eAAOM,KACLC,OAAOmH,QAAQX,SAASL,MAAjB,GAAwBnC,KAAKI,WAAWJ,KAAKmC,MAAD,EAAOoD,KAAK,IAAhB,CAAzC,CADR;MAGD;IACF;ACTD,IAAM1D,YAAuB,SAAvBA,QAAwB7B,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,SAAY;AACrE,UAAInD,KAAK6B,SAAS;AAChB,YAAI7B,KAAK6B,mBAAmBgC,QAAQ;AAIlC7D,eAAK6B,QAAQ2D,YAAY;AACzB,cAAI,CAACxF,KAAK6B,QAAQuB,KAAKlG,KAAlB,GAA0B;AAC7BzB,mBAAOM,KACLC,OACEmH,QAAQX,SAASX,QAAQC,UACzB9B,KAAKI,WACLlD,OACA8C,KAAK6B,OAJD,CADR;UAQD;mBACQ,OAAO7B,KAAK6B,YAAY,UAAU;AAC3C,cAAM4D,WAAW,IAAI5B,OAAO7D,KAAK6B,OAAhB;AACjB,cAAI,CAAC4D,SAASrC,KAAKlG,KAAd,GAAsB;AACzBzB,mBAAOM,KACLC,OACEmH,QAAQX,SAASX,QAAQC,UACzB9B,KAAKI,WACLlD,OACA8C,KAAK6B,OAJD,CADR;UAQD;QACF;MACF;IACF;AC3BD,IAAA,QAAe;MACbnB,UAAAA;MACAC;MACA3D,MAAAA;MACA4E;MACA,QAAM8D;MACN7D,SAAAA;IANa;ACHf,IAAMb,SAA2B,SAA3BA,QAA4BhB,MAAM9C,OAAOO,UAAUkB,QAAQwE,SAAY;AAC3E,UAAM1H,SAAmB,CAAA;AACzB,UAAMkK,WACJ3F,KAAKU,YAAa,CAACV,KAAKU,YAAY/B,OAAO6B,eAAeR,KAAKlE,KAA3B;AACtC,UAAI6J,UAAU;AACZ,YAAI1I,aAAaC,OAAO,QAAR,KAAqB,CAAC8C,KAAKU,UAAU;AACnD,iBAAOjD,SAAQ;QAChB;AACDmI,cAAMlF,SAASV,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,SAAS,QAArD;AACA,YAAI,CAAClG,aAAaC,OAAO,QAAR,GAAmB;AAClC0I,gBAAM5I,KAAKgD,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAAxC;AACAyC,gBAAMhE,MAAM5B,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAAzC;AACAyC,gBAAM/D,QAAQ7B,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAA3C;AACA,cAAInD,KAAKW,eAAe,MAAM;AAC5BiF,kBAAMjF,WAAWX,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAA9C;UACD;QACF;MACF;AACD1F,eAAShC,MAAD;IACT;ACnBD,IAAMwF,UAA2B,SAA3BA,QAA4BjB,MAAM9C,OAAOO,UAAUkB,QAAQwE,SAAY;AAC3E,UAAM1H,SAAmB,CAAA;AACzB,UAAMkK,WACJ3F,KAAKU,YAAa,CAACV,KAAKU,YAAY/B,OAAO6B,eAAeR,KAAKlE,KAA3B;AACtC,UAAI6J,UAAU;AACZ,YAAI1I,aAAaC,KAAD,KAAW,CAAC8C,KAAKU,UAAU;AACzC,iBAAOjD,SAAQ;QAChB;AACDmI,cAAMlF,SAASV,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAA5C;AACA,YAAIjG,UAAUC,QAAW;AACvByI,gBAAM5I,KAAKgD,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAAxC;QACD;MACF;AACD1F,eAAShC,MAAD;IACT;ACdD,IAAM2F,UAA2B,SAA3BA,QAA4BpB,MAAM9C,OAAOO,UAAUkB,QAAQwE,SAAY;AAC3E,UAAM1H,SAAmB,CAAA;AACzB,UAAMkK,WACJ3F,KAAKU,YAAa,CAACV,KAAKU,YAAY/B,OAAO6B,eAAeR,KAAKlE,KAA3B;AACtC,UAAI6J,UAAU;AACZ,YAAIzI,UAAU,IAAI;AAChBA,kBAAQC;QACT;AACD,YAAIF,aAAaC,KAAD,KAAW,CAAC8C,KAAKU,UAAU;AACzC,iBAAOjD,SAAQ;QAChB;AACDmI,cAAMlF,SAASV,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAA5C;AACA,YAAIjG,UAAUC,QAAW;AACvByI,gBAAM5I,KAAKgD,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAAxC;AACAyC,gBAAMhE,MAAM5B,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAAzC;QACD;MACF;AACD1F,eAAShC,MAAD;IACT;AClBD,IAAM4G,WAA4B,SAA5BA,UAA6BrC,MAAM9C,OAAOO,UAAUkB,QAAQwE,SAAY;AAC5E,UAAM1H,SAAmB,CAAA;AACzB,UAAMkK,WACJ3F,KAAKU,YAAa,CAACV,KAAKU,YAAY/B,OAAO6B,eAAeR,KAAKlE,KAA3B;AACtC,UAAI6J,UAAU;AACZ,YAAI1I,aAAaC,KAAD,KAAW,CAAC8C,KAAKU,UAAU;AACzC,iBAAOjD,SAAQ;QAChB;AACDmI,cAAMlF,SAASV,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAA5C;AACA,YAAIjG,UAAUC,QAAW;AACvByI,gBAAM5I,KAAKgD,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAAxC;QACD;MACF;AACD1F,eAAShC,MAAD;IACT;ACdD,IAAM6F,UAA2B,SAA3BA,QAA4BtB,MAAM9C,OAAOO,UAAUkB,QAAQwE,SAAY;AAC3E,UAAM1H,SAAmB,CAAA;AACzB,UAAMkK,WACJ3F,KAAKU,YAAa,CAACV,KAAKU,YAAY/B,OAAO6B,eAAeR,KAAKlE,KAA3B;AACtC,UAAI6J,UAAU;AACZ,YAAI1I,aAAaC,KAAD,KAAW,CAAC8C,KAAKU,UAAU;AACzC,iBAAOjD,SAAQ;QAChB;AACDmI,cAAMlF,SAASV,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAA5C;AACA,YAAI,CAAClG,aAAaC,KAAD,GAAS;AACxB0I,gBAAM5I,KAAKgD,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAAxC;QACD;MACF;AACD1F,eAAShC,MAAD;IACT;ACdD,IAAM4F,WAA4B,SAA5BA,SAA6BrB,MAAM9C,OAAOO,UAAUkB,QAAQwE,SAAY;AAC5E,UAAM1H,SAAmB,CAAA;AACzB,UAAMkK,WACJ3F,KAAKU,YAAa,CAACV,KAAKU,YAAY/B,OAAO6B,eAAeR,KAAKlE,KAA3B;AACtC,UAAI6J,UAAU;AACZ,YAAI1I,aAAaC,KAAD,KAAW,CAAC8C,KAAKU,UAAU;AACzC,iBAAOjD,SAAQ;QAChB;AACDmI,cAAMlF,SAASV,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAA5C;AACA,YAAIjG,UAAUC,QAAW;AACvByI,gBAAM5I,KAAKgD,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAAxC;AACAyC,gBAAMhE,MAAM5B,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAAzC;QACD;MACF;AACD1F,eAAShC,MAAD;IACT;ACfD,IAAM6G,UAA4B,SAA5BA,SAA6BtC,MAAM9C,OAAOO,UAAUkB,QAAQwE,SAAY;AAC5E,UAAM1H,SAAmB,CAAA;AACzB,UAAMkK,WACJ3F,KAAKU,YAAa,CAACV,KAAKU,YAAY/B,OAAO6B,eAAeR,KAAKlE,KAA3B;AACtC,UAAI6J,UAAU;AACZ,YAAI1I,aAAaC,KAAD,KAAW,CAAC8C,KAAKU,UAAU;AACzC,iBAAOjD,SAAQ;QAChB;AACDmI,cAAMlF,SAASV,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAA5C;AACA,YAAIjG,UAAUC,QAAW;AACvByI,gBAAM5I,KAAKgD,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAAxC;AACAyC,gBAAMhE,MAAM5B,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAAzC;QACD;MACF;AACD1F,eAAShC,MAAD;IACT;AChBD,IAAMyF,SAA0B,SAA1BA,OAA2BlB,MAAM9C,OAAOO,UAAUkB,QAAQwE,SAAY;AAC1E,UAAM1H,SAAmB,CAAA;AACzB,UAAMkK,WACJ3F,KAAKU,YAAa,CAACV,KAAKU,YAAY/B,OAAO6B,eAAeR,KAAKlE,KAA3B;AACtC,UAAI6J,UAAU;AACZ,aAAKzI,UAAUC,UAAaD,UAAU,SAAS,CAAC8C,KAAKU,UAAU;AAC7D,iBAAOjD,SAAQ;QAChB;AACDmI,cAAMlF,SAASV,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,SAAS,OAArD;AACA,YAAIjG,UAAUC,UAAaD,UAAU,MAAM;AACzC0I,gBAAM5I,KAAKgD,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAAxC;AACAyC,gBAAMhE,MAAM5B,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAAzC;QACD;MACF;AACD1F,eAAShC,MAAD;IACT;ACdD,IAAM0F,UAA2B,SAA3BA,QAA4BnB,MAAM9C,OAAOO,UAAUkB,QAAQwE,SAAY;AAC3E,UAAM1H,SAAmB,CAAA;AACzB,UAAMkK,WACJ3F,KAAKU,YAAa,CAACV,KAAKU,YAAY/B,OAAO6B,eAAeR,KAAKlE,KAA3B;AACtC,UAAI6J,UAAU;AACZ,YAAI1I,aAAaC,KAAD,KAAW,CAAC8C,KAAKU,UAAU;AACzC,iBAAOjD,SAAQ;QAChB;AACDmI,cAAMlF,SAASV,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAA5C;AACA,YAAIjG,UAAUC,QAAW;AACvByI,gBAAM5I,KAAKgD,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAAxC;QACD;MACF;AACD1F,eAAShC,MAAD;IACT;ACdD,IAAM0G,OAAO;AAEb,IAAMC,cAA+B,SAA/BA,YACJpC,MACA9C,OACAO,UACAkB,QACAwE,SACG;AACH,UAAM1H,SAAmB,CAAA;AACzB,UAAMkK,WACJ3F,KAAKU,YAAa,CAACV,KAAKU,YAAY/B,OAAO6B,eAAeR,KAAKlE,KAA3B;AACtC,UAAI6J,UAAU;AACZ,YAAI1I,aAAaC,KAAD,KAAW,CAAC8C,KAAKU,UAAU;AACzC,iBAAOjD,SAAQ;QAChB;AACDmI,cAAMlF,SAASV,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAA5C;AACA,YAAIjG,UAAUC,QAAW;AACvByI,gBAAMzD,IAAD,EAAOnC,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAAzC;QACD;MACF;AACD1F,eAAShC,MAAD;IACT;ACtBD,IAAMoG,WAA4B,SAA5BA,SAA6B7B,MAAM9C,OAAOO,UAAUkB,QAAQwE,SAAY;AAC5E,UAAM1H,SAAmB,CAAA;AACzB,UAAMkK,WACJ3F,KAAKU,YAAa,CAACV,KAAKU,YAAY/B,OAAO6B,eAAeR,KAAKlE,KAA3B;AACtC,UAAI6J,UAAU;AACZ,YAAI1I,aAAaC,OAAO,QAAR,KAAqB,CAAC8C,KAAKU,UAAU;AACnD,iBAAOjD,SAAQ;QAChB;AACDmI,cAAMlF,SAASV,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAA5C;AACA,YAAI,CAAClG,aAAaC,OAAO,QAAR,GAAmB;AAClC0I,gBAAM/D,QAAQ7B,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAA3C;QACD;MACF;AACD1F,eAAShC,MAAD;IACT;ACdD,IAAMmF,QAAyB,SAAzBA,MAA0BZ,MAAM9C,OAAOO,UAAUkB,QAAQwE,SAAY;AAEzE,UAAM1H,SAAmB,CAAA;AACzB,UAAMkK,WACJ3F,KAAKU,YAAa,CAACV,KAAKU,YAAY/B,OAAO6B,eAAeR,KAAKlE,KAA3B;AAEtC,UAAI6J,UAAU;AACZ,YAAI1I,aAAaC,OAAO,MAAR,KAAmB,CAAC8C,KAAKU,UAAU;AACjD,iBAAOjD,SAAQ;QAChB;AACDmI,cAAMlF,SAASV,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAA5C;AACA,YAAI,CAAClG,aAAaC,OAAO,MAAR,GAAiB;AAChC,cAAI2I;AAEJ,cAAI3I,iBAAiB4I,MAAM;AACzBD,yBAAa3I;UACd,OAAM;AACL2I,yBAAa,IAAIC,KAAK5I,KAAT;UACd;AAED0I,gBAAM5I,KAAKgD,MAAM6F,YAAYlH,QAAQlD,QAAQ0H,OAA7C;AACA,cAAI0C,YAAY;AACdD,kBAAMhE,MAAM5B,MAAM6F,WAAWjB,QAAX,GAAsBjG,QAAQlD,QAAQ0H,OAAxD;UACD;QACF;MACF;AACD1F,eAAShC,MAAD;IACT;AC5BD,IAAMiF,YAA6B,SAA7BA,UAA8BV,MAAM9C,OAAOO,UAAUkB,QAAQwE,SAAY;AAC7E,UAAM1H,SAAmB,CAAA;AACzB,UAAMuB,QAAOI,MAAMC,QAAQH,KAAd,IAAuB,UAAU,OAAOA;AACrD0I,YAAMlF,SAASV,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,SAASnG,KAArD;AACAS,eAAShC,MAAD;IACT;ACJD,IAAMuB,QAAyB,SAAzBA,MAA0BgD,MAAM9C,OAAOO,UAAUkB,QAAQwE,SAAY;AACzE,UAAMgC,WAAWnF,KAAKhD;AACtB,UAAMvB,SAAmB,CAAA;AACzB,UAAMkK,WACJ3F,KAAKU,YAAa,CAACV,KAAKU,YAAY/B,OAAO6B,eAAeR,KAAKlE,KAA3B;AACtC,UAAI6J,UAAU;AACZ,YAAI1I,aAAaC,OAAOiI,QAAR,KAAqB,CAACnF,KAAKU,UAAU;AACnD,iBAAOjD,SAAQ;QAChB;AACDmI,cAAMlF,SAASV,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,SAASgC,QAArD;AACA,YAAI,CAAClI,aAAaC,OAAOiI,QAAR,GAAmB;AAClCS,gBAAM5I,KAAKgD,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAAxC;QACD;MACF;AACD1F,eAAShC,MAAD;IACT;ACfD,IAAM8G,MAAwB,SAAxBA,KAAyBvC,MAAM9C,OAAOO,UAAUkB,QAAQwE,SAAY;AACxE,UAAM1H,SAAmB,CAAA;AACzB,UAAMkK,WACJ3F,KAAKU,YAAa,CAACV,KAAKU,YAAY/B,OAAO6B,eAAeR,KAAKlE,KAA3B;AACtC,UAAI6J,UAAU;AACZ,YAAI1I,aAAaC,KAAD,KAAW,CAAC8C,KAAKU,UAAU;AACzC,iBAAOjD,SAAQ;QAChB;AACDmI,cAAMlF,SAASV,MAAM9C,OAAOyB,QAAQlD,QAAQ0H,OAA5C;MACD;AACD1F,eAAShC,MAAD;IACT;ACCD,IAAA,aAAe;MACbuF;MACAC,QAAAA;MACAG,QAAAA;MACA,WAAAiB;MACAf,QAAAA;MACAD,SAAAA;MACA,SAAA0E;MACA7E,OAAAA;MACAC,QAAAA;MACA,QAAM6E;MACNnE,SAAAA;MACAjB,MAAAA;MACAY,KAAKxE;MACLyE,KAAKzE;MACLuE,OAAOvE;MACP0D,UAAAA;MACA6B;IAjBa;ACyCR,IAAMC,WAAW/B,YAAW;ICtB7BgC,SAAAA,WAAAA;AAqBJ,eAAAA,QAAYwD,YAAmB;AAAA,aAH/BL,QAAoC;AAGL,aAF/BM,YAAsCC;AAGpC,aAAKC,OAAOH,UAAZ;MACD;;aAEDG,SAAA,SAAOR,QAAAA,QAAc;AAAA,YAAA,QAAA;AACnB,YAAI,CAACA,QAAO;AACV,gBAAM,IAAI1C,MAAM,yCAAV;QACP;AACD,YAAI,OAAO0C,WAAU,YAAYxI,MAAMC,QAAQuI,MAAd,GAAsB;AACrD,gBAAM,IAAI1C,MAAM,yBAAV;QACP;AACD,aAAK0C,QAAQ,CAAA;AAEbtH,eAAOC,KAAKqH,MAAZ,EAAmBhK,QAAQ,SAAAyK,MAAQ;AACjC,cAAMC,OAAaV,OAAMS,IAAD;AACxB,gBAAKT,MAAMS,IAAX,IAAmBjJ,MAAMC,QAAQiJ,IAAd,IAAsBA,OAAO,CAACA,IAAD;SAFlD;;aAMF9D,WAAA,SAASA,UAAAA,WAA6B;AACpC,YAAIA,WAAU;AACZ,eAAK0D,YAAY7F,UAAUI,YAAW,GAAI+B,SAAhB;QAC3B;AACD,eAAO,KAAK0D;;AAWdP,aAAAA,WAAA,SAASY,SAAAA,SAAiBC,IAAaC,IAAqC;AAAA,YAAA,SAAA;AAAA,YAAlDD,OAAkD,QAAA;AAAlDA,UAAAA,KAAS,CAAA;QAAyC;AAAA,YAArCC,OAAqC,QAAA;AAArCA,eAAU,SAAMA,MAAA;UAAA;QAAqB;AAC1E,YAAI9H,SAAiB4H;AACrB,YAAIpD,UAA0BqD;AAC9B,YAAI/I,WAA6BgJ;AACjC,YAAI,OAAOtD,YAAY,YAAY;AACjC1F,qBAAW0F;AACXA,oBAAU,CAAA;QACX;AACD,YAAI,CAAC,KAAKyC,SAAStH,OAAOC,KAAK,KAAKqH,KAAjB,EAAwBlK,WAAW,GAAG;AACvD,cAAI+B,UAAU;AACZA,qBAAS,MAAMkB,MAAP;UACT;AACD,iBAAOG,QAAQC,QAAQJ,MAAhB;QACR;AAED,iBAAS+H,SAAShJ,SAA8C;AAC9D,cAAIjC,SAA0B,CAAA;AAC9B,cAAIE,SAA8B,CAAA;AAElC,mBAASgL,IAAIxH,GAAoC;AAC/C,gBAAI/B,MAAMC,QAAQ8B,CAAd,GAAkB;AAAA,kBAAA;AACpB1D,wBAASA,UAAAA,QAAOmL,OAAP,MAAA,SAAiBzH,CAAjB;YACV,OAAM;AACL1D,qBAAOM,KAAKoD,CAAZ;YACD;UACF;AAED,mBAAShD,IAAI,GAAGA,IAAIuB,QAAQhC,QAAQS,KAAK;AACvCwK,gBAAIjJ,QAAQvB,CAAD,CAAR;UACJ;AACD,cAAI,CAACV,OAAOC,QAAQ;AAClB+B,qBAAS,MAAMkB,MAAP;UACT,OAAM;AACLhD,qBAASH,mBAAmBC,MAAD;AAC1BgC,qBAGUhC,QAAQE,MAHnB;UAID;QACF;AAED,YAAIwH,QAAQX,UAAU;AACpB,cAAIA,aAAW,KAAKA,SAAL;AACf,cAAIA,eAAa2D,UAAiB;AAChC3D,yBAAW/B,YAAW;UACvB;AACDJ,oBAAUmC,YAAUW,QAAQX,QAAnB;AACTW,kBAAQX,WAAWA;QACpB,OAAM;AACLW,kBAAQX,WAAW,KAAKA,SAAL;QACpB;AAED,YAAMqE,SAA6C,CAAA;AACnD,YAAMtI,OAAO4E,QAAQ5E,QAAQD,OAAOC,KAAK,KAAKqH,KAAjB;AAC7BrH,aAAK3C,QAAQ,SAAAkL,GAAK;AAChB,cAAMvJ,MAAM,OAAKqI,MAAMkB,CAAX;AACZ,cAAI5J,QAAQyB,OAAOmI,CAAD;AAClBvJ,cAAI3B,QAAQ,SAAAmL,GAAK;AACf,gBAAI/G,OAAyB+G;AAC7B,gBAAI,OAAO/G,KAAKgH,cAAc,YAAY;AACxC,kBAAIrI,WAAW4H,SAAS;AACtB5H,yBAAM,SAAA,CAAA,GAAQA,MAAR;cACP;AACDzB,sBAAQyB,OAAOmI,CAAD,IAAM9G,KAAKgH,UAAU9J,KAAf;YACrB;AACD,gBAAI,OAAO8C,SAAS,YAAY;AAC9BA,qBAAO;gBACLiH,WAAWjH;;YAEd,OAAM;AACLA,qBAAI,SAAA,CAAA,GAAQA,IAAR;YACL;AAGDA,iBAAKiH,YAAY,OAAKC,oBAAoBlH,IAAzB;AACjB,gBAAI,CAACA,KAAKiH,WAAW;AACnB;YACD;AAEDjH,iBAAKlE,QAAQgL;AACb9G,iBAAKI,YAAYJ,KAAKI,aAAa0G;AACnC9G,iBAAKhD,OAAO,OAAKmK,QAAQnH,IAAb;AACZ6G,mBAAOC,CAAD,IAAMD,OAAOC,CAAD,KAAO,CAAA;AACzBD,mBAAOC,CAAD,EAAI/K,KAAK;cACbiE;cACA9C;cACAyB;cACA7C,OAAOgL;aAJT;WA1BF;SAHF;AAqCA,YAAMM,cAAc,CAAA;AACpB,eAAO3I,SACLoI,QACA1D,SACA,SAACkE,MAAMC,MAAS;AACd,cAAMtH,OAAOqH,KAAKrH;AAClB,cAAIuH,QACDvH,KAAKhD,SAAS,YAAYgD,KAAKhD,SAAS,aACxC,OAAOgD,KAAKrE,WAAW,YACtB,OAAOqE,KAAKwH,iBAAiB;AACjCD,iBAAOA,SAASvH,KAAKU,YAAa,CAACV,KAAKU,YAAY2G,KAAKnK;AACzD8C,eAAKlE,QAAQuL,KAAKvL;AAElB,mBAAS2L,aAAalI,KAAamI,QAAkB;AACnD,mBAAA,SAAA,CAAA,GACKA,QADL;cAEEtH,WAAcJ,KAAKI,YAAV,MAAuBb;cAChCY,YAAYH,KAAKG,aAAiBH,CAAAA,EAAAA,OAAAA,KAAKG,YAAYZ,CAAAA,GAAvC,CAA8C,IAAA,CAACA,GAAD;YAH5D,CAAA;UAKD;AAED,mBAASoI,GAAGxI,GAAyC;AAAA,gBAAzCA,MAAyC,QAAA;AAAzCA,kBAAqC,CAAA;YAAI;AACnD,gBAAIyI,YAAYxK,MAAMC,QAAQ8B,CAAd,IAAmBA,IAAI,CAACA,CAAD;AACvC,gBAAI,CAACgE,QAAQ0E,mBAAmBD,UAAUlM,QAAQ;AAChD+G,cAAAA,QAAOR,QAAQ,oBAAoB2F,SAAnC;YACD;AACD,gBAAIA,UAAUlM,UAAUsE,KAAKL,YAAYxC,QAAW;AAClDyK,0BAAY,CAAA,EAAGhB,OAAO5G,KAAKL,OAAf;YACb;AAGD,gBAAImI,eAAeF,UAAUG,IAAIhI,gBAAgBC,MAAMrB,MAAP,CAA7B;AAEnB,gBAAIwE,QAAQvE,SAASkJ,aAAapM,QAAQ;AACxC0L,0BAAYpH,KAAKlE,KAAN,IAAe;AAC1B,qBAAOwL,KAAKQ,YAAD;YACZ;AACD,gBAAI,CAACP,MAAM;AACTD,mBAAKQ,YAAD;YACL,OAAM;AAIL,kBAAI9H,KAAKU,YAAY,CAAC2G,KAAKnK,OAAO;AAChC,oBAAI8C,KAAKL,YAAYxC,QAAW;AAC9B2K,iCAAe,CAAA,EACZlB,OAAO5G,KAAKL,OADA,EAEZoI,IAAIhI,gBAAgBC,MAAMrB,MAAP,CAFP;gBAGhB,WAAUwE,QAAQtH,OAAO;AACxBiM,iCAAe,CACb3E,QAAQtH,MACNmE,MACAhE,OAAOmH,QAAQX,SAAS9B,UAAUV,KAAKlE,KAAjC,CAFR,CADa;gBAMhB;AACD,uBAAOwL,KAAKQ,YAAD;cACZ;AAED,kBAAIE,eAAqC,CAAA;AACzC,kBAAIhI,KAAKwH,cAAc;AACrBlJ,uBAAOC,KAAK8I,KAAKnK,KAAjB,EAAwB6K,IAAI,SAAAxI,KAAO;AACjCyI,+BAAazI,GAAD,IAAQS,KAAKwH;iBAD3B;cAGD;AACDQ,6BAAY,SAAA,CAAA,GACPA,cACAX,KAAKrH,KAAKrE,MAFH;AAKZ,kBAAMsM,oBAAgD,CAAA;AAEtD3J,qBAAOC,KAAKyJ,YAAZ,EAA0BpM,QAAQ,SAAAE,OAAS;AACzC,oBAAMoM,cAAcF,aAAalM,KAAD;AAChC,oBAAMqM,kBAAkB/K,MAAMC,QAAQ6K,WAAd,IACpBA,cACA,CAACA,WAAD;AACJD,kCAAkBnM,KAAD,IAAUqM,gBAAgBJ,IACzCN,aAAaW,KAAK,MAAMtM,KAAxB,CADyB;eAL7B;AASA,kBAAM4L,SAAS,IAAIjF,QAAOwF,iBAAX;AACfP,qBAAOlF,SAASW,QAAQX,QAAxB;AACA,kBAAI6E,KAAKrH,KAAKmD,SAAS;AACrBkE,qBAAKrH,KAAKmD,QAAQX,WAAWW,QAAQX;AACrC6E,qBAAKrH,KAAKmD,QAAQtH,QAAQsH,QAAQtH;cACnC;AACD6L,qBAAO/B,SAAS0B,KAAKnK,OAAOmK,KAAKrH,KAAKmD,WAAWA,SAAS,SAAAkF,MAAQ;AAChE,oBAAMC,cAAc,CAAA;AACpB,oBAAIR,gBAAgBA,aAAapM,QAAQ;AACvC4M,8BAAYvM,KAAZ,MAAAuM,aAAoBR,YAAT;gBACZ;AACD,oBAAIO,QAAQA,KAAK3M,QAAQ;AACvB4M,8BAAYvM,KAAZ,MAAAuM,aAAoBD,IAAT;gBACZ;AACDf,qBAAKgB,YAAY5M,SAAS4M,cAAc,IAApC;eARN;YAUD;UACF;AAED,cAAIC;AACJ,cAAIvI,KAAKwI,gBAAgB;AACvBD,kBAAMvI,KAAKwI,eAAexI,MAAMqH,KAAKnK,OAAOyK,IAAIN,KAAK1I,QAAQwE,OAAvD;UACP,WAAUnD,KAAKiH,WAAW;AACzB,gBAAI;AACFsB,oBAAMvI,KAAKiH,UAAUjH,MAAMqH,KAAKnK,OAAOyK,IAAIN,KAAK1I,QAAQwE,OAAlD;qBACCtH,OAAO;AACdiH,sBAAQjH,SAARiH,OAAAA,SAAAA,QAAQjH,MAAQA,KAAhB;AAEA,kBAAI,CAACsH,QAAQsF,wBAAwB;AACnCC,2BAAW,WAAM;AACf,wBAAM7M;mBACL,CAFO;cAGX;AACD8L,iBAAG9L,MAAM8D,OAAP;YACH;AACD,gBAAI4I,QAAQ,MAAM;AAChBZ,iBAAE;YACH,WAAUY,QAAQ,OAAO;AACxBZ,iBACE,OAAO3H,KAAKL,YAAY,aACpBK,KAAKL,QAAQK,KAAKI,aAAaJ,KAAKlE,KAApC,IACAkE,KAAKL,YAAcK,KAAKI,aAAaJ,KAAKlE,SAA1C,QAHJ;YAKH,WAAUyM,eAAenL,OAAO;AAC/BuK,iBAAGY,GAAD;YACH,WAAUA,eAAerF,OAAO;AAC/ByE,iBAAGY,IAAI5I,OAAL;YACH;UACF;AACD,cAAI4I,OAAQA,IAAsBI,MAAM;AACrCJ,gBAAsBI,KACrB,WAAA;AAAA,qBAAMhB,GAAE;eACR,SAAAxI,GAAC;AAAA,qBAAIwI,GAAGxI,CAAD;aAFT;UAID;WAEH,SAAAzB,SAAW;AACTgJ,mBAAShJ,OAAD;WAEViB,MA3Ia;;aA+IjBwI,UAAA,SAAQnH,QAAAA,MAAwB;AAC9B,YAAIA,KAAKhD,SAASG,UAAa6C,KAAK6B,mBAAmBgC,QAAQ;AAC7D7D,eAAKhD,OAAO;QACb;AACD,YACE,OAAOgD,KAAKiH,cAAc,cAC1BjH,KAAKhD,QACL,CAAC4L,WAAWpI,eAAeR,KAAKhD,IAA/B,GACD;AACA,gBAAM,IAAIkG,MAAMlH,OAAO,wBAAwBgE,KAAKhD,IAA9B,CAAhB;QACP;AACD,eAAOgD,KAAKhD,QAAQ;;aAGtBkK,sBAAA,SAAoBlH,oBAAAA,MAAwB;AAC1C,YAAI,OAAOA,KAAKiH,cAAc,YAAY;AACxC,iBAAOjH,KAAKiH;QACb;AACD,YAAM1I,OAAOD,OAAOC,KAAKyB,IAAZ;AACb,YAAM6I,eAAetK,KAAKiB,QAAQ,SAAb;AACrB,YAAIqJ,iBAAiB,IAAI;AACvBtK,eAAKuK,OAAOD,cAAc,CAA1B;QACD;AACD,YAAItK,KAAK7C,WAAW,KAAK6C,KAAK,CAAD,MAAQ,YAAY;AAC/C,iBAAOqK,WAAWlI;QACnB;AACD,eAAOkI,WAAW,KAAKzB,QAAQnH,IAAb,CAAD,KAAwB7C;;;;AA5TvCsF,WAEGsG,WAAW,SAASA,SAAS/L,OAAciK,WAAW;AAC3D,UAAI,OAAOA,cAAc,YAAY;AACnC,cAAM,IAAI/D,MACR,kEADI;MAGP;AACD0F,iBAAW5L,KAAD,IAASiK;IACpB;AATGxE,WAWGR,UAAUA;AAXbQ,WAaGD,WAAW2D;AAbd1D,WAeGmG,aAAaA;;;;;ACxBtB,SAAS,MAAM,OAAO,OAAO,KAAK;AAChC,SAAO,IAAI,OAAO,IAAI,OAAO,GAAG,CAAC;AACnC;AACA,SAAS,SAAS,OAAO,OAAO;AAC9B,SAAO,OAAO,UAAU,aAAa,MAAM,KAAK,IAAI;AACtD;AACA,SAAS,QAAQ,WAAW;AAC1B,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;AACA,SAAS,aAAa,WAAW;AAC/B,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;AACA,SAAS,gBAAgB,MAAM;AAC7B,SAAO,SAAS,MAAM,MAAM;AAC9B;AACA,SAAS,cAAc,MAAM;AAC3B,SAAO,SAAS,MAAM,WAAW;AACnC;AACA,SAAS,YAAY,WAAW;AAC9B,SAAO,CAAC,OAAO,QAAQ,EAAE,SAAS,QAAQ,SAAS,CAAC,IAAI,MAAM;AAChE;AACA,SAAS,iBAAiB,WAAW;AACnC,SAAO,gBAAgB,YAAY,SAAS,CAAC;AAC/C;AACA,SAAS,kBAAkB,WAAW,OAAO,KAAK;AAChD,MAAI,QAAQ,QAAQ;AAClB,UAAM;AAAA,EACR;AACA,QAAM,YAAY,aAAa,SAAS;AACxC,QAAM,gBAAgB,iBAAiB,SAAS;AAChD,QAAM,SAAS,cAAc,aAAa;AAC1C,MAAI,oBAAoB,kBAAkB,MAAM,eAAe,MAAM,QAAQ,WAAW,UAAU,SAAS,cAAc,UAAU,WAAW;AAC9I,MAAI,MAAM,UAAU,MAAM,IAAI,MAAM,SAAS,MAAM,GAAG;AACpD,wBAAoB,qBAAqB,iBAAiB;AAAA,EAC5D;AACA,SAAO,CAAC,mBAAmB,qBAAqB,iBAAiB,CAAC;AACpE;AACA,SAAS,sBAAsB,WAAW;AACxC,QAAM,oBAAoB,qBAAqB,SAAS;AACxD,SAAO,CAAC,8BAA8B,SAAS,GAAG,mBAAmB,8BAA8B,iBAAiB,CAAC;AACvH;AACA,SAAS,8BAA8B,WAAW;AAChD,SAAO,UAAU,QAAQ,cAAc,eAAa,qBAAqB,SAAS,CAAC;AACrF;AACA,SAAS,YAAY,MAAM,SAAS,KAAK;AACvC,QAAM,KAAK,CAAC,QAAQ,OAAO;AAC3B,QAAM,KAAK,CAAC,SAAS,MAAM;AAC3B,QAAM,KAAK,CAAC,OAAO,QAAQ;AAC3B,QAAM,KAAK,CAAC,UAAU,KAAK;AAC3B,UAAQ,MAAM;AAAA,IACZ,KAAK;AAAA,IACL,KAAK;AACH,UAAI,IAAK,QAAO,UAAU,KAAK;AAC/B,aAAO,UAAU,KAAK;AAAA,IACxB,KAAK;AAAA,IACL,KAAK;AACH,aAAO,UAAU,KAAK;AAAA,IACxB;AACE,aAAO,CAAC;AAAA,EACZ;AACF;AACA,SAAS,0BAA0B,WAAW,eAAe,WAAW,KAAK;AAC3E,QAAM,YAAY,aAAa,SAAS;AACxC,MAAI,OAAO,YAAY,QAAQ,SAAS,GAAG,cAAc,SAAS,GAAG;AACrE,MAAI,WAAW;AACb,WAAO,KAAK,IAAI,UAAQ,OAAO,MAAM,SAAS;AAC9C,QAAI,eAAe;AACjB,aAAO,KAAK,OAAO,KAAK,IAAI,6BAA6B,CAAC;AAAA,IAC5D;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,WAAW;AACvC,SAAO,UAAU,QAAQ,0BAA0B,UAAQ,gBAAgB,IAAI,CAAC;AAClF;AACA,SAAS,oBAAoB,SAAS;AACpC,SAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,GAAG;AAAA,EACL;AACF;AACA,SAAS,iBAAiB,SAAS;AACjC,SAAO,OAAO,YAAY,WAAW,oBAAoB,OAAO,IAAI;AAAA,IAClE,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACR;AACF;AACA,SAAS,iBAAiB,MAAM;AAC9B,QAAM;AAAA,IACJ,GAAAI;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,KAAK;AAAA,IACL,MAAMA;AAAA,IACN,OAAOA,KAAI;AAAA,IACX,QAAQ,IAAI;AAAA,IACZ,GAAAA;AAAA,IACA;AAAA,EACF;AACF;AAvIA,IAKM,OACA,YACA,YACA,KACA,KACA,OACA,OACA,cAIA,iBAMA;AAtBN;AAAA;AAKA,IAAM,QAAQ,CAAC,OAAO,SAAS,UAAU,MAAM;AAC/C,IAAM,aAAa,CAAC,SAAS,KAAK;AAClC,IAAM,aAA0B,MAAM,OAAO,CAAC,KAAK,SAAS,IAAI,OAAO,MAAM,OAAO,MAAM,WAAW,CAAC,GAAG,OAAO,MAAM,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;AACxI,IAAM,MAAM,KAAK;AACjB,IAAM,MAAM,KAAK;AACjB,IAAM,QAAQ,KAAK;AACnB,IAAM,QAAQ,KAAK;AACnB,IAAM,eAAe,CAAAC,QAAM;AAAA,MACzB,GAAGA;AAAA,MACH,GAAGA;AAAA,IACL;AACA,IAAM,kBAAkB;AAAA,MACtB,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,KAAK;AAAA,IACP;AACA,IAAM,uBAAuB;AAAA,MAC3B,OAAO;AAAA,MACP,KAAK;AAAA,IACP;AAAA;AAAA;;;ACtBA,SAAS,2BAA2B,MAAM,WAAW,KAAK;AACxD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,WAAW,YAAY,SAAS;AACtC,QAAM,gBAAgB,iBAAiB,SAAS;AAChD,QAAM,cAAc,cAAc,aAAa;AAC/C,QAAM,OAAO,QAAQ,SAAS;AAC9B,QAAM,aAAa,aAAa;AAChC,QAAM,UAAU,UAAU,IAAI,UAAU,QAAQ,IAAI,SAAS,QAAQ;AACrE,QAAM,UAAU,UAAU,IAAI,UAAU,SAAS,IAAI,SAAS,SAAS;AACvE,QAAM,cAAc,UAAU,WAAW,IAAI,IAAI,SAAS,WAAW,IAAI;AACzE,MAAI;AACJ,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,eAAS;AAAA,QACP,GAAG;AAAA,QACH,GAAG,UAAU,IAAI,SAAS;AAAA,MAC5B;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG;AAAA,QACH,GAAG,UAAU,IAAI,UAAU;AAAA,MAC7B;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG,UAAU,IAAI,UAAU;AAAA,QAC3B,GAAG;AAAA,MACL;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG,UAAU,IAAI,SAAS;AAAA,QAC1B,GAAG;AAAA,MACL;AACA;AAAA,IACF;AACE,eAAS;AAAA,QACP,GAAG,UAAU;AAAA,QACb,GAAG,UAAU;AAAA,MACf;AAAA,EACJ;AACA,UAAQ,aAAa,SAAS,GAAG;AAAA,IAC/B,KAAK;AACH,aAAO,aAAa,KAAK,eAAe,OAAO,aAAa,KAAK;AACjE;AAAA,IACF,KAAK;AACH,aAAO,aAAa,KAAK,eAAe,OAAO,aAAa,KAAK;AACjE;AAAA,EACJ;AACA,SAAO;AACT;AAqGA,eAAe,eAAe,OAAO,SAAS;AAC5C,MAAI;AACJ,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ,GAAAC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ,WAAW;AAAA,IACX,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,UAAU;AAAA,EACZ,IAAI,SAAS,SAAS,KAAK;AAC3B,QAAM,gBAAgB,iBAAiB,OAAO;AAC9C,QAAM,aAAa,mBAAmB,aAAa,cAAc;AACjE,QAAM,UAAU,SAAS,cAAc,aAAa,cAAc;AAClE,QAAM,qBAAqB,iBAAiB,MAAM,SAAS,gBAAgB;AAAA,IACzE,WAAW,wBAAwB,OAAO,SAAS,aAAa,OAAO,SAAS,SAAS,UAAU,OAAO,OAAO,OAAO,wBAAwB,QAAQ,UAAU,QAAQ,kBAAmB,OAAO,SAAS,sBAAsB,OAAO,SAAS,SAAS,mBAAmB,SAAS,QAAQ;AAAA,IAChS;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,CAAC;AACF,QAAM,OAAO,mBAAmB,aAAa;AAAA,IAC3C,GAAAA;AAAA,IACA;AAAA,IACA,OAAO,MAAM,SAAS;AAAA,IACtB,QAAQ,MAAM,SAAS;AAAA,EACzB,IAAI,MAAM;AACV,QAAM,eAAe,OAAO,SAAS,mBAAmB,OAAO,SAAS,SAAS,gBAAgB,SAAS,QAAQ;AAClH,QAAM,cAAe,OAAO,SAAS,aAAa,OAAO,SAAS,SAAS,UAAU,YAAY,KAAO,OAAO,SAAS,YAAY,OAAO,SAAS,SAAS,SAAS,YAAY,MAAO;AAAA,IACvL,GAAG;AAAA,IACH,GAAG;AAAA,EACL,IAAI;AAAA,IACF,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM,oBAAoB,iBAAiB,SAAS,wDAAwD,MAAM,SAAS,sDAAsD;AAAA,IAC/K;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,IAAI,IAAI;AACT,SAAO;AAAA,IACL,MAAM,mBAAmB,MAAM,kBAAkB,MAAM,cAAc,OAAO,YAAY;AAAA,IACxF,SAAS,kBAAkB,SAAS,mBAAmB,SAAS,cAAc,UAAU,YAAY;AAAA,IACpG,OAAO,mBAAmB,OAAO,kBAAkB,OAAO,cAAc,QAAQ,YAAY;AAAA,IAC5F,QAAQ,kBAAkB,QAAQ,mBAAmB,QAAQ,cAAc,SAAS,YAAY;AAAA,EAClG;AACF;AAoFA,SAAS,iBAAiB,WAAW,eAAe,mBAAmB;AACrE,QAAM,qCAAqC,YAAY,CAAC,GAAG,kBAAkB,OAAO,eAAa,aAAa,SAAS,MAAM,SAAS,GAAG,GAAG,kBAAkB,OAAO,eAAa,aAAa,SAAS,MAAM,SAAS,CAAC,IAAI,kBAAkB,OAAO,eAAa,QAAQ,SAAS,MAAM,SAAS;AAClS,SAAO,mCAAmC,OAAO,eAAa;AAC5D,QAAI,WAAW;AACb,aAAO,aAAa,SAAS,MAAM,cAAc,gBAAgB,8BAA8B,SAAS,MAAM,YAAY;AAAA,IAC5H;AACA,WAAO;AAAA,EACT,CAAC;AACH;AA6NA,SAAS,eAAe,UAAU,MAAM;AACtC,SAAO;AAAA,IACL,KAAK,SAAS,MAAM,KAAK;AAAA,IACzB,OAAO,SAAS,QAAQ,KAAK;AAAA,IAC7B,QAAQ,SAAS,SAAS,KAAK;AAAA,IAC/B,MAAM,SAAS,OAAO,KAAK;AAAA,EAC7B;AACF;AACA,SAAS,sBAAsB,UAAU;AACvC,SAAO,MAAM,KAAK,UAAQ,SAAS,IAAI,KAAK,CAAC;AAC/C;AA2DA,SAAS,gBAAgB,OAAO;AAC9B,QAAM,OAAO,IAAI,GAAG,MAAM,IAAI,UAAQ,KAAK,IAAI,CAAC;AAChD,QAAM,OAAO,IAAI,GAAG,MAAM,IAAI,UAAQ,KAAK,GAAG,CAAC;AAC/C,QAAM,OAAO,IAAI,GAAG,MAAM,IAAI,UAAQ,KAAK,KAAK,CAAC;AACjD,QAAM,OAAO,IAAI,GAAG,MAAM,IAAI,UAAQ,KAAK,MAAM,CAAC;AAClD,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO,OAAO;AAAA,IACd,QAAQ,OAAO;AAAA,EACjB;AACF;AACA,SAAS,eAAe,OAAO;AAC7B,QAAM,cAAc,MAAM,MAAM,EAAE,KAAK,CAACC,IAAGC,OAAMD,GAAE,IAAIC,GAAE,CAAC;AAC1D,QAAM,SAAS,CAAC;AAChB,MAAI,WAAW;AACf,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,UAAM,OAAO,YAAY,CAAC;AAC1B,QAAI,CAAC,YAAY,KAAK,IAAI,SAAS,IAAI,SAAS,SAAS,GAAG;AAC1D,aAAO,KAAK,CAAC,IAAI,CAAC;AAAA,IACpB,OAAO;AACL,aAAO,OAAO,SAAS,CAAC,EAAE,KAAK,IAAI;AAAA,IACrC;AACA,eAAW;AAAA,EACb;AACA,SAAO,OAAO,IAAI,UAAQ,iBAAiB,gBAAgB,IAAI,CAAC,CAAC;AACnE;AA4GA,eAAe,qBAAqB,OAAO,SAAS;AAClD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,MAAM,OAAO,SAAS,SAAS,OAAO,SAAS,SAAS,MAAM,SAAS,QAAQ;AACrF,QAAM,OAAO,QAAQ,SAAS;AAC9B,QAAM,YAAY,aAAa,SAAS;AACxC,QAAM,aAAa,YAAY,SAAS,MAAM;AAC9C,QAAM,gBAAgB,CAAC,QAAQ,KAAK,EAAE,SAAS,IAAI,IAAI,KAAK;AAC5D,QAAM,iBAAiB,OAAO,aAAa,KAAK;AAChD,QAAM,WAAW,SAAS,SAAS,KAAK;AAGxC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OAAO,aAAa,WAAW;AAAA,IACjC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,eAAe;AAAA,EACjB,IAAI;AAAA,IACF,UAAU,SAAS,YAAY;AAAA,IAC/B,WAAW,SAAS,aAAa;AAAA,IACjC,eAAe,SAAS;AAAA,EAC1B;AACA,MAAI,aAAa,OAAO,kBAAkB,UAAU;AAClD,gBAAY,cAAc,QAAQ,gBAAgB,KAAK;AAAA,EACzD;AACA,SAAO,aAAa;AAAA,IAClB,GAAG,YAAY;AAAA,IACf,GAAG,WAAW;AAAA,EAChB,IAAI;AAAA,IACF,GAAG,WAAW;AAAA,IACd,GAAG,YAAY;AAAA,EACjB;AACF;AA/vBA,IAkEM,iBA0JA,OA4FA,eA8FA,MAwIA,MAqFA,QAqJA,QAuCA,OA2EA,YAwEA;AAl8BN;AAAA;AAAA;AACA;AAiEA,IAAM,kBAAkB,OAAO,WAAW,UAAU,WAAW;AAC7D,YAAM;AAAA,QACJ,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,aAAa,CAAC;AAAA,QACd;AAAA,MACF,IAAI;AACJ,YAAM,kBAAkB,WAAW,OAAO,OAAO;AACjD,YAAM,MAAM,OAAO,SAAS,SAAS,OAAO,SAAS,SAAS,MAAM,QAAQ;AAC5E,UAAI,QAAQ,MAAM,SAAS,gBAAgB;AAAA,QACzC;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD,UAAI;AAAA,QACF,GAAAF;AAAA,QACA;AAAA,MACF,IAAI,2BAA2B,OAAO,WAAW,GAAG;AACpD,UAAI,oBAAoB;AACxB,UAAI,iBAAiB,CAAC;AACtB,UAAI,aAAa;AACjB,eAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,gBAAgB,CAAC;AACrB,cAAM;AAAA,UACJ,GAAG;AAAA,UACH,GAAG;AAAA,UACH;AAAA,UACA;AAAA,QACF,IAAI,MAAM,GAAG;AAAA,UACX,GAAAA;AAAA,UACA;AAAA,UACA,kBAAkB;AAAA,UAClB,WAAW;AAAA,UACX;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,UAAU;AAAA,YACR;AAAA,YACA;AAAA,UACF;AAAA,QACF,CAAC;AACD,QAAAA,KAAI,SAAS,OAAO,QAAQA;AAC5B,YAAI,SAAS,OAAO,QAAQ;AAC5B,yBAAiB;AAAA,UACf,GAAG;AAAA,UACH,CAAC,IAAI,GAAG;AAAA,YACN,GAAG,eAAe,IAAI;AAAA,YACtB,GAAG;AAAA,UACL;AAAA,QACF;AACA,YAAI,SAAS,cAAc,IAAI;AAC7B;AACA,cAAI,OAAO,UAAU,UAAU;AAC7B,gBAAI,MAAM,WAAW;AACnB,kCAAoB,MAAM;AAAA,YAC5B;AACA,gBAAI,MAAM,OAAO;AACf,sBAAQ,MAAM,UAAU,OAAO,MAAM,SAAS,gBAAgB;AAAA,gBAC5D;AAAA,gBACA;AAAA,gBACA;AAAA,cACF,CAAC,IAAI,MAAM;AAAA,YACb;AACA,aAAC;AAAA,cACC,GAAAA;AAAA,cACA;AAAA,YACF,IAAI,2BAA2B,OAAO,mBAAmB,GAAG;AAAA,UAC9D;AACA,cAAI;AAAA,QACN;AAAA,MACF;AACA,aAAO;AAAA,QACL,GAAAA;AAAA,QACA;AAAA,QACA,WAAW;AAAA,QACX;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAwEA,IAAM,QAAQ,cAAY;AAAA,MACxB,MAAM;AAAA,MACN;AAAA,MACA,MAAM,GAAG,OAAO;AACd,cAAM;AAAA,UACJ,GAAAA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AAEJ,cAAM;AAAA,UACJ;AAAA,UACA,UAAU;AAAA,QACZ,IAAI,SAAS,SAAS,KAAK,KAAK,CAAC;AACjC,YAAI,WAAW,MAAM;AACnB,iBAAO,CAAC;AAAA,QACV;AACA,cAAM,gBAAgB,iBAAiB,OAAO;AAC9C,cAAM,SAAS;AAAA,UACb,GAAAA;AAAA,UACA;AAAA,QACF;AACA,cAAM,OAAO,iBAAiB,SAAS;AACvC,cAAM,SAAS,cAAc,IAAI;AACjC,cAAM,kBAAkB,MAAM,SAAS,cAAc,OAAO;AAC5D,cAAM,UAAU,SAAS;AACzB,cAAM,UAAU,UAAU,QAAQ;AAClC,cAAM,UAAU,UAAU,WAAW;AACrC,cAAM,aAAa,UAAU,iBAAiB;AAC9C,cAAM,UAAU,MAAM,UAAU,MAAM,IAAI,MAAM,UAAU,IAAI,IAAI,OAAO,IAAI,IAAI,MAAM,SAAS,MAAM;AACtG,cAAM,YAAY,OAAO,IAAI,IAAI,MAAM,UAAU,IAAI;AACrD,cAAM,oBAAoB,OAAO,SAAS,mBAAmB,OAAO,SAAS,SAAS,gBAAgB,OAAO;AAC7G,YAAI,aAAa,oBAAoB,kBAAkB,UAAU,IAAI;AAGrE,YAAI,CAAC,cAAc,CAAE,OAAO,SAAS,aAAa,OAAO,SAAS,SAAS,UAAU,iBAAiB,IAAK;AACzG,uBAAa,SAAS,SAAS,UAAU,KAAK,MAAM,SAAS,MAAM;AAAA,QACrE;AACA,cAAM,oBAAoB,UAAU,IAAI,YAAY;AAIpD,cAAM,yBAAyB,aAAa,IAAI,gBAAgB,MAAM,IAAI,IAAI;AAC9E,cAAM,aAAa,IAAI,cAAc,OAAO,GAAG,sBAAsB;AACrE,cAAM,aAAa,IAAI,cAAc,OAAO,GAAG,sBAAsB;AAIrE,cAAM,QAAQ;AACd,cAAMG,OAAM,aAAa,gBAAgB,MAAM,IAAI;AACnD,cAAM,SAAS,aAAa,IAAI,gBAAgB,MAAM,IAAI,IAAI;AAC9D,cAAMC,UAAS,MAAM,OAAO,QAAQD,IAAG;AAMvC,cAAM,kBAAkB,CAAC,eAAe,SAAS,aAAa,SAAS,KAAK,QAAQ,WAAWC,WAAU,MAAM,UAAU,MAAM,IAAI,KAAK,SAAS,QAAQ,aAAa,cAAc,gBAAgB,MAAM,IAAI,IAAI;AAClN,cAAM,kBAAkB,kBAAkB,SAAS,QAAQ,SAAS,QAAQ,SAASD,OAAM;AAC3F,eAAO;AAAA,UACL,CAAC,IAAI,GAAG,OAAO,IAAI,IAAI;AAAA,UACvB,MAAM;AAAA,YACJ,CAAC,IAAI,GAAGC;AAAA,YACR,cAAc,SAASA,UAAS;AAAA,YAChC,GAAI,mBAAmB;AAAA,cACrB;AAAA,YACF;AAAA,UACF;AAAA,UACA,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAiBA,IAAM,gBAAgB,SAAU,SAAS;AACvC,UAAI,YAAY,QAAQ;AACtB,kBAAU,CAAC;AAAA,MACb;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA,MAAM,GAAG,OAAO;AACd,cAAI,uBAAuB,wBAAwB;AACnD,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM;AAAA,YACJ,YAAY;AAAA,YACZ;AAAA,YACA,oBAAoB;AAAA,YACpB,gBAAgB;AAAA,YAChB,GAAG;AAAA,UACL,IAAI,SAAS,SAAS,KAAK;AAC3B,gBAAM,eAAe,cAAc,UAAa,sBAAsB,aAAa,iBAAiB,aAAa,MAAM,eAAe,iBAAiB,IAAI;AAC3J,gBAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,gBAAM,iBAAiB,wBAAwB,eAAe,kBAAkB,OAAO,SAAS,sBAAsB,UAAU;AAChI,gBAAM,mBAAmB,aAAa,YAAY;AAClD,cAAI,oBAAoB,MAAM;AAC5B,mBAAO,CAAC;AAAA,UACV;AACA,gBAAM,iBAAiB,kBAAkB,kBAAkB,OAAO,OAAO,SAAS,SAAS,OAAO,SAAS,SAAS,MAAM,SAAS,QAAQ,EAAE;AAG7I,cAAI,cAAc,kBAAkB;AAClC,mBAAO;AAAA,cACL,OAAO;AAAA,gBACL,WAAW,aAAa,CAAC;AAAA,cAC3B;AAAA,YACF;AAAA,UACF;AACA,gBAAM,mBAAmB,CAAC,SAAS,QAAQ,gBAAgB,CAAC,GAAG,SAAS,eAAe,CAAC,CAAC,GAAG,SAAS,eAAe,CAAC,CAAC,CAAC;AACvH,gBAAM,eAAe,CAAC,KAAM,yBAAyB,eAAe,kBAAkB,OAAO,SAAS,uBAAuB,cAAc,CAAC,GAAI;AAAA,YAC9I,WAAW;AAAA,YACX,WAAW;AAAA,UACb,CAAC;AACD,gBAAM,gBAAgB,aAAa,eAAe,CAAC;AAGnD,cAAI,eAAe;AACjB,mBAAO;AAAA,cACL,MAAM;AAAA,gBACJ,OAAO,eAAe;AAAA,gBACtB,WAAW;AAAA,cACb;AAAA,cACA,OAAO;AAAA,gBACL,WAAW;AAAA,cACb;AAAA,YACF;AAAA,UACF;AACA,gBAAM,8BAA8B,aAAa,IAAI,CAAAC,OAAK;AACxD,kBAAMC,aAAY,aAAaD,GAAE,SAAS;AAC1C,mBAAO,CAACA,GAAE,WAAWC,cAAa;AAAA;AAAA,cAElCD,GAAE,UAAU,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC,KAAKE,OAAM,MAAMA,IAAG,CAAC;AAAA;AAAA;AAAA,cAErDF,GAAE,UAAU,CAAC;AAAA,eAAGA,GAAE,SAAS;AAAA,UAC7B,CAAC,EAAE,KAAK,CAACJ,IAAGC,OAAMD,GAAE,CAAC,IAAIC,GAAE,CAAC,CAAC;AAC7B,gBAAM,8BAA8B,4BAA4B,OAAO,CAAAG,OAAKA,GAAE,CAAC,EAAE;AAAA,YAAM;AAAA;AAAA;AAAA,YAGvF,aAAaA,GAAE,CAAC,CAAC,IAAI,IAAI;AAAA,UAAC,EAAE,MAAM,CAAAE,OAAKA,MAAK,CAAC,CAAC;AAC9C,gBAAM,mBAAmB,wBAAwB,4BAA4B,CAAC,MAAM,OAAO,SAAS,sBAAsB,CAAC,MAAM,4BAA4B,CAAC,EAAE,CAAC;AACjK,cAAI,mBAAmB,WAAW;AAChC,mBAAO;AAAA,cACL,MAAM;AAAA,gBACJ,OAAO,eAAe;AAAA,gBACtB,WAAW;AAAA,cACb;AAAA,cACA,OAAO;AAAA,gBACL,WAAW;AAAA,cACb;AAAA,YACF;AAAA,UACF;AACA,iBAAO,CAAC;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAQA,IAAM,OAAO,SAAU,SAAS;AAC9B,UAAI,YAAY,QAAQ;AACtB,kBAAU,CAAC;AAAA,MACb;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA,MAAM,GAAG,OAAO;AACd,cAAI,uBAAuB;AAC3B,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM;AAAA,YACJ,UAAU,gBAAgB;AAAA,YAC1B,WAAW,iBAAiB;AAAA,YAC5B,oBAAoB;AAAA,YACpB,mBAAmB;AAAA,YACnB,4BAA4B;AAAA,YAC5B,gBAAgB;AAAA,YAChB,GAAG;AAAA,UACL,IAAI,SAAS,SAAS,KAAK;AAM3B,eAAK,wBAAwB,eAAe,UAAU,QAAQ,sBAAsB,iBAAiB;AACnG,mBAAO,CAAC;AAAA,UACV;AACA,gBAAM,OAAO,QAAQ,SAAS;AAC9B,gBAAM,kBAAkB,YAAY,gBAAgB;AACpD,gBAAM,kBAAkB,QAAQ,gBAAgB,MAAM;AACtD,gBAAM,MAAM,OAAO,SAAS,SAAS,OAAO,SAAS,SAAS,MAAM,SAAS,QAAQ;AACrF,gBAAM,qBAAqB,gCAAgC,mBAAmB,CAAC,gBAAgB,CAAC,qBAAqB,gBAAgB,CAAC,IAAI,sBAAsB,gBAAgB;AAChL,gBAAM,+BAA+B,8BAA8B;AACnE,cAAI,CAAC,+BAA+B,8BAA8B;AAChE,+BAAmB,KAAK,GAAG,0BAA0B,kBAAkB,eAAe,2BAA2B,GAAG,CAAC;AAAA,UACvH;AACA,gBAAMC,cAAa,CAAC,kBAAkB,GAAG,kBAAkB;AAC3D,gBAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,gBAAM,YAAY,CAAC;AACnB,cAAI,kBAAkB,uBAAuB,eAAe,SAAS,OAAO,SAAS,qBAAqB,cAAc,CAAC;AACzH,cAAI,eAAe;AACjB,sBAAU,KAAK,SAAS,IAAI,CAAC;AAAA,UAC/B;AACA,cAAI,gBAAgB;AAClB,kBAAMC,SAAQ,kBAAkB,WAAW,OAAO,GAAG;AACrD,sBAAU,KAAK,SAASA,OAAM,CAAC,CAAC,GAAG,SAASA,OAAM,CAAC,CAAC,CAAC;AAAA,UACvD;AACA,0BAAgB,CAAC,GAAG,eAAe;AAAA,YACjC;AAAA,YACA;AAAA,UACF,CAAC;AAGD,cAAI,CAAC,UAAU,MAAM,CAAAC,UAAQA,SAAQ,CAAC,GAAG;AACvC,gBAAI,uBAAuB;AAC3B,kBAAM,eAAe,wBAAwB,eAAe,SAAS,OAAO,SAAS,sBAAsB,UAAU,KAAK;AAC1H,kBAAM,gBAAgBF,YAAW,SAAS;AAC1C,gBAAI,eAAe;AAEjB,qBAAO;AAAA,gBACL,MAAM;AAAA,kBACJ,OAAO;AAAA,kBACP,WAAW;AAAA,gBACb;AAAA,gBACA,OAAO;AAAA,kBACL,WAAW;AAAA,gBACb;AAAA,cACF;AAAA,YACF;AAIA,gBAAI,kBAAkB,wBAAwB,cAAc,OAAO,CAAAH,OAAKA,GAAE,UAAU,CAAC,KAAK,CAAC,EAAE,KAAK,CAACJ,IAAGC,OAAMD,GAAE,UAAU,CAAC,IAAIC,GAAE,UAAU,CAAC,CAAC,EAAE,CAAC,MAAM,OAAO,SAAS,sBAAsB;AAG1L,gBAAI,CAAC,gBAAgB;AACnB,sBAAQ,kBAAkB;AAAA,gBACxB,KAAK,WACH;AACE,sBAAI;AACJ,wBAAMS,cAAa,yBAAyB,cAAc,OAAO,CAAAN,OAAK;AACpE,wBAAI,8BAA8B;AAChC,4BAAM,kBAAkB,YAAYA,GAAE,SAAS;AAC/C,6BAAO,oBAAoB;AAAA;AAAA,sBAG3B,oBAAoB;AAAA,oBACtB;AACA,2BAAO;AAAA,kBACT,CAAC,EAAE,IAAI,CAAAA,OAAK,CAACA,GAAE,WAAWA,GAAE,UAAU,OAAO,CAAAO,cAAYA,YAAW,CAAC,EAAE,OAAO,CAAC,KAAKA,cAAa,MAAMA,WAAU,CAAC,CAAC,CAAC,EAAE,KAAK,CAACX,IAAGC,OAAMD,GAAE,CAAC,IAAIC,GAAE,CAAC,CAAC,EAAE,CAAC,MAAM,OAAO,SAAS,uBAAuB,CAAC;AACjM,sBAAIS,YAAW;AACb,qCAAiBA;AAAA,kBACnB;AACA;AAAA,gBACF;AAAA,gBACF,KAAK;AACH,mCAAiB;AACjB;AAAA,cACJ;AAAA,YACF;AACA,gBAAI,cAAc,gBAAgB;AAChC,qBAAO;AAAA,gBACL,OAAO;AAAA,kBACL,WAAW;AAAA,gBACb;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,iBAAO,CAAC;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAkBA,IAAM,OAAO,SAAU,SAAS;AAC9B,UAAI,YAAY,QAAQ;AACtB,kBAAU,CAAC;AAAA,MACb;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA,MAAM,GAAG,OAAO;AACd,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,gBAAM;AAAA,YACJ,WAAW;AAAA,YACX,GAAG;AAAA,UACL,IAAI,SAAS,SAAS,KAAK;AAC3B,kBAAQ,UAAU;AAAA,YAChB,KAAK,mBACH;AACE,oBAAM,WAAW,MAAM,eAAe,OAAO;AAAA,gBAC3C,GAAG;AAAA,gBACH,gBAAgB;AAAA,cAClB,CAAC;AACD,oBAAM,UAAU,eAAe,UAAU,MAAM,SAAS;AACxD,qBAAO;AAAA,gBACL,MAAM;AAAA,kBACJ,wBAAwB;AAAA,kBACxB,iBAAiB,sBAAsB,OAAO;AAAA,gBAChD;AAAA,cACF;AAAA,YACF;AAAA,YACF,KAAK,WACH;AACE,oBAAM,WAAW,MAAM,eAAe,OAAO;AAAA,gBAC3C,GAAG;AAAA,gBACH,aAAa;AAAA,cACf,CAAC;AACD,oBAAM,UAAU,eAAe,UAAU,MAAM,QAAQ;AACvD,qBAAO;AAAA,gBACL,MAAM;AAAA,kBACJ,gBAAgB;AAAA,kBAChB,SAAS,sBAAsB,OAAO;AAAA,gBACxC;AAAA,cACF;AAAA,YACF;AAAA,YACF,SACE;AACE,qBAAO,CAAC;AAAA,YACV;AAAA,UACJ;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAkCA,IAAM,SAAS,SAAU,SAAS;AAChC,UAAI,YAAY,QAAQ;AACtB,kBAAU,CAAC;AAAA,MACb;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA,MAAM,GAAG,OAAO;AACd,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AAIJ,gBAAM;AAAA,YACJ,UAAU;AAAA,YACV,GAAAX;AAAA,YACA;AAAA,UACF,IAAI,SAAS,SAAS,KAAK;AAC3B,gBAAM,oBAAoB,MAAM,KAAM,OAAO,SAAS,kBAAkB,OAAO,SAAS,SAAS,eAAe,SAAS,SAAS,MAAO,CAAC,CAAC;AAC3I,gBAAM,cAAc,eAAe,iBAAiB;AACpD,gBAAM,WAAW,iBAAiB,gBAAgB,iBAAiB,CAAC;AACpE,gBAAM,gBAAgB,iBAAiB,OAAO;AAC9C,mBAAS,wBAAwB;AAE/B,gBAAI,YAAY,WAAW,KAAK,YAAY,CAAC,EAAE,OAAO,YAAY,CAAC,EAAE,SAASA,MAAK,QAAQ,KAAK,MAAM;AAEpG,qBAAO,YAAY,KAAK,UAAQA,KAAI,KAAK,OAAO,cAAc,QAAQA,KAAI,KAAK,QAAQ,cAAc,SAAS,IAAI,KAAK,MAAM,cAAc,OAAO,IAAI,KAAK,SAAS,cAAc,MAAM,KAAK;AAAA,YAC/L;AAGA,gBAAI,YAAY,UAAU,GAAG;AAC3B,kBAAI,YAAY,SAAS,MAAM,KAAK;AAClC,sBAAM,YAAY,YAAY,CAAC;AAC/B,sBAAM,WAAW,YAAY,YAAY,SAAS,CAAC;AACnD,sBAAM,QAAQ,QAAQ,SAAS,MAAM;AACrC,sBAAMa,OAAM,UAAU;AACtB,sBAAMC,UAAS,SAAS;AACxB,sBAAMC,QAAO,QAAQ,UAAU,OAAO,SAAS;AAC/C,sBAAMC,SAAQ,QAAQ,UAAU,QAAQ,SAAS;AACjD,sBAAMC,SAAQD,SAAQD;AACtB,sBAAMG,UAASJ,UAASD;AACxB,uBAAO;AAAA,kBACL,KAAAA;AAAA,kBACA,QAAAC;AAAA,kBACA,MAAAC;AAAA,kBACA,OAAAC;AAAA,kBACA,OAAAC;AAAA,kBACA,QAAAC;AAAA,kBACA,GAAGH;AAAA,kBACH,GAAGF;AAAA,gBACL;AAAA,cACF;AACA,oBAAM,aAAa,QAAQ,SAAS,MAAM;AAC1C,oBAAM,WAAW,IAAI,GAAG,YAAY,IAAI,UAAQ,KAAK,KAAK,CAAC;AAC3D,oBAAM,UAAU,IAAI,GAAG,YAAY,IAAI,UAAQ,KAAK,IAAI,CAAC;AACzD,oBAAM,eAAe,YAAY,OAAO,UAAQ,aAAa,KAAK,SAAS,UAAU,KAAK,UAAU,QAAQ;AAC5G,oBAAM,MAAM,aAAa,CAAC,EAAE;AAC5B,oBAAM,SAAS,aAAa,aAAa,SAAS,CAAC,EAAE;AACrD,oBAAM,OAAO;AACb,oBAAM,QAAQ;AACd,oBAAM,QAAQ,QAAQ;AACtB,oBAAM,SAAS,SAAS;AACxB,qBAAO;AAAA,gBACL;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,GAAG;AAAA,gBACH,GAAG;AAAA,cACL;AAAA,YACF;AACA,mBAAO;AAAA,UACT;AACA,gBAAM,aAAa,MAAM,SAAS,gBAAgB;AAAA,YAChD,WAAW;AAAA,cACT;AAAA,YACF;AAAA,YACA,UAAU,SAAS;AAAA,YACnB;AAAA,UACF,CAAC;AACD,cAAI,MAAM,UAAU,MAAM,WAAW,UAAU,KAAK,MAAM,UAAU,MAAM,WAAW,UAAU,KAAK,MAAM,UAAU,UAAU,WAAW,UAAU,SAAS,MAAM,UAAU,WAAW,WAAW,UAAU,QAAQ;AAClN,mBAAO;AAAA,cACL,OAAO;AAAA,gBACL,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AACA,iBAAO,CAAC;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAoDA,IAAM,SAAS,SAAU,SAAS;AAChC,UAAI,YAAY,QAAQ;AACtB,kBAAU;AAAA,MACZ;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA,MAAM,GAAG,OAAO;AACd,cAAI,uBAAuB;AAC3B,gBAAM;AAAA,YACJ,GAAAb;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM,aAAa,MAAM,qBAAqB,OAAO,OAAO;AAI5D,cAAI,gBAAgB,wBAAwB,eAAe,WAAW,OAAO,SAAS,sBAAsB,eAAe,wBAAwB,eAAe,UAAU,QAAQ,sBAAsB,iBAAiB;AACzN,mBAAO,CAAC;AAAA,UACV;AACA,iBAAO;AAAA,YACL,GAAGA,KAAI,WAAW;AAAA,YAClB,GAAG,IAAI,WAAW;AAAA,YAClB,MAAM;AAAA,cACJ,GAAG;AAAA,cACH;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAOA,IAAM,QAAQ,SAAU,SAAS;AAC/B,UAAI,YAAY,QAAQ;AACtB,kBAAU,CAAC;AAAA,MACb;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA,MAAM,GAAG,OAAO;AACd,gBAAM;AAAA,YACJ,GAAAA;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM;AAAA,YACJ,UAAU,gBAAgB;AAAA,YAC1B,WAAW,iBAAiB;AAAA,YAC5B,UAAU;AAAA,cACR,IAAI,UAAQ;AACV,oBAAI;AAAA,kBACF,GAAAA;AAAA,kBACA,GAAAmB;AAAA,gBACF,IAAI;AACJ,uBAAO;AAAA,kBACL,GAAAnB;AAAA,kBACA,GAAAmB;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,YACA,GAAG;AAAA,UACL,IAAI,SAAS,SAAS,KAAK;AAC3B,gBAAM,SAAS;AAAA,YACb,GAAAnB;AAAA,YACA;AAAA,UACF;AACA,gBAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,gBAAM,YAAY,YAAY,QAAQ,SAAS,CAAC;AAChD,gBAAM,WAAW,gBAAgB,SAAS;AAC1C,cAAI,gBAAgB,OAAO,QAAQ;AACnC,cAAI,iBAAiB,OAAO,SAAS;AACrC,cAAI,eAAe;AACjB,kBAAM,UAAU,aAAa,MAAM,QAAQ;AAC3C,kBAAM,UAAU,aAAa,MAAM,WAAW;AAC9C,kBAAMoB,OAAM,gBAAgB,SAAS,OAAO;AAC5C,kBAAMjB,OAAM,gBAAgB,SAAS,OAAO;AAC5C,4BAAgB,MAAMiB,MAAK,eAAejB,IAAG;AAAA,UAC/C;AACA,cAAI,gBAAgB;AAClB,kBAAM,UAAU,cAAc,MAAM,QAAQ;AAC5C,kBAAM,UAAU,cAAc,MAAM,WAAW;AAC/C,kBAAMiB,OAAM,iBAAiB,SAAS,OAAO;AAC7C,kBAAMjB,OAAM,iBAAiB,SAAS,OAAO;AAC7C,6BAAiB,MAAMiB,MAAK,gBAAgBjB,IAAG;AAAA,UACjD;AACA,gBAAM,gBAAgB,QAAQ,GAAG;AAAA,YAC/B,GAAG;AAAA,YACH,CAAC,QAAQ,GAAG;AAAA,YACZ,CAAC,SAAS,GAAG;AAAA,UACf,CAAC;AACD,iBAAO;AAAA,YACL,GAAG;AAAA,YACH,MAAM;AAAA,cACJ,GAAG,cAAc,IAAIH;AAAA,cACrB,GAAG,cAAc,IAAI;AAAA,cACrB,SAAS;AAAA,gBACP,CAAC,QAAQ,GAAG;AAAA,gBACZ,CAAC,SAAS,GAAG;AAAA,cACf;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAIA,IAAM,aAAa,SAAU,SAAS;AACpC,UAAI,YAAY,QAAQ;AACtB,kBAAU,CAAC;AAAA,MACb;AACA,aAAO;AAAA,QACL;AAAA,QACA,GAAG,OAAO;AACR,gBAAM;AAAA,YACJ,GAAAA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM;AAAA,YACJ,QAAAI,UAAS;AAAA,YACT,UAAU,gBAAgB;AAAA,YAC1B,WAAW,iBAAiB;AAAA,UAC9B,IAAI,SAAS,SAAS,KAAK;AAC3B,gBAAM,SAAS;AAAA,YACb,GAAAJ;AAAA,YACA;AAAA,UACF;AACA,gBAAM,YAAY,YAAY,SAAS;AACvC,gBAAM,WAAW,gBAAgB,SAAS;AAC1C,cAAI,gBAAgB,OAAO,QAAQ;AACnC,cAAI,iBAAiB,OAAO,SAAS;AACrC,gBAAM,YAAY,SAASI,SAAQ,KAAK;AACxC,gBAAM,iBAAiB,OAAO,cAAc,WAAW;AAAA,YACrD,UAAU;AAAA,YACV,WAAW;AAAA,UACb,IAAI;AAAA,YACF,UAAU;AAAA,YACV,WAAW;AAAA,YACX,GAAG;AAAA,UACL;AACA,cAAI,eAAe;AACjB,kBAAM,MAAM,aAAa,MAAM,WAAW;AAC1C,kBAAM,WAAW,MAAM,UAAU,QAAQ,IAAI,MAAM,SAAS,GAAG,IAAI,eAAe;AAClF,kBAAM,WAAW,MAAM,UAAU,QAAQ,IAAI,MAAM,UAAU,GAAG,IAAI,eAAe;AACnF,gBAAI,gBAAgB,UAAU;AAC5B,8BAAgB;AAAA,YAClB,WAAW,gBAAgB,UAAU;AACnC,8BAAgB;AAAA,YAClB;AAAA,UACF;AACA,cAAI,gBAAgB;AAClB,gBAAI,uBAAuB;AAC3B,kBAAM,MAAM,aAAa,MAAM,UAAU;AACzC,kBAAM,eAAe,CAAC,OAAO,MAAM,EAAE,SAAS,QAAQ,SAAS,CAAC;AAChE,kBAAM,WAAW,MAAM,UAAU,SAAS,IAAI,MAAM,SAAS,GAAG,KAAK,iBAAiB,wBAAwB,eAAe,WAAW,OAAO,SAAS,sBAAsB,SAAS,MAAM,IAAI,MAAM,eAAe,IAAI,eAAe;AACzO,kBAAM,WAAW,MAAM,UAAU,SAAS,IAAI,MAAM,UAAU,GAAG,KAAK,eAAe,MAAM,yBAAyB,eAAe,WAAW,OAAO,SAAS,uBAAuB,SAAS,MAAM,MAAM,eAAe,eAAe,YAAY;AACpP,gBAAI,iBAAiB,UAAU;AAC7B,+BAAiB;AAAA,YACnB,WAAW,iBAAiB,UAAU;AACpC,+BAAiB;AAAA,YACnB;AAAA,UACF;AACA,iBAAO;AAAA,YACL,CAAC,QAAQ,GAAG;AAAA,YACZ,CAAC,SAAS,GAAG;AAAA,UACf;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAQA,IAAM,OAAO,SAAU,SAAS;AAC9B,UAAI,YAAY,QAAQ;AACtB,kBAAU,CAAC;AAAA,MACb;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA,MAAM,GAAG,OAAO;AACd,cAAI,uBAAuB;AAC3B,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM;AAAA,YACJ,QAAQ,MAAM;AAAA,YAAC;AAAA,YACf,GAAG;AAAA,UACL,IAAI,SAAS,SAAS,KAAK;AAC3B,gBAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,gBAAM,OAAO,QAAQ,SAAS;AAC9B,gBAAM,YAAY,aAAa,SAAS;AACxC,gBAAM,UAAU,YAAY,SAAS,MAAM;AAC3C,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI,MAAM;AACV,cAAI;AACJ,cAAI;AACJ,cAAI,SAAS,SAAS,SAAS,UAAU;AACvC,yBAAa;AACb,wBAAY,eAAgB,OAAO,SAAS,SAAS,OAAO,SAAS,SAAS,MAAM,SAAS,QAAQ,KAAM,UAAU,SAAS,SAAS;AAAA,UACzI,OAAO;AACL,wBAAY;AACZ,yBAAa,cAAc,QAAQ,QAAQ;AAAA,UAC7C;AACA,gBAAM,wBAAwB,SAAS,SAAS,MAAM,SAAS;AAC/D,gBAAM,uBAAuB,QAAQ,SAAS,OAAO,SAAS;AAC9D,gBAAM,0BAA0B,IAAI,SAAS,SAAS,UAAU,GAAG,qBAAqB;AACxF,gBAAM,yBAAyB,IAAI,QAAQ,SAAS,SAAS,GAAG,oBAAoB;AACpF,gBAAM,UAAU,CAAC,MAAM,eAAe;AACtC,cAAI,kBAAkB;AACtB,cAAI,iBAAiB;AACrB,eAAK,wBAAwB,MAAM,eAAe,UAAU,QAAQ,sBAAsB,QAAQ,GAAG;AACnG,6BAAiB;AAAA,UACnB;AACA,eAAK,yBAAyB,MAAM,eAAe,UAAU,QAAQ,uBAAuB,QAAQ,GAAG;AACrG,8BAAkB;AAAA,UACpB;AACA,cAAI,WAAW,CAAC,WAAW;AACzB,kBAAM,OAAO,IAAI,SAAS,MAAM,CAAC;AACjC,kBAAM,OAAO,IAAI,SAAS,OAAO,CAAC;AAClC,kBAAM,OAAO,IAAI,SAAS,KAAK,CAAC;AAChC,kBAAM,OAAO,IAAI,SAAS,QAAQ,CAAC;AACnC,gBAAI,SAAS;AACX,+BAAiB,QAAQ,KAAK,SAAS,KAAK,SAAS,IAAI,OAAO,OAAO,IAAI,SAAS,MAAM,SAAS,KAAK;AAAA,YAC1G,OAAO;AACL,gCAAkB,SAAS,KAAK,SAAS,KAAK,SAAS,IAAI,OAAO,OAAO,IAAI,SAAS,KAAK,SAAS,MAAM;AAAA,YAC5G;AAAA,UACF;AACA,gBAAM,MAAM;AAAA,YACV,GAAG;AAAA,YACH;AAAA,YACA;AAAA,UACF,CAAC;AACD,gBAAM,iBAAiB,MAAM,SAAS,cAAc,SAAS,QAAQ;AACrE,cAAI,UAAU,eAAe,SAAS,WAAW,eAAe,QAAQ;AACtE,mBAAO;AAAA,cACL,OAAO;AAAA,gBACL,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AACA,iBAAO,CAAC;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;AC9gCA,SAAS,YAAY;AACnB,SAAO,OAAO,WAAW;AAC3B;AACA,SAAS,YAAY,MAAM;AACzB,MAAI,OAAO,IAAI,GAAG;AAChB,YAAQ,KAAK,YAAY,IAAI,YAAY;AAAA,EAC3C;AAIA,SAAO;AACT;AACA,SAAS,UAAU,MAAM;AACvB,MAAI;AACJ,UAAQ,QAAQ,SAAS,sBAAsB,KAAK,kBAAkB,OAAO,SAAS,oBAAoB,gBAAgB;AAC5H;AACA,SAAS,mBAAmB,MAAM;AAChC,MAAI;AACJ,UAAQ,QAAQ,OAAO,IAAI,IAAI,KAAK,gBAAgB,KAAK,aAAa,OAAO,aAAa,OAAO,SAAS,KAAK;AACjH;AACA,SAAS,OAAO,OAAO;AACrB,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,QAAQ,iBAAiB,UAAU,KAAK,EAAE;AACpE;AACA,SAAS,UAAU,OAAO;AACxB,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,WAAW,iBAAiB,UAAU,KAAK,EAAE;AACvE;AACA,SAAS,cAAc,OAAO;AAC5B,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,eAAe,iBAAiB,UAAU,KAAK,EAAE;AAC3E;AACA,SAAS,aAAa,OAAO;AAC3B,MAAI,CAAC,UAAU,KAAK,OAAO,eAAe,aAAa;AACrD,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,cAAc,iBAAiB,UAAU,KAAK,EAAE;AAC1E;AACA,SAAS,kBAAkB,SAAS;AAClC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,OAAO;AAC5B,SAAO,kCAAkC,KAAK,WAAW,YAAY,SAAS,KAAK,CAAC,CAAC,UAAU,UAAU,EAAE,SAAS,OAAO;AAC7H;AACA,SAAS,eAAe,SAAS;AAC/B,SAAO,CAAC,SAAS,MAAM,IAAI,EAAE,SAAS,YAAY,OAAO,CAAC;AAC5D;AACA,SAAS,WAAW,SAAS;AAC3B,SAAO,CAAC,iBAAiB,QAAQ,EAAE,KAAK,cAAY;AAClD,QAAI;AACF,aAAO,QAAQ,QAAQ,QAAQ;AAAA,IACjC,SAAS,GAAG;AACV,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACH;AACA,SAAS,kBAAkB,cAAc;AACvC,QAAM,SAAS,SAAS;AACxB,QAAM,MAAM,UAAU,YAAY,IAAI,iBAAiB,YAAY,IAAI;AAIvE,SAAO,CAAC,aAAa,aAAa,SAAS,UAAU,aAAa,EAAE,KAAK,WAAS,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,SAAS,KAAK,MAAM,IAAI,gBAAgB,IAAI,kBAAkB,WAAW,UAAU,CAAC,WAAW,IAAI,iBAAiB,IAAI,mBAAmB,SAAS,UAAU,CAAC,WAAW,IAAI,SAAS,IAAI,WAAW,SAAS,UAAU,CAAC,aAAa,aAAa,SAAS,UAAU,eAAe,QAAQ,EAAE,KAAK,YAAU,IAAI,cAAc,IAAI,SAAS,KAAK,CAAC,KAAK,CAAC,SAAS,UAAU,UAAU,SAAS,EAAE,KAAK,YAAU,IAAI,WAAW,IAAI,SAAS,KAAK,CAAC;AACniB;AACA,SAAS,mBAAmB,SAAS;AACnC,MAAI,cAAc,cAAc,OAAO;AACvC,SAAO,cAAc,WAAW,KAAK,CAAC,sBAAsB,WAAW,GAAG;AACxE,QAAI,kBAAkB,WAAW,GAAG;AAClC,aAAO;AAAA,IACT,WAAW,WAAW,WAAW,GAAG;AAClC,aAAO;AAAA,IACT;AACA,kBAAc,cAAc,WAAW;AAAA,EACzC;AACA,SAAO;AACT;AACA,SAAS,WAAW;AAClB,MAAI,OAAO,QAAQ,eAAe,CAAC,IAAI,SAAU,QAAO;AACxD,SAAO,IAAI,SAAS,2BAA2B,MAAM;AACvD;AACA,SAAS,sBAAsB,MAAM;AACnC,SAAO,CAAC,QAAQ,QAAQ,WAAW,EAAE,SAAS,YAAY,IAAI,CAAC;AACjE;AACA,SAAS,iBAAiB,SAAS;AACjC,SAAO,UAAU,OAAO,EAAE,iBAAiB,OAAO;AACpD;AACA,SAAS,cAAc,SAAS;AAC9B,MAAI,UAAU,OAAO,GAAG;AACtB,WAAO;AAAA,MACL,YAAY,QAAQ;AAAA,MACpB,WAAW,QAAQ;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AAAA,IACL,YAAY,QAAQ;AAAA,IACpB,WAAW,QAAQ;AAAA,EACrB;AACF;AACA,SAAS,cAAc,MAAM;AAC3B,MAAI,YAAY,IAAI,MAAM,QAAQ;AAChC,WAAO;AAAA,EACT;AACA,QAAM;AAAA;AAAA,IAEN,KAAK;AAAA,IAEL,KAAK;AAAA,IAEL,aAAa,IAAI,KAAK,KAAK;AAAA,IAE3B,mBAAmB,IAAI;AAAA;AACvB,SAAO,aAAa,MAAM,IAAI,OAAO,OAAO;AAC9C;AACA,SAAS,2BAA2B,MAAM;AACxC,QAAM,aAAa,cAAc,IAAI;AACrC,MAAI,sBAAsB,UAAU,GAAG;AACrC,WAAO,KAAK,gBAAgB,KAAK,cAAc,OAAO,KAAK;AAAA,EAC7D;AACA,MAAI,cAAc,UAAU,KAAK,kBAAkB,UAAU,GAAG;AAC9D,WAAO;AAAA,EACT;AACA,SAAO,2BAA2B,UAAU;AAC9C;AACA,SAAS,qBAAqB,MAAM,MAAM,iBAAiB;AACzD,MAAI;AACJ,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AACA,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AACA,QAAM,qBAAqB,2BAA2B,IAAI;AAC1D,QAAM,SAAS,yBAAyB,uBAAuB,KAAK,kBAAkB,OAAO,SAAS,qBAAqB;AAC3H,QAAM,MAAM,UAAU,kBAAkB;AACxC,MAAI,QAAQ;AACV,UAAM,eAAe,gBAAgB,GAAG;AACxC,WAAO,KAAK,OAAO,KAAK,IAAI,kBAAkB,CAAC,GAAG,kBAAkB,kBAAkB,IAAI,qBAAqB,CAAC,GAAG,gBAAgB,kBAAkB,qBAAqB,YAAY,IAAI,CAAC,CAAC;AAAA,EAC9L;AACA,SAAO,KAAK,OAAO,oBAAoB,qBAAqB,oBAAoB,CAAC,GAAG,eAAe,CAAC;AACtG;AACA,SAAS,gBAAgB,KAAK;AAC5B,SAAO,IAAI,UAAU,OAAO,eAAe,IAAI,MAAM,IAAI,IAAI,eAAe;AAC9E;AAvJA;AAAA;AAAA;AAAA;;;ACIO,SAAS,QAAQ,GAAGiB,MAAK;AAC5B,MAAI,eAAe,CAAC,GAAG;AACnB,QAAI;AAAA,EACR;AACA,MAAI,YAAY,aAAa,CAAC;AAC9B,MAAIA,SAAQ,MAAM,IAAI,KAAK,IAAIA,MAAK,KAAK,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC;AAE9D,MAAI,WAAW;AACX,QAAI,SAAS,OAAO,IAAIA,IAAG,GAAG,EAAE,IAAI;AAAA,EACxC;AAEA,MAAI,KAAK,IAAI,IAAIA,IAAG,IAAI,MAAU;AAC9B,WAAO;AAAA,EACX;AAEA,MAAIA,SAAQ,KAAK;AAIb,SAAK,IAAI,IAAK,IAAIA,OAAOA,OAAM,IAAIA,QAAO,WAAW,OAAOA,IAAG,CAAC;AAAA,EACpE,OACK;AAGD,QAAK,IAAIA,OAAO,WAAW,OAAOA,IAAG,CAAC;AAAA,EAC1C;AACA,SAAO;AACX;AAKO,SAAS,QAAQ,KAAK;AACzB,SAAO,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,GAAG,CAAC;AACvC;AAMO,SAAS,eAAe,GAAG;AAC9B,SAAO,OAAO,MAAM,YAAY,EAAE,QAAQ,GAAG,MAAM,MAAM,WAAW,CAAC,MAAM;AAC/E;AAKO,SAAS,aAAa,GAAG;AAC5B,SAAO,OAAO,MAAM,YAAY,EAAE,QAAQ,GAAG,MAAM;AACvD;AAKO,SAAS,WAAWC,IAAG;AAC1B,EAAAA,KAAI,WAAWA,EAAC;AAChB,MAAI,MAAMA,EAAC,KAAKA,KAAI,KAAKA,KAAI,GAAG;AAC5B,IAAAA,KAAI;AAAA,EACR;AACA,SAAOA;AACX;AAKO,SAAS,oBAAoB,GAAG;AACnC,MAAI,KAAK,GAAG;AACR,WAAO,GAAG,OAAO,OAAO,CAAC,IAAI,KAAK,GAAG;AAAA,EACzC;AACA,SAAO;AACX;AAKO,SAAS,KAAKC,IAAG;AACpB,SAAOA,GAAE,WAAW,IAAI,MAAMA,KAAI,OAAOA,EAAC;AAC9C;AAjFA;AAAA;AAAA;AAAA;;;ACSO,SAAS,SAAS,GAAG,GAAGC,IAAG;AAC9B,SAAO;AAAA,IACH,GAAG,QAAQ,GAAG,GAAG,IAAI;AAAA,IACrB,GAAG,QAAQ,GAAG,GAAG,IAAI;AAAA,IACrB,GAAG,QAAQA,IAAG,GAAG,IAAI;AAAA,EACzB;AACJ;AAMO,SAAS,SAAS,GAAG,GAAGA,IAAG;AAC9B,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,EAAAA,KAAI,QAAQA,IAAG,GAAG;AAClB,MAAIC,OAAM,KAAK,IAAI,GAAG,GAAGD,EAAC;AAC1B,MAAIE,OAAM,KAAK,IAAI,GAAG,GAAGF,EAAC;AAC1B,MAAIG,KAAI;AACR,MAAIC,KAAI;AACR,MAAIC,MAAKJ,OAAMC,QAAO;AACtB,MAAID,SAAQC,MAAK;AACb,IAAAE,KAAI;AACJ,IAAAD,KAAI;AAAA,EACR,OACK;AACD,QAAIG,KAAIL,OAAMC;AACd,IAAAE,KAAIC,KAAI,MAAMC,MAAK,IAAIL,OAAMC,QAAOI,MAAKL,OAAMC;AAC/C,YAAQD,MAAK;AAAA,MACT,KAAK;AACD,QAAAE,MAAK,IAAIH,MAAKM,MAAK,IAAIN,KAAI,IAAI;AAC/B;AAAA,MACJ,KAAK;AACD,QAAAG,MAAKH,KAAI,KAAKM,KAAI;AAClB;AAAA,MACJ,KAAKN;AACD,QAAAG,MAAK,IAAI,KAAKG,KAAI;AAClB;AAAA,MACJ;AACI;AAAA,IACR;AACA,IAAAH,MAAK;AAAA,EACT;AACA,SAAO,EAAE,GAAGA,IAAG,GAAGC,IAAG,GAAGC,GAAE;AAC9B;AACA,SAAS,QAAQE,IAAG,GAAG,GAAG;AACtB,MAAI,IAAI,GAAG;AACP,SAAK;AAAA,EACT;AACA,MAAI,IAAI,GAAG;AACP,SAAK;AAAA,EACT;AACA,MAAI,IAAI,IAAI,GAAG;AACX,WAAOA,MAAK,IAAIA,OAAM,IAAI;AAAA,EAC9B;AACA,MAAI,IAAI,IAAI,GAAG;AACX,WAAO;AAAA,EACX;AACA,MAAI,IAAI,IAAI,GAAG;AACX,WAAOA,MAAK,IAAIA,OAAM,IAAI,IAAI,KAAK;AAAA,EACvC;AACA,SAAOA;AACX;AAOO,SAAS,SAASJ,IAAGC,IAAGC,IAAG;AAC9B,MAAI;AACJ,MAAI;AACJ,MAAIL;AACJ,EAAAG,KAAI,QAAQA,IAAG,GAAG;AAClB,EAAAC,KAAI,QAAQA,IAAG,GAAG;AAClB,EAAAC,KAAI,QAAQA,IAAG,GAAG;AAClB,MAAID,OAAM,GAAG;AAET,QAAIC;AACJ,IAAAL,KAAIK;AACJ,QAAIA;AAAA,EACR,OACK;AACD,QAAI,IAAIA,KAAI,MAAMA,MAAK,IAAID,MAAKC,KAAID,KAAIC,KAAID;AAC5C,QAAIG,KAAI,IAAIF,KAAI;AAChB,QAAI,QAAQE,IAAG,GAAGJ,KAAI,IAAI,CAAC;AAC3B,QAAI,QAAQI,IAAG,GAAGJ,EAAC;AACnB,IAAAH,KAAI,QAAQO,IAAG,GAAGJ,KAAI,IAAI,CAAC;AAAA,EAC/B;AACA,SAAO,EAAE,GAAG,IAAI,KAAK,GAAG,IAAI,KAAK,GAAGH,KAAI,IAAI;AAChD;AAOO,SAAS,SAAS,GAAG,GAAGA,IAAG;AAC9B,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,EAAAA,KAAI,QAAQA,IAAG,GAAG;AAClB,MAAIC,OAAM,KAAK,IAAI,GAAG,GAAGD,EAAC;AAC1B,MAAIE,OAAM,KAAK,IAAI,GAAG,GAAGF,EAAC;AAC1B,MAAIG,KAAI;AACR,MAAIK,KAAIP;AACR,MAAIK,KAAIL,OAAMC;AACd,MAAIE,KAAIH,SAAQ,IAAI,IAAIK,KAAIL;AAC5B,MAAIA,SAAQC,MAAK;AACb,IAAAC,KAAI;AAAA,EACR,OACK;AACD,YAAQF,MAAK;AAAA,MACT,KAAK;AACD,QAAAE,MAAK,IAAIH,MAAKM,MAAK,IAAIN,KAAI,IAAI;AAC/B;AAAA,MACJ,KAAK;AACD,QAAAG,MAAKH,KAAI,KAAKM,KAAI;AAClB;AAAA,MACJ,KAAKN;AACD,QAAAG,MAAK,IAAI,KAAKG,KAAI;AAClB;AAAA,MACJ;AACI;AAAA,IACR;AACA,IAAAH,MAAK;AAAA,EACT;AACA,SAAO,EAAE,GAAGA,IAAG,GAAGC,IAAG,GAAGI,GAAE;AAC9B;AAOO,SAAS,SAASL,IAAGC,IAAGI,IAAG;AAC9B,EAAAL,KAAI,QAAQA,IAAG,GAAG,IAAI;AACtB,EAAAC,KAAI,QAAQA,IAAG,GAAG;AAClB,EAAAI,KAAI,QAAQA,IAAG,GAAG;AAClB,MAAI,IAAI,KAAK,MAAML,EAAC;AACpB,MAAIM,KAAIN,KAAI;AACZ,MAAII,KAAIC,MAAK,IAAIJ;AACjB,MAAI,IAAII,MAAK,IAAIC,KAAIL;AACrB,MAAI,IAAII,MAAK,KAAK,IAAIC,MAAKL;AAC3B,MAAI,MAAM,IAAI;AACd,MAAI,IAAI,CAACI,IAAG,GAAGD,IAAGA,IAAG,GAAGC,EAAC,EAAE,GAAG;AAC9B,MAAI,IAAI,CAAC,GAAGA,IAAGA,IAAG,GAAGD,IAAGA,EAAC,EAAE,GAAG;AAC9B,MAAIP,KAAI,CAACO,IAAGA,IAAG,GAAGC,IAAGA,IAAG,CAAC,EAAE,GAAG;AAC9B,SAAO,EAAE,GAAG,IAAI,KAAK,GAAG,IAAI,KAAK,GAAGR,KAAI,IAAI;AAChD;AAOO,SAAS,SAAS,GAAG,GAAGA,IAAG,YAAY;AAC1C,MAAIU,OAAM;AAAA,IACN,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAAA,IAC/B,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAAA,IAC/B,KAAK,KAAK,MAAMV,EAAC,EAAE,SAAS,EAAE,CAAC;AAAA,EACnC;AAEA,MAAI,cACAU,KAAI,CAAC,EAAE,WAAWA,KAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAClCA,KAAI,CAAC,EAAE,WAAWA,KAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAClCA,KAAI,CAAC,EAAE,WAAWA,KAAI,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG;AACrC,WAAOA,KAAI,CAAC,EAAE,OAAO,CAAC,IAAIA,KAAI,CAAC,EAAE,OAAO,CAAC,IAAIA,KAAI,CAAC,EAAE,OAAO,CAAC;AAAA,EAChE;AACA,SAAOA,KAAI,KAAK,EAAE;AACtB;AAQO,SAAS,UAAU,GAAG,GAAGV,IAAGW,IAAG,YAAY;AAC9C,MAAID,OAAM;AAAA,IACN,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAAA,IAC/B,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAAA,IAC/B,KAAK,KAAK,MAAMV,EAAC,EAAE,SAAS,EAAE,CAAC;AAAA,IAC/B,KAAK,oBAAoBW,EAAC,CAAC;AAAA,EAC/B;AAEA,MAAI,cACAD,KAAI,CAAC,EAAE,WAAWA,KAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAClCA,KAAI,CAAC,EAAE,WAAWA,KAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAClCA,KAAI,CAAC,EAAE,WAAWA,KAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAClCA,KAAI,CAAC,EAAE,WAAWA,KAAI,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG;AACrC,WAAOA,KAAI,CAAC,EAAE,OAAO,CAAC,IAAIA,KAAI,CAAC,EAAE,OAAO,CAAC,IAAIA,KAAI,CAAC,EAAE,OAAO,CAAC,IAAIA,KAAI,CAAC,EAAE,OAAO,CAAC;AAAA,EACnF;AACA,SAAOA,KAAI,KAAK,EAAE;AACtB;AAKO,SAAS,cAAc,GAAG,GAAGV,IAAGW,IAAG;AACtC,MAAID,OAAM;AAAA,IACN,KAAK,oBAAoBC,EAAC,CAAC;AAAA,IAC3B,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAAA,IAC/B,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAAA,IAC/B,KAAK,KAAK,MAAMX,EAAC,EAAE,SAAS,EAAE,CAAC;AAAA,EACnC;AACA,SAAOU,KAAI,KAAK,EAAE;AACtB;AAEO,SAAS,oBAAoBJ,IAAG;AACnC,SAAO,KAAK,MAAM,WAAWA,EAAC,IAAI,GAAG,EAAE,SAAS,EAAE;AACtD;AAEO,SAAS,oBAAoBH,IAAG;AACnC,SAAO,gBAAgBA,EAAC,IAAI;AAChC;AAEO,SAAS,gBAAgB,KAAK;AACjC,SAAO,SAAS,KAAK,EAAE;AAC3B;AACO,SAAS,oBAAoB,OAAO;AACvC,SAAO;AAAA,IACH,GAAG,SAAS;AAAA,IACZ,IAAI,QAAQ,UAAW;AAAA,IACvB,GAAG,QAAQ;AAAA,EACf;AACJ;AA1OA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAIW;AAJX;AAAA;AAIO,IAAI,QAAQ;AAAA,MACf,WAAW;AAAA,MACX,cAAc;AAAA,MACd,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,WAAW;AAAA,MACX,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,SAAS;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe;AAAA,MACf,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,MACV,WAAW;AAAA,MACX,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,eAAe;AAAA,MACf,eAAe;AAAA,MACf,eAAe;AAAA,MACf,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,aAAa;AAAA,MACb,aAAa;AAAA,MACb,SAAS;AAAA,MACT,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,aAAa;AAAA,MACb,MAAM;AAAA,MACN,UAAU;AAAA,MACV,SAAS;AAAA,MACT,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,OAAO;AAAA,MACP,eAAe;AAAA,MACf,UAAU;AAAA,MACV,WAAW;AAAA,MACX,cAAc;AAAA,MACd,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,sBAAsB;AAAA,MACtB,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,aAAa;AAAA,MACb,eAAe;AAAA,MACf,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,MAAM;AAAA,MACN,WAAW;AAAA,MACX,OAAO;AAAA,MACP,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,kBAAkB;AAAA,MAClB,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,aAAa;AAAA,MACb,MAAM;AAAA,MACN,SAAS;AAAA,MACT,OAAO;AAAA,MACP,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,eAAe;AAAA,MACf,WAAW;AAAA,MACX,eAAe;AAAA,MACf,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,eAAe;AAAA,MACf,KAAK;AAAA,MACL,WAAW;AAAA,MACX,WAAW;AAAA,MACX,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,MACX,MAAM;AAAA,MACN,aAAa;AAAA,MACb,WAAW;AAAA,MACX,KAAK;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,aAAa;AAAA,IACjB;AAAA;AAAA;;;ACnIO,SAAS,WAAW,OAAO;AAC9B,MAAI,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE;AAC7B,MAAIS,KAAI;AACR,MAAIC,KAAI;AACR,MAAIC,KAAI;AACR,MAAIC,KAAI;AACR,MAAI,KAAK;AACT,MAAIC,UAAS;AACb,MAAI,OAAO,UAAU,UAAU;AAC3B,YAAQ,oBAAoB,KAAK;AAAA,EACrC;AACA,MAAI,OAAO,UAAU,UAAU;AAC3B,QAAI,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,GAAG;AAC/E,YAAM,SAAS,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AACxC,WAAK;AACL,MAAAA,UAAS,OAAO,MAAM,CAAC,EAAE,OAAO,EAAE,MAAM,MAAM,SAAS;AAAA,IAC3D,WACS,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,GAAG;AACpF,MAAAH,KAAI,oBAAoB,MAAM,CAAC;AAC/B,MAAAC,KAAI,oBAAoB,MAAM,CAAC;AAC/B,YAAM,SAAS,MAAM,GAAGD,IAAGC,EAAC;AAC5B,WAAK;AACL,MAAAE,UAAS;AAAA,IACb,WACS,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,GAAG;AACpF,MAAAH,KAAI,oBAAoB,MAAM,CAAC;AAC/B,MAAAE,KAAI,oBAAoB,MAAM,CAAC;AAC/B,YAAM,SAAS,MAAM,GAAGF,IAAGE,EAAC;AAC5B,WAAK;AACL,MAAAC,UAAS;AAAA,IACb;AACA,QAAI,OAAO,UAAU,eAAe,KAAK,OAAO,GAAG,GAAG;AAClD,MAAAJ,KAAI,MAAM;AAAA,IACd;AAAA,EACJ;AACA,EAAAA,KAAI,WAAWA,EAAC;AAChB,SAAO;AAAA,IACH;AAAA,IACA,QAAQ,MAAM,UAAUI;AAAA,IACxB,GAAG,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,IACnC,GAAG,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,IACnC,GAAG,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,IACnC,GAAGJ;AAAA,EACP;AACJ;AA6BO,SAAS,oBAAoB,OAAO;AACvC,UAAQ,MAAM,KAAK,EAAE,YAAY;AACjC,MAAI,MAAM,WAAW,GAAG;AACpB,WAAO;AAAA,EACX;AACA,MAAI,QAAQ;AACZ,MAAI,MAAM,KAAK,GAAG;AACd,YAAQ,MAAM,KAAK;AACnB,YAAQ;AAAA,EACZ,WACS,UAAU,eAAe;AAC9B,WAAO,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,QAAQ,OAAO;AAAA,EACpD;AAKA,MAAI,QAAQ,SAAS,IAAI,KAAK,KAAK;AACnC,MAAI,OAAO;AACP,WAAO,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,EACnD;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,EAChE;AACA,UAAQ,SAAS,IAAI,KAAK,KAAK;AAC/B,MAAI,OAAO;AACP,WAAO,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,EACnD;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,EAChE;AACA,UAAQ,SAAS,IAAI,KAAK,KAAK;AAC/B,MAAI,OAAO;AACP,WAAO,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,EACnD;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,EAChE;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO;AAAA,MACH,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,oBAAoB,MAAM,CAAC,CAAC;AAAA,MAC/B,QAAQ,QAAQ,SAAS;AAAA,IAC7B;AAAA,EACJ;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO;AAAA,MACH,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,QAAQ,QAAQ,SAAS;AAAA,IAC7B;AAAA,EACJ;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO;AAAA,MACH,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,oBAAoB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MAC1C,QAAQ,QAAQ,SAAS;AAAA,IAC7B;AAAA,EACJ;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO;AAAA,MACH,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,QAAQ,QAAQ,SAAS;AAAA,IAC7B;AAAA,EACJ;AACA,SAAO;AACX;AAKO,SAAS,eAAe,OAAO;AAClC,SAAO,QAAQ,SAAS,SAAS,KAAK,OAAO,KAAK,CAAC,CAAC;AACxD;AAtLA,IAoEI,aAEA,YAEA,UAIA,mBACA,mBACA;AA9EJ;AAAA;AACA;AACA;AACA;AAiEA,IAAI,cAAc;AAElB,IAAI,aAAa;AAEjB,IAAI,WAAW,MAAM,OAAO,YAAY,OAAO,EAAE,OAAO,aAAa,GAAG;AAIxE,IAAI,oBAAoB,cAAc,OAAO,UAAU,YAAY,EAAE,OAAO,UAAU,YAAY,EAAE,OAAO,UAAU,WAAW;AAChI,IAAI,oBAAoB,cAAc,OAAO,UAAU,YAAY,EAAE,OAAO,UAAU,YAAY,EAAE,OAAO,UAAU,YAAY,EAAE,OAAO,UAAU,WAAW;AAC/J,IAAI,WAAW;AAAA,MACX,UAAU,IAAI,OAAO,QAAQ;AAAA,MAC7B,KAAK,IAAI,OAAO,QAAQ,iBAAiB;AAAA,MACzC,MAAM,IAAI,OAAO,SAAS,iBAAiB;AAAA,MAC3C,KAAK,IAAI,OAAO,QAAQ,iBAAiB;AAAA,MACzC,MAAM,IAAI,OAAO,SAAS,iBAAiB;AAAA,MAC3C,KAAK,IAAI,OAAO,QAAQ,iBAAiB;AAAA,MACzC,MAAM,IAAI,OAAO,SAAS,iBAAiB;AAAA,MAC3C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACV;AAAA;AAAA;;;AC6ZO,SAAS,UAAU,OAAO,MAAM;AACnC,MAAI,UAAU,QAAQ;AAAE,YAAQ;AAAA,EAAI;AACpC,MAAI,SAAS,QAAQ;AAAE,WAAO,CAAC;AAAA,EAAG;AAClC,SAAO,IAAI,UAAU,OAAO,IAAI;AACpC;AA3fA,IAII;AAJJ;AAAA;AAAA;AACA;AACA;AACA;AACA,IAAI;AAAA,IAA2B,WAAY;AACvC,eAASK,WAAU,OAAO,MAAM;AAC5B,YAAI,UAAU,QAAQ;AAAE,kBAAQ;AAAA,QAAI;AACpC,YAAI,SAAS,QAAQ;AAAE,iBAAO,CAAC;AAAA,QAAG;AAClC,YAAI;AAEJ,YAAI,iBAAiBA,YAAW;AAE5B,iBAAO;AAAA,QACX;AACA,YAAI,OAAO,UAAU,UAAU;AAC3B,kBAAQ,oBAAoB,KAAK;AAAA,QACrC;AACA,aAAK,gBAAgB;AACrB,YAAI,MAAM,WAAW,KAAK;AAC1B,aAAK,gBAAgB;AACrB,aAAK,IAAI,IAAI;AACb,aAAK,IAAI,IAAI;AACb,aAAK,IAAI,IAAI;AACb,aAAK,IAAI,IAAI;AACb,aAAK,SAAS,KAAK,MAAM,MAAM,KAAK,CAAC,IAAI;AACzC,aAAK,UAAU,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,KAAK,IAAI;AACtE,aAAK,eAAe,KAAK;AAKzB,YAAI,KAAK,IAAI,GAAG;AACZ,eAAK,IAAI,KAAK,MAAM,KAAK,CAAC;AAAA,QAC9B;AACA,YAAI,KAAK,IAAI,GAAG;AACZ,eAAK,IAAI,KAAK,MAAM,KAAK,CAAC;AAAA,QAC9B;AACA,YAAI,KAAK,IAAI,GAAG;AACZ,eAAK,IAAI,KAAK,MAAM,KAAK,CAAC;AAAA,QAC9B;AACA,aAAK,UAAU,IAAI;AAAA,MACvB;AACA,MAAAA,WAAU,UAAU,SAAS,WAAY;AACrC,eAAO,KAAK,cAAc,IAAI;AAAA,MAClC;AACA,MAAAA,WAAU,UAAU,UAAU,WAAY;AACtC,eAAO,CAAC,KAAK,OAAO;AAAA,MACxB;AAIA,MAAAA,WAAU,UAAU,gBAAgB,WAAY;AAE5C,YAAI,MAAM,KAAK,MAAM;AACrB,gBAAQ,IAAI,IAAI,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI,OAAO;AAAA,MACvD;AAIA,MAAAA,WAAU,UAAU,eAAe,WAAY;AAE3C,YAAI,MAAM,KAAK,MAAM;AACrB,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI,QAAQ,IAAI,IAAI;AACpB,YAAI,QAAQ,IAAI,IAAI;AACpB,YAAI,QAAQ,IAAI,IAAI;AACpB,YAAI,SAAS,SAAS;AAClB,cAAI,QAAQ;AAAA,QAChB,OACK;AAED,cAAI,KAAK,KAAK,QAAQ,SAAS,OAAO,GAAG;AAAA,QAC7C;AACA,YAAI,SAAS,SAAS;AAClB,cAAI,QAAQ;AAAA,QAChB,OACK;AAED,cAAI,KAAK,KAAK,QAAQ,SAAS,OAAO,GAAG;AAAA,QAC7C;AACA,YAAI,SAAS,SAAS;AAClB,cAAI,QAAQ;AAAA,QAChB,OACK;AAED,cAAI,KAAK,KAAK,QAAQ,SAAS,OAAO,GAAG;AAAA,QAC7C;AACA,eAAO,SAAS,IAAI,SAAS,IAAI,SAAS;AAAA,MAC9C;AAIA,MAAAA,WAAU,UAAU,WAAW,WAAY;AACvC,eAAO,KAAK;AAAA,MAChB;AAMA,MAAAA,WAAU,UAAU,WAAW,SAAU,OAAO;AAC5C,aAAK,IAAI,WAAW,KAAK;AACzB,aAAK,SAAS,KAAK,MAAM,MAAM,KAAK,CAAC,IAAI;AACzC,eAAO;AAAA,MACX;AAIA,MAAAA,WAAU,UAAU,eAAe,WAAY;AAC3C,YAAIC,KAAI,KAAK,MAAM,EAAE;AACrB,eAAOA,OAAM;AAAA,MACjB;AAIA,MAAAD,WAAU,UAAU,QAAQ,WAAY;AACpC,YAAI,MAAM,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACzC,eAAO,EAAE,GAAG,IAAI,IAAI,KAAK,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,KAAK,EAAE;AAAA,MAC3D;AAKA,MAAAA,WAAU,UAAU,cAAc,WAAY;AAC1C,YAAI,MAAM,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACzC,YAAIE,KAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC9B,YAAID,KAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC9B,YAAIE,KAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC9B,eAAO,KAAK,MAAM,IAAI,OAAO,OAAOD,IAAG,IAAI,EAAE,OAAOD,IAAG,KAAK,EAAE,OAAOE,IAAG,IAAI,IAAI,QAAQ,OAAOD,IAAG,IAAI,EAAE,OAAOD,IAAG,KAAK,EAAE,OAAOE,IAAG,KAAK,EAAE,OAAO,KAAK,QAAQ,GAAG;AAAA,MACrK;AAIA,MAAAH,WAAU,UAAU,QAAQ,WAAY;AACpC,YAAI,MAAM,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACzC,eAAO,EAAE,GAAG,IAAI,IAAI,KAAK,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,KAAK,EAAE;AAAA,MAC3D;AAKA,MAAAA,WAAU,UAAU,cAAc,WAAY;AAC1C,YAAI,MAAM,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACzC,YAAIE,KAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC9B,YAAID,KAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC9B,YAAIG,KAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC9B,eAAO,KAAK,MAAM,IAAI,OAAO,OAAOF,IAAG,IAAI,EAAE,OAAOD,IAAG,KAAK,EAAE,OAAOG,IAAG,IAAI,IAAI,QAAQ,OAAOF,IAAG,IAAI,EAAE,OAAOD,IAAG,KAAK,EAAE,OAAOG,IAAG,KAAK,EAAE,OAAO,KAAK,QAAQ,GAAG;AAAA,MACrK;AAKA,MAAAJ,WAAU,UAAU,QAAQ,SAAU,YAAY;AAC9C,YAAI,eAAe,QAAQ;AAAE,uBAAa;AAAA,QAAO;AACjD,eAAO,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,UAAU;AAAA,MACtD;AAKA,MAAAA,WAAU,UAAU,cAAc,SAAU,YAAY;AACpD,YAAI,eAAe,QAAQ;AAAE,uBAAa;AAAA,QAAO;AACjD,eAAO,MAAM,KAAK,MAAM,UAAU;AAAA,MACtC;AAKA,MAAAA,WAAU,UAAU,SAAS,SAAU,YAAY;AAC/C,YAAI,eAAe,QAAQ;AAAE,uBAAa;AAAA,QAAO;AACjD,eAAO,UAAU,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,UAAU;AAAA,MAC/D;AAKA,MAAAA,WAAU,UAAU,eAAe,SAAU,YAAY;AACrD,YAAI,eAAe,QAAQ;AAAE,uBAAa;AAAA,QAAO;AACjD,eAAO,MAAM,KAAK,OAAO,UAAU;AAAA,MACvC;AAKA,MAAAA,WAAU,UAAU,mBAAmB,SAAU,gBAAgB;AAC7D,YAAI,mBAAmB,QAAQ;AAAE,2BAAiB;AAAA,QAAO;AACzD,eAAO,KAAK,MAAM,IAAI,KAAK,YAAY,cAAc,IAAI,KAAK,aAAa,cAAc;AAAA,MAC7F;AAIA,MAAAA,WAAU,UAAU,QAAQ,WAAY;AACpC,eAAO;AAAA,UACH,GAAG,KAAK,MAAM,KAAK,CAAC;AAAA,UACpB,GAAG,KAAK,MAAM,KAAK,CAAC;AAAA,UACpB,GAAG,KAAK,MAAM,KAAK,CAAC;AAAA,UACpB,GAAG,KAAK;AAAA,QACZ;AAAA,MACJ;AAKA,MAAAA,WAAU,UAAU,cAAc,WAAY;AAC1C,YAAI,IAAI,KAAK,MAAM,KAAK,CAAC;AACzB,YAAI,IAAI,KAAK,MAAM,KAAK,CAAC;AACzB,YAAIK,KAAI,KAAK,MAAM,KAAK,CAAC;AACzB,eAAO,KAAK,MAAM,IAAI,OAAO,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,OAAOA,IAAG,GAAG,IAAI,QAAQ,OAAO,GAAG,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,OAAOA,IAAG,IAAI,EAAE,OAAO,KAAK,QAAQ,GAAG;AAAA,MACjK;AAIA,MAAAL,WAAU,UAAU,kBAAkB,WAAY;AAC9C,YAAI,MAAM,SAAUM,IAAG;AAAE,iBAAO,GAAG,OAAO,KAAK,MAAM,QAAQA,IAAG,GAAG,IAAI,GAAG,GAAG,GAAG;AAAA,QAAG;AACnF,eAAO;AAAA,UACH,GAAG,IAAI,KAAK,CAAC;AAAA,UACb,GAAG,IAAI,KAAK,CAAC;AAAA,UACb,GAAG,IAAI,KAAK,CAAC;AAAA,UACb,GAAG,KAAK;AAAA,QACZ;AAAA,MACJ;AAIA,MAAAN,WAAU,UAAU,wBAAwB,WAAY;AACpD,YAAI,MAAM,SAAUM,IAAG;AAAE,iBAAO,KAAK,MAAM,QAAQA,IAAG,GAAG,IAAI,GAAG;AAAA,QAAG;AACnE,eAAO,KAAK,MAAM,IACZ,OAAO,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,GAAG,IAAI,IACrF,QAAQ,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,EAAE,OAAO,KAAK,QAAQ,GAAG;AAAA,MAC1H;AAIA,MAAAN,WAAU,UAAU,SAAS,WAAY;AACrC,YAAI,KAAK,MAAM,GAAG;AACd,iBAAO;AAAA,QACX;AACA,YAAI,KAAK,IAAI,GAAG;AACZ,iBAAO;AAAA,QACX;AACA,YAAIO,OAAM,MAAM,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK;AACtD,iBAAS,KAAK,GAAG,KAAK,OAAO,QAAQ,KAAK,GAAG,KAAK,GAAG,QAAQ,MAAM;AAC/D,cAAI,KAAK,GAAG,EAAE,GAAG,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC;AAC1C,cAAIA,SAAQ,OAAO;AACf,mBAAO;AAAA,UACX;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AACA,MAAAP,WAAU,UAAU,WAAW,SAAUQ,SAAQ;AAC7C,YAAI,YAAY,QAAQA,OAAM;AAC9B,QAAAA,UAASA,YAAW,QAAQA,YAAW,SAASA,UAAS,KAAK;AAC9D,YAAI,kBAAkB;AACtB,YAAI,WAAW,KAAK,IAAI,KAAK,KAAK,KAAK;AACvC,YAAI,mBAAmB,CAAC,aAAa,aAAaA,QAAO,WAAW,KAAK,KAAKA,YAAW;AACzF,YAAI,kBAAkB;AAGlB,cAAIA,YAAW,UAAU,KAAK,MAAM,GAAG;AACnC,mBAAO,KAAK,OAAO;AAAA,UACvB;AACA,iBAAO,KAAK,YAAY;AAAA,QAC5B;AACA,YAAIA,YAAW,OAAO;AAClB,4BAAkB,KAAK,YAAY;AAAA,QACvC;AACA,YAAIA,YAAW,QAAQ;AACnB,4BAAkB,KAAK,sBAAsB;AAAA,QACjD;AACA,YAAIA,YAAW,SAASA,YAAW,QAAQ;AACvC,4BAAkB,KAAK,YAAY;AAAA,QACvC;AACA,YAAIA,YAAW,QAAQ;AACnB,4BAAkB,KAAK,YAAY,IAAI;AAAA,QAC3C;AACA,YAAIA,YAAW,QAAQ;AACnB,4BAAkB,KAAK,aAAa,IAAI;AAAA,QAC5C;AACA,YAAIA,YAAW,QAAQ;AACnB,4BAAkB,KAAK,aAAa;AAAA,QACxC;AACA,YAAIA,YAAW,QAAQ;AACnB,4BAAkB,KAAK,OAAO;AAAA,QAClC;AACA,YAAIA,YAAW,OAAO;AAClB,4BAAkB,KAAK,YAAY;AAAA,QACvC;AACA,YAAIA,YAAW,OAAO;AAClB,4BAAkB,KAAK,YAAY;AAAA,QACvC;AACA,eAAO,mBAAmB,KAAK,YAAY;AAAA,MAC/C;AACA,MAAAR,WAAU,UAAU,WAAW,WAAY;AACvC,gBAAQ,KAAK,MAAM,KAAK,CAAC,KAAK,OAAO,KAAK,MAAM,KAAK,CAAC,KAAK,KAAK,KAAK,MAAM,KAAK,CAAC;AAAA,MACrF;AACA,MAAAA,WAAU,UAAU,QAAQ,WAAY;AACpC,eAAO,IAAIA,WAAU,KAAK,SAAS,CAAC;AAAA,MACxC;AAKA,MAAAA,WAAU,UAAU,UAAU,SAAU,QAAQ;AAC5C,YAAI,WAAW,QAAQ;AAAE,mBAAS;AAAA,QAAI;AACtC,YAAI,MAAM,KAAK,MAAM;AACrB,YAAI,KAAK,SAAS;AAClB,YAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,eAAO,IAAIA,WAAU,GAAG;AAAA,MAC5B;AAKA,MAAAA,WAAU,UAAU,WAAW,SAAU,QAAQ;AAC7C,YAAI,WAAW,QAAQ;AAAE,mBAAS;AAAA,QAAI;AACtC,YAAI,MAAM,KAAK,MAAM;AACrB,YAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,MAAM,EAAE,SAAS,IAAI,CAAC,CAAC;AAC5E,YAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,MAAM,EAAE,SAAS,IAAI,CAAC,CAAC;AAC5E,YAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,MAAM,EAAE,SAAS,IAAI,CAAC,CAAC;AAC5E,eAAO,IAAIA,WAAU,GAAG;AAAA,MAC5B;AAMA,MAAAA,WAAU,UAAU,SAAS,SAAU,QAAQ;AAC3C,YAAI,WAAW,QAAQ;AAAE,mBAAS;AAAA,QAAI;AACtC,YAAI,MAAM,KAAK,MAAM;AACrB,YAAI,KAAK,SAAS;AAClB,YAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,eAAO,IAAIA,WAAU,GAAG;AAAA,MAC5B;AAMA,MAAAA,WAAU,UAAU,OAAO,SAAU,QAAQ;AACzC,YAAI,WAAW,QAAQ;AAAE,mBAAS;AAAA,QAAI;AACtC,eAAO,KAAK,IAAI,SAAS,MAAM;AAAA,MACnC;AAMA,MAAAA,WAAU,UAAU,QAAQ,SAAU,QAAQ;AAC1C,YAAI,WAAW,QAAQ;AAAE,mBAAS;AAAA,QAAI;AACtC,eAAO,KAAK,IAAI,SAAS,MAAM;AAAA,MACnC;AAMA,MAAAA,WAAU,UAAU,aAAa,SAAU,QAAQ;AAC/C,YAAI,WAAW,QAAQ;AAAE,mBAAS;AAAA,QAAI;AACtC,YAAI,MAAM,KAAK,MAAM;AACrB,YAAI,KAAK,SAAS;AAClB,YAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,eAAO,IAAIA,WAAU,GAAG;AAAA,MAC5B;AAKA,MAAAA,WAAU,UAAU,WAAW,SAAU,QAAQ;AAC7C,YAAI,WAAW,QAAQ;AAAE,mBAAS;AAAA,QAAI;AACtC,YAAI,MAAM,KAAK,MAAM;AACrB,YAAI,KAAK,SAAS;AAClB,YAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,eAAO,IAAIA,WAAU,GAAG;AAAA,MAC5B;AAKA,MAAAA,WAAU,UAAU,YAAY,WAAY;AACxC,eAAO,KAAK,WAAW,GAAG;AAAA,MAC9B;AAKA,MAAAA,WAAU,UAAU,OAAO,SAAU,QAAQ;AACzC,YAAI,MAAM,KAAK,MAAM;AACrB,YAAI,OAAO,IAAI,IAAI,UAAU;AAC7B,YAAI,IAAI,MAAM,IAAI,MAAM,MAAM;AAC9B,eAAO,IAAIA,WAAU,GAAG;AAAA,MAC5B;AAKA,MAAAA,WAAU,UAAU,MAAM,SAAU,OAAO,QAAQ;AAC/C,YAAI,WAAW,QAAQ;AAAE,mBAAS;AAAA,QAAI;AACtC,YAAI,OAAO,KAAK,MAAM;AACtB,YAAI,OAAO,IAAIA,WAAU,KAAK,EAAE,MAAM;AACtC,YAAIS,KAAI,SAAS;AACjB,YAAI,OAAO;AAAA,UACP,IAAI,KAAK,IAAI,KAAK,KAAKA,KAAI,KAAK;AAAA,UAChC,IAAI,KAAK,IAAI,KAAK,KAAKA,KAAI,KAAK;AAAA,UAChC,IAAI,KAAK,IAAI,KAAK,KAAKA,KAAI,KAAK;AAAA,UAChC,IAAI,KAAK,IAAI,KAAK,KAAKA,KAAI,KAAK;AAAA,QACpC;AACA,eAAO,IAAIT,WAAU,IAAI;AAAA,MAC7B;AACA,MAAAA,WAAU,UAAU,YAAY,SAAU,SAAS,QAAQ;AACvD,YAAI,YAAY,QAAQ;AAAE,oBAAU;AAAA,QAAG;AACvC,YAAI,WAAW,QAAQ;AAAE,mBAAS;AAAA,QAAI;AACtC,YAAI,MAAM,KAAK,MAAM;AACrB,YAAI,OAAO,MAAM;AACjB,YAAI,MAAM,CAAC,IAAI;AACf,aAAK,IAAI,KAAK,IAAI,KAAM,OAAO,WAAY,KAAK,OAAO,KAAK,EAAE,WAAU;AACpE,cAAI,KAAK,IAAI,IAAI,QAAQ;AACzB,cAAI,KAAK,IAAIA,WAAU,GAAG,CAAC;AAAA,QAC/B;AACA,eAAO;AAAA,MACX;AAIA,MAAAA,WAAU,UAAU,aAAa,WAAY;AACzC,YAAI,MAAM,KAAK,MAAM;AACrB,YAAI,KAAK,IAAI,IAAI,OAAO;AACxB,eAAO,IAAIA,WAAU,GAAG;AAAA,MAC5B;AACA,MAAAA,WAAU,UAAU,gBAAgB,SAAU,SAAS;AACnD,YAAI,YAAY,QAAQ;AAAE,oBAAU;AAAA,QAAG;AACvC,YAAI,MAAM,KAAK,MAAM;AACrB,YAAIE,KAAI,IAAI;AACZ,YAAID,KAAI,IAAI;AACZ,YAAIE,KAAI,IAAI;AACZ,YAAI,MAAM,CAAC;AACX,YAAI,eAAe,IAAI;AACvB,eAAO,WAAW;AACd,cAAI,KAAK,IAAIH,WAAU,EAAE,GAAGE,IAAG,GAAGD,IAAG,GAAGE,GAAE,CAAC,CAAC;AAC5C,UAAAA,MAAKA,KAAI,gBAAgB;AAAA,QAC7B;AACA,eAAO;AAAA,MACX;AACA,MAAAH,WAAU,UAAU,kBAAkB,WAAY;AAC9C,YAAI,MAAM,KAAK,MAAM;AACrB,YAAIE,KAAI,IAAI;AACZ,eAAO;AAAA,UACH;AAAA,UACA,IAAIF,WAAU,EAAE,IAAIE,KAAI,MAAM,KAAK,GAAG,IAAI,GAAG,GAAG,IAAI,EAAE,CAAC;AAAA,UACvD,IAAIF,WAAU,EAAE,IAAIE,KAAI,OAAO,KAAK,GAAG,IAAI,GAAG,GAAG,IAAI,EAAE,CAAC;AAAA,QAC5D;AAAA,MACJ;AAIA,MAAAF,WAAU,UAAU,eAAe,SAAU,YAAY;AACrD,YAAI,KAAK,KAAK,MAAM;AACpB,YAAI,KAAK,IAAIA,WAAU,UAAU,EAAE,MAAM;AACzC,YAAI,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,GAAG;AAClC,eAAO,IAAIA,WAAU;AAAA,UACjB,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,IAAI,GAAG,MAAM;AAAA,UAC9C,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,IAAI,GAAG,MAAM;AAAA,UAC9C,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,IAAI,GAAG,MAAM;AAAA,UAC9C,GAAG;AAAA,QACP,CAAC;AAAA,MACL;AAIA,MAAAA,WAAU,UAAU,QAAQ,WAAY;AACpC,eAAO,KAAK,OAAO,CAAC;AAAA,MACxB;AAIA,MAAAA,WAAU,UAAU,SAAS,WAAY;AACrC,eAAO,KAAK,OAAO,CAAC;AAAA,MACxB;AAKA,MAAAA,WAAU,UAAU,SAAS,SAAU,GAAG;AACtC,YAAI,MAAM,KAAK,MAAM;AACrB,YAAIE,KAAI,IAAI;AACZ,YAAI,SAAS,CAAC,IAAI;AAClB,YAAI,YAAY,MAAM;AACtB,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,iBAAO,KAAK,IAAIF,WAAU,EAAE,IAAIE,KAAI,IAAI,aAAa,KAAK,GAAG,IAAI,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC;AAAA,QACnF;AACA,eAAO;AAAA,MACX;AAIA,MAAAF,WAAU,UAAU,SAAS,SAAU,OAAO;AAC1C,eAAO,KAAK,YAAY,MAAM,IAAIA,WAAU,KAAK,EAAE,YAAY;AAAA,MACnE;AACA,aAAOA;AAAA,IACX,EAAE;AAAA;AAAA;;;AC3eK,SAAS,YAAY,QAAQ,QAAQ;AACxC,MAAI,KAAK,IAAI,UAAU,MAAM;AAC7B,MAAI,KAAK,IAAI,UAAU,MAAM;AAC7B,UAAS,KAAK,IAAI,GAAG,aAAa,GAAG,GAAG,aAAa,CAAC,IAAI,SACrD,KAAK,IAAI,GAAG,aAAa,GAAG,GAAG,aAAa,CAAC,IAAI;AAC1D;AAcO,SAAS,WAAW,QAAQ,QAAQ,OAAO;AAC9C,MAAI,IAAI;AACR,MAAI,UAAU,QAAQ;AAAE,YAAQ,EAAE,OAAO,MAAM,MAAM,QAAQ;AAAA,EAAG;AAChE,MAAI,mBAAmB,YAAY,QAAQ,MAAM;AACjD,YAAU,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,KAAK,UAAU,KAAK,MAAM,UAAU,QAAQ,OAAO,SAAS,KAAK,UAAU;AAAA,IAC/H,KAAK;AAAA,IACL,KAAK;AACD,aAAO,oBAAoB;AAAA,IAC/B,KAAK;AACD,aAAO,oBAAoB;AAAA,IAC/B,KAAK;AACD,aAAO,oBAAoB;AAAA,IAC/B;AACI,aAAO;AAAA,EACf;AACJ;AAkBO,SAAS,aAAa,WAAW,WAAW,MAAM;AACrD,MAAI,SAAS,QAAQ;AAAE,WAAO,EAAE,uBAAuB,OAAO,OAAO,MAAM,MAAM,QAAQ;AAAA,EAAG;AAC5F,MAAI,YAAY;AAChB,MAAI,YAAY;AAChB,MAAI,wBAAwB,KAAK,uBAAuB,QAAQ,KAAK,OAAOU,QAAO,KAAK;AACxF,WAAS,KAAK,GAAG,cAAc,WAAW,KAAK,YAAY,QAAQ,MAAM;AACrE,QAAI,QAAQ,YAAY,EAAE;AAC1B,QAAI,QAAQ,YAAY,WAAW,KAAK;AACxC,QAAI,QAAQ,WAAW;AACnB,kBAAY;AACZ,kBAAY,IAAI,UAAU,KAAK;AAAA,IACnC;AAAA,EACJ;AACA,MAAI,WAAW,WAAW,WAAW,EAAE,OAAc,MAAMA,MAAK,CAAC,KAAK,CAAC,uBAAuB;AAC1F,WAAO;AAAA,EACX;AACA,OAAK,wBAAwB;AAC7B,SAAO,aAAa,WAAW,CAAC,QAAQ,MAAM,GAAG,IAAI;AACzD;AA/EA;AAAA;AAAA;AAAA;AAAA;;;ACKO,SAAS,WAAW,YAAY,aAAa;AAChD,MAAI,QAAQ,IAAI,UAAU,UAAU;AACpC,MAAI,aAAa,MAAM,cAAc,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AACvE,MAAI,mBAAmB;AACvB,MAAI,eAAe,MAAM,eAAe,uBAAuB;AAC/D,MAAI,aAAa;AACb,QAAIC,KAAI,IAAI,UAAU,WAAW;AACjC,uBAAmB,MAAM,cAAcA,GAAE,GAAGA,GAAE,GAAGA,GAAE,GAAGA,GAAE,CAAC;AAAA,EAC7D;AACA,SAAO,8CAA8C,OAAO,cAAc,gBAAgB,EAAE,OAAO,YAAY,eAAe,EAAE,OAAO,kBAAkB,GAAG;AAChK;AAfA;AAAA;AAAA;AACA;AAAA;AAAA;;;ACKO,SAAS,UAAU,OAAO,MAAM;AACnC,MAAI,WAAW;AAAA,IACX,GAAG,oBAAoB,MAAM,CAAC;AAAA,IAC9B,GAAG,oBAAoB,MAAM,CAAC;AAAA,IAC9B,GAAG,oBAAoB,MAAM,CAAC;AAAA,EAClC;AACA,MAAI,MAAM,MAAM,QAAW;AACvB,aAAS,IAAI,OAAO,MAAM,CAAC;AAAA,EAC/B;AACA,SAAO,IAAI,UAAU,UAAU,IAAI;AACvC;AAEO,SAAS,eAAe;AAC3B,SAAO,IAAI,UAAU;AAAA,IACjB,GAAG,KAAK,OAAO;AAAA,IACf,GAAG,KAAK,OAAO;AAAA,IACf,GAAG,KAAK,OAAO;AAAA,EACnB,CAAC;AACL;AAxBA;AAAA;AAAA;AACA;AAAA;AAAA;;;ACGO,SAAS,OAAO,SAAS;AAC5B,MAAI,YAAY,QAAQ;AAAE,cAAU,CAAC;AAAA,EAAG;AAExC,MAAI,QAAQ,UAAU,UAClB,QAAQ,UAAU,MAAM;AACxB,QAAI,cAAc,QAAQ;AAC1B,QAAI,SAAS,CAAC;AACd,YAAQ,QAAQ;AAChB,WAAO,cAAc,OAAO,QAAQ;AAIhC,cAAQ,QAAQ;AAChB,UAAI,QAAQ,MAAM;AACd,gBAAQ,QAAQ;AAAA,MACpB;AACA,aAAO,KAAK,OAAO,OAAO,CAAC;AAAA,IAC/B;AACA,YAAQ,QAAQ;AAChB,WAAO;AAAA,EACX;AAEA,MAAIC,KAAI,QAAQ,QAAQ,KAAK,QAAQ,IAAI;AAEzC,MAAIC,KAAI,eAAeD,IAAG,OAAO;AAEjC,MAAIE,KAAI,eAAeF,IAAGC,IAAG,OAAO;AACpC,MAAI,MAAM,EAAE,GAAGD,IAAG,GAAGC,IAAG,GAAGC,GAAE;AAC7B,MAAI,QAAQ,UAAU,QAAW;AAC7B,QAAI,IAAI,QAAQ;AAAA,EACpB;AAEA,SAAO,IAAI,UAAU,GAAG;AAC5B;AACA,SAAS,QAAQ,KAAK,MAAM;AACxB,MAAI,WAAW,YAAY,GAAG;AAC9B,MAAI,MAAM,aAAa,UAAU,IAAI;AAGrC,MAAI,MAAM,GAAG;AACT,UAAM,MAAM;AAAA,EAChB;AACA,SAAO;AACX;AACA,SAAS,eAAe,KAAK,SAAS;AAClC,MAAI,QAAQ,QAAQ,cAAc;AAC9B,WAAO;AAAA,EACX;AACA,MAAI,QAAQ,eAAe,UAAU;AACjC,WAAO,aAAa,CAAC,GAAG,GAAG,GAAG,QAAQ,IAAI;AAAA,EAC9C;AACA,MAAI,kBAAkB,aAAa,GAAG,EAAE;AACxC,MAAI,OAAO,gBAAgB,CAAC;AAC5B,MAAI,OAAO,gBAAgB,CAAC;AAC5B,UAAQ,QAAQ,YAAY;AAAA,IACxB,KAAK;AACD,aAAO;AACP;AAAA,IACJ,KAAK;AACD,aAAO,OAAO;AACd;AAAA,IACJ,KAAK;AACD,aAAO;AACP;AAAA,IACJ;AACI;AAAA,EACR;AACA,SAAO,aAAa,CAAC,MAAM,IAAI,GAAG,QAAQ,IAAI;AAClD;AACA,SAAS,eAAe,GAAGC,IAAG,SAAS;AACnC,MAAI,OAAO,qBAAqB,GAAGA,EAAC;AACpC,MAAI,OAAO;AACX,UAAQ,QAAQ,YAAY;AAAA,IACxB,KAAK;AACD,aAAO,OAAO;AACd;AAAA,IACJ,KAAK;AACD,cAAQ,OAAO,QAAQ;AACvB;AAAA,IACJ,KAAK;AACD,aAAO;AACP,aAAO;AACP;AAAA,IACJ;AACI;AAAA,EACR;AACA,SAAO,aAAa,CAAC,MAAM,IAAI,GAAG,QAAQ,IAAI;AAClD;AACA,SAAS,qBAAqB,GAAGA,IAAG;AAChC,MAAI,cAAc,aAAa,CAAC,EAAE;AAClC,WAAS,IAAI,GAAG,IAAI,YAAY,SAAS,GAAG,KAAK;AAC7C,QAAI,KAAK,YAAY,CAAC,EAAE,CAAC;AACzB,QAAI,KAAK,YAAY,CAAC,EAAE,CAAC;AACzB,QAAI,KAAK,YAAY,IAAI,CAAC,EAAE,CAAC;AAC7B,QAAI,KAAK,YAAY,IAAI,CAAC,EAAE,CAAC;AAC7B,QAAIA,MAAK,MAAMA,MAAK,IAAI;AACpB,UAAIC,MAAK,KAAK,OAAO,KAAK;AAC1B,UAAIC,KAAI,KAAKD,KAAI;AACjB,aAAOA,KAAID,KAAIE;AAAA,IACnB;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,YAAY,YAAY;AAC7B,MAAI,MAAM,SAAS,YAAY,EAAE;AACjC,MAAI,CAAC,OAAO,MAAM,GAAG,KAAK,MAAM,OAAO,MAAM,GAAG;AAC5C,WAAO,CAAC,KAAK,GAAG;AAAA,EACpB;AACA,MAAI,OAAO,eAAe,UAAU;AAChC,QAAI,aAAa,OAAO,KAAK,SAAU,GAAG;AAAE,aAAO,EAAE,SAAS;AAAA,IAAY,CAAC;AAC3E,QAAI,YAAY;AACZ,UAAI,QAAQ,YAAY,UAAU;AAClC,UAAI,MAAM,UAAU;AAChB,eAAO,MAAM;AAAA,MACjB;AAAA,IACJ;AACA,QAAI,SAAS,IAAI,UAAU,UAAU;AACrC,QAAI,OAAO,SAAS;AAChB,UAAI,MAAM,OAAO,MAAM,EAAE;AACzB,aAAO,CAAC,KAAK,GAAG;AAAA,IACpB;AAAA,EACJ;AACA,SAAO,CAAC,GAAG,GAAG;AAClB;AACA,SAAS,aAAa,KAAK;AAEvB,MAAI,OAAO,OAAO,OAAO,KAAK;AAC1B,WAAO;AAAA,EACX;AACA,WAAS,KAAK,GAAG,WAAW,QAAQ,KAAK,SAAS,QAAQ,MAAM;AAC5D,QAAI,QAAQ,SAAS,EAAE;AACvB,QAAI,QAAQ,YAAY,KAAK;AAC7B,QAAI,MAAM,YAAY,OAAO,MAAM,SAAS,CAAC,KAAK,OAAO,MAAM,SAAS,CAAC,GAAG;AACxE,aAAO;AAAA,IACX;AAAA,EACJ;AACA,QAAM,MAAM,iBAAiB;AACjC;AACA,SAAS,aAAaC,QAAO,MAAM;AAC/B,MAAI,SAAS,QAAW;AACpB,WAAO,KAAK,MAAMA,OAAM,CAAC,IAAI,KAAK,OAAO,KAAKA,OAAM,CAAC,IAAI,IAAIA,OAAM,CAAC,EAAE;AAAA,EAC1E;AAEA,MAAIC,OAAMD,OAAM,CAAC,KAAK;AACtB,MAAIE,OAAMF,OAAM,CAAC,KAAK;AACtB,UAAQ,OAAO,OAAO,SAAS;AAC/B,MAAI,MAAM,OAAO;AACjB,SAAO,KAAK,MAAME,OAAM,OAAOD,OAAMC,KAAI;AAC7C;AACA,SAAS,YAAY,OAAO;AACxB,MAAI,OAAO,MAAM,YAAY,CAAC,EAAE,CAAC;AACjC,MAAI,OAAO,MAAM,YAAY,MAAM,YAAY,SAAS,CAAC,EAAE,CAAC;AAC5D,MAAI,OAAO,MAAM,YAAY,MAAM,YAAY,SAAS,CAAC,EAAE,CAAC;AAC5D,MAAI,OAAO,MAAM,YAAY,CAAC,EAAE,CAAC;AACjC,SAAO;AAAA,IACH,MAAM,MAAM;AAAA,IACZ,UAAU,MAAM;AAAA,IAChB,aAAa,MAAM;AAAA,IACnB,iBAAiB,CAAC,MAAM,IAAI;AAAA,IAC5B,iBAAiB,CAAC,MAAM,IAAI;AAAA,EAChC;AACJ;AArKA,IAyKW;AAzKX;AAAA;AAGA;AAsKO,IAAI,SAAS;AAAA,MAChB;AAAA,QACI,MAAM;AAAA,QACN,UAAU;AAAA,QACV,aAAa;AAAA,UACT,CAAC,GAAG,CAAC;AAAA,UACL,CAAC,KAAK,CAAC;AAAA,QACX;AAAA,MACJ;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,UAAU,CAAC,KAAK,EAAE;AAAA,QAClB,aAAa;AAAA,UACT,CAAC,IAAI,GAAG;AAAA,UACR,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,KAAK,EAAE;AAAA,QACZ;AAAA,MACJ;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,UAAU,CAAC,IAAI,EAAE;AAAA,QACjB,aAAa;AAAA,UACT,CAAC,IAAI,GAAG;AAAA,UACR,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,KAAK,EAAE;AAAA,QACZ;AAAA,MACJ;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,UAAU,CAAC,IAAI,EAAE;AAAA,QACjB,aAAa;AAAA,UACT,CAAC,IAAI,GAAG;AAAA,UACR,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,KAAK,EAAE;AAAA,QACZ;AAAA,MACJ;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,UAAU,CAAC,IAAI,GAAG;AAAA,QAClB,aAAa;AAAA,UACT,CAAC,IAAI,GAAG;AAAA,UACR,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,KAAK,EAAE;AAAA,QACZ;AAAA,MACJ;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,UAAU,CAAC,KAAK,GAAG;AAAA,QACnB,aAAa;AAAA,UACT,CAAC,IAAI,GAAG;AAAA,UACR,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,KAAK,EAAE;AAAA,QACZ;AAAA,MACJ;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,UAAU,CAAC,KAAK,GAAG;AAAA,QACnB,aAAa;AAAA,UACT,CAAC,IAAI,GAAG;AAAA,UACR,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,KAAK,EAAE;AAAA,QACZ;AAAA,MACJ;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,UAAU,CAAC,KAAK,GAAG;AAAA,QACnB,aAAa;AAAA,UACT,CAAC,IAAI,GAAG;AAAA,UACR,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,IAAI,EAAE;AAAA,UACP,CAAC,KAAK,EAAE;AAAA,QACZ;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA;;;ACrRA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWO;AAXP;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,IAAO,qBAAQ;AAAA;AAAA;;;ACXf;AAAA;AAAA;AAAA;AAIA,SAAS,QAAQ,OAAO,QAAQ;AAC5B,MAAI,UAAU,QAAQ;AAClB,WAAO;AAAA,EACX;AACA,MAAI,UAAU,KAAK,KAAK,UAAU,MAAM,GAAG;AACvC,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,eAAe,WAAW,YAAY;AAC3C,MAAI,UAAU,WAAW,WAAW,QAAQ;AACxC,WAAO;AAAA,EACX;AACA,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,QAAI,CAAC,QAAQ,UAAU,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG;AACvC,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,WAAW,UAAUC,UAAS;AACnC,MAAIA,aAAY,QAAQ;AAAE,IAAAA,WAAU;AAAA,EAAgB;AACpD,MAAI,QAAQ;AACZ,WAAS,WAAW;AAChB,QAAI,UAAU,CAAC;AACf,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,cAAQ,EAAE,IAAI,UAAU,EAAE;AAAA,IAC9B;AACA,QAAI,SAAS,MAAM,aAAa,QAAQA,SAAQ,SAAS,MAAM,QAAQ,GAAG;AACtE,aAAO,MAAM;AAAA,IACjB;AACA,QAAI,aAAa,SAAS,MAAM,MAAM,OAAO;AAC7C,YAAQ;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,MACV,UAAU;AAAA,IACd;AACA,WAAO;AAAA,EACX;AACA,WAAS,QAAQ,SAAS,QAAQ;AAC9B,YAAQ;AAAA,EACZ;AACA,SAAO;AACX;AAhDA,IAAI;AAAJ;AAAA;AAAA,IAAI,YAAY,OAAO,SACnB,SAAS,SAAS,OAAO;AACrB,aAAO,OAAO,UAAU,YAAY,UAAU;AAAA,IAClD;AAAA;AAAA;", "names": ["o", "n", "u", "e", "t", "r", "i", "a", "s", "f", "d", "o", "s", "a", "e", "f", "t", "h", "n", "u", "d", "r", "i", "c", "l", "m", "M", "Y", "p", "v", "D", "w", "e", "t", "r", "s", "a", "f", "i", "n", "s", "a", "o", "e", "e", "t", "e", "t", "e", "t", "e", "i", "convertFieldsError", "errors", "length", "fields", "for<PERSON>ach", "error", "field", "push", "format", "template", "args", "i", "len", "apply", "str", "replace", "formatRegExp", "x", "String", "Number", "JSON", "stringify", "_", "isNativeStringType", "type", "isEmptyValue", "value", "undefined", "Array", "isArray", "asyncParallelArray", "arr", "func", "callback", "results", "total", "arr<PERSON><PERSON><PERSON>", "count", "a", "asyncSerialArray", "index", "next", "original", "flatten<PERSON>bj<PERSON>rr", "obj<PERSON>rr", "ret", "Object", "keys", "k", "asyncMap", "option", "source", "first", "pending", "Promise", "resolve", "reject", "AsyncValidationError", "flattenArr", "e", "firstFields", "obj<PERSON><PERSON><PERSON><PERSON><PERSON>", "obj<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "indexOf", "isErrorObj", "obj", "message", "getValue", "path", "v", "complementError", "rule", "oe", "fieldValue", "fullFields", "fullField", "deepMerge", "target", "s", "hasOwnProperty", "newMessages", "required", "whitespace", "date", "parse", "invalid", "types", "string", "method", "array", "object", "number", "integer", "regexp", "email", "url", "hex", "min", "max", "range", "pattern", "mismatch", "clone", "cloned", "warning", "urlReg", "ENUM", "enumerable", "boolean", "floatFn", "any", "messages", "<PERSON><PERSON><PERSON>", "process", "env", "window", "document", "console", "warn", "ASYNC_VALIDATOR_NO_WARNING", "every", "Error", "options", "test", "word", "b", "includeBoundaries", "v4", "v6seg", "v6", "trim", "v46Exact", "RegExp", "v4exact", "v6exact", "ip", "exact", "protocol", "auth", "ipv4", "ipv6", "host", "domain", "tld", "port", "regex", "parseInt", "getTime", "getMonth", "getYear", "isNaN", "match", "getUrlRegex", "custom", "ruleType", "spRegexp", "val", "num", "join", "lastIndex", "_pattern", "enumRule", "validate", "rules", "dateObject", "Date", "float", "enumValidator", "descriptor", "_messages", "defaultMessages", "define", "name", "item", "source_", "o", "oc", "complete", "add", "concat", "series", "z", "r", "transform", "validator", "getValidationMethod", "getType", "errorFields", "data", "doIt", "deep", "defaultField", "addFullField", "schema", "cb", "errorList", "suppressWarning", "filledErrors", "map", "fieldsSchema", "paredFieldsSchema", "fieldSchema", "fieldSchemaList", "bind", "errs", "finalErrors", "res", "asyncValidator", "suppressValidatorError", "setTimeout", "then", "validators", "messageIndex", "splice", "register", "x", "v", "x", "a", "b", "max", "offset", "d", "alignment", "v", "placements", "sides", "side", "placement", "overflow", "top", "bottom", "left", "right", "width", "height", "y", "min", "max", "a", "c", "b", "max", "min", "h", "s", "l", "d", "p", "v", "f", "hex", "a", "a", "s", "v", "l", "format", "TinyColor", "s", "h", "v", "l", "b", "x", "hex", "format", "p", "size", "s", "h", "s", "v", "S", "m", "b", "range", "max", "min", "isEqual"]}