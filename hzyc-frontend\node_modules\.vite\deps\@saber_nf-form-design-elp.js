import {
  require_vue
} from "./chunk-CZCGTF2U.js";
import "./chunk-7PYUCYS5.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/@saber/nf-form-design-elp/lib/nf-form-design-elp.js
var require_nf_form_design_elp = __commonJS({
  "node_modules/@saber/nf-form-design-elp/lib/nf-form-design-elp.js"(exports, module) {
    (function(o, r) {
      typeof exports == "object" && typeof module < "u" ? module.exports = r(require_vue()) : typeof define == "function" && define.amd ? define(["vue"], r) : (o = typeof globalThis < "u" ? globalThis : o || self, o.NfFormDesignElp = r(o.Vue));
    })(exports, function(require$$0) {
      "use strict";
      var __vite_style__ = document.createElement("style");
      __vite_style__.textContent = `.nfd-field-item .title[data-v-e34a6329]{padding:8px 12px 0;font-size:13px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.nfd-field-item .field-group[data-v-e34a6329]{position:relative;overflow:hidden;padding:8px 10px 10px;margin:0 -5px}.nfd-field-item .field-group .el-col[data-v-e34a6329]{padding-left:5px;padding-right:5px}.nfd-field-item .field-group .field-item[data-v-e34a6329]{font-size:12px;padding:5px 0;margin-bottom:5px;border:1px solid #f4f6fc;display:flex;align-items:center;cursor:move;background:#f4f6fc;color:#333;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.nfd-field-item .field-group .field-item[data-v-e34a6329]:hover{color:#409eff;outline:1px dashed #409eff}.nfd-field-item .field-group .field-item .icon[data-v-e34a6329],.nfd-field-item .field-group .field-item .el-icon[data-v-e34a6329]{margin-left:8px;font-size:14px;display:inline-block;vertical-align:middle}.nfd-field-item .field-group .field-item .field-title[data-v-e34a6329]{margin-left:8px}.nfd-field[data-v-4a08c8fe]{height:calc(100% - 5px);padding-top:5px;background:#fff}.nfd-field .el-tabs{width:100%;height:100%}.nfd-field .el-tabs .el-tabs__header{margin:0;height:45px;background:#fff;z-index:1000;position:relative;display:block;top:0}.nfd-field .el-tabs .el-tabs__content{height:calc(100% - 45px);overflow-y:auto;margin-top:0}.nfd-field .el-tabs .el-tabs__item{padding:0 10px}.nfd-field .el-tabs--top .el-tabs__item.is-top:nth-child(2){padding-left:10px}.nfd-field .el-tabs--top .el-tabs__item.is-top:last-child{padding-right:10px}.nfd-field .el-alert{padding:5px 0 0;width:90%;margin:0 5%}.nfd-field .el-col{margin-bottom:0!important}.nfd-toolbar[data-v-0a1fc9bb]{width:100%;height:45px!important;display:flex;justify-content:space-between;align-items:center;border-bottom:solid 2px #e4e7ed}.nfd-toolbar .el-button.is-text[data-v-0a1fc9bb]{padding:0}.widget-item__item[data-v-c899f5cc]{display:flex;flex-direction:column;width:100%}.widget-item__item--tip[data-v-c899f5cc]{font-size:13px;color:var(--el-color-info);margin-bottom:-18px;height:24px;line-height:22px}.widget-item__item--tip--primary[data-v-c899f5cc]{color:var(--el-color-primary)}.widget-item__item--tip--danger[data-v-c899f5cc]{color:var(--el-color-danger)}.widget-item__item--tip--warning[data-v-c899f5cc]{color:var(--el-color-warning)}.nfd-widget-button .el-button[data-v-06127b38]{position:absolute;z-index:1002;height:26px;width:26px;padding:5px}.nfd-widget-button .delete[data-v-06127b38]{right:var(--0864edb2);bottom:var(--2d743d56)}.nfd-widget-button .copy[data-v-06127b38]{right:calc(var(--0864edb2) + 30px);bottom:var(--2d743d56)}.nfd-widget-button .clear[data-v-06127b38]{right:calc(var(--0864edb2) + 60px);bottom:var(--2d743d56)}.nfd-widget-button .td[data-v-06127b38]{background-color:#fff;position:absolute;right:calc(var(--0864edb2));bottom:var(--2d743d56)}.nfd-widget-button .table-item-delete[data-v-06127b38]{padding:2px 3px;height:20px;width:20px;font-size:12px}.nfd-table{position:relative;padding:2px;float:left;width:100%;box-sizing:border-box}.nfd-table__main{width:100%}.nfd-table__title{padding:10px 5px;font-weight:700}.nfd-table__cell{border:1px dashed #ccc;padding:10px;display:table-cell;height:55px;min-width:65px;cursor:pointer;position:relative}.nfd-table__cell.active-item{outline:2px solid #409eff!important}.nfd-table__widget{outline:1px dashed #ccc;padding:5px;min-height:40px;cursor:pointer;position:relative;display:flex;align-items:center}.nfd-table__widget--item+.nfd-table__widget--item{margin-left:5px}.nfd-table__widget--item{position:relative;flex:1}.nfd-table__widget--item:after{position:absolute;left:0;right:0;bottom:0;top:0;display:block;z-index:1000;content:""}.nfd-table__widget--item.active-item{outline:2px solid #409eff!important}.nfd-table__widget--item:hover{outline:2px solid #409eff!important;outline-offset:-1px;cursor:move}.nfd-config[data-v-63a1ac16]{height:calc(100% - 5px);padding-top:5px;background:#fff;--el-component-size: 28px}.nfd-config[data-v-63a1ac16] .el-tabs{width:100%;height:100%}.nfd-config[data-v-63a1ac16] .el-tabs .el-tabs__header{margin:0;height:45px;background:#fff;z-index:1000;position:relative;display:block;top:0}.nfd-config[data-v-63a1ac16] .el-tabs .el-tabs__content{height:calc(100% - 45px);overflow-y:auto;margin-top:0}.nfd-config[data-v-63a1ac16] .el-tabs .el-tabs__content .el-collapse-item__header{font-size:15px;font-weight:700}.nfd-config[data-v-63a1ac16] .el-tabs .el-tabs__content .el-collapse-item__header .el-icon{margin-right:5px}.nfd-config[data-v-63a1ac16] .el-tabs .el-tabs__content .el-collapse-item__title{display:flex;align-items:center}.nfd-config[data-v-63a1ac16] .el-tabs .el-tabs__content .el-collapse-item__content{padding-bottom:10px}.nfd-config[data-v-63a1ac16] .el-tabs .el-tabs__content .el-collapse-item__content .el-form-item{margin-bottom:2px}.nfd-config[data-v-63a1ac16] .el-tabs .el-tabs__content .el-collapse-item__content .el-form-item .el-form-item__label{font-weight:500}.nfd-config[data-v-63a1ac16] .el-tabs .el-tabs__content .el-collapse-item__content .el-form-item .el-input-number.is-controls-right .el-input-number__decrease,.nfd-config[data-v-63a1ac16] .el-tabs .el-tabs__content .el-collapse-item__content .el-form-item .el-input-number.is-controls-right .el-input-number__increase{--el-input-number-controls-height: 13px !important}.nfd-config[data-v-63a1ac16] .el-tabs .el-tabs__content .el-collapse-item__content .el-form-item .el-select__wrapper{min-height:var(--el-component-size);padding:2px 12px}.nfd-config[data-v-63a1ac16] .el-tabs .el-tabs__content .el-collapse-item__content .el-form--label-top .el-form-item{display:block}.nfd-config[data-v-63a1ac16] .el-tabs .el-tabs__content .el-collapse-item__content .el-divider--horizontal .el-divider__text{font-size:15px}.nfd-config[data-v-63a1ac16] .el-tabs .el-tabs__content .el-collapse-item__content .el-alert{--el-alert-padding: 2px 16px}.nfd-config[data-v-63a1ac16] .el-tabs .el-tabs__content ul{margin:0;padding:0;width:100%}.nfd-config[data-v-63a1ac16] .el-tabs .el-tabs__content ul li{display:flex;align-items:center;width:100%}.nfd-config[data-v-63a1ac16] .el-tabs .el-tabs__content ul li .ghost{list-style:none;font-size:0;height:35px}.nfd-split-panel{display:flex}.nfd-split-panel.layout-h{flex-direction:column}.nfd-split-panel.layout-v{flex-direction:row;width:100%}.nfd-split-panel>div{position:relative;z-index:1}.nfd-split-panel-resizer{display:block;position:relative;z-index:2}.layout-h>.nfd-split-panel-resizer{width:100%;height:10px;margin-top:-10px;cursor:row-resize}.layout-v>.nfd-split-panel-resizer{width:8px;height:100%;cursor:col-resize;position:relative;border-left:1px solid #e0e0e0;border-right:1px solid #e0e0e0}.layout-v>.nfd-split-panel-resizer:before{display:block;content:"";width:3px;height:40px;position:absolute;top:calc(50% - 45px);left:50%;margin-top:-20px;margin-left:-2.5px;border-left:1px solid #aaa;border-right:1px solid #aaa}.layout-v>.nfd-split-panel-resizer:hover:before{border-color:#999}.nfd-generate-drawer .el-drawer__header{margin-bottom:0}.nfd-generate-drawer .el-drawer__body{padding:0;overflow-y:auto}.nfd-generate-drawer .el-drawer__body .el-tabs__header{margin:0 10px 15px}.nfd-generate-drawer .el-drawer__body .el-tabs,.nfd-generate-drawer .el-drawer__body .el-tab-pane{height:100%}.nfd-generate-drawer__footer{padding:10px 16px;box-sizing:border-box;border-top:1px solid #f0f0f0;width:100%;position:absolute;left:0;bottom:0;background-color:#fff;display:flex}.nfd-generate-drawer .el-button{width:100%}.nf-form-design{height:100%;background:#fff;outline:1px solid #e4e7ed}.nf-form-design ::-webkit-scrollbar{width:0px!important;height:0px!important}.nf-form-design ::-webkit-scrollbar-track{-webkit-box-shadow:inset 0 0 6px rgba(0,0,0,0)!important;border-radius:10px!important;background-color:#0000!important}.nf-form-design ::-webkit-scrollbar-thumb{border-radius:10px!important;-webkit-box-shadow:inset 0 0 6px rgba(0,0,0,0)!important;background-color:#fff!important}.nf-form-design .widget{height:calc(100% - 45px);position:relative;top:0;left:0;right:0;bottom:0;padding:0}.nf-form-design .widget .el-form{height:100%}.nf-form-design .widget .el-form .el-row{height:100%;display:inherit!important}.nf-form-design .widget .el-form .el-row:before,.nf-form-design .widget .el-form .el-row:after{display:table!important;content:""!important}.nf-form-design .widget .el-form [class*=el-col-]{float:left!important}.nf-form-design .widget .el-form .el-col{margin-bottom:0!important}.nf-form-design .widget .widget-list{width:100%;height:100%;padding-bottom:50px;overflow:hidden;overflow-y:scroll}.nf-form-design .widget .widget-list .widget-item{min-height:30px;padding:5px 10px 18px;margin:0;position:relative}.nf-form-design .widget .widget-list .widget-dynamic{position:relative;padding:2px;float:left;width:100%;box-sizing:border-box}.nf-form-design .widget .widget-list .widget-dynamic__body{display:flex;padding:5px;width:100%;height:100%;overflow:scroll hidden;outline:1px dashed #ccc;outline-offset:-1px;min-height:110px;box-sizing:border-box}.nf-form-design .widget .widget-list .widget-dynamic__body::-webkit-scrollbar{width:6px!important;height:6px!important}.nf-form-design .widget .widget-list .widget-dynamic__body::-webkit-scrollbar-thumb{background-color:#e5e7eb!important}.nf-form-design .widget .widget-list .widget-dynamic__body .ghost{background:#fff;border-left:5px solid #409eff;box-sizing:border-box;font-size:0;content:"";overflow:hidden;padding:0!important;position:relative;outline:none 0;height:100%;min-height:90px;width:0!important;min-width:0!important;max-width:0!important;margin:1px 2px 0}.nf-form-design .widget .widget-list .widget-dynamic__item{position:relative;height:100%}.nf-form-design .widget .widget-list .widget-dynamic__item .nfd-dynamic{border:1px solid #ebeef5;background:#fff;font-size:14px;margin:1px}.nf-form-design .widget .widget-list .widget-dynamic__item .nfd-dynamic__header,.nf-form-design .widget .widget-list .widget-dynamic__item .nfd-dynamic__body{position:relative;width:100%;padding:12px 10px;min-width:0;box-sizing:border-box;vertical-align:middle;text-align:center;overflow:hidden;text-overflow:ellipsis;white-space:normal;word-break:break-all}.nf-form-design .widget .widget-list .widget-dynamic__item .nfd-dynamic__header{color:#909399;font-weight:600;border-bottom:1px solid #ebeef5}.nf-form-design .widget .widget-list .widget-group{position:relative;width:100%;float:left;padding:2px;box-sizing:border-box}.nf-form-design .widget .widget-list .widget-group__head{margin:10px}.nf-form-design .widget .widget-list .widget-group__body{min-height:150px;height:100%;outline:1px dashed #ccc;outline-offset:-1px;overflow:hidden;padding-bottom:15px;box-sizing:border-box}.nf-form-design .widget .widget-list .widget-group__body .ghost{background:#fff;border-left:5px solid #409eff;box-sizing:border-box;font-size:0;content:"";overflow:hidden;padding:0!important;position:relative;outline:none 0;height:100%;min-height:57px;width:0!important;min-width:0!important;margin:1px 2px 0}.nf-form-design .widget .widget-list .widget-group__item{padding:10px;margin:3px;position:relative;border-left:5px solid transparent;background:#fff}.nf-form-design .widget .ghost{background:#fff;border-top:5px solid #409eff;box-sizing:border-box;font-size:0;content:"";overflow:hidden;padding:0!important;position:relative;outline:none 0;height:0!important;width:100%;margin:2px 0}.nf-form-design .required .el-form-item__label:before{content:"*";color:#f56c6c;margin-right:4px}.nf-form-design .required .nfd-dynamic__header:before{content:"*";color:#f56c6c;margin-right:4px}.nf-form-design .required-title h3:before{content:"*";color:#f56c6c;margin-right:4px}.nf-form-design .drag:after{position:absolute;left:0;right:0;bottom:0;top:0;display:block;z-index:1001;content:""}.nf-form-design .hover:hover{background:#ecf8ff;cursor:move}.nf-form-design .hover-item:hover{outline:1px solid #409eff;outline-offset:-1px;cursor:move}.nf-form-design .active{border-left:3px solid #409eff;background:#ecf5ff}.nf-form-design .active-item{outline:1px solid #409eff;outline-offset:-1px}.nf-form-design .danger{color:#f56c6c}.nf-form-design .el-container,.nf-form-design .el-form,.nf-form-design .el-form .el-row{height:100%}.nf-form-design .editor-fullscreen{position:fixed!important;top:0;right:0;bottom:0;left:0;width:100%;height:100%!important;z-index:1003;padding:20px;box-sizing:border-box;background:#fff}.nf-form-design .editor-fullscreen svg{top:20px!important;left:25px!important}.nf-form-design .nf-date,.nf-form-design .nf-time,.nf-form-design .nf-map,.nf-form-design .nf-color,.nf-form-design .el-date-editor,.nf-form-design .el-cascader,.nf-form-design .el-select,.nf-form-design .el-select-v2,.nf-form-design .el-input-number:not(.el-slider .el-input-number),.nf-form-design .el-input-tag{width:100%}.nfd-drawer .el-drawer__body{padding:0 0 60px;overflow-y:auto}.nfd-drawer__footer{padding:10px 16px;box-sizing:border-box;border-top:1px solid #f0f0f0;width:100%;position:absolute;left:0;bottom:0;background-color:#fff;display:flex}.nfd-drawer__footer .el-button{width:50%}.dic[data-v-8312aa7f]{display:flex;margin-bottom:5px}.el-tag[data-v-6c6260d7]{vertical-align:top}.el-tag+.el-tag[data-v-6c6260d7]{margin-left:5px}.input-new-tag[data-v-6c6260d7]{width:90px;margin-left:5px;vertical-align:bottom}.color-picker[data-v-6c6260d7]{left:10px;vertical-align:top}.custom-tree-node[data-v-78d6bafb]{flex:1;display:flex;align-items:center;justify-content:space-between;font-size:14px;padding-right:8px}[data-v-4d83c9aa] h4,[data-v-58f6bf02] h4{margin:10px 0 0}
/*$vite$:1*/`, document.head.appendChild(__vite_style__);
      var commonjsGlobal = typeof globalThis < "u" ? globalThis : typeof window < "u" ? window : typeof global < "u" ? global : typeof self < "u" ? self : {};
      function getDefaultExportFromCjs(o) {
        return o && o.__esModule && Object.prototype.hasOwnProperty.call(o, "default") ? o.default : o;
      }
      function getAugmentedNamespace(o) {
        if (o.__esModule) return o;
        var r = o.default;
        if (typeof r == "function") {
          var t = function s() {
            return this instanceof s ? Reflect.construct(r, arguments, this.constructor) : r.apply(this, arguments);
          };
          t.prototype = r.prototype;
        } else t = {};
        return Object.defineProperty(t, "__esModule", { value: true }), Object.keys(o).forEach(function(s) {
          var n = Object.getOwnPropertyDescriptor(o, s);
          Object.defineProperty(t, s, n.get ? n : { enumerable: true, get: function() {
            return o[s];
          } });
        }), t;
      }
      var vuedraggable_umd = { exports: {} };
      function ownKeys(o, r) {
        var t = Object.keys(o);
        if (Object.getOwnPropertySymbols) {
          var s = Object.getOwnPropertySymbols(o);
          r && (s = s.filter(function(n) {
            return Object.getOwnPropertyDescriptor(o, n).enumerable;
          })), t.push.apply(t, s);
        }
        return t;
      }
      function _objectSpread2(o) {
        for (var r = 1; r < arguments.length; r++) {
          var t = arguments[r] != null ? arguments[r] : {};
          r % 2 ? ownKeys(Object(t), true).forEach(function(s) {
            _defineProperty(o, s, t[s]);
          }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(o, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(s) {
            Object.defineProperty(o, s, Object.getOwnPropertyDescriptor(t, s));
          });
        }
        return o;
      }
      function _typeof(o) {
        "@babel/helpers - typeof";
        return typeof Symbol == "function" && typeof Symbol.iterator == "symbol" ? _typeof = function(r) {
          return typeof r;
        } : _typeof = function(r) {
          return r && typeof Symbol == "function" && r.constructor === Symbol && r !== Symbol.prototype ? "symbol" : typeof r;
        }, _typeof(o);
      }
      function _defineProperty(o, r, t) {
        return r in o ? Object.defineProperty(o, r, { value: t, enumerable: true, configurable: true, writable: true }) : o[r] = t, o;
      }
      function _extends() {
        return _extends = Object.assign || function(o) {
          for (var r = 1; r < arguments.length; r++) {
            var t = arguments[r];
            for (var s in t) Object.prototype.hasOwnProperty.call(t, s) && (o[s] = t[s]);
          }
          return o;
        }, _extends.apply(this, arguments);
      }
      function _objectWithoutPropertiesLoose(o, r) {
        if (o == null) return {};
        var t = {}, s = Object.keys(o), n, d;
        for (d = 0; d < s.length; d++) n = s[d], !(r.indexOf(n) >= 0) && (t[n] = o[n]);
        return t;
      }
      function _objectWithoutProperties(o, r) {
        if (o == null) return {};
        var t = _objectWithoutPropertiesLoose(o, r), s, n;
        if (Object.getOwnPropertySymbols) {
          var d = Object.getOwnPropertySymbols(o);
          for (n = 0; n < d.length; n++) s = d[n], !(r.indexOf(s) >= 0) && Object.prototype.propertyIsEnumerable.call(o, s) && (t[s] = o[s]);
        }
        return t;
      }
      function _toConsumableArray(o) {
        return _arrayWithoutHoles(o) || _iterableToArray(o) || _unsupportedIterableToArray(o) || _nonIterableSpread();
      }
      function _arrayWithoutHoles(o) {
        if (Array.isArray(o)) return _arrayLikeToArray(o);
      }
      function _iterableToArray(o) {
        if (typeof Symbol < "u" && o[Symbol.iterator] != null || o["@@iterator"] != null) return Array.from(o);
      }
      function _unsupportedIterableToArray(o, r) {
        if (o) {
          if (typeof o == "string") return _arrayLikeToArray(o, r);
          var t = Object.prototype.toString.call(o).slice(8, -1);
          if (t === "Object" && o.constructor && (t = o.constructor.name), t === "Map" || t === "Set") return Array.from(o);
          if (t === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)) return _arrayLikeToArray(o, r);
        }
      }
      function _arrayLikeToArray(o, r) {
        (r == null || r > o.length) && (r = o.length);
        for (var t = 0, s = new Array(r); t < r; t++) s[t] = o[t];
        return s;
      }
      function _nonIterableSpread() {
        throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`);
      }
      var version$1 = "1.14.0";
      function userAgent(o) {
        if (typeof window < "u" && window.navigator) return !!navigator.userAgent.match(o);
      }
      var IE11OrLess = userAgent(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i), Edge = userAgent(/Edge/i), FireFox = userAgent(/firefox/i), Safari = userAgent(/safari/i) && !userAgent(/chrome/i) && !userAgent(/android/i), IOS = userAgent(/iP(ad|od|hone)/i), ChromeForAndroid = userAgent(/chrome/i) && userAgent(/android/i), captureMode = { capture: false, passive: false };
      function on(o, r, t) {
        o.addEventListener(r, t, !IE11OrLess && captureMode);
      }
      function off(o, r, t) {
        o.removeEventListener(r, t, !IE11OrLess && captureMode);
      }
      function matches(o, r) {
        if (r) {
          if (r[0] === ">" && (r = r.substring(1)), o) try {
            if (o.matches) return o.matches(r);
            if (o.msMatchesSelector) return o.msMatchesSelector(r);
            if (o.webkitMatchesSelector) return o.webkitMatchesSelector(r);
          } catch {
            return false;
          }
          return false;
        }
      }
      function getParentOrHost(o) {
        return o.host && o !== document && o.host.nodeType ? o.host : o.parentNode;
      }
      function closest(o, r, t, s) {
        if (o) {
          t = t || document;
          do {
            if (r != null && (r[0] === ">" ? o.parentNode === t && matches(o, r) : matches(o, r)) || s && o === t) return o;
            if (o === t) break;
          } while (o = getParentOrHost(o));
        }
        return null;
      }
      var R_SPACE = /\s+/g;
      function toggleClass(o, r, t) {
        if (o && r) if (o.classList) o.classList[t ? "add" : "remove"](r);
        else {
          var s = (" " + o.className + " ").replace(R_SPACE, " ").replace(" " + r + " ", " ");
          o.className = (s + (t ? " " + r : "")).replace(R_SPACE, " ");
        }
      }
      function css(o, r, t) {
        var s = o && o.style;
        if (s) {
          if (t === void 0) return document.defaultView && document.defaultView.getComputedStyle ? t = document.defaultView.getComputedStyle(o, "") : o.currentStyle && (t = o.currentStyle), r === void 0 ? t : t[r];
          !(r in s) && r.indexOf("webkit") === -1 && (r = "-webkit-" + r), s[r] = t + (typeof t == "string" ? "" : "px");
        }
      }
      function matrix(o, r) {
        var t = "";
        if (typeof o == "string") t = o;
        else do {
          var s = css(o, "transform");
          s && s !== "none" && (t = s + " " + t);
        } while (!r && (o = o.parentNode));
        var n = window.DOMMatrix || window.WebKitCSSMatrix || window.CSSMatrix || window.MSCSSMatrix;
        return n && new n(t);
      }
      function find(o, r, t) {
        if (o) {
          var s = o.getElementsByTagName(r), n = 0, d = s.length;
          if (t) for (; n < d; n++) t(s[n], n);
          return s;
        }
        return [];
      }
      function getWindowScrollingElement() {
        var o = document.scrollingElement;
        return o || document.documentElement;
      }
      function getRect(o, r, t, s, n) {
        if (!(!o.getBoundingClientRect && o !== window)) {
          var d, l, a, u, c, f, m;
          if (o !== window && o.parentNode && o !== getWindowScrollingElement() ? (d = o.getBoundingClientRect(), l = d.top, a = d.left, u = d.bottom, c = d.right, f = d.height, m = d.width) : (l = 0, a = 0, u = window.innerHeight, c = window.innerWidth, f = window.innerHeight, m = window.innerWidth), (r || t) && o !== window && (n = n || o.parentNode, !IE11OrLess)) do
            if (n && n.getBoundingClientRect && (css(n, "transform") !== "none" || t && css(n, "position") !== "static")) {
              var $ = n.getBoundingClientRect();
              l -= $.top + parseInt(css(n, "border-top-width")), a -= $.left + parseInt(css(n, "border-left-width")), u = l + d.height, c = a + d.width;
              break;
            }
          while (n = n.parentNode);
          if (s && o !== window) {
            var p = matrix(n || o), g = p && p.a, b = p && p.d;
            p && (l /= b, a /= g, m /= g, f /= b, u = l + f, c = a + m);
          }
          return { top: l, left: a, bottom: u, right: c, width: m, height: f };
        }
      }
      function isScrolledPast(o, r, t) {
        for (var s = getParentAutoScrollElement(o, true), n = getRect(o)[r]; s; ) {
          var d = getRect(s)[t], l = void 0;
          if (l = n >= d, !l) return s;
          if (s === getWindowScrollingElement()) break;
          s = getParentAutoScrollElement(s, false);
        }
        return false;
      }
      function getChild(o, r, t, s) {
        for (var n = 0, d = 0, l = o.children; d < l.length; ) {
          if (l[d].style.display !== "none" && l[d] !== Sortable.ghost && (s || l[d] !== Sortable.dragged) && closest(l[d], t.draggable, o, false)) {
            if (n === r) return l[d];
            n++;
          }
          d++;
        }
        return null;
      }
      function lastChild(o, r) {
        for (var t = o.lastElementChild; t && (t === Sortable.ghost || css(t, "display") === "none" || r && !matches(t, r)); ) t = t.previousElementSibling;
        return t || null;
      }
      function index$1(o, r) {
        var t = 0;
        if (!o || !o.parentNode) return -1;
        for (; o = o.previousElementSibling; ) o.nodeName.toUpperCase() !== "TEMPLATE" && o !== Sortable.clone && (!r || matches(o, r)) && t++;
        return t;
      }
      function getRelativeScrollOffset(o) {
        var r = 0, t = 0, s = getWindowScrollingElement();
        if (o) do {
          var n = matrix(o), d = n.a, l = n.d;
          r += o.scrollLeft * d, t += o.scrollTop * l;
        } while (o !== s && (o = o.parentNode));
        return [r, t];
      }
      function indexOfObject(o, r) {
        for (var t in o) if (o.hasOwnProperty(t)) {
          for (var s in r) if (r.hasOwnProperty(s) && r[s] === o[t][s]) return Number(t);
        }
        return -1;
      }
      function getParentAutoScrollElement(o, r) {
        if (!o || !o.getBoundingClientRect) return getWindowScrollingElement();
        var t = o, s = false;
        do
          if (t.clientWidth < t.scrollWidth || t.clientHeight < t.scrollHeight) {
            var n = css(t);
            if (t.clientWidth < t.scrollWidth && (n.overflowX == "auto" || n.overflowX == "scroll") || t.clientHeight < t.scrollHeight && (n.overflowY == "auto" || n.overflowY == "scroll")) {
              if (!t.getBoundingClientRect || t === document.body) return getWindowScrollingElement();
              if (s || r) return t;
              s = true;
            }
          }
        while (t = t.parentNode);
        return getWindowScrollingElement();
      }
      function extend(o, r) {
        if (o && r) for (var t in r) r.hasOwnProperty(t) && (o[t] = r[t]);
        return o;
      }
      function isRectEqual(o, r) {
        return Math.round(o.top) === Math.round(r.top) && Math.round(o.left) === Math.round(r.left) && Math.round(o.height) === Math.round(r.height) && Math.round(o.width) === Math.round(r.width);
      }
      var _throttleTimeout;
      function throttle(o, r) {
        return function() {
          if (!_throttleTimeout) {
            var t = arguments, s = this;
            t.length === 1 ? o.call(s, t[0]) : o.apply(s, t), _throttleTimeout = setTimeout(function() {
              _throttleTimeout = void 0;
            }, r);
          }
        };
      }
      function cancelThrottle() {
        clearTimeout(_throttleTimeout), _throttleTimeout = void 0;
      }
      function scrollBy(o, r, t) {
        o.scrollLeft += r, o.scrollTop += t;
      }
      function clone(o) {
        var r = window.Polymer, t = window.jQuery || window.Zepto;
        return r && r.dom ? r.dom(o).cloneNode(true) : t ? t(o).clone(true)[0] : o.cloneNode(true);
      }
      function setRect(o, r) {
        css(o, "position", "absolute"), css(o, "top", r.top), css(o, "left", r.left), css(o, "width", r.width), css(o, "height", r.height);
      }
      function unsetRect(o) {
        css(o, "position", ""), css(o, "top", ""), css(o, "left", ""), css(o, "width", ""), css(o, "height", "");
      }
      var expando = "Sortable" + (/* @__PURE__ */ new Date()).getTime();
      function AnimationStateManager() {
        var o = [], r;
        return { captureAnimationState: function() {
          if (o = [], !!this.options.animation) {
            var s = [].slice.call(this.el.children);
            s.forEach(function(n) {
              if (!(css(n, "display") === "none" || n === Sortable.ghost)) {
                o.push({ target: n, rect: getRect(n) });
                var d = _objectSpread2({}, o[o.length - 1].rect);
                if (n.thisAnimationDuration) {
                  var l = matrix(n, true);
                  l && (d.top -= l.f, d.left -= l.e);
                }
                n.fromRect = d;
              }
            });
          }
        }, addAnimationState: function(s) {
          o.push(s);
        }, removeAnimationState: function(s) {
          o.splice(indexOfObject(o, { target: s }), 1);
        }, animateAll: function(s) {
          var n = this;
          if (!this.options.animation) {
            clearTimeout(r), typeof s == "function" && s();
            return;
          }
          var d = false, l = 0;
          o.forEach(function(a) {
            var u = 0, c = a.target, f = c.fromRect, m = getRect(c), $ = c.prevFromRect, p = c.prevToRect, g = a.rect, b = matrix(c, true);
            b && (m.top -= b.f, m.left -= b.e), c.toRect = m, c.thisAnimationDuration && isRectEqual($, m) && !isRectEqual(f, m) && (g.top - m.top) / (g.left - m.left) === (f.top - m.top) / (f.left - m.left) && (u = calculateRealTime(g, $, p, n.options)), isRectEqual(m, f) || (c.prevFromRect = f, c.prevToRect = m, u || (u = n.options.animation), n.animate(c, g, m, u)), u && (d = true, l = Math.max(l, u), clearTimeout(c.animationResetTimer), c.animationResetTimer = setTimeout(function() {
              c.animationTime = 0, c.prevFromRect = null, c.fromRect = null, c.prevToRect = null, c.thisAnimationDuration = null;
            }, u), c.thisAnimationDuration = u);
          }), clearTimeout(r), d ? r = setTimeout(function() {
            typeof s == "function" && s();
          }, l) : typeof s == "function" && s(), o = [];
        }, animate: function(s, n, d, l) {
          if (l) {
            css(s, "transition", ""), css(s, "transform", "");
            var a = matrix(this.el), u = a && a.a, c = a && a.d, f = (n.left - d.left) / (u || 1), m = (n.top - d.top) / (c || 1);
            s.animatingX = !!f, s.animatingY = !!m, css(s, "transform", "translate3d(" + f + "px," + m + "px,0)"), this.forRepaintDummy = repaint(s), css(s, "transition", "transform " + l + "ms" + (this.options.easing ? " " + this.options.easing : "")), css(s, "transform", "translate3d(0,0,0)"), typeof s.animated == "number" && clearTimeout(s.animated), s.animated = setTimeout(function() {
              css(s, "transition", ""), css(s, "transform", ""), s.animated = false, s.animatingX = false, s.animatingY = false;
            }, l);
          }
        } };
      }
      function repaint(o) {
        return o.offsetWidth;
      }
      function calculateRealTime(o, r, t, s) {
        return Math.sqrt(Math.pow(r.top - o.top, 2) + Math.pow(r.left - o.left, 2)) / Math.sqrt(Math.pow(r.top - t.top, 2) + Math.pow(r.left - t.left, 2)) * s.animation;
      }
      var plugins = [], defaults = { initializeByDefault: true }, PluginManager = { mount: function(r) {
        for (var t in defaults) defaults.hasOwnProperty(t) && !(t in r) && (r[t] = defaults[t]);
        plugins.forEach(function(s) {
          if (s.pluginName === r.pluginName) throw "Sortable: Cannot mount plugin ".concat(r.pluginName, " more than once");
        }), plugins.push(r);
      }, pluginEvent: function(r, t, s) {
        var n = this;
        this.eventCanceled = false, s.cancel = function() {
          n.eventCanceled = true;
        };
        var d = r + "Global";
        plugins.forEach(function(l) {
          t[l.pluginName] && (t[l.pluginName][d] && t[l.pluginName][d](_objectSpread2({ sortable: t }, s)), t.options[l.pluginName] && t[l.pluginName][r] && t[l.pluginName][r](_objectSpread2({ sortable: t }, s)));
        });
      }, initializePlugins: function(r, t, s, n) {
        plugins.forEach(function(a) {
          var u = a.pluginName;
          if (!(!r.options[u] && !a.initializeByDefault)) {
            var c = new a(r, t, r.options);
            c.sortable = r, c.options = r.options, r[u] = c, _extends(s, c.defaults);
          }
        });
        for (var d in r.options) if (r.options.hasOwnProperty(d)) {
          var l = this.modifyOption(r, d, r.options[d]);
          typeof l < "u" && (r.options[d] = l);
        }
      }, getEventProperties: function(r, t) {
        var s = {};
        return plugins.forEach(function(n) {
          typeof n.eventProperties == "function" && _extends(s, n.eventProperties.call(t[n.pluginName], r));
        }), s;
      }, modifyOption: function(r, t, s) {
        var n;
        return plugins.forEach(function(d) {
          r[d.pluginName] && d.optionListeners && typeof d.optionListeners[t] == "function" && (n = d.optionListeners[t].call(r[d.pluginName], s));
        }), n;
      } };
      function dispatchEvent(o) {
        var r = o.sortable, t = o.rootEl, s = o.name, n = o.targetEl, d = o.cloneEl, l = o.toEl, a = o.fromEl, u = o.oldIndex, c = o.newIndex, f = o.oldDraggableIndex, m = o.newDraggableIndex, $ = o.originalEvent, p = o.putSortable, g = o.extraEventProperties;
        if (r = r || t && t[expando], !!r) {
          var b, h = r.options, q = "on" + s.charAt(0).toUpperCase() + s.substr(1);
          window.CustomEvent && !IE11OrLess && !Edge ? b = new CustomEvent(s, { bubbles: true, cancelable: true }) : (b = document.createEvent("Event"), b.initEvent(s, true, true)), b.to = l || t, b.from = a || t, b.item = n || t, b.clone = d, b.oldIndex = u, b.newIndex = c, b.oldDraggableIndex = f, b.newDraggableIndex = m, b.originalEvent = $, b.pullMode = p ? p.lastPutMode : void 0;
          var v = _objectSpread2(_objectSpread2({}, g), PluginManager.getEventProperties(s, r));
          for (var y in v) b[y] = v[y];
          t && t.dispatchEvent(b), h[q] && h[q].call(r, b);
        }
      }
      var _excluded = ["evt"], pluginEvent = function(r, t) {
        var s = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {}, n = s.evt, d = _objectWithoutProperties(s, _excluded);
        PluginManager.pluginEvent.bind(Sortable)(r, t, _objectSpread2({ dragEl, parentEl, ghostEl, rootEl, nextEl, lastDownEl, cloneEl, cloneHidden, dragStarted: moved, putSortable, activeSortable: Sortable.active, originalEvent: n, oldIndex, oldDraggableIndex, newIndex, newDraggableIndex, hideGhostForTarget: _hideGhostForTarget, unhideGhostForTarget: _unhideGhostForTarget, cloneNowHidden: function() {
          cloneHidden = true;
        }, cloneNowShown: function() {
          cloneHidden = false;
        }, dispatchSortableEvent: function(a) {
          _dispatchEvent({ sortable: t, name: a, originalEvent: n });
        } }, d));
      };
      function _dispatchEvent(o) {
        dispatchEvent(_objectSpread2({ putSortable, cloneEl, targetEl: dragEl, rootEl, oldIndex, oldDraggableIndex, newIndex, newDraggableIndex }, o));
      }
      var dragEl, parentEl, ghostEl, rootEl, nextEl, lastDownEl, cloneEl, cloneHidden, oldIndex, newIndex, oldDraggableIndex, newDraggableIndex, activeGroup, putSortable, awaitingDragStarted = false, ignoreNextClick = false, sortables = [], tapEvt, touchEvt, lastDx, lastDy, tapDistanceLeft, tapDistanceTop, moved, lastTarget, lastDirection, pastFirstInvertThresh = false, isCircumstantialInvert = false, targetMoveDistance, ghostRelativeParent, ghostRelativeParentInitialScroll = [], _silent = false, savedInputChecked = [], documentExists = typeof document < "u", PositionGhostAbsolutely = IOS, CSSFloatProperty = Edge || IE11OrLess ? "cssFloat" : "float", supportDraggable = documentExists && !ChromeForAndroid && !IOS && "draggable" in document.createElement("div"), supportCssPointerEvents = function() {
        if (documentExists) {
          if (IE11OrLess) return false;
          var o = document.createElement("x");
          return o.style.cssText = "pointer-events:auto", o.style.pointerEvents === "auto";
        }
      }(), _detectDirection = function(r, t) {
        var s = css(r), n = parseInt(s.width) - parseInt(s.paddingLeft) - parseInt(s.paddingRight) - parseInt(s.borderLeftWidth) - parseInt(s.borderRightWidth), d = getChild(r, 0, t), l = getChild(r, 1, t), a = d && css(d), u = l && css(l), c = a && parseInt(a.marginLeft) + parseInt(a.marginRight) + getRect(d).width, f = u && parseInt(u.marginLeft) + parseInt(u.marginRight) + getRect(l).width;
        if (s.display === "flex") return s.flexDirection === "column" || s.flexDirection === "column-reverse" ? "vertical" : "horizontal";
        if (s.display === "grid") return s.gridTemplateColumns.split(" ").length <= 1 ? "vertical" : "horizontal";
        if (d && a.float && a.float !== "none") {
          var m = a.float === "left" ? "left" : "right";
          return l && (u.clear === "both" || u.clear === m) ? "vertical" : "horizontal";
        }
        return d && (a.display === "block" || a.display === "flex" || a.display === "table" || a.display === "grid" || c >= n && s[CSSFloatProperty] === "none" || l && s[CSSFloatProperty] === "none" && c + f > n) ? "vertical" : "horizontal";
      }, _dragElInRowColumn = function(r, t, s) {
        var n = s ? r.left : r.top, d = s ? r.right : r.bottom, l = s ? r.width : r.height, a = s ? t.left : t.top, u = s ? t.right : t.bottom, c = s ? t.width : t.height;
        return n === a || d === u || n + l / 2 === a + c / 2;
      }, _detectNearestEmptySortable = function(r, t) {
        var s;
        return sortables.some(function(n) {
          var d = n[expando].options.emptyInsertThreshold;
          if (!(!d || lastChild(n))) {
            var l = getRect(n), a = r >= l.left - d && r <= l.right + d, u = t >= l.top - d && t <= l.bottom + d;
            if (a && u) return s = n;
          }
        }), s;
      }, _prepareGroup = function(r) {
        function t(d, l) {
          return function(a, u, c, f) {
            var m = a.options.group.name && u.options.group.name && a.options.group.name === u.options.group.name;
            if (d == null && (l || m)) return true;
            if (d == null || d === false) return false;
            if (l && d === "clone") return d;
            if (typeof d == "function") return t(d(a, u, c, f), l)(a, u, c, f);
            var $ = (l ? a : u).options.group.name;
            return d === true || typeof d == "string" && d === $ || d.join && d.indexOf($) > -1;
          };
        }
        var s = {}, n = r.group;
        (!n || _typeof(n) != "object") && (n = { name: n }), s.name = n.name, s.checkPull = t(n.pull, true), s.checkPut = t(n.put), s.revertClone = n.revertClone, r.group = s;
      }, _hideGhostForTarget = function() {
        !supportCssPointerEvents && ghostEl && css(ghostEl, "display", "none");
      }, _unhideGhostForTarget = function() {
        !supportCssPointerEvents && ghostEl && css(ghostEl, "display", "");
      };
      documentExists && document.addEventListener("click", function(o) {
        if (ignoreNextClick) return o.preventDefault(), o.stopPropagation && o.stopPropagation(), o.stopImmediatePropagation && o.stopImmediatePropagation(), ignoreNextClick = false, false;
      }, true);
      var nearestEmptyInsertDetectEvent = function(r) {
        if (dragEl) {
          r = r.touches ? r.touches[0] : r;
          var t = _detectNearestEmptySortable(r.clientX, r.clientY);
          if (t) {
            var s = {};
            for (var n in r) r.hasOwnProperty(n) && (s[n] = r[n]);
            s.target = s.rootEl = t, s.preventDefault = void 0, s.stopPropagation = void 0, t[expando]._onDragOver(s);
          }
        }
      }, _checkOutsideTargetEl = function(r) {
        dragEl && dragEl.parentNode[expando]._isOutsideThisEl(r.target);
      };
      function Sortable(o, r) {
        if (!(o && o.nodeType && o.nodeType === 1)) throw "Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(o));
        this.el = o, this.options = r = _extends({}, r), o[expando] = this;
        var t = { group: null, sort: true, disabled: false, store: null, handle: null, draggable: /^[uo]l$/i.test(o.nodeName) ? ">li" : ">*", swapThreshold: 1, invertSwap: false, invertedSwapThreshold: null, removeCloneOnHide: true, direction: function() {
          return _detectDirection(o, this.options);
        }, ghostClass: "sortable-ghost", chosenClass: "sortable-chosen", dragClass: "sortable-drag", ignore: "a, img", filter: null, preventOnFilter: true, animation: 0, easing: null, setData: function(l, a) {
          l.setData("Text", a.textContent);
        }, dropBubble: false, dragoverBubble: false, dataIdAttr: "data-id", delay: 0, delayOnTouchOnly: false, touchStartThreshold: (Number.parseInt ? Number : window).parseInt(window.devicePixelRatio, 10) || 1, forceFallback: false, fallbackClass: "sortable-fallback", fallbackOnBody: false, fallbackTolerance: 0, fallbackOffset: { x: 0, y: 0 }, supportPointer: Sortable.supportPointer !== false && "PointerEvent" in window && !Safari, emptyInsertThreshold: 5 };
        PluginManager.initializePlugins(this, o, t);
        for (var s in t) !(s in r) && (r[s] = t[s]);
        _prepareGroup(r);
        for (var n in this) n.charAt(0) === "_" && typeof this[n] == "function" && (this[n] = this[n].bind(this));
        this.nativeDraggable = r.forceFallback ? false : supportDraggable, this.nativeDraggable && (this.options.touchStartThreshold = 1), r.supportPointer ? on(o, "pointerdown", this._onTapStart) : (on(o, "mousedown", this._onTapStart), on(o, "touchstart", this._onTapStart)), this.nativeDraggable && (on(o, "dragover", this), on(o, "dragenter", this)), sortables.push(this.el), r.store && r.store.get && this.sort(r.store.get(this) || []), _extends(this, AnimationStateManager());
      }
      Sortable.prototype = { constructor: Sortable, _isOutsideThisEl: function(r) {
        !this.el.contains(r) && r !== this.el && (lastTarget = null);
      }, _getDirection: function(r, t) {
        return typeof this.options.direction == "function" ? this.options.direction.call(this, r, t, dragEl) : this.options.direction;
      }, _onTapStart: function(r) {
        if (r.cancelable) {
          var t = this, s = this.el, n = this.options, d = n.preventOnFilter, l = r.type, a = r.touches && r.touches[0] || r.pointerType && r.pointerType === "touch" && r, u = (a || r).target, c = r.target.shadowRoot && (r.path && r.path[0] || r.composedPath && r.composedPath()[0]) || u, f = n.filter;
          if (_saveInputCheckedState(s), !dragEl && !(/mousedown|pointerdown/.test(l) && r.button !== 0 || n.disabled) && !c.isContentEditable && !(!this.nativeDraggable && Safari && u && u.tagName.toUpperCase() === "SELECT") && (u = closest(u, n.draggable, s, false), !(u && u.animated) && lastDownEl !== u)) {
            if (oldIndex = index$1(u), oldDraggableIndex = index$1(u, n.draggable), typeof f == "function") {
              if (f.call(this, r, u, this)) {
                _dispatchEvent({ sortable: t, rootEl: c, name: "filter", targetEl: u, toEl: s, fromEl: s }), pluginEvent("filter", t, { evt: r }), d && r.cancelable && r.preventDefault();
                return;
              }
            } else if (f && (f = f.split(",").some(function(m) {
              if (m = closest(c, m.trim(), s, false), m) return _dispatchEvent({ sortable: t, rootEl: m, name: "filter", targetEl: u, fromEl: s, toEl: s }), pluginEvent("filter", t, { evt: r }), true;
            }), f)) {
              d && r.cancelable && r.preventDefault();
              return;
            }
            n.handle && !closest(c, n.handle, s, false) || this._prepareDragStart(r, a, u);
          }
        }
      }, _prepareDragStart: function(r, t, s) {
        var n = this, d = n.el, l = n.options, a = d.ownerDocument, u;
        if (s && !dragEl && s.parentNode === d) {
          var c = getRect(s);
          if (rootEl = d, dragEl = s, parentEl = dragEl.parentNode, nextEl = dragEl.nextSibling, lastDownEl = s, activeGroup = l.group, Sortable.dragged = dragEl, tapEvt = { target: dragEl, clientX: (t || r).clientX, clientY: (t || r).clientY }, tapDistanceLeft = tapEvt.clientX - c.left, tapDistanceTop = tapEvt.clientY - c.top, this._lastX = (t || r).clientX, this._lastY = (t || r).clientY, dragEl.style["will-change"] = "all", u = function() {
            if (pluginEvent("delayEnded", n, { evt: r }), Sortable.eventCanceled) {
              n._onDrop();
              return;
            }
            n._disableDelayedDragEvents(), !FireFox && n.nativeDraggable && (dragEl.draggable = true), n._triggerDragStart(r, t), _dispatchEvent({ sortable: n, name: "choose", originalEvent: r }), toggleClass(dragEl, l.chosenClass, true);
          }, l.ignore.split(",").forEach(function(f) {
            find(dragEl, f.trim(), _disableDraggable);
          }), on(a, "dragover", nearestEmptyInsertDetectEvent), on(a, "mousemove", nearestEmptyInsertDetectEvent), on(a, "touchmove", nearestEmptyInsertDetectEvent), on(a, "mouseup", n._onDrop), on(a, "touchend", n._onDrop), on(a, "touchcancel", n._onDrop), FireFox && this.nativeDraggable && (this.options.touchStartThreshold = 4, dragEl.draggable = true), pluginEvent("delayStart", this, { evt: r }), l.delay && (!l.delayOnTouchOnly || t) && (!this.nativeDraggable || !(Edge || IE11OrLess))) {
            if (Sortable.eventCanceled) {
              this._onDrop();
              return;
            }
            on(a, "mouseup", n._disableDelayedDrag), on(a, "touchend", n._disableDelayedDrag), on(a, "touchcancel", n._disableDelayedDrag), on(a, "mousemove", n._delayedDragTouchMoveHandler), on(a, "touchmove", n._delayedDragTouchMoveHandler), l.supportPointer && on(a, "pointermove", n._delayedDragTouchMoveHandler), n._dragStartTimer = setTimeout(u, l.delay);
          } else u();
        }
      }, _delayedDragTouchMoveHandler: function(r) {
        var t = r.touches ? r.touches[0] : r;
        Math.max(Math.abs(t.clientX - this._lastX), Math.abs(t.clientY - this._lastY)) >= Math.floor(this.options.touchStartThreshold / (this.nativeDraggable && window.devicePixelRatio || 1)) && this._disableDelayedDrag();
      }, _disableDelayedDrag: function() {
        dragEl && _disableDraggable(dragEl), clearTimeout(this._dragStartTimer), this._disableDelayedDragEvents();
      }, _disableDelayedDragEvents: function() {
        var r = this.el.ownerDocument;
        off(r, "mouseup", this._disableDelayedDrag), off(r, "touchend", this._disableDelayedDrag), off(r, "touchcancel", this._disableDelayedDrag), off(r, "mousemove", this._delayedDragTouchMoveHandler), off(r, "touchmove", this._delayedDragTouchMoveHandler), off(r, "pointermove", this._delayedDragTouchMoveHandler);
      }, _triggerDragStart: function(r, t) {
        t = t || r.pointerType == "touch" && r, !this.nativeDraggable || t ? this.options.supportPointer ? on(document, "pointermove", this._onTouchMove) : t ? on(document, "touchmove", this._onTouchMove) : on(document, "mousemove", this._onTouchMove) : (on(dragEl, "dragend", this), on(rootEl, "dragstart", this._onDragStart));
        try {
          document.selection ? _nextTick(function() {
            document.selection.empty();
          }) : window.getSelection().removeAllRanges();
        } catch {
        }
      }, _dragStarted: function(r, t) {
        if (awaitingDragStarted = false, rootEl && dragEl) {
          pluginEvent("dragStarted", this, { evt: t }), this.nativeDraggable && on(document, "dragover", _checkOutsideTargetEl);
          var s = this.options;
          !r && toggleClass(dragEl, s.dragClass, false), toggleClass(dragEl, s.ghostClass, true), Sortable.active = this, r && this._appendGhost(), _dispatchEvent({ sortable: this, name: "start", originalEvent: t });
        } else this._nulling();
      }, _emulateDragOver: function() {
        if (touchEvt) {
          this._lastX = touchEvt.clientX, this._lastY = touchEvt.clientY, _hideGhostForTarget();
          for (var r = document.elementFromPoint(touchEvt.clientX, touchEvt.clientY), t = r; r && r.shadowRoot && (r = r.shadowRoot.elementFromPoint(touchEvt.clientX, touchEvt.clientY), r !== t); ) t = r;
          if (dragEl.parentNode[expando]._isOutsideThisEl(r), t) do {
            if (t[expando]) {
              var s = void 0;
              if (s = t[expando]._onDragOver({ clientX: touchEvt.clientX, clientY: touchEvt.clientY, target: r, rootEl: t }), s && !this.options.dragoverBubble) break;
            }
            r = t;
          } while (t = t.parentNode);
          _unhideGhostForTarget();
        }
      }, _onTouchMove: function(r) {
        if (tapEvt) {
          var t = this.options, s = t.fallbackTolerance, n = t.fallbackOffset, d = r.touches ? r.touches[0] : r, l = ghostEl && matrix(ghostEl, true), a = ghostEl && l && l.a, u = ghostEl && l && l.d, c = PositionGhostAbsolutely && ghostRelativeParent && getRelativeScrollOffset(ghostRelativeParent), f = (d.clientX - tapEvt.clientX + n.x) / (a || 1) + (c ? c[0] - ghostRelativeParentInitialScroll[0] : 0) / (a || 1), m = (d.clientY - tapEvt.clientY + n.y) / (u || 1) + (c ? c[1] - ghostRelativeParentInitialScroll[1] : 0) / (u || 1);
          if (!Sortable.active && !awaitingDragStarted) {
            if (s && Math.max(Math.abs(d.clientX - this._lastX), Math.abs(d.clientY - this._lastY)) < s) return;
            this._onDragStart(r, true);
          }
          if (ghostEl) {
            l ? (l.e += f - (lastDx || 0), l.f += m - (lastDy || 0)) : l = { a: 1, b: 0, c: 0, d: 1, e: f, f: m };
            var $ = "matrix(".concat(l.a, ",").concat(l.b, ",").concat(l.c, ",").concat(l.d, ",").concat(l.e, ",").concat(l.f, ")");
            css(ghostEl, "webkitTransform", $), css(ghostEl, "mozTransform", $), css(ghostEl, "msTransform", $), css(ghostEl, "transform", $), lastDx = f, lastDy = m, touchEvt = d;
          }
          r.cancelable && r.preventDefault();
        }
      }, _appendGhost: function() {
        if (!ghostEl) {
          var r = this.options.fallbackOnBody ? document.body : rootEl, t = getRect(dragEl, true, PositionGhostAbsolutely, true, r), s = this.options;
          if (PositionGhostAbsolutely) {
            for (ghostRelativeParent = r; css(ghostRelativeParent, "position") === "static" && css(ghostRelativeParent, "transform") === "none" && ghostRelativeParent !== document; ) ghostRelativeParent = ghostRelativeParent.parentNode;
            ghostRelativeParent !== document.body && ghostRelativeParent !== document.documentElement ? (ghostRelativeParent === document && (ghostRelativeParent = getWindowScrollingElement()), t.top += ghostRelativeParent.scrollTop, t.left += ghostRelativeParent.scrollLeft) : ghostRelativeParent = getWindowScrollingElement(), ghostRelativeParentInitialScroll = getRelativeScrollOffset(ghostRelativeParent);
          }
          ghostEl = dragEl.cloneNode(true), toggleClass(ghostEl, s.ghostClass, false), toggleClass(ghostEl, s.fallbackClass, true), toggleClass(ghostEl, s.dragClass, true), css(ghostEl, "transition", ""), css(ghostEl, "transform", ""), css(ghostEl, "box-sizing", "border-box"), css(ghostEl, "margin", 0), css(ghostEl, "top", t.top), css(ghostEl, "left", t.left), css(ghostEl, "width", t.width), css(ghostEl, "height", t.height), css(ghostEl, "opacity", "0.8"), css(ghostEl, "position", PositionGhostAbsolutely ? "absolute" : "fixed"), css(ghostEl, "zIndex", "100000"), css(ghostEl, "pointerEvents", "none"), Sortable.ghost = ghostEl, r.appendChild(ghostEl), css(ghostEl, "transform-origin", tapDistanceLeft / parseInt(ghostEl.style.width) * 100 + "% " + tapDistanceTop / parseInt(ghostEl.style.height) * 100 + "%");
        }
      }, _onDragStart: function(r, t) {
        var s = this, n = r.dataTransfer, d = s.options;
        if (pluginEvent("dragStart", this, { evt: r }), Sortable.eventCanceled) {
          this._onDrop();
          return;
        }
        pluginEvent("setupClone", this), Sortable.eventCanceled || (cloneEl = clone(dragEl), cloneEl.draggable = false, cloneEl.style["will-change"] = "", this._hideClone(), toggleClass(cloneEl, this.options.chosenClass, false), Sortable.clone = cloneEl), s.cloneId = _nextTick(function() {
          pluginEvent("clone", s), !Sortable.eventCanceled && (s.options.removeCloneOnHide || rootEl.insertBefore(cloneEl, dragEl), s._hideClone(), _dispatchEvent({ sortable: s, name: "clone" }));
        }), !t && toggleClass(dragEl, d.dragClass, true), t ? (ignoreNextClick = true, s._loopId = setInterval(s._emulateDragOver, 50)) : (off(document, "mouseup", s._onDrop), off(document, "touchend", s._onDrop), off(document, "touchcancel", s._onDrop), n && (n.effectAllowed = "move", d.setData && d.setData.call(s, n, dragEl)), on(document, "drop", s), css(dragEl, "transform", "translateZ(0)")), awaitingDragStarted = true, s._dragStartId = _nextTick(s._dragStarted.bind(s, t, r)), on(document, "selectstart", s), moved = true, Safari && css(document.body, "user-select", "none");
      }, _onDragOver: function(r) {
        var t = this.el, s = r.target, n, d, l, a = this.options, u = a.group, c = Sortable.active, f = activeGroup === u, m = a.sort, $ = putSortable || c, p, g = this, b = false;
        if (_silent) return;
        function h(H, X) {
          pluginEvent(H, g, _objectSpread2({ evt: r, isOwner: f, axis: p ? "vertical" : "horizontal", revert: l, dragRect: n, targetRect: d, canSort: m, fromSortable: $, target: s, completed: v, onMove: function(Z, ee) {
            return _onMove(rootEl, t, dragEl, n, Z, getRect(Z), r, ee);
          }, changed: y }, X));
        }
        function q() {
          h("dragOverAnimationCapture"), g.captureAnimationState(), g !== $ && $.captureAnimationState();
        }
        function v(H) {
          return h("dragOverCompleted", { insertion: H }), H && (f ? c._hideClone() : c._showClone(g), g !== $ && (toggleClass(dragEl, putSortable ? putSortable.options.ghostClass : c.options.ghostClass, false), toggleClass(dragEl, a.ghostClass, true)), putSortable !== g && g !== Sortable.active ? putSortable = g : g === Sortable.active && putSortable && (putSortable = null), $ === g && (g._ignoreWhileAnimating = s), g.animateAll(function() {
            h("dragOverAnimationComplete"), g._ignoreWhileAnimating = null;
          }), g !== $ && ($.animateAll(), $._ignoreWhileAnimating = null)), (s === dragEl && !dragEl.animated || s === t && !s.animated) && (lastTarget = null), !a.dragoverBubble && !r.rootEl && s !== document && (dragEl.parentNode[expando]._isOutsideThisEl(r.target), !H && nearestEmptyInsertDetectEvent(r)), !a.dragoverBubble && r.stopPropagation && r.stopPropagation(), b = true;
        }
        function y() {
          newIndex = index$1(dragEl), newDraggableIndex = index$1(dragEl, a.draggable), _dispatchEvent({ sortable: g, name: "change", toEl: t, newIndex, newDraggableIndex, originalEvent: r });
        }
        if (r.preventDefault !== void 0 && r.cancelable && r.preventDefault(), s = closest(s, a.draggable, t, true), h("dragOver"), Sortable.eventCanceled) return b;
        if (dragEl.contains(r.target) || s.animated && s.animatingX && s.animatingY || g._ignoreWhileAnimating === s) return v(false);
        if (ignoreNextClick = false, c && !a.disabled && (f ? m || (l = parentEl !== rootEl) : putSortable === this || (this.lastPutMode = activeGroup.checkPull(this, c, dragEl, r)) && u.checkPut(this, c, dragEl, r))) {
          if (p = this._getDirection(r, s) === "vertical", n = getRect(dragEl), h("dragOverValid"), Sortable.eventCanceled) return b;
          if (l) return parentEl = rootEl, q(), this._hideClone(), h("revert"), Sortable.eventCanceled || (nextEl ? rootEl.insertBefore(dragEl, nextEl) : rootEl.appendChild(dragEl)), v(true);
          var w = lastChild(t, a.draggable);
          if (!w || _ghostIsLast(r, p, this) && !w.animated) {
            if (w === dragEl) return v(false);
            if (w && t === r.target && (s = w), s && (d = getRect(s)), _onMove(rootEl, t, dragEl, n, s, d, r, !!s) !== false) return q(), t.appendChild(dragEl), parentEl = t, y(), v(true);
          } else if (w && _ghostIsFirst(r, p, this)) {
            var T = getChild(t, 0, a, true);
            if (T === dragEl) return v(false);
            if (s = T, d = getRect(s), _onMove(rootEl, t, dragEl, n, s, d, r, false) !== false) return q(), t.insertBefore(dragEl, T), parentEl = t, y(), v(true);
          } else if (s.parentNode === t) {
            d = getRect(s);
            var E = 0, P, V = dragEl.parentNode !== t, S = !_dragElInRowColumn(dragEl.animated && dragEl.toRect || n, s.animated && s.toRect || d, p), k = p ? "top" : "left", _ = isScrolledPast(s, "top", "top") || isScrolledPast(dragEl, "top", "top"), W = _ ? _.scrollTop : void 0;
            lastTarget !== s && (P = d[k], pastFirstInvertThresh = false, isCircumstantialInvert = !S && a.invertSwap || V), E = _getSwapDirection(r, s, d, p, S ? 1 : a.swapThreshold, a.invertedSwapThreshold == null ? a.swapThreshold : a.invertedSwapThreshold, isCircumstantialInvert, lastTarget === s);
            var B;
            if (E !== 0) {
              var O = index$1(dragEl);
              do
                O -= E, B = parentEl.children[O];
              while (B && (css(B, "display") === "none" || B === ghostEl));
            }
            if (E === 0 || B === s) return v(false);
            lastTarget = s, lastDirection = E;
            var j = s.nextElementSibling, I = false;
            I = E === 1;
            var R = _onMove(rootEl, t, dragEl, n, s, d, r, I);
            if (R !== false) return (R === 1 || R === -1) && (I = R === 1), _silent = true, setTimeout(_unsilent, 30), q(), I && !j ? t.appendChild(dragEl) : s.parentNode.insertBefore(dragEl, I ? j : s), _ && scrollBy(_, 0, W - _.scrollTop), parentEl = dragEl.parentNode, P !== void 0 && !isCircumstantialInvert && (targetMoveDistance = Math.abs(P - getRect(s)[k])), y(), v(true);
          }
          if (t.contains(dragEl)) return v(false);
        }
        return false;
      }, _ignoreWhileAnimating: null, _offMoveEvents: function() {
        off(document, "mousemove", this._onTouchMove), off(document, "touchmove", this._onTouchMove), off(document, "pointermove", this._onTouchMove), off(document, "dragover", nearestEmptyInsertDetectEvent), off(document, "mousemove", nearestEmptyInsertDetectEvent), off(document, "touchmove", nearestEmptyInsertDetectEvent);
      }, _offUpEvents: function() {
        var r = this.el.ownerDocument;
        off(r, "mouseup", this._onDrop), off(r, "touchend", this._onDrop), off(r, "pointerup", this._onDrop), off(r, "touchcancel", this._onDrop), off(document, "selectstart", this);
      }, _onDrop: function(r) {
        var t = this.el, s = this.options;
        if (newIndex = index$1(dragEl), newDraggableIndex = index$1(dragEl, s.draggable), pluginEvent("drop", this, { evt: r }), parentEl = dragEl && dragEl.parentNode, newIndex = index$1(dragEl), newDraggableIndex = index$1(dragEl, s.draggable), Sortable.eventCanceled) {
          this._nulling();
          return;
        }
        awaitingDragStarted = false, isCircumstantialInvert = false, pastFirstInvertThresh = false, clearInterval(this._loopId), clearTimeout(this._dragStartTimer), _cancelNextTick(this.cloneId), _cancelNextTick(this._dragStartId), this.nativeDraggable && (off(document, "drop", this), off(t, "dragstart", this._onDragStart)), this._offMoveEvents(), this._offUpEvents(), Safari && css(document.body, "user-select", ""), css(dragEl, "transform", ""), r && (moved && (r.cancelable && r.preventDefault(), !s.dropBubble && r.stopPropagation()), ghostEl && ghostEl.parentNode && ghostEl.parentNode.removeChild(ghostEl), (rootEl === parentEl || putSortable && putSortable.lastPutMode !== "clone") && cloneEl && cloneEl.parentNode && cloneEl.parentNode.removeChild(cloneEl), dragEl && (this.nativeDraggable && off(dragEl, "dragend", this), _disableDraggable(dragEl), dragEl.style["will-change"] = "", moved && !awaitingDragStarted && toggleClass(dragEl, putSortable ? putSortable.options.ghostClass : this.options.ghostClass, false), toggleClass(dragEl, this.options.chosenClass, false), _dispatchEvent({ sortable: this, name: "unchoose", toEl: parentEl, newIndex: null, newDraggableIndex: null, originalEvent: r }), rootEl !== parentEl ? (newIndex >= 0 && (_dispatchEvent({ rootEl: parentEl, name: "add", toEl: parentEl, fromEl: rootEl, originalEvent: r }), _dispatchEvent({ sortable: this, name: "remove", toEl: parentEl, originalEvent: r }), _dispatchEvent({ rootEl: parentEl, name: "sort", toEl: parentEl, fromEl: rootEl, originalEvent: r }), _dispatchEvent({ sortable: this, name: "sort", toEl: parentEl, originalEvent: r })), putSortable && putSortable.save()) : newIndex !== oldIndex && newIndex >= 0 && (_dispatchEvent({ sortable: this, name: "update", toEl: parentEl, originalEvent: r }), _dispatchEvent({ sortable: this, name: "sort", toEl: parentEl, originalEvent: r })), Sortable.active && ((newIndex == null || newIndex === -1) && (newIndex = oldIndex, newDraggableIndex = oldDraggableIndex), _dispatchEvent({ sortable: this, name: "end", toEl: parentEl, originalEvent: r }), this.save()))), this._nulling();
      }, _nulling: function() {
        pluginEvent("nulling", this), rootEl = dragEl = parentEl = ghostEl = nextEl = cloneEl = lastDownEl = cloneHidden = tapEvt = touchEvt = moved = newIndex = newDraggableIndex = oldIndex = oldDraggableIndex = lastTarget = lastDirection = putSortable = activeGroup = Sortable.dragged = Sortable.ghost = Sortable.clone = Sortable.active = null, savedInputChecked.forEach(function(r) {
          r.checked = true;
        }), savedInputChecked.length = lastDx = lastDy = 0;
      }, handleEvent: function(r) {
        switch (r.type) {
          case "drop":
          case "dragend":
            this._onDrop(r);
            break;
          case "dragenter":
          case "dragover":
            dragEl && (this._onDragOver(r), _globalDragOver(r));
            break;
          case "selectstart":
            r.preventDefault();
            break;
        }
      }, toArray: function() {
        for (var r = [], t, s = this.el.children, n = 0, d = s.length, l = this.options; n < d; n++) t = s[n], closest(t, l.draggable, this.el, false) && r.push(t.getAttribute(l.dataIdAttr) || _generateId(t));
        return r;
      }, sort: function(r, t) {
        var s = {}, n = this.el;
        this.toArray().forEach(function(d, l) {
          var a = n.children[l];
          closest(a, this.options.draggable, n, false) && (s[d] = a);
        }, this), t && this.captureAnimationState(), r.forEach(function(d) {
          s[d] && (n.removeChild(s[d]), n.appendChild(s[d]));
        }), t && this.animateAll();
      }, save: function() {
        var r = this.options.store;
        r && r.set && r.set(this);
      }, closest: function(r, t) {
        return closest(r, t || this.options.draggable, this.el, false);
      }, option: function(r, t) {
        var s = this.options;
        if (t === void 0) return s[r];
        var n = PluginManager.modifyOption(this, r, t);
        typeof n < "u" ? s[r] = n : s[r] = t, r === "group" && _prepareGroup(s);
      }, destroy: function() {
        pluginEvent("destroy", this);
        var r = this.el;
        r[expando] = null, off(r, "mousedown", this._onTapStart), off(r, "touchstart", this._onTapStart), off(r, "pointerdown", this._onTapStart), this.nativeDraggable && (off(r, "dragover", this), off(r, "dragenter", this)), Array.prototype.forEach.call(r.querySelectorAll("[draggable]"), function(t) {
          t.removeAttribute("draggable");
        }), this._onDrop(), this._disableDelayedDragEvents(), sortables.splice(sortables.indexOf(this.el), 1), this.el = r = null;
      }, _hideClone: function() {
        if (!cloneHidden) {
          if (pluginEvent("hideClone", this), Sortable.eventCanceled) return;
          css(cloneEl, "display", "none"), this.options.removeCloneOnHide && cloneEl.parentNode && cloneEl.parentNode.removeChild(cloneEl), cloneHidden = true;
        }
      }, _showClone: function(r) {
        if (r.lastPutMode !== "clone") {
          this._hideClone();
          return;
        }
        if (cloneHidden) {
          if (pluginEvent("showClone", this), Sortable.eventCanceled) return;
          dragEl.parentNode == rootEl && !this.options.group.revertClone ? rootEl.insertBefore(cloneEl, dragEl) : nextEl ? rootEl.insertBefore(cloneEl, nextEl) : rootEl.appendChild(cloneEl), this.options.group.revertClone && this.animate(dragEl, cloneEl), css(cloneEl, "display", ""), cloneHidden = false;
        }
      } };
      function _globalDragOver(o) {
        o.dataTransfer && (o.dataTransfer.dropEffect = "move"), o.cancelable && o.preventDefault();
      }
      function _onMove(o, r, t, s, n, d, l, a) {
        var u, c = o[expando], f = c.options.onMove, m;
        return window.CustomEvent && !IE11OrLess && !Edge ? u = new CustomEvent("move", { bubbles: true, cancelable: true }) : (u = document.createEvent("Event"), u.initEvent("move", true, true)), u.to = r, u.from = o, u.dragged = t, u.draggedRect = s, u.related = n || r, u.relatedRect = d || getRect(r), u.willInsertAfter = a, u.originalEvent = l, o.dispatchEvent(u), f && (m = f.call(c, u, l)), m;
      }
      function _disableDraggable(o) {
        o.draggable = false;
      }
      function _unsilent() {
        _silent = false;
      }
      function _ghostIsFirst(o, r, t) {
        var s = getRect(getChild(t.el, 0, t.options, true)), n = 10;
        return r ? o.clientX < s.left - n || o.clientY < s.top && o.clientX < s.right : o.clientY < s.top - n || o.clientY < s.bottom && o.clientX < s.left;
      }
      function _ghostIsLast(o, r, t) {
        var s = getRect(lastChild(t.el, t.options.draggable)), n = 10;
        return r ? o.clientX > s.right + n || o.clientX <= s.right && o.clientY > s.bottom && o.clientX >= s.left : o.clientX > s.right && o.clientY > s.top || o.clientX <= s.right && o.clientY > s.bottom + n;
      }
      function _getSwapDirection(o, r, t, s, n, d, l, a) {
        var u = s ? o.clientY : o.clientX, c = s ? t.height : t.width, f = s ? t.top : t.left, m = s ? t.bottom : t.right, $ = false;
        if (!l) {
          if (a && targetMoveDistance < c * n) {
            if (!pastFirstInvertThresh && (lastDirection === 1 ? u > f + c * d / 2 : u < m - c * d / 2) && (pastFirstInvertThresh = true), pastFirstInvertThresh) $ = true;
            else if (lastDirection === 1 ? u < f + targetMoveDistance : u > m - targetMoveDistance) return -lastDirection;
          } else if (u > f + c * (1 - n) / 2 && u < m - c * (1 - n) / 2) return _getInsertDirection(r);
        }
        return $ = $ || l, $ && (u < f + c * d / 2 || u > m - c * d / 2) ? u > f + c / 2 ? 1 : -1 : 0;
      }
      function _getInsertDirection(o) {
        return index$1(dragEl) < index$1(o) ? 1 : -1;
      }
      function _generateId(o) {
        for (var r = o.tagName + o.className + o.src + o.href + o.textContent, t = r.length, s = 0; t--; ) s += r.charCodeAt(t);
        return s.toString(36);
      }
      function _saveInputCheckedState(o) {
        savedInputChecked.length = 0;
        for (var r = o.getElementsByTagName("input"), t = r.length; t--; ) {
          var s = r[t];
          s.checked && savedInputChecked.push(s);
        }
      }
      function _nextTick(o) {
        return setTimeout(o, 0);
      }
      function _cancelNextTick(o) {
        return clearTimeout(o);
      }
      documentExists && on(document, "touchmove", function(o) {
        (Sortable.active || awaitingDragStarted) && o.cancelable && o.preventDefault();
      }), Sortable.utils = { on, off, css, find, is: function(r, t) {
        return !!closest(r, t, r, false);
      }, extend, throttle, closest, toggleClass, clone, index: index$1, nextTick: _nextTick, cancelNextTick: _cancelNextTick, detectDirection: _detectDirection, getChild }, Sortable.get = function(o) {
        return o[expando];
      }, Sortable.mount = function() {
        for (var o = arguments.length, r = new Array(o), t = 0; t < o; t++) r[t] = arguments[t];
        r[0].constructor === Array && (r = r[0]), r.forEach(function(s) {
          if (!s.prototype || !s.prototype.constructor) throw "Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(s));
          s.utils && (Sortable.utils = _objectSpread2(_objectSpread2({}, Sortable.utils), s.utils)), PluginManager.mount(s);
        });
      }, Sortable.create = function(o, r) {
        return new Sortable(o, r);
      }, Sortable.version = version$1;
      var autoScrolls = [], scrollEl, scrollRootEl, scrolling = false, lastAutoScrollX, lastAutoScrollY, touchEvt$1, pointerElemChangedInterval;
      function AutoScrollPlugin() {
        function o() {
          this.defaults = { scroll: true, forceAutoScrollFallback: false, scrollSensitivity: 30, scrollSpeed: 10, bubbleScroll: true };
          for (var r in this) r.charAt(0) === "_" && typeof this[r] == "function" && (this[r] = this[r].bind(this));
        }
        return o.prototype = { dragStarted: function(t) {
          var s = t.originalEvent;
          this.sortable.nativeDraggable ? on(document, "dragover", this._handleAutoScroll) : this.options.supportPointer ? on(document, "pointermove", this._handleFallbackAutoScroll) : s.touches ? on(document, "touchmove", this._handleFallbackAutoScroll) : on(document, "mousemove", this._handleFallbackAutoScroll);
        }, dragOverCompleted: function(t) {
          var s = t.originalEvent;
          !this.options.dragOverBubble && !s.rootEl && this._handleAutoScroll(s);
        }, drop: function() {
          this.sortable.nativeDraggable ? off(document, "dragover", this._handleAutoScroll) : (off(document, "pointermove", this._handleFallbackAutoScroll), off(document, "touchmove", this._handleFallbackAutoScroll), off(document, "mousemove", this._handleFallbackAutoScroll)), clearPointerElemChangedInterval(), clearAutoScrolls(), cancelThrottle();
        }, nulling: function() {
          touchEvt$1 = scrollRootEl = scrollEl = scrolling = pointerElemChangedInterval = lastAutoScrollX = lastAutoScrollY = null, autoScrolls.length = 0;
        }, _handleFallbackAutoScroll: function(t) {
          this._handleAutoScroll(t, true);
        }, _handleAutoScroll: function(t, s) {
          var n = this, d = (t.touches ? t.touches[0] : t).clientX, l = (t.touches ? t.touches[0] : t).clientY, a = document.elementFromPoint(d, l);
          if (touchEvt$1 = t, s || this.options.forceAutoScrollFallback || Edge || IE11OrLess || Safari) {
            autoScroll(t, this.options, a, s);
            var u = getParentAutoScrollElement(a, true);
            scrolling && (!pointerElemChangedInterval || d !== lastAutoScrollX || l !== lastAutoScrollY) && (pointerElemChangedInterval && clearPointerElemChangedInterval(), pointerElemChangedInterval = setInterval(function() {
              var c = getParentAutoScrollElement(document.elementFromPoint(d, l), true);
              c !== u && (u = c, clearAutoScrolls()), autoScroll(t, n.options, c, s);
            }, 10), lastAutoScrollX = d, lastAutoScrollY = l);
          } else {
            if (!this.options.bubbleScroll || getParentAutoScrollElement(a, true) === getWindowScrollingElement()) {
              clearAutoScrolls();
              return;
            }
            autoScroll(t, this.options, getParentAutoScrollElement(a, false), false);
          }
        } }, _extends(o, { pluginName: "scroll", initializeByDefault: true });
      }
      function clearAutoScrolls() {
        autoScrolls.forEach(function(o) {
          clearInterval(o.pid);
        }), autoScrolls = [];
      }
      function clearPointerElemChangedInterval() {
        clearInterval(pointerElemChangedInterval);
      }
      var autoScroll = throttle(function(o, r, t, s) {
        if (r.scroll) {
          var n = (o.touches ? o.touches[0] : o).clientX, d = (o.touches ? o.touches[0] : o).clientY, l = r.scrollSensitivity, a = r.scrollSpeed, u = getWindowScrollingElement(), c = false, f;
          scrollRootEl !== t && (scrollRootEl = t, clearAutoScrolls(), scrollEl = r.scroll, f = r.scrollFn, scrollEl === true && (scrollEl = getParentAutoScrollElement(t, true)));
          var m = 0, $ = scrollEl;
          do {
            var p = $, g = getRect(p), b = g.top, h = g.bottom, q = g.left, v = g.right, y = g.width, w = g.height, T = void 0, E = void 0, P = p.scrollWidth, V = p.scrollHeight, S = css(p), k = p.scrollLeft, _ = p.scrollTop;
            p === u ? (T = y < P && (S.overflowX === "auto" || S.overflowX === "scroll" || S.overflowX === "visible"), E = w < V && (S.overflowY === "auto" || S.overflowY === "scroll" || S.overflowY === "visible")) : (T = y < P && (S.overflowX === "auto" || S.overflowX === "scroll"), E = w < V && (S.overflowY === "auto" || S.overflowY === "scroll"));
            var W = T && (Math.abs(v - n) <= l && k + y < P) - (Math.abs(q - n) <= l && !!k), B = E && (Math.abs(h - d) <= l && _ + w < V) - (Math.abs(b - d) <= l && !!_);
            if (!autoScrolls[m]) for (var O = 0; O <= m; O++) autoScrolls[O] || (autoScrolls[O] = {});
            (autoScrolls[m].vx != W || autoScrolls[m].vy != B || autoScrolls[m].el !== p) && (autoScrolls[m].el = p, autoScrolls[m].vx = W, autoScrolls[m].vy = B, clearInterval(autoScrolls[m].pid), (W != 0 || B != 0) && (c = true, autoScrolls[m].pid = setInterval(function() {
              s && this.layer === 0 && Sortable.active._onTouchMove(touchEvt$1);
              var j = autoScrolls[this.layer].vy ? autoScrolls[this.layer].vy * a : 0, I = autoScrolls[this.layer].vx ? autoScrolls[this.layer].vx * a : 0;
              typeof f == "function" && f.call(Sortable.dragged.parentNode[expando], I, j, o, touchEvt$1, autoScrolls[this.layer].el) !== "continue" || scrollBy(autoScrolls[this.layer].el, I, j);
            }.bind({ layer: m }), 24))), m++;
          } while (r.bubbleScroll && $ !== u && ($ = getParentAutoScrollElement($, false)));
          scrolling = c;
        }
      }, 30), drop = function(r) {
        var t = r.originalEvent, s = r.putSortable, n = r.dragEl, d = r.activeSortable, l = r.dispatchSortableEvent, a = r.hideGhostForTarget, u = r.unhideGhostForTarget;
        if (t) {
          var c = s || d;
          a();
          var f = t.changedTouches && t.changedTouches.length ? t.changedTouches[0] : t, m = document.elementFromPoint(f.clientX, f.clientY);
          u(), c && !c.el.contains(m) && (l("spill"), this.onSpill({ dragEl: n, putSortable: s }));
        }
      };
      function Revert() {
      }
      Revert.prototype = { startIndex: null, dragStart: function(r) {
        var t = r.oldDraggableIndex;
        this.startIndex = t;
      }, onSpill: function(r) {
        var t = r.dragEl, s = r.putSortable;
        this.sortable.captureAnimationState(), s && s.captureAnimationState();
        var n = getChild(this.sortable.el, this.startIndex, this.options);
        n ? this.sortable.el.insertBefore(t, n) : this.sortable.el.appendChild(t), this.sortable.animateAll(), s && s.animateAll();
      }, drop }, _extends(Revert, { pluginName: "revertOnSpill" });
      function Remove() {
      }
      Remove.prototype = { onSpill: function(r) {
        var t = r.dragEl, s = r.putSortable, n = s || this.sortable;
        n.captureAnimationState(), t.parentNode && t.parentNode.removeChild(t), n.animateAll();
      }, drop }, _extends(Remove, { pluginName: "removeOnSpill" });
      var lastSwapEl;
      function SwapPlugin() {
        function o() {
          this.defaults = { swapClass: "sortable-swap-highlight" };
        }
        return o.prototype = { dragStart: function(t) {
          var s = t.dragEl;
          lastSwapEl = s;
        }, dragOverValid: function(t) {
          var s = t.completed, n = t.target, d = t.onMove, l = t.activeSortable, a = t.changed, u = t.cancel;
          if (l.options.swap) {
            var c = this.sortable.el, f = this.options;
            if (n && n !== c) {
              var m = lastSwapEl;
              d(n) !== false ? (toggleClass(n, f.swapClass, true), lastSwapEl = n) : lastSwapEl = null, m && m !== lastSwapEl && toggleClass(m, f.swapClass, false);
            }
            a(), s(true), u();
          }
        }, drop: function(t) {
          var s = t.activeSortable, n = t.putSortable, d = t.dragEl, l = n || this.sortable, a = this.options;
          lastSwapEl && toggleClass(lastSwapEl, a.swapClass, false), lastSwapEl && (a.swap || n && n.options.swap) && d !== lastSwapEl && (l.captureAnimationState(), l !== s && s.captureAnimationState(), swapNodes(d, lastSwapEl), l.animateAll(), l !== s && s.animateAll());
        }, nulling: function() {
          lastSwapEl = null;
        } }, _extends(o, { pluginName: "swap", eventProperties: function() {
          return { swapItem: lastSwapEl };
        } });
      }
      function swapNodes(o, r) {
        var t = o.parentNode, s = r.parentNode, n, d;
        !t || !s || t.isEqualNode(r) || s.isEqualNode(o) || (n = index$1(o), d = index$1(r), t.isEqualNode(s) && n < d && d++, t.insertBefore(r, t.children[n]), s.insertBefore(o, s.children[d]));
      }
      var multiDragElements = [], multiDragClones = [], lastMultiDragSelect, multiDragSortable, initialFolding = false, folding = false, dragStarted = false, dragEl$1, clonesFromRect, clonesHidden;
      function MultiDragPlugin() {
        function o(r) {
          for (var t in this) t.charAt(0) === "_" && typeof this[t] == "function" && (this[t] = this[t].bind(this));
          r.options.supportPointer ? on(document, "pointerup", this._deselectMultiDrag) : (on(document, "mouseup", this._deselectMultiDrag), on(document, "touchend", this._deselectMultiDrag)), on(document, "keydown", this._checkKeyDown), on(document, "keyup", this._checkKeyUp), this.defaults = { selectedClass: "sortable-selected", multiDragKey: null, setData: function(n, d) {
            var l = "";
            multiDragElements.length && multiDragSortable === r ? multiDragElements.forEach(function(a, u) {
              l += (u ? ", " : "") + a.textContent;
            }) : l = d.textContent, n.setData("Text", l);
          } };
        }
        return o.prototype = { multiDragKeyDown: false, isMultiDrag: false, delayStartGlobal: function(t) {
          var s = t.dragEl;
          dragEl$1 = s;
        }, delayEnded: function() {
          this.isMultiDrag = ~multiDragElements.indexOf(dragEl$1);
        }, setupClone: function(t) {
          var s = t.sortable, n = t.cancel;
          if (this.isMultiDrag) {
            for (var d = 0; d < multiDragElements.length; d++) multiDragClones.push(clone(multiDragElements[d])), multiDragClones[d].sortableIndex = multiDragElements[d].sortableIndex, multiDragClones[d].draggable = false, multiDragClones[d].style["will-change"] = "", toggleClass(multiDragClones[d], this.options.selectedClass, false), multiDragElements[d] === dragEl$1 && toggleClass(multiDragClones[d], this.options.chosenClass, false);
            s._hideClone(), n();
          }
        }, clone: function(t) {
          var s = t.sortable, n = t.rootEl, d = t.dispatchSortableEvent, l = t.cancel;
          this.isMultiDrag && (this.options.removeCloneOnHide || multiDragElements.length && multiDragSortable === s && (insertMultiDragClones(true, n), d("clone"), l()));
        }, showClone: function(t) {
          var s = t.cloneNowShown, n = t.rootEl, d = t.cancel;
          this.isMultiDrag && (insertMultiDragClones(false, n), multiDragClones.forEach(function(l) {
            css(l, "display", "");
          }), s(), clonesHidden = false, d());
        }, hideClone: function(t) {
          var s = this;
          t.sortable;
          var n = t.cloneNowHidden, d = t.cancel;
          this.isMultiDrag && (multiDragClones.forEach(function(l) {
            css(l, "display", "none"), s.options.removeCloneOnHide && l.parentNode && l.parentNode.removeChild(l);
          }), n(), clonesHidden = true, d());
        }, dragStartGlobal: function(t) {
          t.sortable, !this.isMultiDrag && multiDragSortable && multiDragSortable.multiDrag._deselectMultiDrag(), multiDragElements.forEach(function(s) {
            s.sortableIndex = index$1(s);
          }), multiDragElements = multiDragElements.sort(function(s, n) {
            return s.sortableIndex - n.sortableIndex;
          }), dragStarted = true;
        }, dragStarted: function(t) {
          var s = this, n = t.sortable;
          if (this.isMultiDrag) {
            if (this.options.sort && (n.captureAnimationState(), this.options.animation)) {
              multiDragElements.forEach(function(l) {
                l !== dragEl$1 && css(l, "position", "absolute");
              });
              var d = getRect(dragEl$1, false, true, true);
              multiDragElements.forEach(function(l) {
                l !== dragEl$1 && setRect(l, d);
              }), folding = true, initialFolding = true;
            }
            n.animateAll(function() {
              folding = false, initialFolding = false, s.options.animation && multiDragElements.forEach(function(l) {
                unsetRect(l);
              }), s.options.sort && removeMultiDragElements();
            });
          }
        }, dragOver: function(t) {
          var s = t.target, n = t.completed, d = t.cancel;
          folding && ~multiDragElements.indexOf(s) && (n(false), d());
        }, revert: function(t) {
          var s = t.fromSortable, n = t.rootEl, d = t.sortable, l = t.dragRect;
          multiDragElements.length > 1 && (multiDragElements.forEach(function(a) {
            d.addAnimationState({ target: a, rect: folding ? getRect(a) : l }), unsetRect(a), a.fromRect = l, s.removeAnimationState(a);
          }), folding = false, insertMultiDragElements(!this.options.removeCloneOnHide, n));
        }, dragOverCompleted: function(t) {
          var s = t.sortable, n = t.isOwner, d = t.insertion, l = t.activeSortable, a = t.parentEl, u = t.putSortable, c = this.options;
          if (d) {
            if (n && l._hideClone(), initialFolding = false, c.animation && multiDragElements.length > 1 && (folding || !n && !l.options.sort && !u)) {
              var f = getRect(dragEl$1, false, true, true);
              multiDragElements.forEach(function($) {
                $ !== dragEl$1 && (setRect($, f), a.appendChild($));
              }), folding = true;
            }
            if (!n) if (folding || removeMultiDragElements(), multiDragElements.length > 1) {
              var m = clonesHidden;
              l._showClone(s), l.options.animation && !clonesHidden && m && multiDragClones.forEach(function($) {
                l.addAnimationState({ target: $, rect: clonesFromRect }), $.fromRect = clonesFromRect, $.thisAnimationDuration = null;
              });
            } else l._showClone(s);
          }
        }, dragOverAnimationCapture: function(t) {
          var s = t.dragRect, n = t.isOwner, d = t.activeSortable;
          if (multiDragElements.forEach(function(a) {
            a.thisAnimationDuration = null;
          }), d.options.animation && !n && d.multiDrag.isMultiDrag) {
            clonesFromRect = _extends({}, s);
            var l = matrix(dragEl$1, true);
            clonesFromRect.top -= l.f, clonesFromRect.left -= l.e;
          }
        }, dragOverAnimationComplete: function() {
          folding && (folding = false, removeMultiDragElements());
        }, drop: function(t) {
          var s = t.originalEvent, n = t.rootEl, d = t.parentEl, l = t.sortable, a = t.dispatchSortableEvent, u = t.oldIndex, c = t.putSortable, f = c || this.sortable;
          if (s) {
            var m = this.options, $ = d.children;
            if (!dragStarted) if (m.multiDragKey && !this.multiDragKeyDown && this._deselectMultiDrag(), toggleClass(dragEl$1, m.selectedClass, !~multiDragElements.indexOf(dragEl$1)), ~multiDragElements.indexOf(dragEl$1)) multiDragElements.splice(multiDragElements.indexOf(dragEl$1), 1), lastMultiDragSelect = null, dispatchEvent({ sortable: l, rootEl: n, name: "deselect", targetEl: dragEl$1, originalEvt: s });
            else {
              if (multiDragElements.push(dragEl$1), dispatchEvent({ sortable: l, rootEl: n, name: "select", targetEl: dragEl$1, originalEvt: s }), s.shiftKey && lastMultiDragSelect && l.el.contains(lastMultiDragSelect)) {
                var p = index$1(lastMultiDragSelect), g = index$1(dragEl$1);
                if (~p && ~g && p !== g) {
                  var b, h;
                  for (g > p ? (h = p, b = g) : (h = g, b = p + 1); h < b; h++) ~multiDragElements.indexOf($[h]) || (toggleClass($[h], m.selectedClass, true), multiDragElements.push($[h]), dispatchEvent({ sortable: l, rootEl: n, name: "select", targetEl: $[h], originalEvt: s }));
                }
              } else lastMultiDragSelect = dragEl$1;
              multiDragSortable = f;
            }
            if (dragStarted && this.isMultiDrag) {
              if (folding = false, (d[expando].options.sort || d !== n) && multiDragElements.length > 1) {
                var q = getRect(dragEl$1), v = index$1(dragEl$1, ":not(." + this.options.selectedClass + ")");
                if (!initialFolding && m.animation && (dragEl$1.thisAnimationDuration = null), f.captureAnimationState(), !initialFolding && (m.animation && (dragEl$1.fromRect = q, multiDragElements.forEach(function(w) {
                  if (w.thisAnimationDuration = null, w !== dragEl$1) {
                    var T = folding ? getRect(w) : q;
                    w.fromRect = T, f.addAnimationState({ target: w, rect: T });
                  }
                })), removeMultiDragElements(), multiDragElements.forEach(function(w) {
                  $[v] ? d.insertBefore(w, $[v]) : d.appendChild(w), v++;
                }), u === index$1(dragEl$1))) {
                  var y = false;
                  multiDragElements.forEach(function(w) {
                    if (w.sortableIndex !== index$1(w)) {
                      y = true;
                      return;
                    }
                  }), y && a("update");
                }
                multiDragElements.forEach(function(w) {
                  unsetRect(w);
                }), f.animateAll();
              }
              multiDragSortable = f;
            }
            (n === d || c && c.lastPutMode !== "clone") && multiDragClones.forEach(function(w) {
              w.parentNode && w.parentNode.removeChild(w);
            });
          }
        }, nullingGlobal: function() {
          this.isMultiDrag = dragStarted = false, multiDragClones.length = 0;
        }, destroyGlobal: function() {
          this._deselectMultiDrag(), off(document, "pointerup", this._deselectMultiDrag), off(document, "mouseup", this._deselectMultiDrag), off(document, "touchend", this._deselectMultiDrag), off(document, "keydown", this._checkKeyDown), off(document, "keyup", this._checkKeyUp);
        }, _deselectMultiDrag: function(t) {
          if (!(typeof dragStarted < "u" && dragStarted) && multiDragSortable === this.sortable && !(t && closest(t.target, this.options.draggable, this.sortable.el, false)) && !(t && t.button !== 0)) for (; multiDragElements.length; ) {
            var s = multiDragElements[0];
            toggleClass(s, this.options.selectedClass, false), multiDragElements.shift(), dispatchEvent({ sortable: this.sortable, rootEl: this.sortable.el, name: "deselect", targetEl: s, originalEvt: t });
          }
        }, _checkKeyDown: function(t) {
          t.key === this.options.multiDragKey && (this.multiDragKeyDown = true);
        }, _checkKeyUp: function(t) {
          t.key === this.options.multiDragKey && (this.multiDragKeyDown = false);
        } }, _extends(o, { pluginName: "multiDrag", utils: { select: function(t) {
          var s = t.parentNode[expando];
          !s || !s.options.multiDrag || ~multiDragElements.indexOf(t) || (multiDragSortable && multiDragSortable !== s && (multiDragSortable.multiDrag._deselectMultiDrag(), multiDragSortable = s), toggleClass(t, s.options.selectedClass, true), multiDragElements.push(t));
        }, deselect: function(t) {
          var s = t.parentNode[expando], n = multiDragElements.indexOf(t);
          !s || !s.options.multiDrag || !~n || (toggleClass(t, s.options.selectedClass, false), multiDragElements.splice(n, 1));
        } }, eventProperties: function() {
          var t = this, s = [], n = [];
          return multiDragElements.forEach(function(d) {
            s.push({ multiDragElement: d, index: d.sortableIndex });
            var l;
            folding && d !== dragEl$1 ? l = -1 : folding ? l = index$1(d, ":not(." + t.options.selectedClass + ")") : l = index$1(d), n.push({ multiDragElement: d, index: l });
          }), { items: _toConsumableArray(multiDragElements), clones: [].concat(multiDragClones), oldIndicies: s, newIndicies: n };
        }, optionListeners: { multiDragKey: function(t) {
          return t = t.toLowerCase(), t === "ctrl" ? t = "Control" : t.length > 1 && (t = t.charAt(0).toUpperCase() + t.substr(1)), t;
        } } });
      }
      function insertMultiDragElements(o, r) {
        multiDragElements.forEach(function(t, s) {
          var n = r.children[t.sortableIndex + (o ? Number(s) : 0)];
          n ? r.insertBefore(t, n) : r.appendChild(t);
        });
      }
      function insertMultiDragClones(o, r) {
        multiDragClones.forEach(function(t, s) {
          var n = r.children[t.sortableIndex + (o ? Number(s) : 0)];
          n ? r.insertBefore(t, n) : r.appendChild(t);
        });
      }
      function removeMultiDragElements() {
        multiDragElements.forEach(function(o) {
          o !== dragEl$1 && o.parentNode && o.parentNode.removeChild(o);
        });
      }
      Sortable.mount(new AutoScrollPlugin()), Sortable.mount(Remove, Revert);
      const sortable_esm = Object.freeze(Object.defineProperty({ __proto__: null, MultiDrag: MultiDragPlugin, Sortable, Swap: SwapPlugin, default: Sortable }, Symbol.toStringTag, { value: "Module" })), require$$1 = getAugmentedNamespace(sortable_esm);
      (function(o, r) {
        (function(s, n) {
          o.exports = n(require$$0, require$$1);
        })(typeof self < "u" ? self : commonjsGlobal, function(t, s) {
          return function(n) {
            var d = {};
            function l(a) {
              if (d[a]) return d[a].exports;
              var u = d[a] = { i: a, l: false, exports: {} };
              return n[a].call(u.exports, u, u.exports, l), u.l = true, u.exports;
            }
            return l.m = n, l.c = d, l.d = function(a, u, c) {
              l.o(a, u) || Object.defineProperty(a, u, { enumerable: true, get: c });
            }, l.r = function(a) {
              typeof Symbol < "u" && Symbol.toStringTag && Object.defineProperty(a, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(a, "__esModule", { value: true });
            }, l.t = function(a, u) {
              if (u & 1 && (a = l(a)), u & 8 || u & 4 && typeof a == "object" && a && a.__esModule) return a;
              var c = /* @__PURE__ */ Object.create(null);
              if (l.r(c), Object.defineProperty(c, "default", { enumerable: true, value: a }), u & 2 && typeof a != "string") for (var f in a) l.d(c, f, function(m) {
                return a[m];
              }.bind(null, f));
              return c;
            }, l.n = function(a) {
              var u = a && a.__esModule ? function() {
                return a.default;
              } : function() {
                return a;
              };
              return l.d(u, "a", u), u;
            }, l.o = function(a, u) {
              return Object.prototype.hasOwnProperty.call(a, u);
            }, l.p = "", l(l.s = "fb15");
          }({ "00ee": function(n, d, l) {
            var a = l("b622"), u = a("toStringTag"), c = {};
            c[u] = "z", n.exports = String(c) === "[object z]";
          }, "0366": function(n, d, l) {
            var a = l("1c0b");
            n.exports = function(u, c, f) {
              if (a(u), c === void 0) return u;
              switch (f) {
                case 0:
                  return function() {
                    return u.call(c);
                  };
                case 1:
                  return function(m) {
                    return u.call(c, m);
                  };
                case 2:
                  return function(m, $) {
                    return u.call(c, m, $);
                  };
                case 3:
                  return function(m, $, p) {
                    return u.call(c, m, $, p);
                  };
              }
              return function() {
                return u.apply(c, arguments);
              };
            };
          }, "057f": function(n, d, l) {
            var a = l("fc6a"), u = l("241c").f, c = {}.toString, f = typeof window == "object" && window && Object.getOwnPropertyNames ? Object.getOwnPropertyNames(window) : [], m = function($) {
              try {
                return u($);
              } catch {
                return f.slice();
              }
            };
            n.exports.f = function(p) {
              return f && c.call(p) == "[object Window]" ? m(p) : u(a(p));
            };
          }, "06cf": function(n, d, l) {
            var a = l("83ab"), u = l("d1e7"), c = l("5c6c"), f = l("fc6a"), m = l("c04e"), $ = l("5135"), p = l("0cfb"), g = Object.getOwnPropertyDescriptor;
            d.f = a ? g : function(h, q) {
              if (h = f(h), q = m(q, true), p) try {
                return g(h, q);
              } catch {
              }
              if ($(h, q)) return c(!u.f.call(h, q), h[q]);
            };
          }, "0cfb": function(n, d, l) {
            var a = l("83ab"), u = l("d039"), c = l("cc12");
            n.exports = !a && !u(function() {
              return Object.defineProperty(c("div"), "a", { get: function() {
                return 7;
              } }).a != 7;
            });
          }, "13d5": function(n, d, l) {
            var a = l("23e7"), u = l("d58f").left, c = l("a640"), f = l("ae40"), m = c("reduce"), $ = f("reduce", { 1: 0 });
            a({ target: "Array", proto: true, forced: !m || !$ }, { reduce: function(g) {
              return u(this, g, arguments.length, arguments.length > 1 ? arguments[1] : void 0);
            } });
          }, "14c3": function(n, d, l) {
            var a = l("c6b6"), u = l("9263");
            n.exports = function(c, f) {
              var m = c.exec;
              if (typeof m == "function") {
                var $ = m.call(c, f);
                if (typeof $ != "object") throw TypeError("RegExp exec method returned something other than an Object or null");
                return $;
              }
              if (a(c) !== "RegExp") throw TypeError("RegExp#exec called on incompatible receiver");
              return u.call(c, f);
            };
          }, "159b": function(n, d, l) {
            var a = l("da84"), u = l("fdbc"), c = l("17c2"), f = l("9112");
            for (var m in u) {
              var $ = a[m], p = $ && $.prototype;
              if (p && p.forEach !== c) try {
                f(p, "forEach", c);
              } catch {
                p.forEach = c;
              }
            }
          }, "17c2": function(n, d, l) {
            var a = l("b727").forEach, u = l("a640"), c = l("ae40"), f = u("forEach"), m = c("forEach");
            n.exports = !f || !m ? function(p) {
              return a(this, p, arguments.length > 1 ? arguments[1] : void 0);
            } : [].forEach;
          }, "1be4": function(n, d, l) {
            var a = l("d066");
            n.exports = a("document", "documentElement");
          }, "1c0b": function(n, d) {
            n.exports = function(l) {
              if (typeof l != "function") throw TypeError(String(l) + " is not a function");
              return l;
            };
          }, "1c7e": function(n, d, l) {
            var a = l("b622"), u = a("iterator"), c = false;
            try {
              var f = 0, m = { next: function() {
                return { done: !!f++ };
              }, return: function() {
                c = true;
              } };
              m[u] = function() {
                return this;
              }, Array.from(m, function() {
                throw 2;
              });
            } catch {
            }
            n.exports = function($, p) {
              if (!p && !c) return false;
              var g = false;
              try {
                var b = {};
                b[u] = function() {
                  return { next: function() {
                    return { done: g = true };
                  } };
                }, $(b);
              } catch {
              }
              return g;
            };
          }, "1d80": function(n, d) {
            n.exports = function(l) {
              if (l == null) throw TypeError("Can't call method on " + l);
              return l;
            };
          }, "1dde": function(n, d, l) {
            var a = l("d039"), u = l("b622"), c = l("2d00"), f = u("species");
            n.exports = function(m) {
              return c >= 51 || !a(function() {
                var $ = [], p = $.constructor = {};
                return p[f] = function() {
                  return { foo: 1 };
                }, $[m](Boolean).foo !== 1;
              });
            };
          }, "23cb": function(n, d, l) {
            var a = l("a691"), u = Math.max, c = Math.min;
            n.exports = function(f, m) {
              var $ = a(f);
              return $ < 0 ? u($ + m, 0) : c($, m);
            };
          }, "23e7": function(n, d, l) {
            var a = l("da84"), u = l("06cf").f, c = l("9112"), f = l("6eeb"), m = l("ce4e"), $ = l("e893"), p = l("94ca");
            n.exports = function(g, b) {
              var h = g.target, q = g.global, v = g.stat, y, w, T, E, P, V;
              if (q ? w = a : v ? w = a[h] || m(h, {}) : w = (a[h] || {}).prototype, w) for (T in b) {
                if (P = b[T], g.noTargetGet ? (V = u(w, T), E = V && V.value) : E = w[T], y = p(q ? T : h + (v ? "." : "#") + T, g.forced), !y && E !== void 0) {
                  if (typeof P == typeof E) continue;
                  $(P, E);
                }
                (g.sham || E && E.sham) && c(P, "sham", true), f(w, T, P, g);
              }
            };
          }, "241c": function(n, d, l) {
            var a = l("ca84"), u = l("7839"), c = u.concat("length", "prototype");
            d.f = Object.getOwnPropertyNames || function(m) {
              return a(m, c);
            };
          }, "25f0": function(n, d, l) {
            var a = l("6eeb"), u = l("825a"), c = l("d039"), f = l("ad6d"), m = "toString", $ = RegExp.prototype, p = $[m], g = c(function() {
              return p.call({ source: "a", flags: "b" }) != "/a/b";
            }), b = p.name != m;
            (g || b) && a(RegExp.prototype, m, function() {
              var q = u(this), v = String(q.source), y = q.flags, w = String(y === void 0 && q instanceof RegExp && !("flags" in $) ? f.call(q) : y);
              return "/" + v + "/" + w;
            }, { unsafe: true });
          }, "2ca0": function(n, d, l) {
            var a = l("23e7"), u = l("06cf").f, c = l("50c4"), f = l("5a34"), m = l("1d80"), $ = l("ab13"), p = l("c430"), g = "".startsWith, b = Math.min, h = $("startsWith"), q = !p && !h && !!function() {
              var v = u(String.prototype, "startsWith");
              return v && !v.writable;
            }();
            a({ target: "String", proto: true, forced: !q && !h }, { startsWith: function(y) {
              var w = String(m(this));
              f(y);
              var T = c(b(arguments.length > 1 ? arguments[1] : void 0, w.length)), E = String(y);
              return g ? g.call(w, E, T) : w.slice(T, T + E.length) === E;
            } });
          }, "2d00": function(n, d, l) {
            var a = l("da84"), u = l("342f"), c = a.process, f = c && c.versions, m = f && f.v8, $, p;
            m ? ($ = m.split("."), p = $[0] + $[1]) : u && ($ = u.match(/Edge\/(\d+)/), (!$ || $[1] >= 74) && ($ = u.match(/Chrome\/(\d+)/), $ && (p = $[1]))), n.exports = p && +p;
          }, "342f": function(n, d, l) {
            var a = l("d066");
            n.exports = a("navigator", "userAgent") || "";
          }, "35a1": function(n, d, l) {
            var a = l("f5df"), u = l("3f8c"), c = l("b622"), f = c("iterator");
            n.exports = function(m) {
              if (m != null) return m[f] || m["@@iterator"] || u[a(m)];
            };
          }, "37e8": function(n, d, l) {
            var a = l("83ab"), u = l("9bf2"), c = l("825a"), f = l("df75");
            n.exports = a ? Object.defineProperties : function($, p) {
              c($);
              for (var g = f(p), b = g.length, h = 0, q; b > h; ) u.f($, q = g[h++], p[q]);
              return $;
            };
          }, "3bbe": function(n, d, l) {
            var a = l("861d");
            n.exports = function(u) {
              if (!a(u) && u !== null) throw TypeError("Can't set " + String(u) + " as a prototype");
              return u;
            };
          }, "3ca3": function(n, d, l) {
            var a = l("6547").charAt, u = l("69f3"), c = l("7dd0"), f = "String Iterator", m = u.set, $ = u.getterFor(f);
            c(String, "String", function(p) {
              m(this, { type: f, string: String(p), index: 0 });
            }, function() {
              var g = $(this), b = g.string, h = g.index, q;
              return h >= b.length ? { value: void 0, done: true } : (q = a(b, h), g.index += q.length, { value: q, done: false });
            });
          }, "3f8c": function(n, d) {
            n.exports = {};
          }, 4160: function(n, d, l) {
            var a = l("23e7"), u = l("17c2");
            a({ target: "Array", proto: true, forced: [].forEach != u }, { forEach: u });
          }, "428f": function(n, d, l) {
            var a = l("da84");
            n.exports = a;
          }, "44ad": function(n, d, l) {
            var a = l("d039"), u = l("c6b6"), c = "".split;
            n.exports = a(function() {
              return !Object("z").propertyIsEnumerable(0);
            }) ? function(f) {
              return u(f) == "String" ? c.call(f, "") : Object(f);
            } : Object;
          }, "44d2": function(n, d, l) {
            var a = l("b622"), u = l("7c73"), c = l("9bf2"), f = a("unscopables"), m = Array.prototype;
            m[f] == null && c.f(m, f, { configurable: true, value: u(null) }), n.exports = function($) {
              m[f][$] = true;
            };
          }, "44e7": function(n, d, l) {
            var a = l("861d"), u = l("c6b6"), c = l("b622"), f = c("match");
            n.exports = function(m) {
              var $;
              return a(m) && (($ = m[f]) !== void 0 ? !!$ : u(m) == "RegExp");
            };
          }, 4930: function(n, d, l) {
            var a = l("d039");
            n.exports = !!Object.getOwnPropertySymbols && !a(function() {
              return !String(Symbol());
            });
          }, "4d64": function(n, d, l) {
            var a = l("fc6a"), u = l("50c4"), c = l("23cb"), f = function(m) {
              return function($, p, g) {
                var b = a($), h = u(b.length), q = c(g, h), v;
                if (m && p != p) {
                  for (; h > q; ) if (v = b[q++], v != v) return true;
                } else for (; h > q; q++) if ((m || q in b) && b[q] === p) return m || q || 0;
                return !m && -1;
              };
            };
            n.exports = { includes: f(true), indexOf: f(false) };
          }, "4de4": function(n, d, l) {
            var a = l("23e7"), u = l("b727").filter, c = l("1dde"), f = l("ae40"), m = c("filter"), $ = f("filter");
            a({ target: "Array", proto: true, forced: !m || !$ }, { filter: function(g) {
              return u(this, g, arguments.length > 1 ? arguments[1] : void 0);
            } });
          }, "4df4": function(n, d, l) {
            var a = l("0366"), u = l("7b0b"), c = l("9bdd"), f = l("e95a"), m = l("50c4"), $ = l("8418"), p = l("35a1");
            n.exports = function(b) {
              var h = u(b), q = typeof this == "function" ? this : Array, v = arguments.length, y = v > 1 ? arguments[1] : void 0, w = y !== void 0, T = p(h), E = 0, P, V, S, k, _, W;
              if (w && (y = a(y, v > 2 ? arguments[2] : void 0, 2)), T != null && !(q == Array && f(T))) for (k = T.call(h), _ = k.next, V = new q(); !(S = _.call(k)).done; E++) W = w ? c(k, y, [S.value, E], true) : S.value, $(V, E, W);
              else for (P = m(h.length), V = new q(P); P > E; E++) W = w ? y(h[E], E) : h[E], $(V, E, W);
              return V.length = E, V;
            };
          }, "4fad": function(n, d, l) {
            var a = l("23e7"), u = l("6f53").entries;
            a({ target: "Object", stat: true }, { entries: function(f) {
              return u(f);
            } });
          }, "50c4": function(n, d, l) {
            var a = l("a691"), u = Math.min;
            n.exports = function(c) {
              return c > 0 ? u(a(c), 9007199254740991) : 0;
            };
          }, 5135: function(n, d) {
            var l = {}.hasOwnProperty;
            n.exports = function(a, u) {
              return l.call(a, u);
            };
          }, 5319: function(n, d, l) {
            var a = l("d784"), u = l("825a"), c = l("7b0b"), f = l("50c4"), m = l("a691"), $ = l("1d80"), p = l("8aa5"), g = l("14c3"), b = Math.max, h = Math.min, q = Math.floor, v = /\$([$&'`]|\d\d?|<[^>]*>)/g, y = /\$([$&'`]|\d\d?)/g, w = function(T) {
              return T === void 0 ? T : String(T);
            };
            a("replace", 2, function(T, E, P, V) {
              var S = V.REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE, k = V.REPLACE_KEEPS_$0, _ = S ? "$" : "$0";
              return [function(O, j) {
                var I = $(this), R = O == null ? void 0 : O[T];
                return R !== void 0 ? R.call(O, I, j) : E.call(String(I), O, j);
              }, function(B, O) {
                if (!S && k || typeof O == "string" && O.indexOf(_) === -1) {
                  var j = P(E, B, this, O);
                  if (j.done) return j.value;
                }
                var I = u(B), R = String(this), H = typeof O == "function";
                H || (O = String(O));
                var X = I.global;
                if (X) {
                  var ne = I.unicode;
                  I.lastIndex = 0;
                }
                for (var Z = []; ; ) {
                  var ee = g(I, R);
                  if (ee === null || (Z.push(ee), !X)) break;
                  var oe = String(ee[0]);
                  oe === "" && (I.lastIndex = p(R, f(I.lastIndex), ne));
                }
                for (var le = "", re = 0, Q = 0; Q < Z.length; Q++) {
                  ee = Z[Q];
                  for (var J = String(ee[0]), se = b(h(m(ee.index), R.length), 0), de = [], $e = 1; $e < ee.length; $e++) de.push(w(ee[$e]));
                  var he = ee.groups;
                  if (H) {
                    var pe = [J].concat(de, se, R);
                    he !== void 0 && pe.push(he);
                    var ae = String(O.apply(void 0, pe));
                  } else ae = W(J, R, se, de, he, O);
                  se >= re && (le += R.slice(re, se) + ae, re = se + J.length);
                }
                return le + R.slice(re);
              }];
              function W(B, O, j, I, R, H) {
                var X = j + B.length, ne = I.length, Z = y;
                return R !== void 0 && (R = c(R), Z = v), E.call(H, Z, function(ee, oe) {
                  var le;
                  switch (oe.charAt(0)) {
                    case "$":
                      return "$";
                    case "&":
                      return B;
                    case "`":
                      return O.slice(0, j);
                    case "'":
                      return O.slice(X);
                    case "<":
                      le = R[oe.slice(1, -1)];
                      break;
                    default:
                      var re = +oe;
                      if (re === 0) return ee;
                      if (re > ne) {
                        var Q = q(re / 10);
                        return Q === 0 ? ee : Q <= ne ? I[Q - 1] === void 0 ? oe.charAt(1) : I[Q - 1] + oe.charAt(1) : ee;
                      }
                      le = I[re - 1];
                  }
                  return le === void 0 ? "" : le;
                });
              }
            });
          }, 5692: function(n, d, l) {
            var a = l("c430"), u = l("c6cd");
            (n.exports = function(c, f) {
              return u[c] || (u[c] = f !== void 0 ? f : {});
            })("versions", []).push({ version: "3.6.5", mode: a ? "pure" : "global", copyright: "© 2020 Denis Pushkarev (zloirock.ru)" });
          }, "56ef": function(n, d, l) {
            var a = l("d066"), u = l("241c"), c = l("7418"), f = l("825a");
            n.exports = a("Reflect", "ownKeys") || function($) {
              var p = u.f(f($)), g = c.f;
              return g ? p.concat(g($)) : p;
            };
          }, "5a34": function(n, d, l) {
            var a = l("44e7");
            n.exports = function(u) {
              if (a(u)) throw TypeError("The method doesn't accept regular expressions");
              return u;
            };
          }, "5c6c": function(n, d) {
            n.exports = function(l, a) {
              return { enumerable: !(l & 1), configurable: !(l & 2), writable: !(l & 4), value: a };
            };
          }, "5db7": function(n, d, l) {
            var a = l("23e7"), u = l("a2bf"), c = l("7b0b"), f = l("50c4"), m = l("1c0b"), $ = l("65f0");
            a({ target: "Array", proto: true }, { flatMap: function(g) {
              var b = c(this), h = f(b.length), q;
              return m(g), q = $(b, 0), q.length = u(q, b, b, h, 0, 1, g, arguments.length > 1 ? arguments[1] : void 0), q;
            } });
          }, 6547: function(n, d, l) {
            var a = l("a691"), u = l("1d80"), c = function(f) {
              return function(m, $) {
                var p = String(u(m)), g = a($), b = p.length, h, q;
                return g < 0 || g >= b ? f ? "" : void 0 : (h = p.charCodeAt(g), h < 55296 || h > 56319 || g + 1 === b || (q = p.charCodeAt(g + 1)) < 56320 || q > 57343 ? f ? p.charAt(g) : h : f ? p.slice(g, g + 2) : (h - 55296 << 10) + (q - 56320) + 65536);
              };
            };
            n.exports = { codeAt: c(false), charAt: c(true) };
          }, "65f0": function(n, d, l) {
            var a = l("861d"), u = l("e8b5"), c = l("b622"), f = c("species");
            n.exports = function(m, $) {
              var p;
              return u(m) && (p = m.constructor, typeof p == "function" && (p === Array || u(p.prototype)) ? p = void 0 : a(p) && (p = p[f], p === null && (p = void 0))), new (p === void 0 ? Array : p)($ === 0 ? 0 : $);
            };
          }, "69f3": function(n, d, l) {
            var a = l("7f9a"), u = l("da84"), c = l("861d"), f = l("9112"), m = l("5135"), $ = l("f772"), p = l("d012"), g = u.WeakMap, b, h, q, v = function(S) {
              return q(S) ? h(S) : b(S, {});
            }, y = function(S) {
              return function(k) {
                var _;
                if (!c(k) || (_ = h(k)).type !== S) throw TypeError("Incompatible receiver, " + S + " required");
                return _;
              };
            };
            if (a) {
              var w = new g(), T = w.get, E = w.has, P = w.set;
              b = function(S, k) {
                return P.call(w, S, k), k;
              }, h = function(S) {
                return T.call(w, S) || {};
              }, q = function(S) {
                return E.call(w, S);
              };
            } else {
              var V = $("state");
              p[V] = true, b = function(S, k) {
                return f(S, V, k), k;
              }, h = function(S) {
                return m(S, V) ? S[V] : {};
              }, q = function(S) {
                return m(S, V);
              };
            }
            n.exports = { set: b, get: h, has: q, enforce: v, getterFor: y };
          }, "6eeb": function(n, d, l) {
            var a = l("da84"), u = l("9112"), c = l("5135"), f = l("ce4e"), m = l("8925"), $ = l("69f3"), p = $.get, g = $.enforce, b = String(String).split("String");
            (n.exports = function(h, q, v, y) {
              var w = y ? !!y.unsafe : false, T = y ? !!y.enumerable : false, E = y ? !!y.noTargetGet : false;
              if (typeof v == "function" && (typeof q == "string" && !c(v, "name") && u(v, "name", q), g(v).source = b.join(typeof q == "string" ? q : "")), h === a) {
                T ? h[q] = v : f(q, v);
                return;
              } else w ? !E && h[q] && (T = true) : delete h[q];
              T ? h[q] = v : u(h, q, v);
            })(Function.prototype, "toString", function() {
              return typeof this == "function" && p(this).source || m(this);
            });
          }, "6f53": function(n, d, l) {
            var a = l("83ab"), u = l("df75"), c = l("fc6a"), f = l("d1e7").f, m = function($) {
              return function(p) {
                for (var g = c(p), b = u(g), h = b.length, q = 0, v = [], y; h > q; ) y = b[q++], (!a || f.call(g, y)) && v.push($ ? [y, g[y]] : g[y]);
                return v;
              };
            };
            n.exports = { entries: m(true), values: m(false) };
          }, "73d9": function(n, d, l) {
            var a = l("44d2");
            a("flatMap");
          }, 7418: function(n, d) {
            d.f = Object.getOwnPropertySymbols;
          }, "746f": function(n, d, l) {
            var a = l("428f"), u = l("5135"), c = l("e538"), f = l("9bf2").f;
            n.exports = function(m) {
              var $ = a.Symbol || (a.Symbol = {});
              u($, m) || f($, m, { value: c.f(m) });
            };
          }, 7839: function(n, d) {
            n.exports = ["constructor", "hasOwnProperty", "isPrototypeOf", "propertyIsEnumerable", "toLocaleString", "toString", "valueOf"];
          }, "7b0b": function(n, d, l) {
            var a = l("1d80");
            n.exports = function(u) {
              return Object(a(u));
            };
          }, "7c73": function(n, d, l) {
            var a = l("825a"), u = l("37e8"), c = l("7839"), f = l("d012"), m = l("1be4"), $ = l("cc12"), p = l("f772"), g = ">", b = "<", h = "prototype", q = "script", v = p("IE_PROTO"), y = function() {
            }, w = function(S) {
              return b + q + g + S + b + "/" + q + g;
            }, T = function(S) {
              S.write(w("")), S.close();
              var k = S.parentWindow.Object;
              return S = null, k;
            }, E = function() {
              var S = $("iframe"), k = "java" + q + ":", _;
              return S.style.display = "none", m.appendChild(S), S.src = String(k), _ = S.contentWindow.document, _.open(), _.write(w("document.F=Object")), _.close(), _.F;
            }, P, V = function() {
              try {
                P = document.domain && new ActiveXObject("htmlfile");
              } catch {
              }
              V = P ? T(P) : E();
              for (var S = c.length; S--; ) delete V[h][c[S]];
              return V();
            };
            f[v] = true, n.exports = Object.create || function(k, _) {
              var W;
              return k !== null ? (y[h] = a(k), W = new y(), y[h] = null, W[v] = k) : W = V(), _ === void 0 ? W : u(W, _);
            };
          }, "7dd0": function(n, d, l) {
            var a = l("23e7"), u = l("9ed3"), c = l("e163"), f = l("d2bb"), m = l("d44e"), $ = l("9112"), p = l("6eeb"), g = l("b622"), b = l("c430"), h = l("3f8c"), q = l("ae93"), v = q.IteratorPrototype, y = q.BUGGY_SAFARI_ITERATORS, w = g("iterator"), T = "keys", E = "values", P = "entries", V = function() {
              return this;
            };
            n.exports = function(S, k, _, W, B, O, j) {
              u(_, k, W);
              var I = function(Q) {
                if (Q === B && Z) return Z;
                if (!y && Q in X) return X[Q];
                switch (Q) {
                  case T:
                    return function() {
                      return new _(this, Q);
                    };
                  case E:
                    return function() {
                      return new _(this, Q);
                    };
                  case P:
                    return function() {
                      return new _(this, Q);
                    };
                }
                return function() {
                  return new _(this);
                };
              }, R = k + " Iterator", H = false, X = S.prototype, ne = X[w] || X["@@iterator"] || B && X[B], Z = !y && ne || I(B), ee = k == "Array" && X.entries || ne, oe, le, re;
              if (ee && (oe = c(ee.call(new S())), v !== Object.prototype && oe.next && (!b && c(oe) !== v && (f ? f(oe, v) : typeof oe[w] != "function" && $(oe, w, V)), m(oe, R, true, true), b && (h[R] = V))), B == E && ne && ne.name !== E && (H = true, Z = function() {
                return ne.call(this);
              }), (!b || j) && X[w] !== Z && $(X, w, Z), h[k] = Z, B) if (le = { values: I(E), keys: O ? Z : I(T), entries: I(P) }, j) for (re in le) (y || H || !(re in X)) && p(X, re, le[re]);
              else a({ target: k, proto: true, forced: y || H }, le);
              return le;
            };
          }, "7f9a": function(n, d, l) {
            var a = l("da84"), u = l("8925"), c = a.WeakMap;
            n.exports = typeof c == "function" && /native code/.test(u(c));
          }, "825a": function(n, d, l) {
            var a = l("861d");
            n.exports = function(u) {
              if (!a(u)) throw TypeError(String(u) + " is not an object");
              return u;
            };
          }, "83ab": function(n, d, l) {
            var a = l("d039");
            n.exports = !a(function() {
              return Object.defineProperty({}, 1, { get: function() {
                return 7;
              } })[1] != 7;
            });
          }, 8418: function(n, d, l) {
            var a = l("c04e"), u = l("9bf2"), c = l("5c6c");
            n.exports = function(f, m, $) {
              var p = a(m);
              p in f ? u.f(f, p, c(0, $)) : f[p] = $;
            };
          }, "861d": function(n, d) {
            n.exports = function(l) {
              return typeof l == "object" ? l !== null : typeof l == "function";
            };
          }, 8875: function(n, d, l) {
            var a, u, c;
            (function(f, m) {
              u = [], a = m, c = typeof a == "function" ? a.apply(d, u) : a, c !== void 0 && (n.exports = c);
            })(typeof self < "u" ? self : this, function() {
              function f() {
                var m = Object.getOwnPropertyDescriptor(document, "currentScript");
                if (!m && "currentScript" in document && document.currentScript || m && m.get !== f && document.currentScript) return document.currentScript;
                try {
                  throw new Error();
                } catch (P) {
                  var $ = /.*at [^(]*\((.*):(.+):(.+)\)$/ig, p = /@([^@]*):(\d+):(\d+)\s*$/ig, g = $.exec(P.stack) || p.exec(P.stack), b = g && g[1] || false, h = g && g[2] || false, q = document.location.href.replace(document.location.hash, ""), v, y, w, T = document.getElementsByTagName("script");
                  b === q && (v = document.documentElement.outerHTML, y = new RegExp("(?:[^\\n]+?\\n){0," + (h - 2) + "}[^<]*<script>([\\d\\D]*?)<\\/script>[\\d\\D]*", "i"), w = v.replace(y, "$1").trim());
                  for (var E = 0; E < T.length; E++) if (T[E].readyState === "interactive" || T[E].src === b || b === q && T[E].innerHTML && T[E].innerHTML.trim() === w) return T[E];
                  return null;
                }
              }
              return f;
            });
          }, 8925: function(n, d, l) {
            var a = l("c6cd"), u = Function.toString;
            typeof a.inspectSource != "function" && (a.inspectSource = function(c) {
              return u.call(c);
            }), n.exports = a.inspectSource;
          }, "8aa5": function(n, d, l) {
            var a = l("6547").charAt;
            n.exports = function(u, c, f) {
              return c + (f ? a(u, c).length : 1);
            };
          }, "8bbf": function(n, d) {
            n.exports = t;
          }, "90e3": function(n, d) {
            var l = 0, a = Math.random();
            n.exports = function(u) {
              return "Symbol(" + String(u === void 0 ? "" : u) + ")_" + (++l + a).toString(36);
            };
          }, 9112: function(n, d, l) {
            var a = l("83ab"), u = l("9bf2"), c = l("5c6c");
            n.exports = a ? function(f, m, $) {
              return u.f(f, m, c(1, $));
            } : function(f, m, $) {
              return f[m] = $, f;
            };
          }, 9263: function(n, d, l) {
            var a = l("ad6d"), u = l("9f7f"), c = RegExp.prototype.exec, f = String.prototype.replace, m = c, $ = function() {
              var h = /a/, q = /b*/g;
              return c.call(h, "a"), c.call(q, "a"), h.lastIndex !== 0 || q.lastIndex !== 0;
            }(), p = u.UNSUPPORTED_Y || u.BROKEN_CARET, g = /()??/.exec("")[1] !== void 0, b = $ || g || p;
            b && (m = function(q) {
              var v = this, y, w, T, E, P = p && v.sticky, V = a.call(v), S = v.source, k = 0, _ = q;
              return P && (V = V.replace("y", ""), V.indexOf("g") === -1 && (V += "g"), _ = String(q).slice(v.lastIndex), v.lastIndex > 0 && (!v.multiline || v.multiline && q[v.lastIndex - 1] !== `
`) && (S = "(?: " + S + ")", _ = " " + _, k++), w = new RegExp("^(?:" + S + ")", V)), g && (w = new RegExp("^" + S + "$(?!\\s)", V)), $ && (y = v.lastIndex), T = c.call(P ? w : v, _), P ? T ? (T.input = T.input.slice(k), T[0] = T[0].slice(k), T.index = v.lastIndex, v.lastIndex += T[0].length) : v.lastIndex = 0 : $ && T && (v.lastIndex = v.global ? T.index + T[0].length : y), g && T && T.length > 1 && f.call(T[0], w, function() {
                for (E = 1; E < arguments.length - 2; E++) arguments[E] === void 0 && (T[E] = void 0);
              }), T;
            }), n.exports = m;
          }, "94ca": function(n, d, l) {
            var a = l("d039"), u = /#|\.prototype\./, c = function(g, b) {
              var h = m[f(g)];
              return h == p ? true : h == $ ? false : typeof b == "function" ? a(b) : !!b;
            }, f = c.normalize = function(g) {
              return String(g).replace(u, ".").toLowerCase();
            }, m = c.data = {}, $ = c.NATIVE = "N", p = c.POLYFILL = "P";
            n.exports = c;
          }, "99af": function(n, d, l) {
            var a = l("23e7"), u = l("d039"), c = l("e8b5"), f = l("861d"), m = l("7b0b"), $ = l("50c4"), p = l("8418"), g = l("65f0"), b = l("1dde"), h = l("b622"), q = l("2d00"), v = h("isConcatSpreadable"), y = 9007199254740991, w = "Maximum allowed index exceeded", T = q >= 51 || !u(function() {
              var S = [];
              return S[v] = false, S.concat()[0] !== S;
            }), E = b("concat"), P = function(S) {
              if (!f(S)) return false;
              var k = S[v];
              return k !== void 0 ? !!k : c(S);
            }, V = !T || !E;
            a({ target: "Array", proto: true, forced: V }, { concat: function(k) {
              var _ = m(this), W = g(_, 0), B = 0, O, j, I, R, H;
              for (O = -1, I = arguments.length; O < I; O++) if (H = O === -1 ? _ : arguments[O], P(H)) {
                if (R = $(H.length), B + R > y) throw TypeError(w);
                for (j = 0; j < R; j++, B++) j in H && p(W, B, H[j]);
              } else {
                if (B >= y) throw TypeError(w);
                p(W, B++, H);
              }
              return W.length = B, W;
            } });
          }, "9bdd": function(n, d, l) {
            var a = l("825a");
            n.exports = function(u, c, f, m) {
              try {
                return m ? c(a(f)[0], f[1]) : c(f);
              } catch (p) {
                var $ = u.return;
                throw $ !== void 0 && a($.call(u)), p;
              }
            };
          }, "9bf2": function(n, d, l) {
            var a = l("83ab"), u = l("0cfb"), c = l("825a"), f = l("c04e"), m = Object.defineProperty;
            d.f = a ? m : function(p, g, b) {
              if (c(p), g = f(g, true), c(b), u) try {
                return m(p, g, b);
              } catch {
              }
              if ("get" in b || "set" in b) throw TypeError("Accessors not supported");
              return "value" in b && (p[g] = b.value), p;
            };
          }, "9ed3": function(n, d, l) {
            var a = l("ae93").IteratorPrototype, u = l("7c73"), c = l("5c6c"), f = l("d44e"), m = l("3f8c"), $ = function() {
              return this;
            };
            n.exports = function(p, g, b) {
              var h = g + " Iterator";
              return p.prototype = u(a, { next: c(1, b) }), f(p, h, false, true), m[h] = $, p;
            };
          }, "9f7f": function(n, d, l) {
            var a = l("d039");
            function u(c, f) {
              return RegExp(c, f);
            }
            d.UNSUPPORTED_Y = a(function() {
              var c = u("a", "y");
              return c.lastIndex = 2, c.exec("abcd") != null;
            }), d.BROKEN_CARET = a(function() {
              var c = u("^r", "gy");
              return c.lastIndex = 2, c.exec("str") != null;
            });
          }, a2bf: function(n, d, l) {
            var a = l("e8b5"), u = l("50c4"), c = l("0366"), f = function(m, $, p, g, b, h, q, v) {
              for (var y = b, w = 0, T = q ? c(q, v, 3) : false, E; w < g; ) {
                if (w in p) {
                  if (E = T ? T(p[w], w, $) : p[w], h > 0 && a(E)) y = f(m, $, E, u(E.length), y, h - 1) - 1;
                  else {
                    if (y >= 9007199254740991) throw TypeError("Exceed the acceptable array length");
                    m[y] = E;
                  }
                  y++;
                }
                w++;
              }
              return y;
            };
            n.exports = f;
          }, a352: function(n, d) {
            n.exports = s;
          }, a434: function(n, d, l) {
            var a = l("23e7"), u = l("23cb"), c = l("a691"), f = l("50c4"), m = l("7b0b"), $ = l("65f0"), p = l("8418"), g = l("1dde"), b = l("ae40"), h = g("splice"), q = b("splice", { ACCESSORS: true, 0: 0, 1: 2 }), v = Math.max, y = Math.min, w = 9007199254740991, T = "Maximum allowed length exceeded";
            a({ target: "Array", proto: true, forced: !h || !q }, { splice: function(P, V) {
              var S = m(this), k = f(S.length), _ = u(P, k), W = arguments.length, B, O, j, I, R, H;
              if (W === 0 ? B = O = 0 : W === 1 ? (B = 0, O = k - _) : (B = W - 2, O = y(v(c(V), 0), k - _)), k + B - O > w) throw TypeError(T);
              for (j = $(S, O), I = 0; I < O; I++) R = _ + I, R in S && p(j, I, S[R]);
              if (j.length = O, B < O) {
                for (I = _; I < k - O; I++) R = I + O, H = I + B, R in S ? S[H] = S[R] : delete S[H];
                for (I = k; I > k - O + B; I--) delete S[I - 1];
              } else if (B > O) for (I = k - O; I > _; I--) R = I + O - 1, H = I + B - 1, R in S ? S[H] = S[R] : delete S[H];
              for (I = 0; I < B; I++) S[I + _] = arguments[I + 2];
              return S.length = k - O + B, j;
            } });
          }, a4d3: function(n, d, l) {
            var a = l("23e7"), u = l("da84"), c = l("d066"), f = l("c430"), m = l("83ab"), $ = l("4930"), p = l("fdbf"), g = l("d039"), b = l("5135"), h = l("e8b5"), q = l("861d"), v = l("825a"), y = l("7b0b"), w = l("fc6a"), T = l("c04e"), E = l("5c6c"), P = l("7c73"), V = l("df75"), S = l("241c"), k = l("057f"), _ = l("7418"), W = l("06cf"), B = l("9bf2"), O = l("d1e7"), j = l("9112"), I = l("6eeb"), R = l("5692"), H = l("f772"), X = l("d012"), ne = l("90e3"), Z = l("b622"), ee = l("e538"), oe = l("746f"), le = l("d44e"), re = l("69f3"), Q = l("b727").forEach, J = H("hidden"), se = "Symbol", de = "prototype", $e = Z("toPrimitive"), he = re.set, pe = re.getterFor(se), ae = Object[de], ie = u.Symbol, ve = c("JSON", "stringify"), me = W.f, fe = B.f, Ne = k.f, Se = O.f, ce = R("symbols"), ge = R("op-symbols"), be = R("string-to-symbol-registry"), qe = R("symbol-to-string-registry"), Ve = R("wks"), ye = u.QObject, xe = !ye || !ye[de] || !ye[de].findChild, Ce = m && g(function() {
              return P(fe({}, "a", { get: function() {
                return fe(this, "a", { value: 7 }).a;
              } })).a != 7;
            }) ? function(L, A, F) {
              var K = me(ae, A);
              K && delete ae[A], fe(L, A, F), K && L !== ae && fe(ae, A, K);
            } : fe, we = function(L, A) {
              var F = ce[L] = P(ie[de]);
              return he(F, { type: se, tag: L, description: A }), m || (F.description = A), F;
            }, C = p ? function(L) {
              return typeof L == "symbol";
            } : function(L) {
              return Object(L) instanceof ie;
            }, x = function(A, F, K) {
              A === ae && x(ge, F, K), v(A);
              var Y = T(F, true);
              return v(K), b(ce, Y) ? (K.enumerable ? (b(A, J) && A[J][Y] && (A[J][Y] = false), K = P(K, { enumerable: E(0, false) })) : (b(A, J) || fe(A, J, E(1, {})), A[J][Y] = true), Ce(A, Y, K)) : fe(A, Y, K);
            }, N = function(A, F) {
              v(A);
              var K = w(F), Y = V(K).concat(G(K));
              return Q(Y, function(ue) {
                (!m || U.call(K, ue)) && x(A, ue, K[ue]);
              }), A;
            }, D = function(A, F) {
              return F === void 0 ? P(A) : N(P(A), F);
            }, U = function(A) {
              var F = T(A, true), K = Se.call(this, F);
              return this === ae && b(ce, F) && !b(ge, F) ? false : K || !b(this, F) || !b(ce, F) || b(this, J) && this[J][F] ? K : true;
            }, M = function(A, F) {
              var K = w(A), Y = T(F, true);
              if (!(K === ae && b(ce, Y) && !b(ge, Y))) {
                var ue = me(K, Y);
                return ue && b(ce, Y) && !(b(K, J) && K[J][Y]) && (ue.enumerable = true), ue;
              }
            }, z = function(A) {
              var F = Ne(w(A)), K = [];
              return Q(F, function(Y) {
                !b(ce, Y) && !b(X, Y) && K.push(Y);
              }), K;
            }, G = function(A) {
              var F = A === ae, K = Ne(F ? ge : w(A)), Y = [];
              return Q(K, function(ue) {
                b(ce, ue) && (!F || b(ae, ue)) && Y.push(ce[ue]);
              }), Y;
            };
            if ($ || (ie = function() {
              if (this instanceof ie) throw TypeError("Symbol is not a constructor");
              var A = !arguments.length || arguments[0] === void 0 ? void 0 : String(arguments[0]), F = ne(A), K = function(Y) {
                this === ae && K.call(ge, Y), b(this, J) && b(this[J], F) && (this[J][F] = false), Ce(this, F, E(1, Y));
              };
              return m && xe && Ce(ae, F, { configurable: true, set: K }), we(F, A);
            }, I(ie[de], "toString", function() {
              return pe(this).tag;
            }), I(ie, "withoutSetter", function(L) {
              return we(ne(L), L);
            }), O.f = U, B.f = x, W.f = M, S.f = k.f = z, _.f = G, ee.f = function(L) {
              return we(Z(L), L);
            }, m && (fe(ie[de], "description", { configurable: true, get: function() {
              return pe(this).description;
            } }), f || I(ae, "propertyIsEnumerable", U, { unsafe: true }))), a({ global: true, wrap: true, forced: !$, sham: !$ }, { Symbol: ie }), Q(V(Ve), function(L) {
              oe(L);
            }), a({ target: se, stat: true, forced: !$ }, { for: function(L) {
              var A = String(L);
              if (b(be, A)) return be[A];
              var F = ie(A);
              return be[A] = F, qe[F] = A, F;
            }, keyFor: function(A) {
              if (!C(A)) throw TypeError(A + " is not a symbol");
              if (b(qe, A)) return qe[A];
            }, useSetter: function() {
              xe = true;
            }, useSimple: function() {
              xe = false;
            } }), a({ target: "Object", stat: true, forced: !$, sham: !m }, { create: D, defineProperty: x, defineProperties: N, getOwnPropertyDescriptor: M }), a({ target: "Object", stat: true, forced: !$ }, { getOwnPropertyNames: z, getOwnPropertySymbols: G }), a({ target: "Object", stat: true, forced: g(function() {
              _.f(1);
            }) }, { getOwnPropertySymbols: function(A) {
              return _.f(y(A));
            } }), ve) {
              var te = !$ || g(function() {
                var L = ie();
                return ve([L]) != "[null]" || ve({ a: L }) != "{}" || ve(Object(L)) != "{}";
              });
              a({ target: "JSON", stat: true, forced: te }, { stringify: function(A, F, K) {
                for (var Y = [A], ue = 1, Te; arguments.length > ue; ) Y.push(arguments[ue++]);
                if (Te = F, !(!q(F) && A === void 0 || C(A))) return h(F) || (F = function(De, Ee) {
                  if (typeof Te == "function" && (Ee = Te.call(this, De, Ee)), !C(Ee)) return Ee;
                }), Y[1] = F, ve.apply(null, Y);
              } });
            }
            ie[de][$e] || j(ie[de], $e, ie[de].valueOf), le(ie, se), X[J] = true;
          }, a630: function(n, d, l) {
            var a = l("23e7"), u = l("4df4"), c = l("1c7e"), f = !c(function(m) {
              Array.from(m);
            });
            a({ target: "Array", stat: true, forced: f }, { from: u });
          }, a640: function(n, d, l) {
            var a = l("d039");
            n.exports = function(u, c) {
              var f = [][u];
              return !!f && a(function() {
                f.call(null, c || function() {
                  throw 1;
                }, 1);
              });
            };
          }, a691: function(n, d) {
            var l = Math.ceil, a = Math.floor;
            n.exports = function(u) {
              return isNaN(u = +u) ? 0 : (u > 0 ? a : l)(u);
            };
          }, ab13: function(n, d, l) {
            var a = l("b622"), u = a("match");
            n.exports = function(c) {
              var f = /./;
              try {
                "/./"[c](f);
              } catch {
                try {
                  return f[u] = false, "/./"[c](f);
                } catch {
                }
              }
              return false;
            };
          }, ac1f: function(n, d, l) {
            var a = l("23e7"), u = l("9263");
            a({ target: "RegExp", proto: true, forced: /./.exec !== u }, { exec: u });
          }, ad6d: function(n, d, l) {
            var a = l("825a");
            n.exports = function() {
              var u = a(this), c = "";
              return u.global && (c += "g"), u.ignoreCase && (c += "i"), u.multiline && (c += "m"), u.dotAll && (c += "s"), u.unicode && (c += "u"), u.sticky && (c += "y"), c;
            };
          }, ae40: function(n, d, l) {
            var a = l("83ab"), u = l("d039"), c = l("5135"), f = Object.defineProperty, m = {}, $ = function(p) {
              throw p;
            };
            n.exports = function(p, g) {
              if (c(m, p)) return m[p];
              g || (g = {});
              var b = [][p], h = c(g, "ACCESSORS") ? g.ACCESSORS : false, q = c(g, 0) ? g[0] : $, v = c(g, 1) ? g[1] : void 0;
              return m[p] = !!b && !u(function() {
                if (h && !a) return true;
                var y = { length: -1 };
                h ? f(y, 1, { enumerable: true, get: $ }) : y[1] = 1, b.call(y, q, v);
              });
            };
          }, ae93: function(n, d, l) {
            var a = l("e163"), u = l("9112"), c = l("5135"), f = l("b622"), m = l("c430"), $ = f("iterator"), p = false, g = function() {
              return this;
            }, b, h, q;
            [].keys && (q = [].keys(), "next" in q ? (h = a(a(q)), h !== Object.prototype && (b = h)) : p = true), b == null && (b = {}), !m && !c(b, $) && u(b, $, g), n.exports = { IteratorPrototype: b, BUGGY_SAFARI_ITERATORS: p };
          }, b041: function(n, d, l) {
            var a = l("00ee"), u = l("f5df");
            n.exports = a ? {}.toString : function() {
              return "[object " + u(this) + "]";
            };
          }, b0c0: function(n, d, l) {
            var a = l("83ab"), u = l("9bf2").f, c = Function.prototype, f = c.toString, m = /^\s*function ([^ (]*)/, $ = "name";
            a && !($ in c) && u(c, $, { configurable: true, get: function() {
              try {
                return f.call(this).match(m)[1];
              } catch {
                return "";
              }
            } });
          }, b622: function(n, d, l) {
            var a = l("da84"), u = l("5692"), c = l("5135"), f = l("90e3"), m = l("4930"), $ = l("fdbf"), p = u("wks"), g = a.Symbol, b = $ ? g : g && g.withoutSetter || f;
            n.exports = function(h) {
              return c(p, h) || (m && c(g, h) ? p[h] = g[h] : p[h] = b("Symbol." + h)), p[h];
            };
          }, b64b: function(n, d, l) {
            var a = l("23e7"), u = l("7b0b"), c = l("df75"), f = l("d039"), m = f(function() {
              c(1);
            });
            a({ target: "Object", stat: true, forced: m }, { keys: function(p) {
              return c(u(p));
            } });
          }, b727: function(n, d, l) {
            var a = l("0366"), u = l("44ad"), c = l("7b0b"), f = l("50c4"), m = l("65f0"), $ = [].push, p = function(g) {
              var b = g == 1, h = g == 2, q = g == 3, v = g == 4, y = g == 6, w = g == 5 || y;
              return function(T, E, P, V) {
                for (var S = c(T), k = u(S), _ = a(E, P, 3), W = f(k.length), B = 0, O = V || m, j = b ? O(T, W) : h ? O(T, 0) : void 0, I, R; W > B; B++) if ((w || B in k) && (I = k[B], R = _(I, B, S), g)) {
                  if (b) j[B] = R;
                  else if (R) switch (g) {
                    case 3:
                      return true;
                    case 5:
                      return I;
                    case 6:
                      return B;
                    case 2:
                      $.call(j, I);
                  }
                  else if (v) return false;
                }
                return y ? -1 : q || v ? v : j;
              };
            };
            n.exports = { forEach: p(0), map: p(1), filter: p(2), some: p(3), every: p(4), find: p(5), findIndex: p(6) };
          }, c04e: function(n, d, l) {
            var a = l("861d");
            n.exports = function(u, c) {
              if (!a(u)) return u;
              var f, m;
              if (c && typeof (f = u.toString) == "function" && !a(m = f.call(u)) || typeof (f = u.valueOf) == "function" && !a(m = f.call(u)) || !c && typeof (f = u.toString) == "function" && !a(m = f.call(u))) return m;
              throw TypeError("Can't convert object to primitive value");
            };
          }, c430: function(n, d) {
            n.exports = false;
          }, c6b6: function(n, d) {
            var l = {}.toString;
            n.exports = function(a) {
              return l.call(a).slice(8, -1);
            };
          }, c6cd: function(n, d, l) {
            var a = l("da84"), u = l("ce4e"), c = "__core-js_shared__", f = a[c] || u(c, {});
            n.exports = f;
          }, c740: function(n, d, l) {
            var a = l("23e7"), u = l("b727").findIndex, c = l("44d2"), f = l("ae40"), m = "findIndex", $ = true, p = f(m);
            m in [] && Array(1)[m](function() {
              $ = false;
            }), a({ target: "Array", proto: true, forced: $ || !p }, { findIndex: function(b) {
              return u(this, b, arguments.length > 1 ? arguments[1] : void 0);
            } }), c(m);
          }, c8ba: function(n, d) {
            var l;
            l = /* @__PURE__ */ function() {
              return this;
            }();
            try {
              l = l || new Function("return this")();
            } catch {
              typeof window == "object" && (l = window);
            }
            n.exports = l;
          }, c975: function(n, d, l) {
            var a = l("23e7"), u = l("4d64").indexOf, c = l("a640"), f = l("ae40"), m = [].indexOf, $ = !!m && 1 / [1].indexOf(1, -0) < 0, p = c("indexOf"), g = f("indexOf", { ACCESSORS: true, 1: 0 });
            a({ target: "Array", proto: true, forced: $ || !p || !g }, { indexOf: function(h) {
              return $ ? m.apply(this, arguments) || 0 : u(this, h, arguments.length > 1 ? arguments[1] : void 0);
            } });
          }, ca84: function(n, d, l) {
            var a = l("5135"), u = l("fc6a"), c = l("4d64").indexOf, f = l("d012");
            n.exports = function(m, $) {
              var p = u(m), g = 0, b = [], h;
              for (h in p) !a(f, h) && a(p, h) && b.push(h);
              for (; $.length > g; ) a(p, h = $[g++]) && (~c(b, h) || b.push(h));
              return b;
            };
          }, caad: function(n, d, l) {
            var a = l("23e7"), u = l("4d64").includes, c = l("44d2"), f = l("ae40"), m = f("indexOf", { ACCESSORS: true, 1: 0 });
            a({ target: "Array", proto: true, forced: !m }, { includes: function(p) {
              return u(this, p, arguments.length > 1 ? arguments[1] : void 0);
            } }), c("includes");
          }, cc12: function(n, d, l) {
            var a = l("da84"), u = l("861d"), c = a.document, f = u(c) && u(c.createElement);
            n.exports = function(m) {
              return f ? c.createElement(m) : {};
            };
          }, ce4e: function(n, d, l) {
            var a = l("da84"), u = l("9112");
            n.exports = function(c, f) {
              try {
                u(a, c, f);
              } catch {
                a[c] = f;
              }
              return f;
            };
          }, d012: function(n, d) {
            n.exports = {};
          }, d039: function(n, d) {
            n.exports = function(l) {
              try {
                return !!l();
              } catch {
                return true;
              }
            };
          }, d066: function(n, d, l) {
            var a = l("428f"), u = l("da84"), c = function(f) {
              return typeof f == "function" ? f : void 0;
            };
            n.exports = function(f, m) {
              return arguments.length < 2 ? c(a[f]) || c(u[f]) : a[f] && a[f][m] || u[f] && u[f][m];
            };
          }, d1e7: function(n, d, l) {
            var a = {}.propertyIsEnumerable, u = Object.getOwnPropertyDescriptor, c = u && !a.call({ 1: 2 }, 1);
            d.f = c ? function(m) {
              var $ = u(this, m);
              return !!$ && $.enumerable;
            } : a;
          }, d28b: function(n, d, l) {
            var a = l("746f");
            a("iterator");
          }, d2bb: function(n, d, l) {
            var a = l("825a"), u = l("3bbe");
            n.exports = Object.setPrototypeOf || ("__proto__" in {} ? function() {
              var c = false, f = {}, m;
              try {
                m = Object.getOwnPropertyDescriptor(Object.prototype, "__proto__").set, m.call(f, []), c = f instanceof Array;
              } catch {
              }
              return function(p, g) {
                return a(p), u(g), c ? m.call(p, g) : p.__proto__ = g, p;
              };
            }() : void 0);
          }, d3b7: function(n, d, l) {
            var a = l("00ee"), u = l("6eeb"), c = l("b041");
            a || u(Object.prototype, "toString", c, { unsafe: true });
          }, d44e: function(n, d, l) {
            var a = l("9bf2").f, u = l("5135"), c = l("b622"), f = c("toStringTag");
            n.exports = function(m, $, p) {
              m && !u(m = p ? m : m.prototype, f) && a(m, f, { configurable: true, value: $ });
            };
          }, d58f: function(n, d, l) {
            var a = l("1c0b"), u = l("7b0b"), c = l("44ad"), f = l("50c4"), m = function($) {
              return function(p, g, b, h) {
                a(g);
                var q = u(p), v = c(q), y = f(q.length), w = $ ? y - 1 : 0, T = $ ? -1 : 1;
                if (b < 2) for (; ; ) {
                  if (w in v) {
                    h = v[w], w += T;
                    break;
                  }
                  if (w += T, $ ? w < 0 : y <= w) throw TypeError("Reduce of empty array with no initial value");
                }
                for (; $ ? w >= 0 : y > w; w += T) w in v && (h = g(h, v[w], w, q));
                return h;
              };
            };
            n.exports = { left: m(false), right: m(true) };
          }, d784: function(n, d, l) {
            l("ac1f");
            var a = l("6eeb"), u = l("d039"), c = l("b622"), f = l("9263"), m = l("9112"), $ = c("species"), p = !u(function() {
              var v = /./;
              return v.exec = function() {
                var y = [];
                return y.groups = { a: "7" }, y;
              }, "".replace(v, "$<a>") !== "7";
            }), g = function() {
              return "a".replace(/./, "$0") === "$0";
            }(), b = c("replace"), h = function() {
              return /./[b] ? /./[b]("a", "$0") === "" : false;
            }(), q = !u(function() {
              var v = /(?:)/, y = v.exec;
              v.exec = function() {
                return y.apply(this, arguments);
              };
              var w = "ab".split(v);
              return w.length !== 2 || w[0] !== "a" || w[1] !== "b";
            });
            n.exports = function(v, y, w, T) {
              var E = c(v), P = !u(function() {
                var B = {};
                return B[E] = function() {
                  return 7;
                }, ""[v](B) != 7;
              }), V = P && !u(function() {
                var B = false, O = /a/;
                return v === "split" && (O = {}, O.constructor = {}, O.constructor[$] = function() {
                  return O;
                }, O.flags = "", O[E] = /./[E]), O.exec = function() {
                  return B = true, null;
                }, O[E](""), !B;
              });
              if (!P || !V || v === "replace" && !(p && g && !h) || v === "split" && !q) {
                var S = /./[E], k = w(E, ""[v], function(B, O, j, I, R) {
                  return O.exec === f ? P && !R ? { done: true, value: S.call(O, j, I) } : { done: true, value: B.call(j, O, I) } : { done: false };
                }, { REPLACE_KEEPS_$0: g, REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE: h }), _ = k[0], W = k[1];
                a(String.prototype, v, _), a(RegExp.prototype, E, y == 2 ? function(B, O) {
                  return W.call(B, this, O);
                } : function(B) {
                  return W.call(B, this);
                });
              }
              T && m(RegExp.prototype[E], "sham", true);
            };
          }, d81d: function(n, d, l) {
            var a = l("23e7"), u = l("b727").map, c = l("1dde"), f = l("ae40"), m = c("map"), $ = f("map");
            a({ target: "Array", proto: true, forced: !m || !$ }, { map: function(g) {
              return u(this, g, arguments.length > 1 ? arguments[1] : void 0);
            } });
          }, da84: function(n, d, l) {
            (function(a) {
              var u = function(c) {
                return c && c.Math == Math && c;
              };
              n.exports = u(typeof globalThis == "object" && globalThis) || u(typeof window == "object" && window) || u(typeof self == "object" && self) || u(typeof a == "object" && a) || Function("return this")();
            }).call(this, l("c8ba"));
          }, dbb4: function(n, d, l) {
            var a = l("23e7"), u = l("83ab"), c = l("56ef"), f = l("fc6a"), m = l("06cf"), $ = l("8418");
            a({ target: "Object", stat: true, sham: !u }, { getOwnPropertyDescriptors: function(g) {
              for (var b = f(g), h = m.f, q = c(b), v = {}, y = 0, w, T; q.length > y; ) T = h(b, w = q[y++]), T !== void 0 && $(v, w, T);
              return v;
            } });
          }, dbf1: function(n, d, l) {
            (function(a) {
              l.d(d, "a", function() {
                return c;
              });
              function u() {
                return typeof window < "u" ? window.console : a.console;
              }
              var c = u();
            }).call(this, l("c8ba"));
          }, ddb0: function(n, d, l) {
            var a = l("da84"), u = l("fdbc"), c = l("e260"), f = l("9112"), m = l("b622"), $ = m("iterator"), p = m("toStringTag"), g = c.values;
            for (var b in u) {
              var h = a[b], q = h && h.prototype;
              if (q) {
                if (q[$] !== g) try {
                  f(q, $, g);
                } catch {
                  q[$] = g;
                }
                if (q[p] || f(q, p, b), u[b]) {
                  for (var v in c) if (q[v] !== c[v]) try {
                    f(q, v, c[v]);
                  } catch {
                    q[v] = c[v];
                  }
                }
              }
            }
          }, df75: function(n, d, l) {
            var a = l("ca84"), u = l("7839");
            n.exports = Object.keys || function(f) {
              return a(f, u);
            };
          }, e01a: function(n, d, l) {
            var a = l("23e7"), u = l("83ab"), c = l("da84"), f = l("5135"), m = l("861d"), $ = l("9bf2").f, p = l("e893"), g = c.Symbol;
            if (u && typeof g == "function" && (!("description" in g.prototype) || g().description !== void 0)) {
              var b = {}, h = function() {
                var E = arguments.length < 1 || arguments[0] === void 0 ? void 0 : String(arguments[0]), P = this instanceof h ? new g(E) : E === void 0 ? g() : g(E);
                return E === "" && (b[P] = true), P;
              };
              p(h, g);
              var q = h.prototype = g.prototype;
              q.constructor = h;
              var v = q.toString, y = String(g("test")) == "Symbol(test)", w = /^Symbol\((.*)\)[^)]+$/;
              $(q, "description", { configurable: true, get: function() {
                var E = m(this) ? this.valueOf() : this, P = v.call(E);
                if (f(b, E)) return "";
                var V = y ? P.slice(7, -1) : P.replace(w, "$1");
                return V === "" ? void 0 : V;
              } }), a({ global: true, forced: true }, { Symbol: h });
            }
          }, e163: function(n, d, l) {
            var a = l("5135"), u = l("7b0b"), c = l("f772"), f = l("e177"), m = c("IE_PROTO"), $ = Object.prototype;
            n.exports = f ? Object.getPrototypeOf : function(p) {
              return p = u(p), a(p, m) ? p[m] : typeof p.constructor == "function" && p instanceof p.constructor ? p.constructor.prototype : p instanceof Object ? $ : null;
            };
          }, e177: function(n, d, l) {
            var a = l("d039");
            n.exports = !a(function() {
              function u() {
              }
              return u.prototype.constructor = null, Object.getPrototypeOf(new u()) !== u.prototype;
            });
          }, e260: function(n, d, l) {
            var a = l("fc6a"), u = l("44d2"), c = l("3f8c"), f = l("69f3"), m = l("7dd0"), $ = "Array Iterator", p = f.set, g = f.getterFor($);
            n.exports = m(Array, "Array", function(b, h) {
              p(this, { type: $, target: a(b), index: 0, kind: h });
            }, function() {
              var b = g(this), h = b.target, q = b.kind, v = b.index++;
              return !h || v >= h.length ? (b.target = void 0, { value: void 0, done: true }) : q == "keys" ? { value: v, done: false } : q == "values" ? { value: h[v], done: false } : { value: [v, h[v]], done: false };
            }, "values"), c.Arguments = c.Array, u("keys"), u("values"), u("entries");
          }, e439: function(n, d, l) {
            var a = l("23e7"), u = l("d039"), c = l("fc6a"), f = l("06cf").f, m = l("83ab"), $ = u(function() {
              f(1);
            }), p = !m || $;
            a({ target: "Object", stat: true, forced: p, sham: !m }, { getOwnPropertyDescriptor: function(b, h) {
              return f(c(b), h);
            } });
          }, e538: function(n, d, l) {
            var a = l("b622");
            d.f = a;
          }, e893: function(n, d, l) {
            var a = l("5135"), u = l("56ef"), c = l("06cf"), f = l("9bf2");
            n.exports = function(m, $) {
              for (var p = u($), g = f.f, b = c.f, h = 0; h < p.length; h++) {
                var q = p[h];
                a(m, q) || g(m, q, b($, q));
              }
            };
          }, e8b5: function(n, d, l) {
            var a = l("c6b6");
            n.exports = Array.isArray || function(c) {
              return a(c) == "Array";
            };
          }, e95a: function(n, d, l) {
            var a = l("b622"), u = l("3f8c"), c = a("iterator"), f = Array.prototype;
            n.exports = function(m) {
              return m !== void 0 && (u.Array === m || f[c] === m);
            };
          }, f5df: function(n, d, l) {
            var a = l("00ee"), u = l("c6b6"), c = l("b622"), f = c("toStringTag"), m = u(/* @__PURE__ */ function() {
              return arguments;
            }()) == "Arguments", $ = function(p, g) {
              try {
                return p[g];
              } catch {
              }
            };
            n.exports = a ? u : function(p) {
              var g, b, h;
              return p === void 0 ? "Undefined" : p === null ? "Null" : typeof (b = $(g = Object(p), f)) == "string" ? b : m ? u(g) : (h = u(g)) == "Object" && typeof g.callee == "function" ? "Arguments" : h;
            };
          }, f772: function(n, d, l) {
            var a = l("5692"), u = l("90e3"), c = a("keys");
            n.exports = function(f) {
              return c[f] || (c[f] = u(f));
            };
          }, fb15: function(n, d, l) {
            if (l.r(d), typeof window < "u") {
              var a = window.document.currentScript;
              {
                var u = l("8875");
                a = u(), "currentScript" in document || Object.defineProperty(document, "currentScript", { get: u });
              }
              var c = a && a.src.match(/(.+\/)[^/]+\.js(\?.*)?$/);
              c && (l.p = c[1]);
            }
            l("99af"), l("4de4"), l("4160"), l("c975"), l("d81d"), l("a434"), l("159b"), l("a4d3"), l("e439"), l("dbb4"), l("b64b");
            function f(C, x, N) {
              return x in C ? Object.defineProperty(C, x, { value: N, enumerable: true, configurable: true, writable: true }) : C[x] = N, C;
            }
            function m(C, x) {
              var N = Object.keys(C);
              if (Object.getOwnPropertySymbols) {
                var D = Object.getOwnPropertySymbols(C);
                x && (D = D.filter(function(U) {
                  return Object.getOwnPropertyDescriptor(C, U).enumerable;
                })), N.push.apply(N, D);
              }
              return N;
            }
            function $(C) {
              for (var x = 1; x < arguments.length; x++) {
                var N = arguments[x] != null ? arguments[x] : {};
                x % 2 ? m(Object(N), true).forEach(function(D) {
                  f(C, D, N[D]);
                }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(C, Object.getOwnPropertyDescriptors(N)) : m(Object(N)).forEach(function(D) {
                  Object.defineProperty(C, D, Object.getOwnPropertyDescriptor(N, D));
                });
              }
              return C;
            }
            function p(C) {
              if (Array.isArray(C)) return C;
            }
            l("e01a"), l("d28b"), l("e260"), l("d3b7"), l("3ca3"), l("ddb0");
            function g(C, x) {
              if (!(typeof Symbol > "u" || !(Symbol.iterator in Object(C)))) {
                var N = [], D = true, U = false, M = void 0;
                try {
                  for (var z = C[Symbol.iterator](), G; !(D = (G = z.next()).done) && (N.push(G.value), !(x && N.length === x)); D = true) ;
                } catch (te) {
                  U = true, M = te;
                } finally {
                  try {
                    !D && z.return != null && z.return();
                  } finally {
                    if (U) throw M;
                  }
                }
                return N;
              }
            }
            l("a630"), l("fb6a"), l("b0c0"), l("25f0");
            function b(C, x) {
              (x == null || x > C.length) && (x = C.length);
              for (var N = 0, D = new Array(x); N < x; N++) D[N] = C[N];
              return D;
            }
            function h(C, x) {
              if (C) {
                if (typeof C == "string") return b(C, x);
                var N = Object.prototype.toString.call(C).slice(8, -1);
                if (N === "Object" && C.constructor && (N = C.constructor.name), N === "Map" || N === "Set") return Array.from(C);
                if (N === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(N)) return b(C, x);
              }
            }
            function q() {
              throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`);
            }
            function v(C, x) {
              return p(C) || g(C, x) || h(C, x) || q();
            }
            function y(C) {
              if (Array.isArray(C)) return b(C);
            }
            function w(C) {
              if (typeof Symbol < "u" && Symbol.iterator in Object(C)) return Array.from(C);
            }
            function T() {
              throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`);
            }
            function E(C) {
              return y(C) || w(C) || h(C) || T();
            }
            var P = l("a352"), V = l.n(P);
            function S(C) {
              C.parentElement !== null && C.parentElement.removeChild(C);
            }
            function k(C, x, N) {
              var D = N === 0 ? C.children[0] : C.children[N - 1].nextSibling;
              C.insertBefore(x, D);
            }
            var _ = l("dbf1");
            l("13d5"), l("4fad"), l("ac1f"), l("5319");
            function W(C) {
              var x = /* @__PURE__ */ Object.create(null);
              return function(D) {
                var U = x[D];
                return U || (x[D] = C(D));
              };
            }
            var B = /-(\w)/g, O = W(function(C) {
              return C.replace(B, function(x, N) {
                return N.toUpperCase();
              });
            });
            l("5db7"), l("73d9");
            var j = ["Start", "Add", "Remove", "Update", "End"], I = ["Choose", "Unchoose", "Sort", "Filter", "Clone"], R = ["Move"], H = [R, j, I].flatMap(function(C) {
              return C;
            }).map(function(C) {
              return "on".concat(C);
            }), X = { manage: R, manageAndEmit: j, emit: I };
            function ne(C) {
              return H.indexOf(C) !== -1;
            }
            l("caad"), l("2ca0");
            var Z = ["a", "abbr", "address", "area", "article", "aside", "audio", "b", "base", "bdi", "bdo", "blockquote", "body", "br", "button", "canvas", "caption", "cite", "code", "col", "colgroup", "data", "datalist", "dd", "del", "details", "dfn", "dialog", "div", "dl", "dt", "em", "embed", "fieldset", "figcaption", "figure", "footer", "form", "h1", "h2", "h3", "h4", "h5", "h6", "head", "header", "hgroup", "hr", "html", "i", "iframe", "img", "input", "ins", "kbd", "label", "legend", "li", "link", "main", "map", "mark", "math", "menu", "menuitem", "meta", "meter", "nav", "noscript", "object", "ol", "optgroup", "option", "output", "p", "param", "picture", "pre", "progress", "q", "rb", "rp", "rt", "rtc", "ruby", "s", "samp", "script", "section", "select", "slot", "small", "source", "span", "strong", "style", "sub", "summary", "sup", "svg", "table", "tbody", "td", "template", "textarea", "tfoot", "th", "thead", "time", "title", "tr", "track", "u", "ul", "var", "video", "wbr"];
            function ee(C) {
              return Z.includes(C);
            }
            function oe(C) {
              return ["transition-group", "TransitionGroup"].includes(C);
            }
            function le(C) {
              return ["id", "class", "role", "style"].includes(C) || C.startsWith("data-") || C.startsWith("aria-") || C.startsWith("on");
            }
            function re(C) {
              return C.reduce(function(x, N) {
                var D = v(N, 2), U = D[0], M = D[1];
                return x[U] = M, x;
              }, {});
            }
            function Q(C) {
              var x = C.$attrs, N = C.componentData, D = N === void 0 ? {} : N, U = re(Object.entries(x).filter(function(M) {
                var z = v(M, 2), G = z[0];
                return z[1], le(G);
              }));
              return $($({}, U), D);
            }
            function J(C) {
              var x = C.$attrs, N = C.callBackBuilder, D = re(se(x));
              Object.entries(N).forEach(function(M) {
                var z = v(M, 2), G = z[0], te = z[1];
                X[G].forEach(function(L) {
                  D["on".concat(L)] = te(L);
                });
              });
              var U = "[data-draggable]".concat(D.draggable || "");
              return $($({}, D), {}, { draggable: U });
            }
            function se(C) {
              return Object.entries(C).filter(function(x) {
                var N = v(x, 2), D = N[0];
                return N[1], !le(D);
              }).map(function(x) {
                var N = v(x, 2), D = N[0], U = N[1];
                return [O(D), U];
              }).filter(function(x) {
                var N = v(x, 2), D = N[0];
                return N[1], !ne(D);
              });
            }
            l("c740");
            function de(C, x) {
              if (!(C instanceof x)) throw new TypeError("Cannot call a class as a function");
            }
            function $e(C, x) {
              for (var N = 0; N < x.length; N++) {
                var D = x[N];
                D.enumerable = D.enumerable || false, D.configurable = true, "value" in D && (D.writable = true), Object.defineProperty(C, D.key, D);
              }
            }
            function he(C, x, N) {
              return x && $e(C.prototype, x), C;
            }
            var pe = function(x) {
              var N = x.el;
              return N;
            }, ae = function(x, N) {
              return x.__draggable_context = N;
            }, ie = function(x) {
              return x.__draggable_context;
            }, ve = function() {
              function C(x) {
                var N = x.nodes, D = N.header, U = N.default, M = N.footer, z = x.root, G = x.realList;
                de(this, C), this.defaultNodes = U, this.children = [].concat(E(D), E(U), E(M)), this.externalComponent = z.externalComponent, this.rootTransition = z.transition, this.tag = z.tag, this.realList = G;
              }
              return he(C, [{ key: "render", value: function(N, D) {
                var U = this.tag, M = this.children, z = this._isRootComponent, G = z ? { default: function() {
                  return M;
                } } : M;
                return N(U, D, G);
              } }, { key: "updated", value: function() {
                var N = this.defaultNodes, D = this.realList;
                N.forEach(function(U, M) {
                  ae(pe(U), { element: D[M], index: M });
                });
              } }, { key: "getUnderlyingVm", value: function(N) {
                return ie(N);
              } }, { key: "getVmIndexFromDomIndex", value: function(N, D) {
                var U = this.defaultNodes, M = U.length, z = D.children, G = z.item(N);
                if (G === null) return M;
                var te = ie(G);
                if (te) return te.index;
                if (M === 0) return 0;
                var L = pe(U[0]), A = E(z).findIndex(function(F) {
                  return F === L;
                });
                return N < A ? 0 : M;
              } }, { key: "_isRootComponent", get: function() {
                return this.externalComponent || this.rootTransition;
              } }]), C;
            }(), me = l("8bbf");
            function fe(C, x) {
              var N = C[x];
              return N ? N() : [];
            }
            function Ne(C) {
              var x = C.$slots, N = C.realList, D = C.getKey, U = N || [], M = ["header", "footer"].map(function(F) {
                return fe(x, F);
              }), z = v(M, 2), G = z[0], te = z[1], L = x.item;
              if (!L) throw new Error("draggable element must have an item slot");
              var A = U.flatMap(function(F, K) {
                return L({ element: F, index: K }).map(function(Y) {
                  return Y.key = D(F), Y.props = $($({}, Y.props || {}), {}, { "data-draggable": true }), Y;
                });
              });
              if (A.length !== U.length) throw new Error("Item slot must have only one child");
              return { header: G, footer: te, default: A };
            }
            function Se(C) {
              var x = oe(C), N = !ee(C) && !x;
              return { transition: x, externalComponent: N, tag: N ? Object(me.resolveComponent)(C) : x ? me.TransitionGroup : C };
            }
            function ce(C) {
              var x = C.$slots, N = C.tag, D = C.realList, U = C.getKey, M = Ne({ $slots: x, realList: D, getKey: U }), z = Se(N);
              return new ve({ nodes: M, root: z, realList: D });
            }
            function ge(C, x) {
              var N = this;
              Object(me.nextTick)(function() {
                return N.$emit(C.toLowerCase(), x);
              });
            }
            function be(C) {
              var x = this;
              return function(N, D) {
                if (x.realList !== null) return x["onDrag".concat(C)](N, D);
              };
            }
            function qe(C) {
              var x = this, N = be.call(this, C);
              return function(D, U) {
                N.call(x, D, U), ge.call(x, C, D);
              };
            }
            var Ve = null, ye = { list: { type: Array, required: false, default: null }, modelValue: { type: Array, required: false, default: null }, itemKey: { type: [String, Function], required: true }, clone: { type: Function, default: function(x) {
              return x;
            } }, tag: { type: String, default: "div" }, move: { type: Function, default: null }, componentData: { type: Object, required: false, default: null } }, xe = ["update:modelValue", "change"].concat(E([].concat(E(X.manageAndEmit), E(X.emit)).map(function(C) {
              return C.toLowerCase();
            }))), Ce = Object(me.defineComponent)({ name: "draggable", inheritAttrs: false, props: ye, emits: xe, data: function() {
              return { error: false };
            }, render: function() {
              try {
                this.error = false;
                var x = this.$slots, N = this.$attrs, D = this.tag, U = this.componentData, M = this.realList, z = this.getKey, G = ce({ $slots: x, tag: D, realList: M, getKey: z });
                this.componentStructure = G;
                var te = Q({ $attrs: N, componentData: U });
                return G.render(me.h, te);
              } catch (L) {
                return this.error = true, Object(me.h)("pre", { style: { color: "red" } }, L.stack);
              }
            }, created: function() {
              this.list !== null && this.modelValue !== null && _.a.error("modelValue and list props are mutually exclusive! Please set one or another.");
            }, mounted: function() {
              var x = this;
              if (!this.error) {
                var N = this.$attrs, D = this.$el, U = this.componentStructure;
                U.updated();
                var M = J({ $attrs: N, callBackBuilder: { manageAndEmit: function(te) {
                  return qe.call(x, te);
                }, emit: function(te) {
                  return ge.bind(x, te);
                }, manage: function(te) {
                  return be.call(x, te);
                } } }), z = D.nodeType === 1 ? D : D.parentElement;
                this._sortable = new V.a(z, M), this.targetDomElement = z, z.__draggable_component__ = this;
              }
            }, updated: function() {
              this.componentStructure.updated();
            }, beforeUnmount: function() {
              this._sortable !== void 0 && this._sortable.destroy();
            }, computed: { realList: function() {
              var x = this.list;
              return x || this.modelValue;
            }, getKey: function() {
              var x = this.itemKey;
              return typeof x == "function" ? x : function(N) {
                return N[x];
              };
            } }, watch: { $attrs: { handler: function(x) {
              var N = this._sortable;
              N && se(x).forEach(function(D) {
                var U = v(D, 2), M = U[0], z = U[1];
                N.option(M, z);
              });
            }, deep: true } }, methods: { getUnderlyingVm: function(x) {
              return this.componentStructure.getUnderlyingVm(x) || null;
            }, getUnderlyingPotencialDraggableComponent: function(x) {
              return x.__draggable_component__;
            }, emitChanges: function(x) {
              var N = this;
              Object(me.nextTick)(function() {
                return N.$emit("change", x);
              });
            }, alterList: function(x) {
              if (this.list) {
                x(this.list);
                return;
              }
              var N = E(this.modelValue);
              x(N), this.$emit("update:modelValue", N);
            }, spliceList: function() {
              var x = arguments, N = function(U) {
                return U.splice.apply(U, E(x));
              };
              this.alterList(N);
            }, updatePosition: function(x, N) {
              var D = function(M) {
                return M.splice(N, 0, M.splice(x, 1)[0]);
              };
              this.alterList(D);
            }, getRelatedContextFromMoveEvent: function(x) {
              var N = x.to, D = x.related, U = this.getUnderlyingPotencialDraggableComponent(N);
              if (!U) return { component: U };
              var M = U.realList, z = { list: M, component: U };
              if (N !== D && M) {
                var G = U.getUnderlyingVm(D) || {};
                return $($({}, G), z);
              }
              return z;
            }, getVmIndexFromDomIndex: function(x) {
              return this.componentStructure.getVmIndexFromDomIndex(x, this.targetDomElement);
            }, onDragStart: function(x) {
              this.context = this.getUnderlyingVm(x.item), x.item._underlying_vm_ = this.clone(this.context.element), Ve = x.item;
            }, onDragAdd: function(x) {
              var N = x.item._underlying_vm_;
              if (N !== void 0) {
                S(x.item);
                var D = this.getVmIndexFromDomIndex(x.newIndex);
                this.spliceList(D, 0, N);
                var U = { element: N, newIndex: D };
                this.emitChanges({ added: U });
              }
            }, onDragRemove: function(x) {
              if (k(this.$el, x.item, x.oldIndex), x.pullMode === "clone") {
                S(x.clone);
                return;
              }
              var N = this.context, D = N.index, U = N.element;
              this.spliceList(D, 1);
              var M = { element: U, oldIndex: D };
              this.emitChanges({ removed: M });
            }, onDragUpdate: function(x) {
              S(x.item), k(x.from, x.item, x.oldIndex);
              var N = this.context.index, D = this.getVmIndexFromDomIndex(x.newIndex);
              this.updatePosition(N, D);
              var U = { element: this.context.element, oldIndex: N, newIndex: D };
              this.emitChanges({ moved: U });
            }, computeFutureIndex: function(x, N) {
              if (!x.element) return 0;
              var D = E(N.to.children).filter(function(G) {
                return G.style.display !== "none";
              }), U = D.indexOf(N.related), M = x.component.getVmIndexFromDomIndex(U), z = D.indexOf(Ve) !== -1;
              return z || !N.willInsertAfter ? M : M + 1;
            }, onDragMove: function(x, N) {
              var D = this.move, U = this.realList;
              if (!D || !U) return true;
              var M = this.getRelatedContextFromMoveEvent(x), z = this.computeFutureIndex(M, x), G = $($({}, this.context), {}, { futureIndex: z }), te = $($({}, x), {}, { relatedContext: M, draggedContext: G });
              return D(te, N);
            }, onDragEnd: function() {
              Ve = null;
            } } }), we = Ce;
            d.default = we;
          }, fb6a: function(n, d, l) {
            var a = l("23e7"), u = l("861d"), c = l("e8b5"), f = l("23cb"), m = l("50c4"), $ = l("fc6a"), p = l("8418"), g = l("b622"), b = l("1dde"), h = l("ae40"), q = b("slice"), v = h("slice", { ACCESSORS: true, 0: 0, 1: 2 }), y = g("species"), w = [].slice, T = Math.max;
            a({ target: "Array", proto: true, forced: !q || !v }, { slice: function(P, V) {
              var S = $(this), k = m(S.length), _ = f(P, k), W = f(V === void 0 ? k : V, k), B, O, j;
              if (c(S) && (B = S.constructor, typeof B == "function" && (B === Array || c(B.prototype)) ? B = void 0 : u(B) && (B = B[y], B === null && (B = void 0)), B === Array || B === void 0)) return w.call(S, _, W);
              for (O = new (B === void 0 ? Array : B)(T(W - _, 0)), j = 0; _ < W; _++, j++) _ in S && p(O, j, S[_]);
              return O.length = j, O;
            } });
          }, fc6a: function(n, d, l) {
            var a = l("44ad"), u = l("1d80");
            n.exports = function(c) {
              return a(u(c));
            };
          }, fdbc: function(n, d) {
            n.exports = { CSSRuleList: 0, CSSStyleDeclaration: 0, CSSValueList: 0, ClientRectList: 0, DOMRectList: 0, DOMStringList: 0, DOMTokenList: 1, DataTransferItemList: 0, FileList: 0, HTMLAllCollection: 0, HTMLCollection: 0, HTMLFormElement: 0, HTMLSelectElement: 0, MediaList: 0, MimeTypeArray: 0, NamedNodeMap: 0, NodeList: 1, PaintRequestList: 0, Plugin: 0, PluginArray: 0, SVGLengthList: 0, SVGNumberList: 0, SVGPathSegList: 0, SVGPointList: 0, SVGStringList: 0, SVGTransformList: 0, SourceBufferList: 0, StyleSheetList: 0, TextTrackCueList: 0, TextTrackList: 0, TouchList: 0 };
          }, fdbf: function(n, d, l) {
            var a = l("4930");
            n.exports = a && !Symbol.sham && typeof Symbol.iterator == "symbol";
          } }).default;
        });
      })(vuedraggable_umd);
      var vuedraggable_umdExports = vuedraggable_umd.exports;
      const Draggable = getDefaultExportFromCjs(vuedraggable_umdExports), _export_sfc = (o, r) => {
        const t = o.__vccOpts || o;
        for (const [s, n] of r) t[s] = n;
        return t;
      }, _hoisted_1$q = { class: "nfd-field-item" }, _hoisted_2$g = { key: 0, class: "title" }, _hoisted_3$b = ["innerHTML"], _hoisted_4$a = ["onClick"], _hoisted_5$6 = { class: "field-title" }, _sfc_main$L = { __name: "item", props: { item: { type: [Object, Array] }, type: { type: String, default: "field" }, span: { type: Number, default: 12 } }, emits: ["item-click"], setup(o) {
        return (r, t) => {
          const s = require$$0.resolveComponent("el-icon"), n = require$$0.resolveComponent("el-col");
          return require$$0.openBlock(), require$$0.createElementBlock("div", _hoisted_1$q, [typeof o.item == "object" && o.item.title ? (require$$0.openBlock(), require$$0.createElementBlock("div", _hoisted_2$g, require$$0.toDisplayString(o.item.title), 1)) : require$$0.createCommentVNode("", true), require$$0.createVNode(require$$0.unref(Draggable), { class: "el-row field-group", tag: "ul", list: o.item ? Array.isArray(o.item) ? o.item : o.item.list : [], group: { name: "form", pull: o.type == "template" ? false : "clone", put: false }, "ghost-class": "ghost", sort: false, "item-key": "label" }, { item: require$$0.withCtx(({ element: d }) => [require$$0.createVNode(n, { span: o.span || 12 }, { default: require$$0.withCtx(() => [(require$$0.openBlock(), require$$0.createBlock(require$$0.resolveDynamicComponent(d.tooltip ? "el-tooltip" : "div"), { effect: "dark", placement: "right" }, require$$0.createSlots({ default: require$$0.withCtx(() => [require$$0.createElementVNode("li", { class: "field-item", onClick: (l) => r.$emit("item-click", { item: d, type: o.type }) }, [d.icon && d.icon.startsWith("el-") ? (require$$0.openBlock(), require$$0.createBlock(s, { key: 0 }, { default: require$$0.withCtx(() => [(require$$0.openBlock(), require$$0.createBlock(require$$0.resolveDynamicComponent(d.icon)))]), _: 2 }, 1024)) : d.icon ? (require$$0.openBlock(), require$$0.createElementBlock("i", { key: 1, class: require$$0.normalizeClass(["icon iconfont", d.icon]) }, null, 2)) : require$$0.createCommentVNode("", true), require$$0.createElementVNode("span", _hoisted_5$6, require$$0.toDisplayString(d.title || d.label), 1)], 8, _hoisted_4$a)]), _: 2 }, [d.tooltip ? { name: "content", fn: require$$0.withCtx(() => [require$$0.createElementVNode("span", { innerHTML: d.tooltip }, null, 8, _hoisted_3$b)]), key: "0" } : void 0]), 1024))]), _: 2 }, 1032, ["span"])]), _: 1 }, 8, ["list", "group"])]);
        };
      } }, FieldItem = _export_sfc(_sfc_main$L, [["__scopeId", "data-v-e34a6329"]]), fields = [{ title: "布局字段", list: [{ type: "group", label: "分组", icon: "icon-group", display: true, arrow: true, collapse: true, children: { column: [] } }, { type: "dynamic", label: "子表单", icon: "icon-table", span: 24, display: true, labelPosition: "top", children: { align: "center", headerAlign: "center", index: true, addBtn: true, delBtn: true, column: [] } }, { type: "table", label: "表格", icon: "icon-table2", showLabel: false, span: 24, display: true, labelWidth: "0px", rows: [{ cols: [{ column: [] }, { column: [] }, { column: [] }, { column: [] }] }, { cols: [{ column: [] }, { column: [] }, { column: [] }, { column: [] }] }, { cols: [{ column: [] }, { column: [] }, { column: [] }, { column: [] }] }, { cols: [{ column: [] }, { column: [] }, { column: [] }, { column: [] }] }] }, { type: "title", icon: "icon-text", span: 24, display: true, styles: {}, label: "文本", labelWidth: "0px", textValue: "文本", showLabel: false }] }, { title: "输入字段", list: [{ type: "input", label: "单行文本", icon: "icon-input", display: true }, { type: "password", label: "密码", icon: "icon-password", display: true }, { type: "textarea", label: "多行文本", icon: "icon-textarea", span: 24, display: true }, { type: "number", label: "计数器", icon: "icon-number", controls: true, display: true }, { type: "ueditor", label: "富文本", icon: "icon-richtext", span: 24, display: true, options: { action: "", props: {} } }, { type: "input-tag", label: "标签输入框", icon: "icon-tag", display: true, dataType: "string" }] }, { title: "选择字段", list: [{ type: "radio", label: "单选框组", icon: "icon-radio", dicData: [{ label: "选项一", value: "0" }, { label: "选项二", value: "1" }, { label: "选项三", value: "2" }], display: true, dicOption: "static", props: { label: "label", value: "value", desc: "desc" } }, { type: "checkbox", label: "多选框组", icon: "icon-checkbox", dicData: [{ label: "选项一", value: "0" }, { label: "选项二", value: "1" }, { label: "选项三", value: "2" }], display: true, dicOption: "static", props: { label: "label", value: "value", desc: "desc" }, dataType: "string" }, { type: "select", label: "下拉选择器", icon: "icon-select", dicData: [{ label: "选项一", value: "0" }, { label: "选项二", value: "1" }, { label: "选项三", value: "2" }], cascader: [], display: true, dicOption: "static", props: { label: "label", value: "value", desc: "desc" }, dataType: "string" }, { type: "segmented", label: "分段控制器", icon: "icon-segmented", dicData: [{ label: "选项一", value: "0" }, { label: "选项二", value: "1" }, { label: "选项三", value: "2" }], display: true, dicOption: "static", props: { label: "label", value: "value" } }, { type: "cascader", label: "级联选择器", icon: "icon-link", display: true, dicData: [{ label: "选项一", value: "0", children: [{ label: "选项1-1", value: "11" }, { label: "选项1-2", value: "12" }] }, { label: "选项二", value: "1" }, { label: "选项三", value: "2" }], cascaderIndex: 1, cascader: [], showAllLevels: true, dicOption: "static", separator: "/", props: { label: "label", value: "value", children: "children", desc: "desc" }, dataType: "string" }, { type: "tree", label: "树形选择器", icon: "icon-tree", display: true, dicOption: "static", dicData: [{ label: "选项一", value: "0", children: [{ label: "选项1-1", value: "11" }, { label: "选项1-2", value: "12" }] }, { label: "选项二", value: "1" }, { label: "选项三", value: "2" }], parent: true, cascader: [], props: { label: "label", value: "value", children: "children", desc: "desc" }, dataType: "string" }, { type: "map", label: "地图选择器", icon: "icon-map", display: true, dataType: "string" }, { type: "table-select", label: "表格选择器", icon: "icon-table", display: true, tooltip: "选择结果以输入框形式呈现", children: { border: true, search: true, searchMenuSpan: 6, searchIcon: true, searchIconIndex: 3, column: [], props: { url: "", method: "get", needPage: true, currentPageKey: "current", pageSizeKey: "size", totalKey: "total", recordsKey: "records", resKey: "data", auto: false } }, props: { label: "", value: "" } }, { type: "table-select-v2", label: "表格选择器v2", icon: "icon-table", display: true, span: 24, tooltip: "选择结果以表格形式呈现", children: { btnText: "选 择", btnPosition: "left", index: true, size: "default", border: true, search: true, searchMenuSpan: 6, searchIcon: true, searchIconIndex: 3, column: [], props: { url: "", method: "get", needPage: true, currentPageKey: "current", pageSizeKey: "size", totalKey: "total", recordsKey: "records", resKey: "data", auto: false } }, props: { label: "", value: "" } }] }, { title: "上传字段", list: [{ type: "upload", label: "上传", icon: "icon-upload", span: 24, display: true, showFileList: true, multiple: true, limit: 10, propsHttp: {}, headersConfig: [], dataConfig: [] }] }, { title: "日期时间字段", list: [{ type: "year", label: "年", icon: "icon-year", display: true, format: "YYYY", valueFormat: "YYYY" }, { type: "years", label: "年多选", icon: "icon-year", display: true, format: "YYYY", valueFormat: "YYYY", dataType: "string" }, { type: "yearrange", label: "年范围", icon: "icon-year", display: true, format: "YYYY", valueFormat: "YYYY", dataType: "string" }, { type: "month", label: "月", icon: "icon-month", display: true, format: "YYYY-MM", valueFormat: "YYYY-MM" }, { type: "months", label: "月多选", icon: "icon-month", display: true, format: "YYYY-MM", valueFormat: "YYYY-MM", dataType: "string" }, { type: "monthrange", label: "月范围", icon: "icon-month", display: true, format: "YYYY-MM", valueFormat: "YYYY-MM", dataType: "string" }, { type: "date", label: "日期", icon: "icon-date", display: true, format: "YYYY-MM-DD", valueFormat: "YYYY-MM-DD" }, { type: "dates", label: "日期多选", icon: "icon-date", display: true, format: "YYYY-MM-DD", valueFormat: "YYYY-MM-DD", dataType: "string" }, { type: "daterange", label: "日期范围", icon: "icon-date-range", display: true, format: "YYYY-MM-DD", valueFormat: "YYYY-MM-DD", dataType: "string" }, { type: "week", label: "周", icon: "icon-week", display: true, format: "YYYY 第 ww 周" }, { type: "time", label: "时间", icon: "icon-time", display: true, format: "HH:mm:ss", valueFormat: "HH:mm:ss" }, { type: "timerange", label: "时间范围", icon: "icon-time-range", display: true, format: "HH:mm:ss", valueFormat: "HH:mm:ss", dataType: "string" }, { type: "datetime", label: "日期时间", icon: "icon-datetime", display: true, format: "YYYY-MM-DD HH:mm:ss", valueFormat: "YYYY-MM-DD HH:mm:ss" }, { type: "datetimerange", label: "日期时间范围", icon: "icon-datetime-range", display: true, format: "YYYY-MM-DD HH:mm:ss", valueFormat: "YYYY-MM-DD HH:mm:ss", dataType: "string" }] }, { title: "其他字段", list: [{ label: "签名", type: "sign", icon: "el-icon-edit-pen", display: true, action: "", options: {}, propsHttp: {} }, { type: "switch", label: "开关", icon: "icon-switch", display: true, value: "0", dicData: [{ label: "", value: "0" }, { label: "", value: "1" }] }, { type: "rate", label: "评价", icon: "icon-star", display: true, max: 5, value: 0, texts: ["极差", "失望", "一般", "满意", "惊喜"], colors: ["#99A9BF", "#F7BA2A", "#FF9900"] }, { type: "slider", label: "滑块", icon: "icon-slider", display: true, min: 0, max: 10 }, { type: "color", label: "颜色选择器", icon: "icon-color", display: true }] }], _hoisted_1$p = { class: "nfd-field" }, _sfc_main$K = { __name: "index", props: { customFields: { type: Array, default: [] }, includeFields: { type: Array }, defaultTab: { type: String, default: "basic" } }, emits: ["field-click"], setup(o) {
        const r = o, { proxy: t } = require$$0.getCurrentInstance();
        t.loadScript("css", "https://at.alicdn.com/t/c/font_1254447_wonibn5433.css");
        const s = require$$0.ref("basic"), { customFields: n, includeFields: d, defaultTab: l } = r;
        l && (s.value = l);
        const a = require$$0.computed(() => {
          if (!d || d.length == 0) return fields;
          let c = [];
          return fields.forEach((f) => {
            let m = [];
            f.list.forEach(($) => {
              d.includes($.type) && m.push($);
            }), m.length > 0 && c.push({ ...f, list: m });
          }), c;
        }), u = require$$0.computed(() => {
          let c = [];
          if (n && n.length > 0) {
            let f = t.deepClone(n);
            const m = [];
            for (let $ = 0; $ < f.length; $++) {
              const p = f[$];
              (!p.title || !p.list) && (m.push(p), f.splice($, 1), $--);
            }
            m.length > 0 ? c = f.concat([{ title: "自定义字段", list: m }]) : c = f;
          }
          return c;
        });
        return (c, f) => {
          const m = require$$0.resolveComponent("el-tab-pane"), $ = require$$0.resolveComponent("el-alert"), p = require$$0.resolveComponent("el-tabs");
          return require$$0.openBlock(), require$$0.createElementBlock("div", _hoisted_1$p, [require$$0.createVNode(p, { modelValue: s.value, "onUpdate:modelValue": f[3] || (f[3] = (g) => s.value = g), stretch: "" }, { default: require$$0.withCtx(() => [require$$0.createVNode(m, { label: "基础字段", name: "basic" }, { default: require$$0.withCtx(() => [(require$$0.openBlock(true), require$$0.createElementBlock(require$$0.Fragment, null, require$$0.renderList(a.value, (g, b) => (require$$0.openBlock(), require$$0.createElementBlock("div", { key: b }, [require$$0.createVNode(FieldItem, { item: g, onItemClick: f[0] || (f[0] = (h) => c.$emit("field-click", h)) }, null, 8, ["item"])]))), 128))]), _: 1 }), (require$$0.openBlock(true), require$$0.createElementBlock(require$$0.Fragment, null, require$$0.renderList(u.value, (g) => (require$$0.openBlock(), require$$0.createBlock(m, { label: g.title, name: g.key }, { default: require$$0.withCtx(() => [g.list && g.list.length > 0 && g.list[0].title && g.list[0].list ? (require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, { key: 0 }, [g.type == "template" ? (require$$0.openBlock(), require$$0.createBlock($, { key: 0, type: "error", center: "", closable: false, title: "模版点击会替换右侧区域所有字段" })) : require$$0.createCommentVNode("", true), (require$$0.openBlock(true), require$$0.createElementBlock(require$$0.Fragment, null, require$$0.renderList(g.list, (b) => (require$$0.openBlock(), require$$0.createBlock(FieldItem, { item: b, type: g.type, span: b.span || g.span, onItemClick: f[1] || (f[1] = (h) => c.$emit("field-click", h)) }, null, 8, ["item", "type", "span"]))), 256))], 64)) : (require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, { key: 1 }, [g.type == "template" ? (require$$0.openBlock(), require$$0.createBlock($, { key: 0, type: "error", center: "", closable: false, title: "模版点击会替换右侧区域所有字段" })) : require$$0.createCommentVNode("", true), require$$0.createVNode(FieldItem, { item: g.list, type: g.type, span: g.span, onItemClick: f[2] || (f[2] = (b) => c.$emit("field-click", b)) }, null, 8, ["item", "type", "span"])], 64))]), _: 2 }, 1032, ["label", "name"]))), 256))]), _: 1 }, 8, ["modelValue"])]);
        };
      } }, NfdField = _export_sfc(_sfc_main$K, [["__scopeId", "data-v-4a08c8fe"]]), _hoisted_1$o = { key: 0 }, _hoisted_2$f = { style: { display: "flex", "align-items": "center" } }, _sfc_main$J = { __name: "index", props: { undoRedo: { type: Boolean, default: true }, toolbar: { type: Array, default: () => ["import", "generate", "preview", "clear"] }, historySteps: { type: Object, default: () => ({ index: 0, maxStep: 20, steps: [], storage: false }) } }, emits: ["undo", "redo", "import", "generate", "preview", "clear"], setup(o) {
        return (r, t) => {
          const s = require$$0.resolveComponent("el-button"), n = require$$0.resolveComponent("el-header");
          return require$$0.openBlock(), require$$0.createBlock(n, { class: "nfd-toolbar" }, { default: require$$0.withCtx(() => [o.undoRedo ? (require$$0.openBlock(), require$$0.createElementBlock("div", _hoisted_1$o, [require$$0.createVNode(s, { text: "", type: "primary", icon: "el-icon-refresh-left", disabled: o.historySteps.index == 0, onClick: t[0] || (t[0] = (d) => r.$emit("undo")) }, { default: require$$0.withCtx(() => t[6] || (t[6] = [require$$0.createTextVNode(" 撤销 ")])), _: 1, __: [6] }, 8, ["disabled"]), require$$0.createVNode(s, { text: "", type: "primary", icon: "el-icon-refresh-right", disabled: o.historySteps.index == o.historySteps.steps.length - 1, onClick: t[1] || (t[1] = (d) => r.$emit("redo")) }, { default: require$$0.withCtx(() => t[7] || (t[7] = [require$$0.createTextVNode(" 重做 ")])), _: 1, __: [7] }, 8, ["disabled"])])) : require$$0.createCommentVNode("", true), require$$0.createElementVNode("div", _hoisted_2$f, [require$$0.renderSlot(r.$slots, "toolbar-left", {}, void 0, true), o.toolbar.includes("import") ? (require$$0.openBlock(), require$$0.createBlock(s, { key: 0, text: "", type: "primary", icon: "el-icon-upload", onClick: t[2] || (t[2] = (d) => r.$emit("import")) }, { default: require$$0.withCtx(() => t[8] || (t[8] = [require$$0.createTextVNode(" 导入 ")])), _: 1, __: [8] })) : require$$0.createCommentVNode("", true), o.toolbar.includes("generate") ? (require$$0.openBlock(), require$$0.createBlock(s, { key: 1, text: "", type: "primary", icon: "el-icon-download", onClick: t[3] || (t[3] = (d) => r.$emit("generate")) }, { default: require$$0.withCtx(() => t[9] || (t[9] = [require$$0.createTextVNode(" 导出 ")])), _: 1, __: [9] })) : require$$0.createCommentVNode("", true), o.toolbar.includes("preview") ? (require$$0.openBlock(), require$$0.createBlock(s, { key: 2, text: "", type: "primary", icon: "el-icon-view", onClick: t[4] || (t[4] = (d) => r.$emit("preview")) }, { default: require$$0.withCtx(() => t[10] || (t[10] = [require$$0.createTextVNode(" 预览 ")])), _: 1, __: [10] })) : require$$0.createCommentVNode("", true), o.toolbar.includes("clear") ? (require$$0.openBlock(), require$$0.createBlock(s, { key: 3, text: "", type: "danger", icon: "el-icon-delete", onClick: t[5] || (t[5] = (d) => r.$emit("clear")) }, { default: require$$0.withCtx(() => t[11] || (t[11] = [require$$0.createTextVNode(" 清空 ")])), _: 1, __: [11] })) : require$$0.createCommentVNode("", true), require$$0.renderSlot(r.$slots, "toolbar", {}, void 0, true)])]), _: 3 });
        };
      } }, NfdToolbar = _export_sfc(_sfc_main$J, [["__scopeId", "data-v-0a1fc9bb"]]), widgetEmptyImg = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAUMAAADXBAMAAABynS7VAAAALVBMVEX////m5uby8vLMzMxZq+bX19eJw+2BvOdstOjd4uaFv+nR0dHO5fWUyO271OYa0h7nAAACTklEQVR42u3aPUoDQRjG8QkEsbDI6mJUUGRDKptAsDey+AVCiFjZRPACkZxAUtgKNrbiAUTxAp7C45giRCWTfYfETN7Z/B+YIs3DD4Z5ZxZiIu0pmZJxScHMLRAhQoT43/kzBQu/fgyJK70jc5OOXwNiNBpvxOUMXjdN2wqIxdOHaFw2nx4PVBCjjMQaiEvX6ommEDhxXcNxyd7okoahU2wETlxvK7hdsonxAcTJj8tO7zjqpv1lvRrPjY8IQ2cjHRLtN7cQD8RYurk9RNjoWLq5PUQ4LuWOemIUKSKOedJm3tzbXo6L8GFwknlzr+0nF/VZr5rweVVszJl49T4dces5mXn2piPGh8nMU21Ko3vuxGRXIJY76on9aN/okXg/LpXaVBu9KtR7IcaNwInl25FGD0TxSXuWvAzW133vztLp4bgIHwbHQ2I3PflMZp2RoSN/R5u60LiAxGpTPbFSyyPxMrtRA1FoXERital+owMYOj9EKSrmolJipZZHonBcNBCFxkUkTjAXPxKvmWguvlXr9ZbHNcFLp+WZ+OpKVPHfCIgQIUK0ByLEnBMN/1+ECDFHJxoiRIgQ7YEIEaJzI0SIOScG8OqGCFELMYATDREiRIj2QIQI0bkRIsScEwN4dUOEqIUYwImGCBEiRHsgQoTo3AgRYs6ftBAhQszT0IEIESJEeyBChOjcCFExMYD3IkSIECHaAxEiROdGiBAhOjdChAjRuREiRIjOjRAhQnRuhAgRonMjRIgQnRshQoTo3AgRIkTnRogQIZq5pfQNW79scsyCH28AAAAASUVORK5CYII=";
      function getAsVal(o, r = "") {
        let t = deepClone(o);
        return validateNull(r) || r.split(".").forEach((s) => {
          validateNull(t[s]) ? t = "" : t = t[s];
        }), t;
      }
      function validateNull(o) {
        if (o instanceof Date || typeof o == "boolean" || typeof o == "number" || o instanceof Array) return false;
        if (o instanceof Function) {
          const t = o.toString().replace(/\s+/g, "");
          return !!["({value})=>{}", "(res,cb)=>{}", "(res)=>{}", "()=>{}"].includes(t);
        } else if (o instanceof Object) {
          for (var r in o) return false;
          return true;
        } else return o === "null" || o == null || o === "undefined" || o === void 0 || o === "";
      }
      const deepClone = (o) => {
        var r = getObjType(o), t;
        if (r === "array") t = [];
        else if (r === "object") t = {};
        else return o;
        if (r === "array") for (var s = 0, n = o.length; s < n; s++) o[s] = (o[s] === 0, o[s]), o[s] && delete o[s].$parent, t.push(deepClone(o[s]));
        else if (r === "object") for (var d in o) o && delete o.$parent, t[d] = deepClone(o[d]);
        return t;
      }, getObjType = (o) => {
        var r = Object.prototype.toString, t = { "[object Boolean]": "boolean", "[object Number]": "number", "[object String]": "string", "[object Function]": "function", "[object Array]": "array", "[object Date]": "date", "[object RegExp]": "regExp", "[object Undefined]": "undefined", "[object Null]": "null", "[object Object]": "object" };
        return o instanceof Element ? "element" : t[r.call(o)];
      }, defaultProps = { label: "label", value: "value", desc: "desc", children: "children" }, filterCommonDicProps = (o, r = {}) => {
        if (!r) return [];
        const { label: t, value: s, desc: n, children: d } = deepClone({ ...defaultProps, ...r });
        return !t || !s || !o || (o = o.map((l) => {
          const a = { label: l[t], value: l[s], desc: l[n] };
          return l[d] && (a.children = filterCommonDicProps(l[d], r)), a;
        })), o;
      }, filterDicProps = (o, r = {}) => {
        if (!r) return [];
        const { label: t, value: s, desc: n, children: d } = deepClone({ ...defaultProps, ...r });
        return !t || !s || !o || (o = o.map((l) => {
          const a = { [t]: l.label, [s]: l.value, [n]: l.desc };
          return l.children && (a[d] = filterDicProps(l.children, r)), a;
        })), o;
      }, randomString = () => "a" + Date.now() + Math.ceil(Math.random() * 99999), _sfc_main$I = { name: "widget-form-item", props: { item: { type: Object, default: () => ({}) }, params: { type: Object, default: () => ({}) }, size: { type: String, default: "default" } }, computed: { vBind() {
        const o = Object.assign(this.deepClone(this.item), this.params, { size: this.size, dic: this.item.dicData ? filterDicProps(this.item.dicData, this.item.props) : void 0, rules: this.item.pattern ? this.item.rules.map((t) => (t.pattern && (t.pattern = new RegExp(this.item.pattern)), t)) : this.item.rules });
        return ["change", "blur", "click", "focus"].forEach((t) => delete o[t]), o.event && delete o.event, delete o.value, o;
      } }, data() {
        return { form: {} };
      }, methods: { getComponent(o, r) {
        let t = "nf-", s = o || "input";
        if (this.validateNull(r)) ["img", "array", "url"].includes(o) ? s = "array" : ["time", "timerange"].includes(o) ? s = "time" : ["year", "years", "month", "months", "date", "dates", "daterange", "week", "datetime", "datetimerange", "monthrange", "yearrange"].includes(o) ? s = "date" : ["password", "textarea", "search"].includes(o) ? s = "input" : o == "tree" ? s = "tree-select" : o == "table" && (Object.hasOwnProperty.call(this.item, "rows") ? s = "table" : s = "input-table");
        else return r;
        return t + s;
      }, getPlaceholder(o) {
        const r = o.label;
        return ["select", "checkbox", "radio", "tree", "color", "dates", "date", "datetime", "datetimerange", "daterange", "week", "month", "year", "map", "icon"].includes(o.type) ? `请选择 ${r}` : `请输入 ${r}`;
      } } }, _hoisted_1$n = { class: "widget-item__item" }, _hoisted_2$e = ["innerHTML"];
      function _sfc_render$A(o, r, t, s, n, d) {
        return require$$0.openBlock(), require$$0.createElementBlock("div", _hoisted_1$n, [(require$$0.openBlock(), require$$0.createBlock(require$$0.resolveDynamicComponent(d.getComponent(t.item.type, t.item.component)), require$$0.mergeProps(d.vBind, { multiple: false, placeholder: t.item.placeholder || t.item.label }), { default: require$$0.withCtx(() => [t.params.html ? (require$$0.openBlock(), require$$0.createElementBlock("span", { key: 0, innerHTML: t.params.html }, null, 8, _hoisted_2$e)) : require$$0.createCommentVNode("", true)]), _: 1 }, 16, ["placeholder"])), t.item.columnTip ? (require$$0.openBlock(), require$$0.createElementBlock("span", { key: 0, class: require$$0.normalizeClass(["widget-item__item--tip", ["widget-item__item--tip--" + t.item.columnTipType]]) }, require$$0.toDisplayString(t.item.columnTip), 3)) : require$$0.createCommentVNode("", true)]);
      }
      const WidgetItem = _export_sfc(_sfc_main$I, [["render", _sfc_render$A], ["__scopeId", "data-v-c899f5cc"]]), _hoisted_1$m = { class: "nfd-widget-button" }, _sfc_main$H = { __name: "button", props: { type: { type: String, default: "item" } }, emits: ["copy", "delete", "clear", "td-event"], setup(o) {
        require$$0.useCssVars((n) => ({ "0864edb2": s.value.right, "2d743d56": s.value.bottom }));
        const r = o, { type: t } = require$$0.toRefs(r), s = require$$0.computed(() => {
          let n = 0, d = 0;
          switch (t.value) {
            case "group-item":
              n = 0, d = -22;
              break;
            case "dynamic-item":
              n = 0, d = 0;
              break;
            case "group":
              n = 15, d = -10;
              break;
            case "table-item":
              n = 0, d = 0;
              break;
            case "td":
              n = 0, d = 0;
              break;
            default:
              n = 0, d = -30;
              break;
          }
          return { right: `${n}px`, bottom: `${d}px` };
        });
        return (n, d) => {
          const l = require$$0.resolveComponent("el-button"), a = require$$0.resolveComponent("el-icon-grid"), u = require$$0.resolveComponent("el-dropdown-item"), c = require$$0.resolveComponent("el-dropdown-menu"), f = require$$0.resolveComponent("el-dropdown");
          return require$$0.openBlock(), require$$0.createElementBlock("div", _hoisted_1$m, [["group"].includes(require$$0.unref(t)) ? (require$$0.openBlock(), require$$0.createBlock(l, { key: 0, class: "clear", title: "清空", circle: "", plain: "", size: "default", type: "warning", icon: "el-icon-brush", onClick: d[0] || (d[0] = require$$0.withModifiers((m) => n.$emit("clear"), ["stop"])) })) : require$$0.createCommentVNode("", true), ["td"].includes(require$$0.unref(t)) ? require$$0.createCommentVNode("", true) : (require$$0.openBlock(), require$$0.createBlock(l, { key: 1, class: require$$0.normalizeClass(["delete", require$$0.unref(t) == "table-item" ? "table-item-delete" : ""]), title: "删除", circle: "", plain: "", size: "default", type: "danger", icon: "el-icon-delete", onClick: d[1] || (d[1] = require$$0.withModifiers((m) => n.$emit("delete"), ["stop"])) }, null, 8, ["class"])), ["table-item", "td"].includes(require$$0.unref(t)) ? require$$0.createCommentVNode("", true) : (require$$0.openBlock(), require$$0.createBlock(l, { key: 2, class: "copy", title: "复制", circle: "", plain: "", size: "default", type: "primary", icon: "el-icon-copy-document", onClick: d[2] || (d[2] = require$$0.withModifiers((m) => n.$emit("copy"), ["stop"])) })), ["td"].includes(require$$0.unref(t)) ? (require$$0.openBlock(), require$$0.createBlock(f, { key: 3, class: "td", type: "primary", size: "small" }, { dropdown: require$$0.withCtx(() => [require$$0.createVNode(c, null, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { onClick: d[3] || (d[3] = (m) => n.$emit("td-event", "insert-up")) }, { default: require$$0.withCtx(() => d[17] || (d[17] = [require$$0.createTextVNode(" 上方插入 ")])), _: 1, __: [17] }), require$$0.createVNode(u, { onClick: d[4] || (d[4] = (m) => n.$emit("td-event", "insert-down")) }, { default: require$$0.withCtx(() => d[18] || (d[18] = [require$$0.createTextVNode(" 下方插入 ")])), _: 1, __: [18] }), require$$0.createVNode(u, { onClick: d[5] || (d[5] = (m) => n.$emit("td-event", "insert-right")) }, { default: require$$0.withCtx(() => d[19] || (d[19] = [require$$0.createTextVNode(" 右方插入 ")])), _: 1, __: [19] }), require$$0.createVNode(u, { onClick: d[6] || (d[6] = (m) => n.$emit("td-event", "insert-left")) }, { default: require$$0.withCtx(() => d[20] || (d[20] = [require$$0.createTextVNode(" 左方插入 ")])), _: 1, __: [20] }), require$$0.createVNode(u, { onClick: d[7] || (d[7] = (m) => n.$emit("td-event", "rowspan-up")), divided: "" }, { default: require$$0.withCtx(() => d[21] || (d[21] = [require$$0.createTextVNode(" 向上合并 ")])), _: 1, __: [21] }), require$$0.createVNode(u, { onClick: d[8] || (d[8] = (m) => n.$emit("td-event", "rowspan-down")) }, { default: require$$0.withCtx(() => d[22] || (d[22] = [require$$0.createTextVNode(" 向下合并 ")])), _: 1, __: [22] }), require$$0.createVNode(u, { onClick: d[9] || (d[9] = (m) => n.$emit("td-event", "colspan-right")) }, { default: require$$0.withCtx(() => d[23] || (d[23] = [require$$0.createTextVNode(" 向右合并 ")])), _: 1, __: [23] }), require$$0.createVNode(u, { onClick: d[10] || (d[10] = (m) => n.$emit("td-event", "colspan-left")) }, { default: require$$0.withCtx(() => d[24] || (d[24] = [require$$0.createTextVNode(" 向左合并 ")])), _: 1, __: [24] }), require$$0.createVNode(u, { onClick: d[11] || (d[11] = (m) => n.$emit("td-event", "rowspan-all")), divided: "" }, { default: require$$0.withCtx(() => d[25] || (d[25] = [require$$0.createTextVNode(" 合并整行 ")])), _: 1, __: [25] }), require$$0.createVNode(u, { onClick: d[12] || (d[12] = (m) => n.$emit("td-event", "colspan-all")) }, { default: require$$0.withCtx(() => d[26] || (d[26] = [require$$0.createTextVNode(" 合并整列 ")])), _: 1, __: [26] }), require$$0.createVNode(u, { onClick: d[13] || (d[13] = (m) => n.$emit("td-event", "rowspan-cancel")), divided: "" }, { default: require$$0.withCtx(() => d[27] || (d[27] = [require$$0.createTextVNode(" 取消合并行 ")])), _: 1, __: [27] }), require$$0.createVNode(u, { onClick: d[14] || (d[14] = (m) => n.$emit("td-event", "colspan-cancel")) }, { default: require$$0.withCtx(() => d[28] || (d[28] = [require$$0.createTextVNode(" 取消合并列 ")])), _: 1, __: [28] }), require$$0.createVNode(u, { onClick: d[15] || (d[15] = (m) => n.$emit("td-event", "rowspan-delete")), divided: "", style: { color: "#f56c6c" } }, { default: require$$0.withCtx(() => d[29] || (d[29] = [require$$0.createTextVNode(" 删除整行 ")])), _: 1, __: [29] }), require$$0.createVNode(u, { onClick: d[16] || (d[16] = (m) => n.$emit("td-event", "colspan-delete")), style: { color: "#f56c6c" } }, { default: require$$0.withCtx(() => d[30] || (d[30] = [require$$0.createTextVNode(" 删除整列 ")])), _: 1, __: [30] })]), _: 1 })]), default: require$$0.withCtx(() => [require$$0.createVNode(a, { style: { width: "18px" } })]), _: 1 })) : require$$0.createCommentVNode("", true)]);
        };
      } }, WidgetButton = _export_sfc(_sfc_main$H, [["__scopeId", "data-v-06127b38"]]), _sfc_main$G = { name: "widget-dynamic", props: ["data", "column", "select", "index", "size"], emits: ["update:select", "change"], components: { WidgetItem, WidgetButton, Draggable }, data() {
        return { selectWidget: this.select };
      }, methods: { handleSelectWidget(o) {
        this.selectWidget = this.data.column[o];
      }, handleWidgetClear(o) {
        this.data.column[o].children.column = [], this.selectWidget = this.data.column[o], this.$emit("change");
      }, handleWidgetDelete(o) {
        this.data.column.length - 1 === o ? o === 0 ? this.selectWidget = {} : this.handleSelectWidget(o - 1) : this.handleSelectWidget(o + 1), this.$nextTick(() => {
          this.data.column.splice(o, 1), this.$emit("change");
        });
      }, handleWidgetCloneTable(o) {
        let r = this.deepClone(this.data.column[o]);
        r.prop = randomString(), r.nfId = randomString(), r.children.column.forEach((t) => {
          t.prop = randomString(), t.nfId = randomString();
        }), this.data.column.splice(o, 0, r), this.$nextTick(() => {
          this.handleSelectWidget(o + 1), this.$emit("change");
        });
      }, handleDynamicAdd(o, r) {
        let t = o.newIndex;
        const s = o.item;
        if (t == 1 && t > r.children.column.length - 1 && (t = 0), ["子表单", "富文本", "坐标拾取器", "分组"].includes(s.textContent)) {
          r.children.column.splice(t, 1);
          return;
        }
        const n = this.deepClone(r.children.column[t]);
        n.prop || (n.prop = randomString()), n.nfId || (n.nfId = randomString()), n.subfield = ["table-select", "table-select-v2"].includes(r.type) ? r.type : true, delete n.icon, r.children.column[t] = { ...n }, this.selectWidget = r.children.column[t], this.$emit("change");
      }, handleDynamicSelect(o) {
        this.selectWidget = o;
      }, handleDynamicClone(o, r) {
        const t = this.deepClone(r);
        t.prop = randomString(), t.nfId = randomString(), o.children.column[o.children.column.length] = { ...t }, this.$nextTick(() => {
          this.selectWidget = o.children.column[o.children.column.length - 1], this.$emit("change");
        });
      }, handleDynamicDelete(o, r) {
        o.children.column.length - 1 == r ? r == 0 ? this.selectWidget = o : this.selectWidget = o.children.column[r - 1] : this.selectWidget = o.children.column[r + 1], this.$nextTick(() => {
          o.children.column.splice(r, 1), this.$emit("change");
        });
      } }, watch: { select(o) {
        this.selectWidget = o;
      }, selectWidget: { handler(o) {
        this.$emit("update:select", o);
      }, deep: true } } }, _hoisted_1$l = ["onClick"], _hoisted_2$d = { class: "nfd-dynamic" }, _hoisted_3$a = { class: "nfd-dynamic__header" }, _hoisted_4$9 = { class: "nfd-dynamic__body" };
      function _sfc_render$z(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("widget-item"), a = require$$0.resolveComponent("widget-button"), u = require$$0.resolveComponent("el-empty"), c = require$$0.resolveComponent("draggable");
        return require$$0.openBlock(), require$$0.createElementBlock("div", { class: require$$0.normalizeClass(["widget-dynamic hover", { "required-title": t.column.required }]) }, [require$$0.withDirectives(require$$0.createElementVNode("h3", { style: { margin: "10px" } }, require$$0.toDisplayString(t.column.label), 513), [[require$$0.vShow, t.column.label]]), require$$0.createVNode(c, { class: "widget-dynamic__body", list: t.column.children.column, group: { name: "form" }, "ghost-class": "ghost", animation: 200, handle: ".widget-dynamic__item", "item-key": "prop", onAdd: r[0] || (r[0] = (f) => d.handleDynamicAdd(f, t.column)), onEnd: r[1] || (r[1] = (f) => o.$emit("change")) }, { item: require$$0.withCtx(({ element: f, index: m }) => [t.column.children.column.length > 0 ? (require$$0.openBlock(), require$$0.createElementBlock("div", { key: 0, class: require$$0.normalizeClass(["widget-dynamic__item hover-item drag", { "active-item": n.selectWidget.prop == f.prop, required: f.required }]), style: require$$0.normalizeStyle({ minWidth: f.width ? `${f.width}px` : "25%", width: f.width ? `${f.width}px` : "25%" }), onClick: require$$0.withModifiers(($) => d.handleDynamicSelect(f), ["stop"]) }, [require$$0.createElementVNode("div", _hoisted_2$d, [require$$0.createElementVNode("div", _hoisted_3$a, require$$0.toDisplayString(f.label), 1), require$$0.createElementVNode("div", _hoisted_4$9, [require$$0.createVNode(l, { item: f, params: t.column.params, size: t.size }, null, 8, ["item", "params", "size"]), n.selectWidget.prop == f.prop ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 0, type: "dynamic-item", onDelete: ($) => d.handleDynamicDelete(t.column, m), onCopy: ($) => d.handleDynamicClone(t.column, f) }, null, 8, ["onDelete", "onCopy"])) : require$$0.createCommentVNode("", true)])])], 14, _hoisted_1$l)) : (require$$0.openBlock(), require$$0.createBlock(u, { key: 1, size: "50", style: { width: "100%" }, description: "拖拽字段至此" }))]), _: 1 }, 8, ["list"]), n.selectWidget.prop == t.column.prop ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 0, type: "group", onDelete: r[2] || (r[2] = (f) => d.handleWidgetDelete(t.index)), onCopy: r[3] || (r[3] = (f) => d.handleWidgetCloneTable(t.index)), onClear: r[4] || (r[4] = (f) => d.handleWidgetClear(t.index)) })) : require$$0.createCommentVNode("", true)], 2);
      }
      const WidgetDynamic = _export_sfc(_sfc_main$G, [["render", _sfc_render$z]]), _sfc_main$F = { name: "widget-group", components: { WidgetItem, WidgetDynamic, WidgetButton, Draggable }, props: ["data", "column", "select", "index", "size"], emits: ["update:select", "change"], data() {
        return { selectWidget: this.select };
      }, methods: { handleSelectWidget(o) {
        this.selectWidget = this.data.column[o];
      }, handleWidgetClear(o) {
        this.data.column[o].children.column = [], this.selectWidget = this.data.column[o], this.$emit("change");
      }, handleWidgetDelete(o) {
        this.data.column.length - 1 === o ? o === 0 ? this.selectWidget = {} : this.handleSelectWidget(o - 1) : this.handleSelectWidget(o + 1), this.$nextTick(() => {
          this.data.column.splice(o, 1), this.$emit("change");
        });
      }, handleWidgetCloneTable(o) {
        let r = this.deepClone(this.data.column[o]);
        r.prop = randomString(), r.nfId = randomString(), r.children.column.forEach((t) => {
          t.prop = randomString(), t.nfId = randomString();
        }), this.data.column.splice(o, 0, r), this.$nextTick(() => {
          this.handleSelectWidget(o + 1), this.$emit("change");
        });
      }, handleDynamicSelect(o) {
        this.selectWidget = o;
      }, handleDynamicClone(o, r) {
        const t = this.deepClone(r);
        t.prop = randomString(), t.nfId = randomString(), o.children.column[o.children.column.length] = { ...t }, this.$nextTick(() => {
          this.selectWidget = o.children.column[o.children.column.length - 1], this.$emit("change");
        });
      }, handleDynamicDelete(o, r) {
        o.children.column.length - 1 == r ? r == 0 ? this.selectWidget = o : this.selectWidget = o.children.column[r - 1] : this.selectWidget = o.children.column[r + 1], this.$nextTick(() => {
          o.children.column.splice(r, 1), this.$emit("change");
        });
      }, handleWidgetGroupAdd(o, r) {
        let t = o.newIndex;
        const s = o.item;
        if (t == 1 && t > r.children.column.length - 1 && (t = 0), ["分组"].includes(s.textContent)) {
          r.children.column.splice(t, 1);
          return;
        }
        const n = this.deepClone(r.children.column[t]);
        n.prop || (n.prop = randomString()), n.nfId || (n.nfId = randomString()), delete n.icon, delete n.subfield, n.type == "dynamic" ? n.span = 24 : n.span = 12, r.children.column[t] = { ...n }, this.selectWidget = r.children.column[t], this.$emit("change");
      } }, watch: { select(o) {
        this.selectWidget = o;
      }, selectWidget: { handler(o) {
        this.$emit("update:select", o);
      }, deep: true } } }, _hoisted_1$k = { class: "widget-group hover" }, _hoisted_2$c = { class: "widget-group__head" };
      function _sfc_render$y(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("widget-dynamic"), a = require$$0.resolveComponent("widget-item"), u = require$$0.resolveComponent("widget-button"), c = require$$0.resolveComponent("el-form-item"), f = require$$0.resolveComponent("draggable");
        return require$$0.openBlock(), require$$0.createElementBlock("div", _hoisted_1$k, [require$$0.withDirectives(require$$0.createElementVNode("h4", _hoisted_2$c, [require$$0.withDirectives(require$$0.createElementVNode("i", { class: require$$0.normalizeClass(t.column.icon), style: { "margin-right": "10px" } }, null, 2), [[require$$0.vShow, t.column.icon]]), require$$0.createTextVNode(" " + require$$0.toDisplayString(t.column.label), 1)], 512), [[require$$0.vShow, t.column.label]]), require$$0.createVNode(f, { class: "widget-group__body", list: t.column.children.column, group: { name: "form" }, "ghost-class": "ghost", animation: 200, "item-key": "prop", onAdd: r[2] || (r[2] = (m) => d.handleWidgetGroupAdd(m, t.column)), onEnd: r[3] || (r[3] = (m) => o.$emit("change")) }, { item: require$$0.withCtx(({ element: m, index: $ }) => [m.type == "dynamic" ? (require$$0.openBlock(), require$$0.createBlock(l, { key: 0, class: require$$0.normalizeClass({ active: n.selectWidget.prop == m.prop }), onClick: require$$0.withModifiers((p) => d.handleDynamicSelect(m), ["stop"]), data: t.column.children, column: m, index: $, select: n.selectWidget, "onUpdate:select": r[0] || (r[0] = (p) => n.selectWidget = p), onChange: r[1] || (r[1] = (p) => o.$emit("change")) }, null, 8, ["class", "onClick", "data", "column", "index", "select"])) : (require$$0.openBlock(), require$$0.createElementBlock("div", { key: 1, style: require$$0.normalizeStyle({ width: `${m.span / 24 * 100}%`, float: "left" }) }, [require$$0.createVNode(c, { class: require$$0.normalizeClass(["widget-group__item hover drag", [{ "active-item": n.selectWidget.prop == m.prop, required: m.required }, "nf-form--" + m.labelPosition || ""]]), label: m.label, prop: m.prop, onClick: require$$0.withModifiers((p) => d.handleDynamicSelect(m), ["stop"]) }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { item: m, params: m.params, size: t.size }, null, 8, ["item", "params", "size"]), n.selectWidget.prop == m.prop ? (require$$0.openBlock(), require$$0.createBlock(u, { key: 0, type: "group-item", onDelete: (p) => d.handleDynamicDelete(t.column, $), onCopy: (p) => d.handleDynamicClone(t.column, m) }, null, 8, ["onDelete", "onCopy"])) : require$$0.createCommentVNode("", true)]), _: 2 }, 1032, ["label", "prop", "class", "onClick"])], 4))]), _: 1 }, 8, ["list"]), n.selectWidget.prop == t.column.prop ? (require$$0.openBlock(), require$$0.createBlock(u, { key: 0, type: "group", onDelete: r[4] || (r[4] = (m) => d.handleWidgetDelete(t.index)), onCopy: r[5] || (r[5] = (m) => d.handleWidgetCloneTable(t.index)), onClear: r[6] || (r[6] = (m) => d.handleWidgetClear(t.index)) })) : require$$0.createCommentVNode("", true)]);
      }
      const WidgetGroup = _export_sfc(_sfc_main$F, [["render", _sfc_render$y]]), _sfc_main$E = { name: "widget-table", props: ["data", "column", "select", "index", "size"], components: { Draggable, WidgetItem, WidgetButton }, computed: { rows() {
        return this.column.rows;
      } }, watch: { select(o) {
        this.selectWidget = o, Object.keys(o).length > 0 && (this.selectTd = "");
      }, selectWidget: { handler(o) {
        this.$emit("update:select", o);
      }, deep: true } }, data() {
        return { selectWidget: this.select, selectTd: "" };
      }, methods: { handleSelectWidget(o) {
        this.selectWidget = this.data.column[o], this.selectTd = "";
      }, handleTableDelete(o) {
        this.data.column.length - 1 === o ? o === 0 ? this.selectWidget = {} : this.handleSelectWidget(o - 1) : this.handleSelectWidget(o + 1), this.$nextTick(() => {
          this.data.column.splice(o, 1), this.$emit("change");
        });
      }, handleTableClone(o) {
        let r = this.deepClone(this.data.column[o]);
        r.prop = randomString(), r.nfId = randomString(), r.rows.forEach((t) => {
          t.cols.forEach((s) => {
            s.column.forEach((n) => {
              n.prop = randomString(), n.nfId = randomString();
            }), s.prop = randomString(), s.nfId = randomString();
          });
        }), this.data.column.splice(o + 1, 0, r), this.$nextTick(() => {
          this.handleSelectWidget(o), this.$emit("change");
        });
      }, handleTableClear(o) {
        this.data.column[o].rows.forEach((r) => {
          r.cols.forEach((t) => {
            t.column = [];
          });
        }), this.$emit("change");
      }, handleWidgetAdd(o, r, t) {
        var d, l;
        const s = o.newIndex, n = (l = (d = this.column.rows[r]) == null ? void 0 : d.cols[t]) == null ? void 0 : l.column;
        if (n.length > 0) {
          const a = this.deepClone(n[s]);
          a.prop || (a.prop = randomString()), a.nfId || (a.nfId = randomString()), a.subfield = "table", n.splice(s, 1, a), this.selectWidget = a, this.$emit("change");
        }
      }, handleWidgetDelete(o, r, t) {
        var s, n, d;
        (d = (n = (s = this.column.rows[o]) == null ? void 0 : s.cols[r]) == null ? void 0 : n.column) == null || d.splice(t, 1), this.selectWidget = {}, this.selectTd = "", this.$emit("change");
      }, handleWidgetSelect(o) {
        o.prop || (o.prop = randomString()), o.nfId || (o.nfId = randomString()), this.selectWidget = o, this.selectTd = "";
      }, handleTdSelect(o, r, t) {
        t.prop || (t.prop = randomString()), t.nfId || (t.nfId = randomString()), t.type = "td", this.selectWidget = t, setTimeout(() => {
          this.selectTd = String(o) + r;
        });
      }, handleTableEvent(o, r, t) {
        switch (o) {
          case "rowspan-up":
          case "rowspan-down":
            this.handleTableRowSpan(o, r, t);
            break;
          case "colspan-right":
          case "colspan-left":
            this.handleTableColSpan(o, r, t);
            break;
          case "insert-up":
          case "insert-down":
            this.handleTableRowInsert(o, r, t);
            break;
          case "insert-right":
          case "insert-left":
            this.handleTableColInsert(o, r, t);
            break;
          case "rowspan-all":
            const s = this.column.rows[r];
            if ((s.cols[0].rowspan == 0 ? 0 : s.cols[0].rowspan || 1) != 1) return this.$message.error("合并单元格的列数不一致，无法合并"), false;
            let d = 1;
            for (let c = 1; c < s.cols.length; c++) {
              const f = s.cols[c].rowspan == 0 ? 0 : s.cols[c].rowspan || 1, m = s.cols[c].colspan == 0 ? 0 : s.cols[c].colspan || 1;
              if (f == 1 && m == 1) s.cols[c].colspan = 0, d++;
              else break;
            }
            s.cols[0].colspan = d;
            break;
          case "colspan-all":
            const l = this.column.rows;
            if ((l[0].cols[t].colspan == 0 ? 0 : l[0].cols[t].colspan || 1) != 1) return this.$message.error("合并单元格的行数不一致，无法合并"), false;
            let u = 1;
            for (let c = 1; c < l.length; c++) {
              const f = l[c], m = f.cols[t].colspan == 0 ? 0 : f.cols[t].colspan || 1, $ = f.cols[t].rowspan == 0 ? 0 : f.cols[t].rowspan || 1;
              if (m == 1 && $ == 1) f.cols[t].rowspan = 0, u++;
              else break;
            }
            l[0].cols[t].rowspan = u;
            break;
          case "rowspan-cancel":
            this.handleTableRowSpanCancel(r, t);
            break;
          case "colspan-cancel":
            this.handleTableColSpanCancel(r, t);
            break;
          case "rowspan-delete":
            this.handleTableColSpanCancel(r, t), this.column.rows.splice(r, 1), this.selectTd = "", this.selectWidget = {};
            break;
          case "colspan-delete":
            this.handleTableRowSpanCancel(r, t), this.column.rows.forEach((c) => {
              c.cols.splice(t, 1);
            }), this.selectTd = "", this.selectWidget = {};
            break;
        }
        this.$emit("change");
      }, handleTableRowSpan(o, r, t) {
        var l, a;
        const s = (l = this.column.rows[r]) == null ? void 0 : l.cols[t], n = s.colspan == 0 ? 0 : s.colspan || 1, d = s.rowspan == 0 ? 0 : s.rowspan || 1;
        if (s) {
          if (o == "rowspan-up") {
            if (r > 0) {
              const u = this.getPrevRowCol(r, t);
              if (u) {
                const c = u.colspan == 0 ? 0 : u.colspan || 1, f = u.rowspan == 0 ? 0 : u.rowspan || 1;
                if (n < c || c == 0) return this.$message.error("合并单元格的列数不一致，无法合并"), false;
                for (let m = 0; m < n - c; m++) if (!this.handleTableColSpan("colspan-right", r - 1, t)) return false;
                u.rowspan = d + f, s.rowspan = 0;
              }
            }
          } else if (o == "rowspan-down") {
            const u = (a = this.column.rows[r + d]) == null ? void 0 : a.cols[t];
            if (u) {
              const c = u.colspan == 0 ? 0 : u.colspan || 1, f = u.rowspan == 0 ? 0 : u.rowspan || 1;
              if (n < c || c == 0) return this.$message.error("合并单元格的列数不一致，无法合并"), false;
              for (let m = 0; m < n - c; m++) if (!this.handleTableColSpan("colspan-right", r + d, t)) return false;
              d == 0 || (s.rowspan = d + f), u.rowspan = 0;
            }
          }
        }
        return true;
      }, handleTableColSpan(o, r, t) {
        var l, a;
        const s = (l = this.column.rows[r]) == null ? void 0 : l.cols[t], n = s.rowspan == 0 ? 0 : s.rowspan || 1, d = s.colspan == 0 ? 0 : s.colspan || 1;
        if (s) {
          if (o == "colspan-right") {
            const u = (a = this.column.rows[r]) == null ? void 0 : a.cols[t + d];
            if (u) {
              const c = u.rowspan == 0 ? 0 : u.rowspan || 1, f = u.colspan == 0 ? 0 : u.colspan || 1;
              if (n < c || c == 0) return this.$message.error("合并单元格的行数不一致，无法合并"), false;
              for (let m = 0; m < n - c; m++) if (!this.handleTableRowSpan("rowspan-down", r, t + d)) return false;
              d == 0 || (s.colspan = d + f), u.colspan = 0;
            }
          } else if (o == "colspan-left" && t > 0) {
            const u = this.getPrevCol(r, t);
            if (u) {
              const c = u.rowspan == 0 ? 0 : u.rowspan || 1, f = u.colspan == 0 ? 0 : u.colspan || 1;
              if (n < c || c == 0) return this.$message.error("合并单元格的行数不一致，无法合并"), false;
              for (let m = 0; m < n - c; m++) if (!this.handleTableRowSpan("rowspan-down", r, t - 1)) return false;
              u.colspan = d + f, s.colspan = 0;
            }
          }
        }
        return true;
      }, getPrevRowCol(o, r) {
        var s;
        const t = (s = this.column.rows[o - 1]) == null ? void 0 : s.cols[r];
        if (t) return (t.rowspan == 0 ? 0 : t.rowspan || 1) == 0 ? this.getPrevRowCol(o - 1, r) : t;
      }, getPrevCol(o, r) {
        var s;
        const t = (s = this.column.rows[o]) == null ? void 0 : s.cols[r - 1];
        if (t) return (t.colspan == 0 ? 0 : t.colspan || 1) == 0 ? this.getPrevCol(o, r - 1) : t;
      }, handleTableRowInsert(o, r, t) {
        const s = this.column.rows[r], n = s.cols[t], d = n.rowspan == 0 ? 0 : n.rowspan || 1;
        let l = 0;
        s.cols.forEach((u) => {
          u.colspan == 0 ? l += 0 : l += u.colspan || 1;
        });
        let a = r;
        if (n) {
          o == "insert-up" ? this.selectTd = String(r + 1) + t : o == "insert-down" && (a = r + d);
          const u = [];
          for (let c = 0; c < l; c++) u.push({ column: [], colspan: 1, rowspan: 1 });
          this.column.rows.splice(a, 0, { cols: u });
        }
      }, handleTableColInsert(o, r, t) {
        const s = this.column.rows[r].cols[t], n = s.colspan == 0 ? 0 : s.colspan || 1, d = this.column.rows.length;
        let l = r;
        if (s) {
          o == "insert-right" ? l = t + n : o == "insert-left" && (this.selectTd = String(r) + (t + 1));
          for (let a = 0; a < d; a++) {
            const u = { column: [], colspan: 1, rowspan: 1 };
            this.column.rows[a].cols.splice(l, 0, u);
          }
        }
      }, handleTableRowSpanCancel(o, r) {
        const t = this.column.rows[o], s = t.cols[r], n = s.colspan == 0 ? 0 : s.colspan || 1;
        for (let d = r; d < r + n; d++) t.cols[d].colspan = 1;
      }, handleTableColSpanCancel(o, r) {
        const s = this.column.rows[o].cols[r], n = s.rowspan == 0 ? 0 : s.rowspan || 1;
        for (let d = o; d < o + n; d++) this.column.rows[d].cols[r].rowspan = 1;
      } } }, _hoisted_1$j = { class: "nfd-table" }, _hoisted_2$b = { class: "nfd-table__title" }, _hoisted_3$9 = { class: "nfd-table__main" }, _hoisted_4$8 = ["colspan", "rowspan", "onClick"], _hoisted_5$5 = ["onClick"];
      function _sfc_render$x(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("widget-item"), a = require$$0.resolveComponent("widget-button"), u = require$$0.resolveComponent("draggable");
        return require$$0.openBlock(), require$$0.createElementBlock("div", _hoisted_1$j, [require$$0.createElementVNode("div", _hoisted_2$b, require$$0.toDisplayString(t.column.label), 1), require$$0.createElementVNode("table", _hoisted_3$9, [require$$0.createElementVNode("tbody", null, [(require$$0.openBlock(true), require$$0.createElementBlock(require$$0.Fragment, null, require$$0.renderList(d.rows, (c, f) => (require$$0.openBlock(), require$$0.createElementBlock("tr", { key: f }, [(require$$0.openBlock(true), require$$0.createElementBlock(require$$0.Fragment, null, require$$0.renderList(c.cols, (m, $) => (require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, { key: $ }, [m.colspan != 0 && m.rowspan != 0 ? (require$$0.openBlock(), require$$0.createElementBlock("td", { key: 0, class: require$$0.normalizeClass(["nfd-table__cell hover-item", { "active-item": String(f) + $ == n.selectTd }]), style: require$$0.normalizeStyle(m.style), colspan: m.colspan || 1, rowspan: m.rowspan || 1, onClick: require$$0.withModifiers((p) => d.handleTdSelect(f, $, m), ["stop"]) }, [require$$0.createVNode(u, { class: "nfd-table__widget", list: m.column, group: { name: "form" }, "item-key": "prop", onAdd: (p) => d.handleWidgetAdd(p, f, $), onEnd: r[0] || (r[0] = (p) => o.$emit("change")) }, { item: require$$0.withCtx(({ element: p, index: g }) => [require$$0.createElementVNode("div", { class: require$$0.normalizeClass([{ "active-item": p.prop && n.selectWidget.prop == p.prop, required: p.required }, "nfd-table__widget--item"]), onClick: require$$0.withModifiers((b) => d.handleWidgetSelect(p), ["stop"]) }, [require$$0.createVNode(l, { item: p, params: p.params, size: t.size }, null, 8, ["item", "params", "size"]), p.prop && n.selectWidget.prop == p.prop ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 0, type: "table-item", onDelete: (b) => d.handleWidgetDelete(f, $, g) }, null, 8, ["onDelete"])) : require$$0.createCommentVNode("", true)], 10, _hoisted_5$5)]), _: 2 }, 1032, ["list", "onAdd"]), String(f) + $ == n.selectTd ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 0, type: "td", onDelete: (p) => d.handleWidgetDelete(f, $, t.index), onTdEvent: (p) => d.handleTableEvent(p, f, $) }, null, 8, ["onDelete", "onTdEvent"])) : require$$0.createCommentVNode("", true)], 14, _hoisted_4$8)) : require$$0.createCommentVNode("", true)], 64))), 128))]))), 128))])]), n.selectWidget.prop == t.column.prop ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 0, type: "group", onDelete: r[1] || (r[1] = (c) => d.handleTableDelete(t.index)), onCopy: r[2] || (r[2] = (c) => d.handleTableClone(t.index)), onClear: r[3] || (r[3] = (c) => d.handleTableClear(t.index)) })) : require$$0.createCommentVNode("", true)]);
      }
      const WidgetTable = _export_sfc(_sfc_main$E, [["render", _sfc_render$x]]), _sfc_main$D = { name: "widget", components: { Draggable, WidgetItem, WidgetDynamic, WidgetGroup, WidgetTable, WidgetButton }, props: ["data", "select"], emits: ["update:select", "change"], data() {
        return { widgetEmptyImg, selectWidget: this.select, form: {} };
      }, methods: { handleSelectWidget(o) {
        o == -1 ? this.selectWidget = {} : this.selectWidget = this.data.column[o];
      }, handleWidgetAdd(o) {
        const r = o.newIndex, t = this.deepClone(this.data.column[r]);
        t.prop || (t.prop = randomString()), t.nfId || (t.nfId = randomString()), delete t.icon, delete t.subfield, t.type == "title" && (delete t.label, this.form[t.prop] = t.value), this.data.column[r] = t, this.handleSelectWidget(r), this.$emit("change");
      }, handleWidgetDelete(o) {
        this.data.column.length - 1 === o ? o === 0 ? this.selectWidget = {} : this.handleSelectWidget(o - 1) : this.handleSelectWidget(o + 1), this.$nextTick(() => {
          this.data.column.splice(o, 1), this.$emit("change");
        });
      }, handleWidgetClone(o) {
        let r = this.deepClone(this.data.column[o]);
        r.prop = randomString(), r.nfId = randomString(), this.data.column.push(r), this.$nextTick(() => {
          this.handleSelectWidget(this.data.column.length - 1), this.$emit("change");
        });
      } }, watch: { select(o) {
        this.selectWidget = o;
      }, selectWidget: { handler(o) {
        this.$emit("update:select", o);
      }, deep: true } } };
      function _sfc_render$w(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("widget-dynamic"), a = require$$0.resolveComponent("widget-group"), u = require$$0.resolveComponent("widget-table"), c = require$$0.resolveComponent("widget-item"), f = require$$0.resolveComponent("widget-button"), m = require$$0.resolveComponent("el-form-item"), $ = require$$0.resolveComponent("el-col"), p = require$$0.resolveComponent("draggable"), g = require$$0.resolveComponent("el-row"), b = require$$0.resolveComponent("el-form"), h = require$$0.resolveComponent("el-main");
        return require$$0.openBlock(), require$$0.createBlock(h, { class: "widget", style: require$$0.normalizeStyle({ background: t.data.column && t.data.column.length == 0 ? `url(${n.widgetEmptyImg}) no-repeat 50%` : "" }), onClick: r[7] || (r[7] = (q) => d.handleSelectWidget(-1)) }, { default: require$$0.withCtx(() => [require$$0.createVNode(b, { "label-position": t.data.labelPosition || "left", "label-width": t.data.labelWidth ? `${t.data.labelWidth}px` : "100px", "label-suffix": t.data.labelSuffix || "：", model: n.form, size: t.data.size || "default", ref: "widgetForm" }, { default: require$$0.withCtx(() => [require$$0.createVNode(g, null, { default: require$$0.withCtx(() => [require$$0.createVNode(p, { class: "widget-list", list: t.data.column, group: { name: "form" }, "ghost-class": "ghost", animation: 300, "item-key": "prop", onAdd: d.handleWidgetAdd, onEnd: r[6] || (r[6] = (q) => o.$emit("change")) }, { item: require$$0.withCtx(({ element: q, index: v }) => [["dynamic", "table-select", "table-select-v2"].includes(q.type) ? (require$$0.openBlock(), require$$0.createBlock(l, { key: 0, class: require$$0.normalizeClass({ active: n.selectWidget.prop == q.prop }), onClick: require$$0.withModifiers((y) => d.handleSelectWidget(v), ["stop"]), data: t.data, column: q, index: v, size: t.data.size || q.size || "default", select: n.selectWidget, "onUpdate:select": r[0] || (r[0] = (y) => n.selectWidget = y), onChange: r[1] || (r[1] = (y) => o.$emit("change")) }, null, 8, ["class", "onClick", "data", "column", "index", "size", "select"])) : q.type == "group" ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 1, class: require$$0.normalizeClass({ active: n.selectWidget.prop == q.prop }), onClick: require$$0.withModifiers((y) => d.handleSelectWidget(v), ["stop"]), data: t.data, column: q, index: v, size: t.data.size || q.size || "default", select: n.selectWidget, "onUpdate:select": r[2] || (r[2] = (y) => n.selectWidget = y), onChange: r[3] || (r[3] = (y) => o.$emit("change")) }, null, 8, ["class", "onClick", "data", "column", "index", "size", "select"])) : q.type == "table" ? (require$$0.openBlock(), require$$0.createBlock(u, { key: 2, class: require$$0.normalizeClass({ active: n.selectWidget.prop == q.prop }), onClick: require$$0.withModifiers((y) => d.handleSelectWidget(v), ["stop"]), data: t.data, column: q, index: v, size: t.data.size || q.size || "default", select: n.selectWidget, "onUpdate:select": r[4] || (r[4] = (y) => n.selectWidget = y), onChange: r[5] || (r[5] = (y) => o.$emit("change")) }, null, 8, ["class", "onClick", "data", "column", "index", "size", "select"])) : (require$$0.openBlock(), require$$0.createBlock($, { key: 3, md: q.span || t.data.span || 12, xs: 24, offset: q.offset || 0, style: { width: "100%" } }, { default: require$$0.withCtx(() => [require$$0.createVNode(m, { class: require$$0.normalizeClass(["widget-item hover drag", [{ active: n.selectWidget.prop == q.prop, required: q.required }, "nf-form--" + q.labelPosition || ""]]), label: q.label, prop: q.prop, size: t.data.size || q.size || "default", "label-width": q.labelWidth, onClick: require$$0.withModifiers((y) => d.handleSelectWidget(v), ["stop"]) }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { item: q, params: q.params, size: t.data.size || q.size || "default" }, null, 8, ["item", "params", "size"]), n.selectWidget.prop == q.prop ? (require$$0.openBlock(), require$$0.createBlock(f, { key: 0, onDelete: (y) => d.handleWidgetDelete(v), onCopy: (y) => d.handleWidgetClone(v) }, null, 8, ["onDelete", "onCopy"])) : require$$0.createCommentVNode("", true)]), _: 2 }, 1032, ["label", "prop", "size", "class", "label-width", "onClick"])]), _: 2 }, 1032, ["md", "offset"]))]), _: 1 }, 8, ["list", "onAdd"])]), _: 1 })]), _: 1 }, 8, ["label-position", "label-width", "label-suffix", "model", "size"])]), _: 1 }, 8, ["style"]);
      }
      const NfdWidget = _export_sfc(_sfc_main$D, [["render", _sfc_render$w]]), _sfc_main$C = { name: "widget-config", props: { data: { type: Object, default: () => ({}) }, defaultValues: { type: Object }, isCrud: { type: Boolean, default: false } }, computed: { getComponent() {
        const o = "nfd-config-", { type: r, component: t } = this.data;
        if ((!r || t) && r != "ueditor") return o + "custom";
        let s = "input";
        return [void 0, "input", "password", "url"].includes(r) ? s = "input" : ["year", "years", "month", "months", "week", "date", "dates", "daterange", "datetime", "datetimerange", "time", "timerange", "monthrange", "yearrange"].includes(r) ? s = "date" : ["array", "img"].includes(r) ? s = "array" : ["tree", "cascader"].includes(r) ? s = "tree" : ["radio", "checkbox", "select", "segmented"].includes(r) ? s = "select" : r == "table" ? Object.hasOwnProperty.call(this.data, "rows") ? s = "table" : s = "input-table" : s = r, o + s;
      } }, data() {
        return { fields, collapse: ["1", "2", "3", "4", "5", "6", "7", "8", "9"] };
      }, methods: { changeCollapse(o) {
        o == "close" ? this.collapse = [] : this.collapse = ["1", "2", "3", "4", "5", "6", "7", "8", "9"];
      }, handleChangeType(o) {
        if (!o) return;
        let r;
        for (let t of this.fields) if (r = t.list.find((s) => s.type == o), r) break;
        if (r) {
          for (let t in this.data) !Object.prototype.hasOwnProperty.call(r, t) && !["label", "prop", "span", "nfId", "icon"].includes(t) && delete this.data[t];
          for (let t in r) r && Object.prototype.hasOwnProperty.call(r, t) && !["label", "prop", "span", "nfId", "icon"].includes(t) && (this.data[t] = r[t]);
        }
      } } }, _hoisted_1$i = { class: "widget-config" };
      function _sfc_render$v(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("el-icon-info-filled"), a = require$$0.resolveComponent("el-icon"), u = require$$0.resolveComponent("el-option"), c = require$$0.resolveComponent("el-option-group"), f = require$$0.resolveComponent("el-select"), m = require$$0.resolveComponent("el-form-item"), $ = require$$0.resolveComponent("el-input"), p = require$$0.resolveComponent("el-collapse-item"), g = require$$0.resolveComponent("el-icon-refrigerator"), b = require$$0.resolveComponent("nfd-config-database"), h = require$$0.resolveComponent("el-icon-histogram"), q = require$$0.resolveComponent("nfd-config-crud"), v = require$$0.resolveComponent("el-icon-grid"), y = require$$0.resolveComponent("nfd-config-form"), w = require$$0.resolveComponent("el-icon-help-filled"), T = require$$0.resolveComponent("el-icon-promotion"), E = require$$0.resolveComponent("nfd-config-event"), P = require$$0.resolveComponent("el-collapse"), V = require$$0.resolveComponent("el-form"), S = require$$0.resolveComponent("el-empty");
        return require$$0.openBlock(), require$$0.createElementBlock("div", _hoisted_1$i, [this.data && Object.keys(this.data).length > 0 ? (require$$0.openBlock(), require$$0.createBlock(V, { key: 0, "label-suffix": "：", labelPosition: "left", labelWidth: "90px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(P, { modelValue: n.collapse, "onUpdate:modelValue": r[3] || (r[3] = (k) => n.collapse = k) }, { default: require$$0.withCtx(() => [require$$0.createVNode(p, { name: "1", title: "基本属性" }, { title: require$$0.withCtx(() => [require$$0.createVNode(a, null, { default: require$$0.withCtx(() => [require$$0.createVNode(l)]), _: 1 }), r[4] || (r[4] = require$$0.createElementVNode("span", null, "基本属性", -1))]), default: require$$0.withCtx(() => [t.data.type && !t.data.component ? (require$$0.openBlock(), require$$0.createBlock(m, { key: 0, label: "类型" }, { default: require$$0.withCtx(() => [require$$0.createVNode(f, { modelValue: t.data.type, "onUpdate:modelValue": r[0] || (r[0] = (k) => t.data.type = k), style: { width: "100%" }, placeholder: "请选择类型", filterable: "", onChange: d.handleChangeType }, { default: require$$0.withCtx(() => [(require$$0.openBlock(true), require$$0.createElementBlock(require$$0.Fragment, null, require$$0.renderList(n.fields, (k) => (require$$0.openBlock(), require$$0.createBlock(c, { key: k.title, label: k.title }, { default: require$$0.withCtx(() => [(require$$0.openBlock(true), require$$0.createElementBlock(require$$0.Fragment, null, require$$0.renderList(k.list, (_) => (require$$0.openBlock(), require$$0.createBlock(u, { key: _.type, label: _.label, value: _.type }, null, 8, ["label", "value"]))), 128))]), _: 2 }, 1032, ["label"]))), 128))]), _: 1 }, 8, ["modelValue", "onChange"])]), _: 1 })) : require$$0.createCommentVNode("", true), require$$0.createVNode(m, { label: "属性值" }, { default: require$$0.withCtx(() => [require$$0.createVNode($, { modelValue: t.data.prop, "onUpdate:modelValue": r[1] || (r[1] = (k) => t.data.prop = k), clearable: "", placeholder: "属性值" }, null, 8, ["modelValue"])]), _: 1 }), ["td", "title"].includes(t.data.type) ? require$$0.createCommentVNode("", true) : (require$$0.openBlock(), require$$0.createBlock(m, { key: 1, label: "名称" }, { default: require$$0.withCtx(() => [require$$0.createVNode($, { modelValue: t.data.label, "onUpdate:modelValue": r[2] || (r[2] = (k) => t.data.label = k), clearable: "", placeholder: "名称" }, null, 8, ["modelValue"])]), _: 1 }))]), _: 1 }), ["table", "dynamic", "table-select-v2"].includes(t.data.type) ? (require$$0.openBlock(), require$$0.createBlock(p, { key: 0, name: "6", title: "数据库配置" }, { title: require$$0.withCtx(() => [require$$0.createVNode(a, null, { default: require$$0.withCtx(() => [require$$0.createVNode(g)]), _: 1 }), r[5] || (r[5] = require$$0.createElementVNode("span", null, "数据库配置", -1))]), default: require$$0.withCtx(() => [require$$0.createVNode(b, { data: t.data }, null, 8, ["data"])]), _: 1 })) : require$$0.createCommentVNode("", true), t.isCrud && !["td", "group", "text", "title"].includes(t.data.type) && !["table-select", "table-select-v2", true].includes(t.data.subfield) ? (require$$0.openBlock(), require$$0.createBlock(p, { key: 1, name: "2", title: "表格属性" }, { title: require$$0.withCtx(() => [require$$0.createVNode(a, null, { default: require$$0.withCtx(() => [require$$0.createVNode(h)]), _: 1 }), r[6] || (r[6] = require$$0.createElementVNode("span", null, "表格属性", -1))]), default: require$$0.withCtx(() => [require$$0.createVNode(q, { data: t.data }, null, 8, ["data"])]), _: 1 })) : require$$0.createCommentVNode("", true), ["td", "title"].includes(t.data.type) ? require$$0.createCommentVNode("", true) : (require$$0.openBlock(), require$$0.createBlock(p, { key: 2, name: "3", title: "表单属性" }, { title: require$$0.withCtx(() => [require$$0.createVNode(a, null, { default: require$$0.withCtx(() => [require$$0.createVNode(v)]), _: 1 }), r[7] || (r[7] = require$$0.createElementVNode("span", null, "表单属性", -1))]), default: require$$0.withCtx(() => [require$$0.createVNode(y, { data: t.data, "default-values": t.defaultValues }, null, 8, ["data", "default-values"])]), _: 1 })), require$$0.createVNode(p, { name: "4", title: "组件属性" }, { title: require$$0.withCtx(() => [require$$0.createVNode(a, null, { default: require$$0.withCtx(() => [require$$0.createVNode(w)]), _: 1 }), r[8] || (r[8] = require$$0.createElementVNode("span", null, "组件属性", -1))]), default: require$$0.withCtx(() => [(require$$0.openBlock(), require$$0.createBlock(require$$0.resolveDynamicComponent(d.getComponent), { data: t.data }, null, 8, ["data"]))]), _: 1 }), ["group", "table", "td"].includes(t.data.type) ? require$$0.createCommentVNode("", true) : (require$$0.openBlock(), require$$0.createBlock(p, { key: 3, name: "5", title: "事件属性" }, { title: require$$0.withCtx(() => [require$$0.createVNode(a, null, { default: require$$0.withCtx(() => [require$$0.createVNode(T)]), _: 1 }), r[9] || (r[9] = require$$0.createElementVNode("span", null, "事件属性", -1))]), default: require$$0.withCtx(() => [require$$0.createVNode(E, { data: t.data }, null, 8, ["data"])]), _: 1 }))]), _: 1 }, 8, ["modelValue"])]), _: 1 })) : (require$$0.openBlock(), require$$0.createBlock(S, { key: 1, description: "拖拽字段进行配置", style: { "margin-top": "90%" } }))]);
      }
      const WidgetConfig = _export_sfc(_sfc_main$C, [["render", _sfc_render$v]]), _sfc_main$B = { props: ["data"], data() {
        return { initFunction: "" };
      }, watch: { "data.initFunction": { handler() {
        const { initFunction: o } = this.data;
        this.initFunction = o ? o + "" : `() => {\r
\r
}`;
      }, immediate: true }, initFunction(o) {
        try {
          this.data.initFunction = Function('"use strict";return (' + o + ")")();
        } catch {
        }
      } } }, _hoisted_1$h = { class: "nfd-config-form" }, _hoisted_2$a = { class: "el-form--label-top" }, _hoisted_3$8 = { class: "el-form-item" }, _hoisted_4$7 = { class: "el-form-item__content" };
      function _sfc_render$u(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("el-input-number"), a = require$$0.resolveComponent("el-form-item"), u = require$$0.resolveComponent("el-option"), c = require$$0.resolveComponent("el-select"), f = require$$0.resolveComponent("el-input"), m = require$$0.resolveComponent("el-switch"), $ = require$$0.resolveComponent("el-radio"), p = require$$0.resolveComponent("el-radio-group"), g = require$$0.resolveComponent("nf-codemirror"), b = require$$0.resolveComponent("el-form");
        return require$$0.openBlock(), require$$0.createElementBlock("div", _hoisted_1$h, [require$$0.createVNode(b, { "label-position": "left", "label-suffix": "：", "label-width": "120px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { label: "栅格" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.span, "onUpdate:modelValue": r[0] || (r[0] = (h) => t.data.span = h), min: 0, max: 24, "controls-position": "right", placeholder: "12" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "标签对齐方式" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: t.data.labelPosition, "onUpdate:modelValue": r[1] || (r[1] = (h) => t.data.labelPosition = h), placeholder: "左对齐" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { label: "左对齐", value: "left" }), require$$0.createVNode(u, { label: "右对齐", value: "right" }), require$$0.createVNode(u, { label: "顶部对齐", value: "top" })]), _: 1 }, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "标签宽度" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.labelWidth, "onUpdate:modelValue": r[2] || (r[2] = (h) => t.data.labelWidth = h), min: 80, max: 200, step: 10, "controls-position": "right", placeholder: "120px" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "标签后缀" }, { default: require$$0.withCtx(() => [require$$0.createVNode(f, { modelValue: t.data.labelSuffix, "onUpdate:modelValue": r[3] || (r[3] = (h) => t.data.labelSuffix = h), placeholder: "：" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "项之间的间隔" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.gutter, "onUpdate:modelValue": r[4] || (r[4] = (h) => t.data.gutter = h), min: 0, max: 60, step: 5, "controls-position": "right", placeholder: "0" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "多分组转标签" }, { default: require$$0.withCtx(() => [require$$0.createVNode(m, { modelValue: t.data.tabs, "onUpdate:modelValue": r[5] || (r[5] = (h) => t.data.tabs = h), "active-color": "#409EFF" }, null, 8, ["modelValue"])]), _: 1 }), t.data.tabs ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 0, label: "默认tab" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.tabsActive, "onUpdate:modelValue": r[6] || (r[6] = (h) => t.data.tabsActive = h), min: 0, "controls-position": "right", placeholder: "默认tab" }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), require$$0.createVNode(a, { label: "显示按钮" }, { default: require$$0.withCtx(() => [require$$0.createVNode(m, { modelValue: t.data.menuBtn, "onUpdate:modelValue": r[7] || (r[7] = (h) => t.data.menuBtn = h), "active-color": "#409EFF" }, null, 8, ["modelValue"])]), _: 1 }), t.data.menuBtn ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 1, label: "显示提交按钮" }, { default: require$$0.withCtx(() => [require$$0.createVNode(m, { modelValue: t.data.submitBtn, "onUpdate:modelValue": r[8] || (r[8] = (h) => t.data.submitBtn = h), "active-color": "#409EFF" }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), t.data.menuBtn && t.data.submitBtn ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 2, label: "提交按钮文字" }, { default: require$$0.withCtx(() => [require$$0.createVNode(f, { modelValue: t.data.submitText, "onUpdate:modelValue": r[9] || (r[9] = (h) => t.data.submitText = h), placeholder: "提交按钮的文字", clearable: "" }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), t.data.menuBtn ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 3, label: "显示清空按钮" }, { default: require$$0.withCtx(() => [require$$0.createVNode(m, { modelValue: t.data.emptyBtn, "onUpdate:modelValue": r[10] || (r[10] = (h) => t.data.emptyBtn = h), "active-color": "#409EFF" }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), t.data.menuBtn && t.data.emptyBtn ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 4, label: "清空按钮文字" }, { default: require$$0.withCtx(() => [require$$0.createVNode(f, { modelValue: t.data.emptyText, "onUpdate:modelValue": r[11] || (r[11] = (h) => t.data.emptyText = h), placeholder: "提交按钮的文字", clearable: "" }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), require$$0.createVNode(a, { label: "全局只读" }, { default: require$$0.withCtx(() => [require$$0.createVNode(m, { modelValue: t.data.readonly, "onUpdate:modelValue": r[12] || (r[12] = (h) => t.data.readonly = h) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "全局禁用" }, { default: require$$0.withCtx(() => [require$$0.createVNode(m, { modelValue: t.data.disabled, "onUpdate:modelValue": r[13] || (r[13] = (h) => t.data.disabled = h) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "详情模式" }, { default: require$$0.withCtx(() => [require$$0.createVNode(m, { modelValue: t.data.detail, "onUpdate:modelValue": r[14] || (r[14] = (h) => t.data.detail = h) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "组件大小" }, { default: require$$0.withCtx(() => [require$$0.createVNode(p, { modelValue: t.data.size, "onUpdate:modelValue": r[15] || (r[15] = (h) => t.data.size = h) }, { default: require$$0.withCtx(() => [require$$0.createVNode($, { label: "large", value: "large" }, { default: require$$0.withCtx(() => r[17] || (r[17] = [require$$0.createTextVNode("大")])), _: 1, __: [17] }), require$$0.createVNode($, { label: "default", value: "default" }, { default: require$$0.withCtx(() => r[18] || (r[18] = [require$$0.createTextVNode("默认")])), _: 1, __: [18] }), require$$0.createVNode($, { label: "small", value: "small" }, { default: require$$0.withCtx(() => r[19] || (r[19] = [require$$0.createTextVNode("小")])), _: 1, __: [19] })]), _: 1 }, 8, ["modelValue"])]), _: 1 }), require$$0.createElementVNode("div", _hoisted_2$a, [require$$0.createElementVNode("div", _hoisted_3$8, [r[20] || (r[20] = require$$0.createElementVNode("label", { class: "el-form-item__label", style: { padding: "0" } }, " 初始化函数： ", -1)), require$$0.createElementVNode("div", _hoisted_4$7, [require$$0.createVNode(g, { key: "form-init-function", modelValue: n.initFunction, "onUpdate:modelValue": r[16] || (r[16] = (h) => n.initFunction = h), border: "", style: { height: "300px" } }, null, 8, ["modelValue"])])])])]), _: 1 })]);
      }
      const FormConfig = _export_sfc(_sfc_main$B, [["render", _sfc_render$u]]), _hoisted_1$g = { class: "nfd-config-crud" }, _sfc_main$A = { __name: "crud", props: { data: { type: Object, default: () => ({}) } }, setup(o) {
        return (r, t) => {
          const s = require$$0.resolveComponent("el-input"), n = require$$0.resolveComponent("el-form-item"), d = require$$0.resolveComponent("el-radio"), l = require$$0.resolveComponent("el-radio-group"), a = require$$0.resolveComponent("el-switch"), u = require$$0.resolveComponent("el-divider"), c = require$$0.resolveComponent("el-input-number"), f = require$$0.resolveComponent("el-alert"), m = require$$0.resolveComponent("el-form");
          return require$$0.openBlock(), require$$0.createElementBlock("div", _hoisted_1$g, [require$$0.createVNode(m, { "label-position": "left", "label-suffix": "：", "label-width": "120px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(n, { label: "表格高度" }, { default: require$$0.withCtx(() => [require$$0.createVNode(s, { modelValue: o.data.height, "onUpdate:modelValue": t[0] || (t[0] = ($) => o.data.height = $), placeholder: "表格高度", clearable: "" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(n, { label: "组件大小" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: o.data.size, "onUpdate:modelValue": t[1] || (t[1] = ($) => o.data.size = $) }, { default: require$$0.withCtx(() => [require$$0.createVNode(d, { label: "large", value: "large" }, { default: require$$0.withCtx(() => t[27] || (t[27] = [require$$0.createTextVNode("大")])), _: 1, __: [27] }), require$$0.createVNode(d, { label: "default", value: "default" }, { default: require$$0.withCtx(() => t[28] || (t[28] = [require$$0.createTextVNode("默认")])), _: 1, __: [28] }), require$$0.createVNode(d, { label: "small", value: "small" }, { default: require$$0.withCtx(() => t[29] || (t[29] = [require$$0.createTextVNode("小")])), _: 1, __: [29] })]), _: 1 }, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(n, { label: "弹窗方式" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: o.data.dialogType, "onUpdate:modelValue": t[2] || (t[2] = ($) => o.data.dialogType = $) }, { default: require$$0.withCtx(() => [require$$0.createVNode(d, { label: "弹窗", value: "dialog" }, { default: require$$0.withCtx(() => t[30] || (t[30] = [require$$0.createTextVNode("弹窗")])), _: 1, __: [30] }), require$$0.createVNode(d, { label: "抽屉", value: "drawer" }, { default: require$$0.withCtx(() => t[31] || (t[31] = [require$$0.createTextVNode("抽屉")])), _: 1, __: [31] })]), _: 1 }, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(n, { label: "对齐" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: o.data.align, "onUpdate:modelValue": t[3] || (t[3] = ($) => o.data.align = $) }, { default: require$$0.withCtx(() => [require$$0.createVNode(d, { label: "左", value: "left" }, { default: require$$0.withCtx(() => t[32] || (t[32] = [require$$0.createTextVNode("左")])), _: 1, __: [32] }), require$$0.createVNode(d, { label: "中", value: "center" }, { default: require$$0.withCtx(() => t[33] || (t[33] = [require$$0.createTextVNode("中")])), _: 1, __: [33] }), require$$0.createVNode(d, { label: "右", value: "right" }, { default: require$$0.withCtx(() => t[34] || (t[34] = [require$$0.createTextVNode("右")])), _: 1, __: [34] })]), _: 1 }, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(n, { label: "边框" }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { modelValue: o.data.border, "onUpdate:modelValue": t[4] || (t[4] = ($) => o.data.border = $) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(n, { label: "斑马纹" }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { modelValue: o.data.stripe, "onUpdate:modelValue": t[5] || (t[5] = ($) => o.data.stripe = $) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(u, { "content-position": "center" }, { default: require$$0.withCtx(() => t[35] || (t[35] = [require$$0.createTextVNode("搜 索")])), _: 1, __: [35] }), require$$0.createVNode(n, { label: "搜索显示" }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { modelValue: o.data.search, "onUpdate:modelValue": t[6] || (t[6] = ($) => o.data.search = $) }, null, 8, ["modelValue"])]), _: 1 }), o.data.search ? (require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, { key: 0 }, [require$$0.createVNode(n, { label: "搜索超出隐藏" }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { modelValue: o.data.searchIcon, "onUpdate:modelValue": t[7] || (t[7] = ($) => o.data.searchIcon = $) }, null, 8, ["modelValue"])]), _: 1 }), o.data.searchIcon ? (require$$0.openBlock(), require$$0.createBlock(n, { key: 0, label: "搜索超出个数" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: o.data.searchIndex, "onUpdate:modelValue": t[8] || (t[8] = ($) => o.data.searchIndex = $), placeholder: "默认: 3", min: 1, "controls-position": "right" }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), require$$0.createVNode(n, { label: "搜索组件大小" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: o.data.searchSize, "onUpdate:modelValue": t[9] || (t[9] = ($) => o.data.searchSize = $) }, { default: require$$0.withCtx(() => [require$$0.createVNode(d, { label: "large", value: "large" }, { default: require$$0.withCtx(() => t[36] || (t[36] = [require$$0.createTextVNode("大")])), _: 1, __: [36] }), require$$0.createVNode(d, { label: "default", value: "default" }, { default: require$$0.withCtx(() => t[37] || (t[37] = [require$$0.createTextVNode("默认")])), _: 1, __: [37] }), require$$0.createVNode(d, { label: "small", value: "small" }, { default: require$$0.withCtx(() => t[38] || (t[38] = [require$$0.createTextVNode("小")])), _: 1, __: [38] })]), _: 1 }, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(n, { label: "搜索栅格" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: o.data.searchSpan, "onUpdate:modelValue": t[10] || (t[10] = ($) => o.data.searchSpan = $), placeholder: "6", min: 1, "controls-position": "right" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(n, { label: "搜索按钮栅格" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: o.data.searchMenuSpan, "onUpdate:modelValue": t[11] || (t[11] = ($) => o.data.searchMenuSpan = $), placeholder: "6", min: 1, "controls-position": "right" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(n, { label: "搜索标签宽度" }, { default: require$$0.withCtx(() => [require$$0.createVNode(s, { modelValue: o.data.searchLabelWidth, "onUpdate:modelValue": t[12] || (t[12] = ($) => o.data.searchLabelWidth = $), placeholder: "90px" }, null, 8, ["modelValue"])]), _: 1 })], 64)) : require$$0.createCommentVNode("", true), require$$0.createVNode(u, { "content-position": "center" }, { default: require$$0.withCtx(() => t[39] || (t[39] = [require$$0.createTextVNode("头 部 操 作 栏")])), _: 1, __: [39] }), require$$0.createVNode(n, { label: "操作栏显示" }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { modelValue: o.data.header, "onUpdate:modelValue": t[13] || (t[13] = ($) => o.data.header = $) }, null, 8, ["modelValue"])]), _: 1 }), o.data.header !== false ? (require$$0.openBlock(), require$$0.createBlock(n, { key: 1, label: "新增按钮" }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { modelValue: o.data.addBtn, "onUpdate:modelValue": t[14] || (t[14] = ($) => o.data.addBtn = $) }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), o.data.header !== false ? (require$$0.openBlock(), require$$0.createBlock(n, { key: 2, label: "刷新按钮" }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { modelValue: o.data.refreshBtn, "onUpdate:modelValue": t[15] || (t[15] = ($) => o.data.refreshBtn = $) }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), o.data.header !== false ? (require$$0.openBlock(), require$$0.createBlock(n, { key: 3, label: "搜索显隐按钮" }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { modelValue: o.data.searchBtn, "onUpdate:modelValue": t[16] || (t[16] = ($) => o.data.searchBtn = $) }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), require$$0.createVNode(u, { "content-position": "center" }, { default: require$$0.withCtx(() => t[40] || (t[40] = [require$$0.createTextVNode("菜 单 栏")])), _: 1, __: [40] }), require$$0.createVNode(n, { label: "菜单显示" }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { modelValue: o.data.menu, "onUpdate:modelValue": t[17] || (t[17] = ($) => o.data.menu = $) }, null, 8, ["modelValue"])]), _: 1 }), o.data.menu ? (require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, { key: 4 }, [require$$0.createVNode(n, { label: "菜单组件大小" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: o.data.menuSize, "onUpdate:modelValue": t[18] || (t[18] = ($) => o.data.menuSize = $) }, { default: require$$0.withCtx(() => [require$$0.createVNode(d, { label: "large", value: "large" }, { default: require$$0.withCtx(() => t[41] || (t[41] = [require$$0.createTextVNode("大")])), _: 1, __: [41] }), require$$0.createVNode(d, { label: "default", value: "default" }, { default: require$$0.withCtx(() => t[42] || (t[42] = [require$$0.createTextVNode("默认")])), _: 1, __: [42] }), require$$0.createVNode(d, { label: "small", value: "small" }, { default: require$$0.withCtx(() => t[43] || (t[43] = [require$$0.createTextVNode("小")])), _: 1, __: [43] })]), _: 1 }, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(n, { label: "菜单宽度" }, { default: require$$0.withCtx(() => [require$$0.createVNode(s, { modelValue: o.data.menuWidth, "onUpdate:modelValue": t[19] || (t[19] = ($) => o.data.menuWidth = $), placeholder: "220px" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(n, { label: "查看按钮" }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { modelValue: o.data.viewBtn, "onUpdate:modelValue": t[20] || (t[20] = ($) => o.data.viewBtn = $) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(n, { label: "编辑按钮" }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { modelValue: o.data.editBtn, "onUpdate:modelValue": t[21] || (t[21] = ($) => o.data.editBtn = $) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(n, { label: "删除按钮" }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { modelValue: o.data.delBtn, "onUpdate:modelValue": t[22] || (t[22] = ($) => o.data.delBtn = $) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(f, { title: "按钮单独配置可能无效，可能需配合权限共同使用", type: "warning", closable: false, center: "" })], 64)) : require$$0.createCommentVNode("", true), require$$0.createVNode(u, { "content-position": "center" }, { default: require$$0.withCtx(() => t[44] || (t[44] = [require$$0.createTextVNode("列 表 序 号")])), _: 1, __: [44] }), require$$0.createVNode(n, { label: "序号显示" }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { modelValue: o.data.index, "onUpdate:modelValue": t[23] || (t[23] = ($) => o.data.index = $) }, null, 8, ["modelValue"])]), _: 1 }), o.data.index ? (require$$0.openBlock(), require$$0.createBlock(n, { key: 5, label: "序号宽度" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: o.data.indexWidth, "onUpdate:modelValue": t[24] || (t[24] = ($) => o.data.indexWidth = $), placeholder: "55", min: 1, "controls-position": "right" }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), require$$0.createVNode(u, { "content-position": "center" }, { default: require$$0.withCtx(() => t[45] || (t[45] = [require$$0.createTextVNode("列 表 勾 选")])), _: 1, __: [45] }), require$$0.createVNode(n, { label: "勾选显示" }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { modelValue: o.data.selection, "onUpdate:modelValue": t[25] || (t[25] = ($) => o.data.selection = $) }, null, 8, ["modelValue"])]), _: 1 }), o.data.selection ? (require$$0.openBlock(), require$$0.createBlock(n, { key: 6, label: "勾选宽度" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: o.data.selectionWidth, "onUpdate:modelValue": t[26] || (t[26] = ($) => o.data.selectionWidth = $), placeholder: "55", min: 1, "controls-position": "right" }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true)]), _: 1 })]);
        };
      } }, _hoisted_1$f = { class: "nfd-config-global" }, _sfc_main$z = { __name: "index", props: { data: { type: Object, default: () => ({}) }, isCrud: { type: Boolean, default: false } }, setup(o) {
        const r = require$$0.ref(["1", "2"]);
        return (t, s) => {
          const n = require$$0.resolveComponent("el-icon-histogram"), d = require$$0.resolveComponent("el-icon"), l = require$$0.resolveComponent("el-collapse-item"), a = require$$0.resolveComponent("el-icon-grid"), u = require$$0.resolveComponent("el-collapse");
          return require$$0.openBlock(), require$$0.createElementBlock("div", _hoisted_1$f, [require$$0.createVNode(u, { modelValue: r.value, "onUpdate:modelValue": s[0] || (s[0] = (c) => r.value = c) }, { default: require$$0.withCtx(() => [o.isCrud ? (require$$0.openBlock(), require$$0.createBlock(l, { key: 0, name: "1" }, { title: require$$0.withCtx(() => [require$$0.createVNode(d, null, { default: require$$0.withCtx(() => [require$$0.createVNode(n)]), _: 1 }), s[1] || (s[1] = require$$0.createElementVNode("span", null, "表格配置", -1))]), default: require$$0.withCtx(() => [require$$0.createVNode(_sfc_main$A, { data: o.data }, null, 8, ["data"])]), _: 1 })) : require$$0.createCommentVNode("", true), require$$0.createVNode(l, { name: "2" }, { title: require$$0.withCtx(() => [require$$0.createVNode(d, null, { default: require$$0.withCtx(() => [require$$0.createVNode(a)]), _: 1 }), s[2] || (s[2] = require$$0.createElementVNode("span", null, "表单配置", -1))]), default: require$$0.withCtx(() => [require$$0.createVNode(FormConfig, { data: o.data }, null, 8, ["data"])]), _: 1 })]), _: 1 }, 8, ["modelValue"])]);
        };
      } }, _hoisted_1$e = { class: "nfd-config" }, _sfc_main$y = { __name: "index", props: { form: { type: Object, default: () => ({}) }, widgetSelect: { type: Object, default: () => ({}) }, defaultValues: { type: Object, default: () => ({}) }, isCrud: { type: Boolean, default: false } }, setup(o) {
        const r = o;
        let t = require$$0.ref("global");
        const s = require$$0.ref(true), n = require$$0.ref(), d = (a) => {
          l.value && (s.value = !s.value, n.value.changeCollapse(a));
        };
        let { widgetSelect: l } = require$$0.toRefs(r);
        return require$$0.watch(l, (a) => {
          a && Object.keys(a).length > 0 ? t.value = "widget" : t.value = "global";
        }, { deep: true }), (a, u) => {
          const c = require$$0.resolveComponent("el-icon-open"), f = require$$0.resolveComponent("el-icon"), m = require$$0.resolveComponent("el-icon-turn-off"), $ = require$$0.resolveComponent("el-tab-pane"), p = require$$0.resolveComponent("el-tabs");
          return require$$0.openBlock(), require$$0.createElementBlock("div", _hoisted_1$e, [require$$0.createVNode(p, { modelValue: require$$0.unref(t), "onUpdate:modelValue": u[2] || (u[2] = (g) => require$$0.isRef(t) ? t.value = g : t = g), stretch: "" }, { default: require$$0.withCtx(() => [require$$0.createVNode($, { label: "字段配置", name: "widget", style: { padding: "0 10px" } }, { label: require$$0.withCtx(() => [require$$0.createElementVNode("span", null, [u[3] || (u[3] = require$$0.createTextVNode(" 字段配置 ")), s.value ? (require$$0.openBlock(), require$$0.createBlock(f, { key: 0, onClick: u[0] || (u[0] = (g) => d("close")) }, { default: require$$0.withCtx(() => [require$$0.createVNode(c)]), _: 1 })) : (require$$0.openBlock(), require$$0.createBlock(f, { key: 1, onClick: u[1] || (u[1] = (g) => d("open")) }, { default: require$$0.withCtx(() => [require$$0.createVNode(m)]), _: 1 }))])]), default: require$$0.withCtx(() => [require$$0.createVNode(WidgetConfig, { ref_key: "widgetConfigRef", ref: n, data: require$$0.unref(l), "default-values": o.defaultValues, "is-crud": o.isCrud }, null, 8, ["data", "default-values", "is-crud"])]), _: 1 }), require$$0.createVNode($, { label: "全局配置", name: "global", lazy: "", style: { padding: "0 10px" } }, { default: require$$0.withCtx(() => [require$$0.createVNode(_sfc_main$z, { data: o.form, "is-crud": o.isCrud }, null, 8, ["data", "is-crud"])]), _: 1 })]), _: 1 }, 8, ["modelValue"])]);
        };
      } }, NfdConfig = _export_sfc(_sfc_main$y, [["__scopeId", "data-v-63a1ac16"]]), LAYOUT_HORIZONTAL = "horizontal", LAYOUT_VERTICAL = "vertical", _sfc_main$x = { name: "nfd-split-panel", props: { layout: { type: String, default: LAYOUT_VERTICAL } }, data() {
        return { isResizing: false };
      }, computed: { classnames() {
        return ["nfd-split-panel", "layout-" + this.layout.slice(0, 1), this.isResizing ? "is-resizing" : ""];
      }, cursor() {
        return this.isResizing ? this.layout == LAYOUT_VERTICAL ? "col-resize" : "row-resize" : "";
      }, userSelect() {
        return this.isResizing ? "none" : "";
      } }, methods: { onMouseDown({ target: o, pageX: r, pageY: t }) {
        if (o.className && o.className.match && o.className.match("nfd-split-panel-resizer")) {
          let s = this, { $el: n, layout: d } = s, l = o.previousElementSibling, { offsetWidth: a, offsetHeight: u } = l, c = !!(l.style.width + "").match("%");
          const { addEventListener: f, removeEventListener: m } = window, $ = (h, q = 0) => {
            if (d == LAYOUT_VERTICAL) {
              let v = n.clientWidth, y = h + q;
              return l.style.width = c ? y / v * 100 + "%" : y + "px";
            }
            if (d == LAYOUT_HORIZONTAL) {
              let v = n.clientHeight, y = h + q;
              return l.style.height = c ? y / v * 100 + "%" : y + "px";
            }
          };
          s.isResizing = true;
          let p = $();
          s.$emit("paneResizeStart", l, o, p);
          const g = function({ pageX: h, pageY: q }) {
            p = d == LAYOUT_VERTICAL ? $(a, h - r) : $(u, q - t), s.$emit("paneResize", l, o, p);
          }, b = function() {
            p = d == LAYOUT_VERTICAL ? $(l.clientWidth) : $(l.clientHeight), s.isResizing = false, m("mousemove", g), m("mouseup", b), s.$emit("paneResizeStop", l, o, p);
          };
          f("mousemove", g), f("mouseup", b);
        }
      } } };
      function _sfc_render$t(o, r, t, s, n, d) {
        return require$$0.openBlock(), require$$0.createElementBlock("div", { class: require$$0.normalizeClass(d.classnames), style: require$$0.normalizeStyle({ cursor: d.cursor, userSelect: d.userSelect }), onMousedown: r[0] || (r[0] = require$$0.withModifiers((...l) => d.onMouseDown && d.onMouseDown(...l), ["stop"])) }, [require$$0.renderSlot(o.$slots, "default")], 38);
      }
      const NfdSplitPanel = _export_sfc(_sfc_main$x, [["render", _sfc_render$t]]), _sfc_main$w = {}, _hoisted_1$d = { class: "nfd-split-panel-resizer" };
      function _sfc_render$s(o, r) {
        return require$$0.openBlock(), require$$0.createElementBlock("div", _hoisted_1$d, [require$$0.renderSlot(o.$slots, "default")]);
      }
      const NfdSplitPanelResizer = _export_sfc(_sfc_main$w, [["render", _sfc_render$s]]), _hoisted_1$c = { class: "nfd-drawer__footer" }, _sfc_main$v = { __name: "import", emits: ["submit"], setup(o, { expose: r }) {
        let t = require$$0.ref(""), s = require$$0.ref(false);
        const n = () => {
          s.value = true;
        }, d = () => {
          s.value = false;
        };
        return r({ show: n }), (l, a) => {
          const u = require$$0.resolveComponent("nf-codemirror"), c = require$$0.resolveComponent("el-button"), f = require$$0.resolveComponent("el-drawer");
          return require$$0.openBlock(), require$$0.createBlock(f, { modelValue: require$$0.unref(s), "onUpdate:modelValue": a[2] || (a[2] = (m) => require$$0.isRef(s) ? s.value = m : s = m), title: "导入", size: "50%", class: "nfd-drawer", "append-to-body": "", "destroy-on-close": "" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { key: "import", modelValue: require$$0.unref(t), "onUpdate:modelValue": a[0] || (a[0] = (m) => require$$0.isRef(t) ? t.value = m : t = m) }, null, 8, ["modelValue"]), require$$0.createElementVNode("span", _hoisted_1$c, [require$$0.createVNode(c, { onClick: a[1] || (a[1] = (m) => l.$emit("submit", require$$0.unref(t), d)), type: "primary" }, { default: require$$0.withCtx(() => a[3] || (a[3] = [require$$0.createTextVNode(" 确定 ")])), _: 1, __: [3] }), require$$0.createVNode(c, { onClick: d }, { default: require$$0.withCtx(() => a[4] || (a[4] = [require$$0.createTextVNode("取消")])), _: 1, __: [4] })])]), _: 1 }, 8, ["modelValue"]);
        };
      } };
      function getHtml(o, r) {
        return r ? getCrudHtml(o) : getFormHtml(o);
      }
      function getFormHtml(o) {
        return `
  <template>
    <nf-container>
      <nf-form
        v-if="
          option &&
          ((option.column && option.column.length > 0) ||
            (option.group && option.group.length > 0))
        "
        ref="form"
        v-model="form"
        :option="option"
        @submit="handleSubmit"
      ></nf-form>
    </nf-container>
  </template>
  <script>
  export default {
    name: "temp-form",
    data() {
      return {
        form: {},
        option: ${o},
      };
    },
    methods: {
      handleSubmit(form, done) {
        // 表单自带按钮提交
        if (typeof done == "function") {
          console.log(form);
          done();
        }
        // 不使用自带按钮，自定义按钮提交
        else {
          this.$refs.form.validate((valid, done) => {
            // 校验成功
            if (valid) {
              console.log(this.deepClone(this.form));
            }
            // 校验失败
            else {
              if (msg) {
                const key = Object.keys(msg)[0];
                const rules = msg[key];
                this.$message.error(rules.map((r) => r.message).join(" | "));
              }
            }
            done();
          });
        }
      },
    },
  };
  <\/script>
  `;
      }
      function getCrudHtml(o) {
        return `
  <template>
    <nf-container>
      <nf-crud
        :option="option"
        :table-loading="loading"
        :data="data"
        v-model:page="page"
        :permission="permissionList"
        :before-open="beforeOpen"
        v-model="form"
        ref="crud"
        @row-update="rowUpdate"
        @row-save="rowSave"
        @row-del="rowDel"
        @search-change="searchChange"
        @search-reset="searchReset"
        @selection-change="selectionChange"
        @current-change="currentChange"
        @size-change="sizeChange"
        @refresh-change="refreshChange"
        @on-load="onLoad"
      >
        <template #menu-left>
          <el-button
            type="danger"
            size="default"
            icon="el-icon-delete"
            plain
            v-if="permission[serviceName + '_delete']"
            @click="handleDelete"
          >
            删 除
          </el-button>
        </template>
      </nf-crud>
    </nf-container>
  </template>

  <script>
  import { getList, getDetail, add, update, remove } from "your_api_path";
  import { mapGetters } from "vuex";

  export default {
    data() {
      return {
        serviceName: "your_service_name",
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0,
        },
        selectionList: [],
        option: ${o},
        data: [],
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(
            this.permission[this.serviceName + "_add"],
            false,
          ),
          viewBtn: this.validData(
            this.permission[this.serviceName + "_view"],
            false,
          ),
          delBtn: this.validData(
            this.permission[this.serviceName + "_delete"],
            false,
          ),
          editBtn: this.validData(
            this.permission[this.serviceName + "_edit"],
            false,
          ),
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach((ele) => {
          ids.push(ele.id);
        });
        return ids.join(",");
      },
    },
    methods: {
      rowSave(row, done) {
        add(row).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
          },
          (error) => {
            done();
            console.log(error);
          },
        );
      },
      rowUpdate(row, index, done) {
        update(row).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            done();
          },
          (error) => {
            done();
            console.log(error);
          },
        );
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then((res) => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage) {
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize) {
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(
          page.currentPage,
          page.pageSize,
          Object.assign(params, this.query),
        ).then((res) => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
    },
  };
  <\/script>

  <style></style>

  `;
      }
      const _hoisted_1$b = { class: "nfd-generate-drawer__footer" }, _hoisted_2$9 = { class: "nfd-generate-drawer__footer" }, _sfc_main$u = { __name: "generate", emits: ["submit"], setup(o, { expose: r }) {
        const t = require$$0.ref(""), s = require$$0.ref(""), n = require$$0.ref(false), d = require$$0.ref("data"), { proxy: l } = require$$0.getCurrentInstance(), a = (f, m = false) => {
          t.value = f, s.value = getHtml(l.jsBeautify(f), m), n.value = true;
        }, u = () => {
          n.value = false;
        };
        r({ show: a });
        const c = (f, m) => {
          navigator.clipboard.writeText(m == "html" ? f : l.jsBeautify(f, { minify: m == "string" })).then(() => {
            l.$message.success("复制成功");
          }).catch(() => {
            l.$message.error("复制失败");
          });
        };
        return (f, m) => {
          const $ = require$$0.resolveComponent("nf-codemirror"), p = require$$0.resolveComponent("el-button"), g = require$$0.resolveComponent("el-tab-pane"), b = require$$0.resolveComponent("el-tabs"), h = require$$0.resolveComponent("el-drawer");
          return require$$0.openBlock(), require$$0.createBlock(h, { modelValue: n.value, "onUpdate:modelValue": m[8] || (m[8] = (q) => n.value = q), title: "导出", size: "50%", class: "nfd-generate-drawer", "append-to-body": "", "destroy-on-close": "" }, { default: require$$0.withCtx(() => [require$$0.createVNode(b, { modelValue: d.value, "onUpdate:modelValue": m[7] || (m[7] = (q) => d.value = q), stretch: "" }, { default: require$$0.withCtx(() => [require$$0.createVNode(g, { label: "数 据", name: "data" }, { default: require$$0.withCtx(() => [require$$0.createVNode($, { modelValue: t.value, "onUpdate:modelValue": m[0] || (m[0] = (q) => t.value = q), style: { height: "90%" } }, null, 8, ["modelValue"]), require$$0.createElementVNode("span", _hoisted_1$b, [require$$0.createVNode(p, { onClick: m[1] || (m[1] = (q) => u()), type: "primary" }, { default: require$$0.withCtx(() => m[9] || (m[9] = [require$$0.createTextVNode("确 定")])), _: 1, __: [9] }), require$$0.createVNode(p, { type: "primary", onClick: m[2] || (m[2] = (q) => c(t.value, "json")) }, { default: require$$0.withCtx(() => m[10] || (m[10] = [require$$0.createTextVNode(" 复 制 json ")])), _: 1, __: [10] }), require$$0.createVNode(p, { type: "primary", onClick: m[3] || (m[3] = (q) => c(t.value, "string")) }, { default: require$$0.withCtx(() => m[11] || (m[11] = [require$$0.createTextVNode(" 复 制 string ")])), _: 1, __: [11] })])]), _: 1 }), require$$0.createVNode(g, { label: "页 面", name: "page", lazy: "" }, { default: require$$0.withCtx(() => [require$$0.createVNode($, { modelValue: s.value, "onUpdate:modelValue": m[4] || (m[4] = (q) => s.value = q), language: "text/x-vue", style: { height: "90%" } }, null, 8, ["modelValue"]), require$$0.createElementVNode("span", _hoisted_2$9, [require$$0.createVNode(p, { onClick: m[5] || (m[5] = (q) => u()), type: "primary" }, { default: require$$0.withCtx(() => m[12] || (m[12] = [require$$0.createTextVNode("确 定")])), _: 1, __: [12] }), require$$0.createVNode(p, { type: "primary", onClick: m[6] || (m[6] = (q) => c(s.value, "html")) }, { default: require$$0.withCtx(() => m[13] || (m[13] = [require$$0.createTextVNode(" 复 制 ")])), _: 1, __: [13] })])]), _: 1 })]), _: 1 }, 8, ["modelValue"])]), _: 1 }, 8, ["modelValue"]);
        };
      } };
      function useTransform() {
        const { proxy } = require$$0.getCurrentInstance(), { deepClone } = proxy;
        return { transformToFormOptions: (obj, isPreview = false) => new Promise((resolve, reject) => {
          try {
            const data = deepClone(obj);
            for (let o in data) validateNull(data[o]) && delete data[o];
            for (let i = 0; i < data.column.length; i++) {
              const col = data.column[i];
              if (delete col.subfield, delete col.nfId, isPreview) {
                let event = ["change", "blur", "click", "focus"];
                event.forEach((e) => {
                  col[e] && (col[e] = eval((col[e] + "").replace(/this/g, "proxy")));
                }), col.event && Object.keys(col.event).forEach((key) => col.event[key] = eval((col.event[key] + "").replace(/this/g, "proxy"))), data.initFunction && (data.initFunction = eval((data.initFunction + "").replace(/this/g, "proxy")));
              }
              if (col.type == "dynamic" && col.children && col.children.column && col.children.column.length > 0) proxy.transformToFormOptions(col.children, isPreview).then((o) => {
                col.children = o;
              });
              else if (col.type == "group") {
                data.group || (data.group = []);
                const o = { label: col.label, icon: col.icon, prop: col.prop, arrow: col.arrow, collapse: col.collapse, display: col.display };
                proxy.transformToFormOptions(col.children, isPreview).then((r) => {
                  o.column = r.column, data.group.push(o);
                }), data.column.splice(i, 1), i--;
              } else if (["checkbox", "radio", "tree", "cascader", "select"].includes(col.type)) {
                if (col.dicOption == "static") delete col.dicUrl, delete col.dicMethod, delete col.dicQuery, delete col.dicQueryConfig, col.dicData = filterDicProps(col.dicData, col.props);
                else if (col.dicOption == "remote") if (delete col.dicData, col.dicQueryConfig && col.dicQueryConfig.length > 0) {
                  const o = {};
                  col.dicQueryConfig.forEach((r) => {
                    r.key && r.value && (o[r.key] = r.value);
                  }), col.dicQuery = o, delete col.dicQueryConfig;
                } else delete col.dicQueryConfig;
                delete col.dicOption;
              } else if (["upload"].includes(col.type)) {
                if (col.headersConfig && col.headersConfig.length > 0) {
                  const o = {};
                  col.headersConfig.forEach((r) => {
                    r.key && r.value && (o[r.key] = r.value);
                  }), col.headers = o;
                } else delete col.headers;
                if (delete col.headersConfig, col.dataConfig && col.dataConfig.length > 0) {
                  const o = {};
                  col.dataConfig.forEach((r) => {
                    r.key && r.value && (o[r.key] = r.value);
                  }), col.data = o;
                } else delete col.data;
                delete col.dataConfig;
              } else if (col.type == "table" && Object.hasOwnProperty.call(col, "rows")) {
                const o = col.rows;
                o && o.length > 0 && o.forEach((r) => {
                  const t = r.cols;
                  t && t.length > 0 && t.forEach((s) => {
                    proxy.transformToFormOptions(s, isPreview).then((n) => {
                      s.column = n.column;
                    });
                  });
                });
              }
              delete col.tooltip;
            }
            resolve(data);
          } catch (o) {
            reject(o);
          }
        }), transformToDesignerOptions: (o) => {
          typeof o == "string" && (o = Function('"use strict";return (' + o + ")")());
          const r = deepClone(o);
          return new Promise((t, s) => {
            try {
              if (r.column && r.column.length > 0 && r.column.forEach((n) => {
                if (n.nfId || (n.nfId = randomString()), n.prop || (n.prop = randomString()), ["dynamic", "table-select", "table-select-v2"].includes(n.type) && n.children && n.children.column && n.children.column.length > 0) n.children.column.forEach((l) => {
                  l.subfield = ["table-select", "table-select-v2"].includes(n.type) ? n.type : true;
                }), proxy.transformToDesignerOptions(n.children).then((l) => {
                  n.children = l;
                });
                else if (["checkbox", "radio", "tree", "cascader", "select"].includes(n.type)) {
                  if (!n.dicData && n.dicQuery && typeof n.dicQuery == "object") {
                    const d = [];
                    for (let l in n.dicQuery) d.push({ key: l, value: n.dicQuery[l], $cellEdit: true });
                    n.dicQueryConfig = d;
                  }
                  n.dicUrl ? n.dicOption = "remote" : n.dicOption = "static", n.dicData ? n.props && (n.dicData = filterCommonDicProps(n.dicData, n.props)) : n.dicData = [];
                } else if (["upload"].includes(n.type)) {
                  if (n.headers && typeof n.headers == "object") {
                    const d = [];
                    for (let l in n.headers) d.push({ key: l, value: n.headers[l], $cellEdit: true });
                    n.headersConfig = d;
                  } else n.headersConfig = [];
                  if (n.data && typeof n.data == "object") {
                    const d = [];
                    for (let l in n.data) d.push({ key: l, value: n.data[l], $cellEdit: true });
                    n.dataConfig = d;
                  } else n.dataConfig = [];
                } else ["table"].includes(n.type) && n.rows && n.rows.forEach((d) => {
                  d.cols && d.cols.forEach((l) => {
                    l.column && l.column.length > 0 && l.column.forEach((a) => {
                      a.subfield = "table";
                    }), proxy.transformToDesignerOptions(l).then((a) => {
                      l.column = a.column;
                    });
                  });
                });
              }), r.group && r.group.length > 0) {
                for (let n = 0; n < r.group.length; n++) {
                  r.column || (r.column = []);
                  const d = r.group[n];
                  d.nfId || (d.nfId = randomString());
                  const l = { type: "group", label: d.label, icon: d.icon, prop: d.prop, arrow: d.arrow, collapse: d.collapse, display: d.display, nfId: d.nfId };
                  proxy.transformToDesignerOptions(d).then((a) => {
                    l.children = a, r.column.push(l);
                  });
                }
                delete r.group;
              }
              t(r);
            } catch (n) {
              s(n);
            }
          });
        }, getColumnProp: (o) => new Promise((r, t) => {
          const s = proxy.deepClone(o), n = [];
          if (s.column && s.column.length > 0) for (let d = 0; d < s.column.length; d++) {
            const l = s.column[d], { type: a, nfId: u, prop: c, label: f, children: m } = l;
            if (!u) {
              t("无法处理的数据");
              return;
            }
            const $ = { type: a, nfId: u, prop: c, label: f };
            ["dynamic", "table-select", "group"].includes(a) ? proxy.getColumnProp(m).then((p) => {
              $.children = p;
            }) : ["table"].includes(a) && l.rows.forEach((p) => {
              p.cols.forEach((g) => {
                proxy.getColumnProp(g).then((b) => {
                  $.children || ($.children = []), b && b.length > 0 && ($.children = $.children.concat(b));
                });
              });
            }), n.push($);
          }
          n.length > 0 && r(n);
        }), compareArrays: (o, r, t = "nfId", s = ["type", "prop", "label"]) => {
          const n = [];
          function d(u, c) {
            const f = {};
            return s.forEach((m) => {
              u[m] !== c[m] && (f[m] = { oldValue: u[m], newValue: c[m] });
            }), Object.keys(f).length > 0 ? f : null;
          }
          function l(u, c) {
            const f = { [t]: u[t], prop: u.prop, differences: c };
            return c == "add" && (f.label = u.label, f.type = u.type), f;
          }
          o.forEach((u) => {
            const c = r.find((f) => f[t] === u[t]);
            if (c) {
              const f = d(u, c), m = { [t]: u[t], prop: u.prop, differences: f || {} };
              if (u.children || c.children) {
                const $ = compareArrays(u.children || [], c.children || []);
                $.length > 0 && (m.children = $);
              }
              (Object.keys(m.differences).length > 0 || m.children) && n.push(m);
            } else n.push(l(u, "delete"));
          }), r.forEach((u) => {
            o.find((f) => f[t] === u[t]) || n.push(l(u, "add"));
          });
          function a(u) {
            !u || u.length == 0 || u.forEach((c) => {
              delete c[t], c.children && a(c.children);
            });
          }
          return a(n), n;
        } };
      }
      const _sfc_main$t = { setup() {
        const { transformToFormOptions: o } = useTransform();
        return { transformToFormOptions: o };
      }, props: { isCrud: { type: Boolean, default: false } }, data() {
        return { option: { column: [] }, form: {}, visible: false, data: [] };
      }, methods: { show(o) {
        this.form = {}, this.transformToFormOptions(o, true).then((r) => {
          this.option = r, this.visible = true;
        });
      }, hide() {
        this.visible = false, this.form = {}, this.data = [];
      }, handleSubmit(o, r) {
        r ? this.$alert(o).then(() => {
          r();
        }).catch(() => {
          r();
        }) : this.$refs.form.validate((t, s) => {
          t && this.$alert(this.form).then(() => {
            s();
          }).catch(() => {
            s();
          });
        });
      }, rowSave(o, r) {
        setTimeout(() => {
          this.data.push(o), r();
        }, 500);
      }, rowUpdate(o, r, t) {
        setTimeout(() => {
          this.data[r] = o, t();
        }, 500);
      }, rowDel(o, r) {
        this.$confirm("确定将选择数据删除?", { confirmButtonText: "确定", cancelButtonText: "取消", type: "warning" }).then(() => {
          this.data.splice(r || 0), this.$message({ type: "success", message: "操作成功!" });
        });
      }, searchChange(o, r) {
        this.$message.success("搜索条件: " + JSON.stringify(o)), r();
      } } }, _hoisted_1$a = { class: "nfd-drawer__footer" };
      function _sfc_render$r(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("nf-crud"), a = require$$0.resolveComponent("nf-form"), u = require$$0.resolveComponent("el-button"), c = require$$0.resolveComponent("el-drawer");
        return require$$0.openBlock(), require$$0.createBlock(c, { modelValue: n.visible, "onUpdate:modelValue": r[2] || (r[2] = (f) => n.visible = f), title: `${t.isCrud ? "表格" : "表单"}预览`, size: `${t.isCrud ? "85%" : "65%"}`, class: "nfd-drawer", "append-to-body": "", "destroy-on-close": "" }, { default: require$$0.withCtx(() => [t.isCrud ? (require$$0.openBlock(), require$$0.createBlock(l, { key: 0, style: { padding: "20px" }, option: n.option, modelValue: n.form, "onUpdate:modelValue": r[0] || (r[0] = (f) => n.form = f), data: n.data, onRowSave: d.rowSave, onRowUpdate: d.rowUpdate, onRowDel: d.rowDel, onSearchChange: d.searchChange }, null, 8, ["option", "modelValue", "data", "onRowSave", "onRowUpdate", "onRowDel", "onSearchChange"])) : (require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, { key: 1 }, [n.option.column && n.option.column.length > 0 || n.option.group && n.option.group.length > 0 ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 0, style: { padding: "0 20px" }, ref: "form", modelValue: n.form, "onUpdate:modelValue": r[1] || (r[1] = (f) => n.form = f), option: n.option, onSubmit: d.handleSubmit }, null, 8, ["modelValue", "option", "onSubmit"])) : require$$0.createCommentVNode("", true), require$$0.createElementVNode("span", _hoisted_1$a, [require$$0.createVNode(u, { onClick: d.handleSubmit, type: "primary" }, { default: require$$0.withCtx(() => r[3] || (r[3] = [require$$0.createTextVNode("确定")])), _: 1, __: [3] }, 8, ["onClick"]), require$$0.createVNode(u, { onClick: d.hide }, { default: require$$0.withCtx(() => r[4] || (r[4] = [require$$0.createTextVNode("取消")])), _: 1, __: [4] }, 8, ["onClick"])])], 64))]), _: 1 }, 8, ["modelValue", "title", "size"]);
      }
      const NfdPreviewDrawer = _export_sfc(_sfc_main$t, [["render", _sfc_render$r]]);
      function useHistory() {
        const { proxy: o } = require$$0.getCurrentInstance(), { deepClone: r } = o, t = require$$0.reactive({ index: 0, maxStep: 20, steps: [], storage: false });
        return require$$0.watch(t, (a) => {
          a.storage ? localStorage.setItem("nf-form-design-history", o.jsBeautify(a)) : localStorage.removeItem("nf-form-design-history");
        }, { deep: true }), { historySteps: t, initHistory: (a) => {
          if (a.storage) {
            const f = localStorage.getItem("nf-form-design-history");
            if (f) {
              Object.assign(t, Function('"use strict";return (' + f + ")")());
              const { index: m, steps: $ } = t;
              return require$$0.reactive(r($[m]));
            }
          }
          Object.assign(t, a);
          const { index: u, steps: c } = t;
          return require$$0.reactive(r(c[u]));
        }, handleHistoryChange: (a) => {
          t.index == t.maxStep - 1 ? t.steps.shift() : t.index++, t.steps[t.index] = r(a), t.index < t.steps.length - 1 && (t.steps = t.steps.slice(0, t.index + 1));
        }, handleUndo: () => (t.index != 0 && t.index--, r(t.steps[t.index])), handleRedo: () => (t.index != t.steps.length - 1 && t.index++, r(t.steps[t.index])) };
      }
      function useGetData() {
        const { proxy: o } = require$$0.getCurrentInstance(), { transformToFormOptions: r, getColumnProp: t, compareArrays: s } = useTransform();
        return { getData: async (n = "json", d = {}) => {
          if (n == "string") return o.jsBeautify(await r(o.widget.option), { minify: true, ...d });
          if (n == "app") {
            const l = await r(o.widget.option);
            return o.parseJson(l), l;
          } else return await r(o.widget.option);
        }, parseJson: (n) => {
          for (var d in n) {
            var l = n[d];
            l && l.length > 0 && typeof l == "object" || typeof l == "object" ? o.parseJson(l) : typeof l == "function" && (n[d] = l + "");
          }
        }, getChangeList: async () => {
          const n = o.initColumnProp, d = await t(o.widget.option);
          return s(n, d);
        } };
      }
      const _sfc_main$s = { name: "nf-form-design", components: { NfdField, NfdToolbar, NfdWidget, NfdConfig, NfdImportDrawer: _sfc_main$v, NfdGenerateDrawer: _sfc_main$u, NfdPreviewDrawer, NfdSplitPanel, NfdSplitPanelResizer }, props: { options: { type: [Object, String], default: () => ({ column: [] }) }, storage: { type: Boolean, default: false }, toolbar: { type: Array, default: () => ["import", "generate", "preview", "clear"] }, undoRedo: { type: Boolean, default: true }, includeFields: { type: Array }, customFields: { type: Array }, defaultValues: { type: Object }, defaultFieldTab: { type: String }, asideWidthStorage: { type: Boolean, default: false }, isCrud: { type: Boolean, default: false } }, watch: { options: { handler() {
        this.handleLoadStorage();
      }, deep: true, immediate: true } }, setup() {
        const { historySteps: o, initHistory: r, handleHistoryChange: t, handleUndo: s, handleRedo: n } = useHistory(), { transformToFormOptions: d, transformToDesignerOptions: l, getColumnProp: a } = useTransform(), { getData: u, parseJson: c, getChangeList: f } = useGetData();
        return { historySteps: o, initHistory: r, handleHistoryChange: t, handleUndo: s, handleRedo: n, transformToFormOptions: d, transformToDesignerOptions: l, getColumnProp: a, getData: u, getChangeList: f, parseJson: c };
      }, data() {
        return { form: {}, option: { column: [] }, widget: { option: { column: [] }, select: {} }, asideLeftWidth: "270px", asideRightWidth: "380px", asideLeftWidthKey: "nfd-aside-left-width", asideRightWidthKey: "nfd-aside-right-width", initColumnProp: [] };
      }, methods: { handleLoadStorage() {
        let o = this.deepClone(this.options);
        typeof o == "string" && (o = Function('"use strict";return (' + o + ")")()), o.column || (o.column = []), this.isCrud && (this.widget.option = { height: "auto", calcHeight: 30, border: true, search: true, header: true, menu: true, addBtn: true, viewBtn: true, editBtn: true, delBtn: true, refreshBtn: true, searchBtn: true, index: true, selection: true, ...this.widget.option }), this.transformToDesignerOptions({ ...this.widget.option, ...o }).then((r) => {
          if (this.widget.option = this.initHistory({ index: 0, maxStep: 20, steps: [r], storage: this.storage }), this.asideWidthStorage) {
            const t = localStorage.getItem(this.asideLeftWidthKey);
            t && (this.asideLeftWidth = t);
            const s = localStorage.getItem(this.asideRightWidthKey);
            s && (this.asideRightWidth = s);
          }
          this.getColumnProp(this.widget.option).then((t) => {
            this.initColumnProp = t;
          });
        });
      }, handleBindKeyBoard() {
        this.undoRedo && window.addEventListener("keydown", (o) => {
          (o.metaKey && !o.shiftKey && o.key == "z" || o.ctrlKey && !o.shiftKey && o.key == "z") && (this.widget.option = this.handleUndo()), (o.metaKey && o.shiftKey && o.key == "z" || o.ctrlKey && o.shiftKey && o.key == "z" || o.ctrlKey && o.key == "y") && (this.widget.option = this.handleRedo());
        }, false);
      }, handleFieldClick({ item: o, type: r }) {
        if (r == "field") {
          const t = this.widget.option.column.findIndex((n) => n.prop == this.widget.select.prop) + 1;
          let s = 0;
          t == -1 ? (this.widget.option.column.push(o), s = this.widget.option.column.length - 1) : (this.widget.option.column.splice(t, 0, o), s = t), this.$refs.widgetForm.handleWidgetAdd({ newIndex: s });
        } else r == "template" && o.column && o.column.length > 0 && (o = { ...this.widget.option, ...o }, this.handleImportJson(o));
      }, handleImportJson(o, r) {
        if (o) try {
          this.transformToDesignerOptions(o).then((t) => {
            this.widget.option = t, this.widget.select = {}, this.handleHistoryChange(t), typeof r == "function" && r();
          });
        } catch (t) {
          this.$message.error(t.message);
        }
      }, handleGenerateJson() {
        this.transformToFormOptions(this.widget.option).then((o) => {
          this.$refs.generateDrawer.show(o, this.isCrud);
        });
      }, handlePreview() {
        !this.widget.option.column || this.widget.option.column.length == 0 ? this.$message.error("没有需要展示的内容") : this.$refs.previewDrawer.show(this.widget.option);
      }, handleClear() {
        this.widget.option && this.widget.option.column && this.widget.option.column.length > 0 ? this.$confirm("确定要清空吗？", "警告", { type: "warning" }).then(() => {
          this.widget.option.column = [], this.form = {}, this.widget.select = {}, this.handleHistoryChange(this.widget.option);
        }).catch(() => {
        }) : this.$message.error("没有需要清空的内容");
      }, handlePanelResize(o, r, t) {
        var s, n;
        if (o.id == "left-aside") parseInt(t) <= 150 ? this.asideLeftWidth = "0px" : this.asideLeftWidth = t;
        else {
          const d = ((n = (s = this.$parent.$el) == null ? void 0 : s.parentElement) == null ? void 0 : n.offsetWidth) - parseInt(t) - parseInt(this.asideLeftWidth) - 20;
          d <= 200 ? this.asideRightWidth = "0px" : this.asideRightWidth = `${d}px`;
        }
        this.asideWidthStorage && (localStorage.setItem(this.asideLeftWidthKey, this.asideLeftWidth), localStorage.setItem(this.asideRightWidthKey, this.asideRightWidth));
      } } }, _hoisted_1$9 = { class: "nf-form-design" };
      function _sfc_render$q(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("nfd-field"), a = require$$0.resolveComponent("el-aside"), u = require$$0.resolveComponent("nfd-split-panel-resizer"), c = require$$0.resolveComponent("nfd-toolbar"), f = require$$0.resolveComponent("nfd-widget"), m = require$$0.resolveComponent("el-container"), $ = require$$0.resolveComponent("nfd-config"), p = require$$0.resolveComponent("nfd-split-panel"), g = require$$0.resolveComponent("nfd-import-drawer"), b = require$$0.resolveComponent("nfd-generate-drawer"), h = require$$0.resolveComponent("nfd-preview-drawer");
        return require$$0.openBlock(), require$$0.createElementBlock("div", _hoisted_1$9, [require$$0.createVNode(m, null, { default: require$$0.withCtx(() => [require$$0.createVNode(p, { layout: "vertical", onPaneResize: d.handlePanelResize }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { id: "left-aside", style: require$$0.normalizeStyle({ width: n.asideLeftWidth, minWidth: "0px", maxWidth: "400px" }) }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { "include-fields": t.includeFields, "custom-fields": t.customFields, "default-tab": t.defaultFieldTab, onFieldClick: d.handleFieldClick }, null, 8, ["include-fields", "custom-fields", "default-tab", "onFieldClick"])]), _: 1 }, 8, ["style"]), require$$0.createVNode(u), require$$0.createVNode(m, { direction: "vertical" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { toolbar: t.toolbar, "undo-redo": t.undoRedo, "history-steps": s.historySteps, onUndo: r[0] || (r[0] = (q) => n.widget.option = s.handleUndo()), onRedo: r[1] || (r[1] = (q) => n.widget.option = s.handleRedo()), onImport: r[2] || (r[2] = (q) => o.$refs.importDrawer.show()), onGenerate: d.handleGenerateJson, onPreview: d.handlePreview, onClear: d.handleClear }, { "toolbar-left": require$$0.withCtx(() => [require$$0.renderSlot(o.$slots, "toolbar-left")]), toolbar: require$$0.withCtx(() => [require$$0.renderSlot(o.$slots, "toolbar")]), _: 3 }, 8, ["toolbar", "undo-redo", "history-steps", "onGenerate", "onPreview", "onClear"]), require$$0.createVNode(f, { ref: "widgetForm", data: n.widget.option, "is-crud": t.isCrud, select: n.widget.select, "onUpdate:select": r[3] || (r[3] = (q) => n.widget.select = q), onChange: r[4] || (r[4] = (q) => s.handleHistoryChange(n.widget.option)) }, null, 8, ["data", "is-crud", "select"])]), _: 3 }), require$$0.createVNode(u), require$$0.createVNode(a, { style: require$$0.normalizeStyle({ width: n.asideRightWidth, minWidth: "0px", maxWidth: "600px" }) }, { default: require$$0.withCtx(() => [require$$0.createVNode($, { form: n.widget.option, "widget-select": n.widget.select, "default-values": t.defaultValues, "is-crud": t.isCrud }, null, 8, ["form", "widget-select", "default-values", "is-crud"])]), _: 1 }, 8, ["style"])]), _: 3 }, 8, ["onPaneResize"])]), _: 3 }), require$$0.createVNode(g, { ref: "importDrawer", onSubmit: d.handleImportJson }, null, 8, ["onSubmit"]), require$$0.createVNode(b, { ref: "generateDrawer" }, null, 512), require$$0.createVNode(h, { ref: "previewDrawer", "is-crud": t.isCrud }, null, 8, ["is-crud"])]);
      }
      const NfFormDesign = _export_sfc(_sfc_main$s, [["render", _sfc_render$q]]), _sfc_main$r = { name: "config-form", props: ["data", "defaultValues"], watch: { "data.required"(o) {
        o ? this.validator.required = { required: true, message: `${this.data.label}必须填写` } : this.validator.required = null, this.generateRule();
      }, "data.pattern"(o) {
        o ? this.validator.pattern = { pattern: new RegExp(o), message: `${this.data.label}格式不匹配` } : this.validator.pattern = null, this.generateRule();
      } }, data() {
        return { validator: { type: null, required: null, pattern: null, length: null } };
      }, methods: { generateRule() {
        const o = [];
        Object.keys(this.validator).forEach((r) => {
          this.validator[r] && o.push(this.validator[r]);
        }), o.length == 0 ? delete this.data.rules : this.data.rules = o;
      }, handleColumnTipChange(o) {
        o && (this.data.showChinese = false);
      } } };
      function _sfc_render$p(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("el-input"), a = require$$0.resolveComponent("el-form-item"), u = require$$0.resolveComponent("el-option"), c = require$$0.resolveComponent("el-select"), f = require$$0.resolveComponent("el-input-number"), m = require$$0.resolveComponent("el-switch");
        return require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, null, [!["table", "table-select", "table-select-v2"].includes(t.data.subfield) && !["group", "dynamic", "title", "text", "table"].includes(t.data.type) ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 0, label: "标签宽度" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.labelWidth, "onUpdate:modelValue": r[0] || (r[0] = ($) => t.data.labelWidth = $), placeholder: "标签宽度" }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), !t.data.subfield && !["group", "dynamic", "title", "text", "table"].includes(t.data.type) ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 1, label: "标签位置" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: t.data.labelPosition, "onUpdate:modelValue": r[1] || (r[1] = ($) => t.data.labelPosition = $), placeholder: "标签位置", clearable: "", style: { width: "100%" } }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { label: "上", value: "top" }), require$$0.createVNode(u, { label: "左", value: "left" }), require$$0.createVNode(u, { label: "右", value: "right" })]), _: 1 }, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), ["table-select", "table-select-v2", true].includes(t.data.subfield) ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 2, label: "宽度" }, { default: require$$0.withCtx(() => [require$$0.createVNode(f, { style: { width: "100%" }, modelValue: t.data.width, "onUpdate:modelValue": r[2] || (r[2] = ($) => t.data.width = $), "controls-position": "right", placeholder: "宽度", min: 100 }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), !t.data.subfield && !["group"].includes(t.data.type) ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 3, label: "表单栅格" }, { default: require$$0.withCtx(() => [require$$0.createVNode(f, { style: { width: "100%" }, modelValue: t.data.span, "onUpdate:modelValue": r[3] || (r[3] = ($) => t.data.span = $), "controls-position": "right", placeholder: "表单栅格", min: 2, max: 24 }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), ["checkbox", "select", "cascader", "tree", "map", "upload", "slider", "yearrange", "monthrange", "timerange", "daterange", "datetimerange", "years", "months", "dates", "input-tag"].includes(t.data.type) ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 4, label: "数据类型" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: t.data.dataType, "onUpdate:modelValue": r[4] || (r[4] = ($) => t.data.dataType = $), placeholder: "数据类型", clearable: "" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { label: "字符串", value: "string" }), require$$0.createVNode(u, { label: "数组", value: "array" }), require$$0.createVNode(u, { label: "数字", value: "number" })]), _: 1 }, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), require$$0.createVNode(a, { label: "标题提示" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.labelTip, "onUpdate:modelValue": r[5] || (r[5] = ($) => t.data.labelTip = $), clearable: "", placeholder: "标题提示" }, null, 8, ["modelValue"])]), _: 1 }), t.data.labelTip ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 5, label: "标题提示位置", "label-width": "110px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { style: { width: "100%" }, modelValue: t.data.labelTipPlacement, "onUpdate:modelValue": r[6] || (r[6] = ($) => t.data.labelTipPlacement = $), placeholder: "标题提示位置", clearable: "" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { label: "上", value: "top" }), require$$0.createVNode(u, { label: "下", value: "bottom" }), require$$0.createVNode(u, { label: "左", value: "left" }), require$$0.createVNode(u, { label: "右", value: "right" })]), _: 1 }, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), ["group", "title"].includes(t.data.type) ? require$$0.createCommentVNode("", true) : (require$$0.openBlock(), require$$0.createBlock(a, { key: 6, label: "默认值" }, { default: require$$0.withCtx(() => [t.defaultValues && t.defaultValues[t.data.type] ? (require$$0.openBlock(), require$$0.createBlock(c, { key: 0, modelValue: t.data.value, "onUpdate:modelValue": r[7] || (r[7] = ($) => t.data.value = $), "allow-create": "", clearable: "", filterable: "", placeholder: "默认值" }, { default: require$$0.withCtx(() => [(require$$0.openBlock(true), require$$0.createElementBlock(require$$0.Fragment, null, require$$0.renderList(t.defaultValues[t.data.type], ($) => (require$$0.openBlock(), require$$0.createBlock(u, { key: $.value, label: $.label, value: $.value }, null, 8, ["label", "value"]))), 128))]), _: 1 }, 8, ["modelValue"])) : (require$$0.openBlock(), require$$0.createBlock(l, { key: 1, modelValue: t.data.value, "onUpdate:modelValue": r[8] || (r[8] = ($) => t.data.value = $), clearable: "", placeholder: "默认值" }, null, 8, ["modelValue"]))]), _: 1 })), !t.data.subfield && !["group", "title"].includes(t.data.type) ? (require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, { key: 7 }, [require$$0.createVNode(a, { label: "字段备注" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.columnTip, "onUpdate:modelValue": r[9] || (r[9] = ($) => t.data.columnTip = $), clearable: "", placeholder: "字段备注", onInput: d.handleColumnTipChange }, null, 8, ["modelValue", "onInput"])]), _: 1 }), t.data.columnTip ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 0, label: "备注类型", clearable: "" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { style: { width: "100%" }, modelValue: t.data.columnTipType, "onUpdate:modelValue": r[10] || (r[10] = ($) => t.data.columnTipType = $), placeholder: "字段备注类型", clearable: "" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { label: "默认", value: "" }), require$$0.createVNode(u, { label: "主要", value: "primary" }), require$$0.createVNode(u, { label: "警告", value: "warning" }), require$$0.createVNode(u, { label: "危险", value: "danger" })]), _: 1 }, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true)], 64)) : require$$0.createCommentVNode("", true), ["table-select", "table-select-v2"].includes(t.data.subfield) ? require$$0.createCommentVNode("", true) : (require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, { key: 8 }, [require$$0.createVNode(a, { label: "是否只读" }, { default: require$$0.withCtx(() => [require$$0.createVNode(m, { modelValue: t.data.readonly, "onUpdate:modelValue": r[11] || (r[11] = ($) => t.data.readonly = $) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "是否禁用" }, { default: require$$0.withCtx(() => [require$$0.createVNode(m, { modelValue: t.data.disabled, "onUpdate:modelValue": r[12] || (r[12] = ($) => t.data.disabled = $) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "是否可见" }, { default: require$$0.withCtx(() => [require$$0.createVNode(m, { modelValue: t.data.display, "onUpdate:modelValue": r[13] || (r[13] = ($) => t.data.display = $) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "是否必填" }, { default: require$$0.withCtx(() => [require$$0.createVNode(m, { modelValue: t.data.required, "onUpdate:modelValue": r[14] || (r[14] = ($) => t.data.required = $) }, null, 8, ["modelValue"]), t.data.required ? (require$$0.openBlock(), require$$0.createBlock(l, { key: 0, modelValue: t.data.pattern, "onUpdate:modelValue": r[15] || (r[15] = ($) => t.data.pattern = $), modelModifiers: { lazy: true }, placeholder: "正则表达式" }, null, 8, ["modelValue"])) : require$$0.createCommentVNode("", true)]), _: 1 })], 64)), ["table-select", "table-select-v2"].includes(t.data.subfield) ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 9, label: "可搜索" }, { default: require$$0.withCtx(() => [require$$0.createVNode(m, { modelValue: t.data.search, "onUpdate:modelValue": r[16] || (r[16] = ($) => t.data.search = $) }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true)], 64);
      }
      const Form = _export_sfc(_sfc_main$r, [["render", _sfc_render$p]]), _sfc_main$q = { name: "config-crud", props: ["data"], data() {
        return {};
      } };
      function _sfc_render$o(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("el-switch"), a = require$$0.resolveComponent("el-form-item"), u = require$$0.resolveComponent("el-input-number"), c = require$$0.resolveComponent("el-radio"), f = require$$0.resolveComponent("el-radio-group"), m = require$$0.resolveComponent("el-divider"), $ = require$$0.resolveComponent("el-alert");
        return require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, null, [require$$0.createVNode(a, { label: "可搜索" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.search, "onUpdate:modelValue": r[0] || (r[0] = (p) => t.data.search = p) }, null, 8, ["modelValue"])]), _: 1 }), t.data.subfield !== "table" ? (require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, { key: 0 }, [require$$0.createVNode(a, { label: "隐藏" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.hide, "onUpdate:modelValue": r[1] || (r[1] = (p) => t.data.hide = p) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "宽度" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.width, "onUpdate:modelValue": r[2] || (r[2] = (p) => t.data.width = p), placeholder: "auto", min: 1, "controls-position": "right" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "对齐" }, { default: require$$0.withCtx(() => [require$$0.createVNode(f, { modelValue: t.data.align, "onUpdate:modelValue": r[3] || (r[3] = (p) => t.data.align = p) }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { label: "左", value: "left" }, { default: require$$0.withCtx(() => r[12] || (r[12] = [require$$0.createTextVNode("左")])), _: 1, __: [12] }), require$$0.createVNode(c, { label: "中", value: "center" }, { default: require$$0.withCtx(() => r[13] || (r[13] = [require$$0.createTextVNode("中")])), _: 1, __: [13] }), require$$0.createVNode(c, { label: "右", value: "right" }, { default: require$$0.withCtx(() => r[14] || (r[14] = [require$$0.createTextVNode("右")])), _: 1, __: [14] })]), _: 1 }, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "超出隐藏" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.overHidden, "onUpdate:modelValue": r[4] || (r[4] = (p) => t.data.overHidden = p) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(m, { "content-position": "center" }, { default: require$$0.withCtx(() => r[15] || (r[15] = [require$$0.createTextVNode("弹 窗")])), _: 1, __: [15] }), require$$0.createVNode($, { title: "显示默认为`是`", type: "warning", closable: false, center: "" }), require$$0.createVNode(a, { label: "新增显示" }, { default: require$$0.withCtx(() => [require$$0.createVNode(f, { modelValue: t.data.addDisplay, "onUpdate:modelValue": r[5] || (r[5] = (p) => t.data.addDisplay = p) }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { label: "是", value: true }, { default: require$$0.withCtx(() => r[16] || (r[16] = [require$$0.createTextVNode("是")])), _: 1, __: [16] }), require$$0.createVNode(c, { label: "否", value: false }, { default: require$$0.withCtx(() => r[17] || (r[17] = [require$$0.createTextVNode("否")])), _: 1, __: [17] })]), _: 1 }, 8, ["modelValue"])]), _: 1 }), t.data.addDisplay !== false ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 0, label: "新增禁用" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.addDisabled, "onUpdate:modelValue": r[6] || (r[6] = (p) => t.data.addDisabled = p) }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), t.data.addDisplay !== false ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 1, label: "新增只读" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.addReadonly, "onUpdate:modelValue": r[7] || (r[7] = (p) => t.data.addReadonly = p) }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), require$$0.createVNode(a, { label: "编辑显示" }, { default: require$$0.withCtx(() => [require$$0.createVNode(f, { modelValue: t.data.editDisplay, "onUpdate:modelValue": r[8] || (r[8] = (p) => t.data.editDisplay = p) }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { label: "是", value: true }, { default: require$$0.withCtx(() => r[18] || (r[18] = [require$$0.createTextVNode("是")])), _: 1, __: [18] }), require$$0.createVNode(c, { label: "否", value: false }, { default: require$$0.withCtx(() => r[19] || (r[19] = [require$$0.createTextVNode("否")])), _: 1, __: [19] })]), _: 1 }, 8, ["modelValue"])]), _: 1 }), t.data.editDisplay !== false ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 2, label: "编辑禁用" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.editDisabled, "onUpdate:modelValue": r[9] || (r[9] = (p) => t.data.editDisabled = p) }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), t.data.editDisplay !== false ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 3, label: "编辑只读" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.editReadonly, "onUpdate:modelValue": r[10] || (r[10] = (p) => t.data.editReadonly = p) }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), require$$0.createVNode(a, { label: "查看显示" }, { default: require$$0.withCtx(() => [require$$0.createVNode(f, { modelValue: t.data.viewDisplay, "onUpdate:modelValue": r[11] || (r[11] = (p) => t.data.viewDisplay = p) }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { label: "是", value: true }, { default: require$$0.withCtx(() => r[20] || (r[20] = [require$$0.createTextVNode("是")])), _: 1, __: [20] }), require$$0.createVNode(c, { label: "否", value: false }, { default: require$$0.withCtx(() => r[21] || (r[21] = [require$$0.createTextVNode("否")])), _: 1, __: [21] })]), _: 1 }, 8, ["modelValue"])]), _: 1 })], 64)) : require$$0.createCommentVNode("", true)], 64);
      }
      const Crud = _export_sfc(_sfc_main$q, [["render", _sfc_render$o]]), _sfc_main$p = { name: "config-database", props: ["data"], watch: { "data.prop": { handler() {
        this.data.database || (this.data.database = {});
      }, immediate: true } } };
      function _sfc_render$n(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("el-icon-question-filled"), a = require$$0.resolveComponent("el-icon"), u = require$$0.resolveComponent("el-tooltip"), c = require$$0.resolveComponent("el-switch"), f = require$$0.resolveComponent("el-form-item"), m = require$$0.resolveComponent("el-input");
        return require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, null, [require$$0.createVNode(f, { label: "作为子表存储", "label-width": "130px" }, { label: require$$0.withCtx(() => [require$$0.createVNode(u, { effect: "dark", "raw-content": "", content: "非低代码用户请忽略此配置。<br />是：单独建表存储数据。<br />否：作为主表字段存储数据。", placement: "top-start" }, { default: require$$0.withCtx(() => [require$$0.createElementVNode("span", null, [r[3] || (r[3] = require$$0.createTextVNode(" 作为子表存储 ")), require$$0.createVNode(a, null, { default: require$$0.withCtx(() => [require$$0.createVNode(l)]), _: 1 })])]), _: 1 })]), default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: t.data.database.isSub, "onUpdate:modelValue": r[0] || (r[0] = ($) => t.data.database.isSub = $) }, null, 8, ["modelValue"])]), _: 1 }), t.data.database.isSub ? (require$$0.openBlock(), require$$0.createBlock(f, { key: 0, label: "表名" }, { default: require$$0.withCtx(() => [require$$0.createVNode(m, { modelValue: t.data.database.tableName, "onUpdate:modelValue": r[1] || (r[1] = ($) => t.data.database.tableName = $), clearable: "", placeholder: t.data.prop }, { prepend: require$$0.withCtx(() => r[4] || (r[4] = [require$$0.createTextVNode("父表名_")])), _: 1 }, 8, ["modelValue", "placeholder"])]), _: 1 })) : require$$0.createCommentVNode("", true), t.data.database.isSub ? (require$$0.openBlock(), require$$0.createBlock(f, { key: 1, label: "表注释" }, { default: require$$0.withCtx(() => [require$$0.createVNode(m, { modelValue: t.data.database.tableComment, "onUpdate:modelValue": r[2] || (r[2] = ($) => t.data.database.tableComment = $), clearable: "", placeholder: t.data.label }, { prepend: require$$0.withCtx(() => r[5] || (r[5] = [require$$0.createTextVNode("父表注释 --")])), _: 1 }, 8, ["modelValue", "placeholder"])]), _: 1 })) : require$$0.createCommentVNode("", true)], 64);
      }
      const Database = _export_sfc(_sfc_main$p, [["render", _sfc_render$n]]), _sfc_main$o = { name: "config-custom", props: ["data"], data() {
        return { params: this.data.params || {}, event: this.data.event || {}, options: { fullScreen: true, minimap: { enabled: false } }, isUpdating: false };
      }, watch: { "data.params"(o) {
        this.isUpdating || (this.params = o || {});
      }, "data.event"(o) {
        this.isUpdating || (this.event = o || {});
      }, params(o) {
        try {
          this.isUpdating = true, this.data.params = Function('"use strict";return (' + o + ")")(), this.$nextTick(() => {
            this.isUpdating = false;
          });
        } catch {
          this.isUpdating = false;
        }
      }, event(o) {
        try {
          this.isUpdating = true, this.data.event = Function('"use strict";return (' + o + ")")(), this.$nextTick(() => {
            this.isUpdating = false;
          });
        } catch {
          this.isUpdating = false;
        }
      } } }, _hoisted_1$8 = { class: "el-form--label-top" }, _hoisted_2$8 = { class: "el-form-item el-form-item--small" }, _hoisted_3$7 = { class: "el-form-item__content" }, _hoisted_4$6 = { class: "el-form-item el-form-item--small" }, _hoisted_5$4 = { class: "el-form-item__content" };
      function _sfc_render$m(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("nf-codemirror");
        return require$$0.openBlock(), require$$0.createElementBlock("div", _hoisted_1$8, [require$$0.createElementVNode("div", _hoisted_2$8, [r[2] || (r[2] = require$$0.createElementVNode("label", { class: "el-form-item__label", style: { padding: "0" } }, "自定义属性：", -1)), require$$0.createElementVNode("div", _hoisted_3$7, [(require$$0.openBlock(), require$$0.createBlock(l, { key: t.data.prop, modelValue: n.params, "onUpdate:modelValue": r[0] || (r[0] = (a) => n.params = a), style: { height: "300px" } }, null, 8, ["modelValue"]))])]), require$$0.createElementVNode("div", _hoisted_4$6, [r[3] || (r[3] = require$$0.createElementVNode("label", { class: "el-form-item__label", style: { padding: "0" } }, "自定义事件：", -1)), require$$0.createElementVNode("div", _hoisted_5$4, [(require$$0.openBlock(), require$$0.createBlock(l, { key: t.data.prop, modelValue: n.event, "onUpdate:modelValue": r[1] || (r[1] = (a) => n.event = a), style: { height: "300px" } }, null, 8, ["modelValue"]))])])]);
      }
      const Custom = _export_sfc(_sfc_main$o, [["render", _sfc_render$m]]), _sfc_main$n = { name: "config-input", props: ["data"] };
      function _sfc_render$l(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("el-input"), a = require$$0.resolveComponent("el-form-item"), u = require$$0.resolveComponent("el-input-number"), c = require$$0.resolveComponent("el-switch");
        return require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, null, [require$$0.createVNode(a, { label: "占位内容" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.placeholder, "onUpdate:modelValue": r[0] || (r[0] = (f) => t.data.placeholder = f), clearable: "", placeholder: "占位内容" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "前缀" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.prepend, "onUpdate:modelValue": r[1] || (r[1] = (f) => t.data.prepend = f), clearable: "", placeholder: "前缀" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "后缀" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.append, "onUpdate:modelValue": r[2] || (r[2] = (f) => t.data.append = f), clearable: "", placeholder: "后缀" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "最大长度" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.maxlength, "onUpdate:modelValue": r[3] || (r[3] = (f) => t.data.maxlength = f), min: 1, "controls-position": "right", placeholder: "最大长度" }, null, 8, ["modelValue"])]), _: 1 }), t.data.type != "password" ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 0, label: "显示计数" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: t.data.showWordLimit, "onUpdate:modelValue": r[4] || (r[4] = (f) => t.data.showWordLimit = f) }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), t.data.type == "password" ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 1, label: "显示密码" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: t.data.showPassword, "onUpdate:modelValue": r[5] || (r[5] = (f) => t.data.showPassword = f) }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true)], 64);
      }
      const Input = _export_sfc(_sfc_main$n, [["render", _sfc_render$l]]), _sfc_main$m = { name: "config-input-tag", props: ["data"] };
      function _sfc_render$k(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("el-input"), a = require$$0.resolveComponent("el-form-item"), u = require$$0.resolveComponent("el-input-number"), c = require$$0.resolveComponent("el-option"), f = require$$0.resolveComponent("el-select"), m = require$$0.resolveComponent("el-radio"), $ = require$$0.resolveComponent("el-radio-group"), p = require$$0.resolveComponent("el-switch");
        return require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, null, [require$$0.createVNode(a, { label: "占位内容" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.placeholder, "onUpdate:modelValue": r[0] || (r[0] = (g) => t.data.placeholder = g), clearable: "", placeholder: "占位内容" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "最大数量" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.max, "onUpdate:modelValue": r[1] || (r[1] = (g) => t.data.max = g), min: 1, "controls-position": "right", placeholder: "标签最大数量" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "标签类型" }, { default: require$$0.withCtx(() => [require$$0.createVNode(f, { modelValue: t.data.tagType, "onUpdate:modelValue": r[2] || (r[2] = (g) => t.data.tagType = g), placeholder: "标签类型", clearable: "" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { label: "info", value: "info" }), require$$0.createVNode(c, { label: "primary", value: "primary" }), require$$0.createVNode(c, { label: "danger", value: "danger" }), require$$0.createVNode(c, { label: "warning", value: "warning" })]), _: 1 }, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "标签效果" }, { default: require$$0.withCtx(() => [require$$0.createVNode(f, { modelValue: t.data.tagEffect, "onUpdate:modelValue": r[3] || (r[3] = (g) => t.data.tagEffect = g), placeholder: "标签效果", clearable: "" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { label: "light", value: "light" }), require$$0.createVNode(c, { label: "dark", value: "dark" }), require$$0.createVNode(c, { label: "plain", value: "plain" })]), _: 1 }, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "触发方式" }, { default: require$$0.withCtx(() => [require$$0.createVNode($, { modelValue: t.data.trigger, "onUpdate:modelValue": r[4] || (r[4] = (g) => t.data.trigger = g) }, { default: require$$0.withCtx(() => [require$$0.createVNode(m, { label: "回车", value: "Enter" }, { default: require$$0.withCtx(() => r[6] || (r[6] = [require$$0.createTextVNode("回车")])), _: 1, __: [6] }), require$$0.createVNode(m, { label: "空格", value: "Space" }, { default: require$$0.withCtx(() => r[7] || (r[7] = [require$$0.createTextVNode("空格")])), _: 1, __: [7] })]), _: 1 }, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "可拖动" }, { default: require$$0.withCtx(() => [require$$0.createVNode(p, { modelValue: t.data.draggable, "onUpdate:modelValue": r[5] || (r[5] = (g) => t.data.draggable = g) }, null, 8, ["modelValue"])]), _: 1 })], 64);
      }
      const InputTag = _export_sfc(_sfc_main$m, [["render", _sfc_render$k]]), _sfc_main$l = { name: "config-textarea", props: ["data"] };
      function _sfc_render$j(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("el-input"), a = require$$0.resolveComponent("el-form-item"), u = require$$0.resolveComponent("el-input-number"), c = require$$0.resolveComponent("el-switch");
        return require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, null, [require$$0.createVNode(a, { label: "占位内容" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.placeholder, "onUpdate:modelValue": r[0] || (r[0] = (f) => t.data.placeholder = f), type: "textarea", autosize: "", clearable: "", placeholder: "占位内容" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "最大长度" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.maxlength, "onUpdate:modelValue": r[1] || (r[1] = (f) => t.data.maxlength = f), "controls-position": "right", placeholder: "最大长度" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "显示计数" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: t.data.showWordLimit, "onUpdate:modelValue": r[2] || (r[2] = (f) => t.data.showWordLimit = f) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "最小行" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.minRows, "onUpdate:modelValue": r[3] || (r[3] = (f) => t.data.minRows = f), "controls-position": "right", placeholder: "最小行", min: 1 }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "最大行" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.maxRows, "onUpdate:modelValue": r[4] || (r[4] = (f) => t.data.maxRows = f), "controls-position": "right", placeholder: "最大行", min: 2 }, null, 8, ["modelValue"])]), _: 1 })], 64);
      }
      const Textarea = _export_sfc(_sfc_main$l, [["render", _sfc_render$j]]), _sfc_main$k = { name: "config-number", props: ["data"], methods: { beforeChange() {
        return new Promise((o, r) => {
          const { showChinese: t, columnTip: s } = this.data;
          if (s) return this.$message.error("字段备注有值时此选项无法使用"), r(false);
          o(true);
        });
      } } };
      function _sfc_render$i(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("el-input"), a = require$$0.resolveComponent("el-form-item"), u = require$$0.resolveComponent("el-switch"), c = require$$0.resolveComponent("el-input-number"), f = require$$0.resolveComponent("el-radio");
        return require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, null, [require$$0.createVNode(a, { label: "占位内容" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.placeholder, "onUpdate:modelValue": r[0] || (r[0] = (m) => t.data.placeholder = m), clearable: "", placeholder: "占位内容" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "开启中文大写", "label-width": "120px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.showChinese, "onUpdate:modelValue": r[1] || (r[1] = (m) => t.data.showChinese = m), "before-change": d.beforeChange }, null, 8, ["modelValue", "before-change"])]), _: 1 }), require$$0.createVNode(a, { label: "前缀" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.prefix, "onUpdate:modelValue": r[2] || (r[2] = (m) => t.data.prefix = m), clearable: "", placeholder: "前缀" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "后缀" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.suffix, "onUpdate:modelValue": r[3] || (r[3] = (m) => t.data.suffix = m), clearable: "", placeholder: "后缀" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "最小值" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: t.data.min, "onUpdate:modelValue": r[4] || (r[4] = (m) => t.data.min = m), "controls-position": "right", placeholder: "最小值" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "最大值" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: t.data.max, "onUpdate:modelValue": r[5] || (r[5] = (m) => t.data.max = m), "controls-position": "right", placeholder: "最大值" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "步长" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: t.data.step, "onUpdate:modelValue": r[6] || (r[6] = (m) => t.data.step = m), "controls-position": "right", placeholder: "步长" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "数值精度" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: t.data.precision, "onUpdate:modelValue": r[7] || (r[7] = (m) => t.data.precision = m), "controls-position": "right", placeholder: "数值精度", min: 0, max: 10 }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "开启控制器", "label-width": "100px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.controls, "onUpdate:modelValue": r[8] || (r[8] = (m) => t.data.controls = m) }, null, 8, ["modelValue"])]), _: 1 }), t.data.controls ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 0, label: "控制器位置", "label-width": "100px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(f, { modelValue: t.data.controlsPosition, "onUpdate:modelValue": r[9] || (r[9] = (m) => t.data.controlsPosition = m), label: "", value: "" }, { default: require$$0.withCtx(() => r[11] || (r[11] = [require$$0.createTextVNode("默认")])), _: 1, __: [11] }, 8, ["modelValue"]), require$$0.createVNode(f, { modelValue: t.data.controlsPosition, "onUpdate:modelValue": r[10] || (r[10] = (m) => t.data.controlsPosition = m), label: "right", value: "right" }, { default: require$$0.withCtx(() => r[12] || (r[12] = [require$$0.createTextVNode(" 右 ")])), _: 1, __: [12] }, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true)], 64);
      }
      const Number$1 = _export_sfc(_sfc_main$k, [["render", _sfc_render$i]]), _sfc_main$j = { name: "config-dynamic", props: ["data"] };
      function _sfc_render$h(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("el-option"), a = require$$0.resolveComponent("el-select"), u = require$$0.resolveComponent("el-form-item"), c = require$$0.resolveComponent("el-switch");
        return require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, null, [require$$0.createVNode(u, { label: "对齐方式" }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { modelValue: t.data.children.align, "onUpdate:modelValue": r[0] || (r[0] = (f) => t.data.children.align = f), placeholder: "对齐方式", clearable: "" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { label: "居左", value: "left" }), require$$0.createVNode(l, { label: "居中", value: "center" }), require$$0.createVNode(l, { label: "居右", value: "right" })]), _: 1 }, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(u, { label: "头部对齐方式", "label-width": "110px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { modelValue: t.data.children.headerAlign, "onUpdate:modelValue": r[1] || (r[1] = (f) => t.data.children.headerAlign = f), placeholder: "对齐方式", clearable: "" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { label: "居左", value: "left" }), require$$0.createVNode(l, { label: "居中", value: "center" }), require$$0.createVNode(l, { label: "居右", value: "right" })]), _: 1 }, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(u, { label: "表单格式" }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { modelValue: t.data.children.type, "onUpdate:modelValue": r[2] || (r[2] = (f) => t.data.children.type = f), placeholder: "表单格式", clearable: "" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { label: "表格", value: "table" }), require$$0.createVNode(l, { label: "表单", value: "form" })]), _: 1 }, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(u, { label: "序号" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: t.data.children.index, "onUpdate:modelValue": r[3] || (r[3] = (f) => t.data.children.index = f) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(u, { label: "添加按钮" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: t.data.children.addBtn, "onUpdate:modelValue": r[4] || (r[4] = (f) => t.data.children.addBtn = f) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(u, { label: "删除按钮" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: t.data.children.delBtn, "onUpdate:modelValue": r[5] || (r[5] = (f) => t.data.children.delBtn = f) }, null, 8, ["modelValue"])]), _: 1 })], 64);
      }
      const Dynamic = _export_sfc(_sfc_main$j, [["render", _sfc_render$h]]), _sfc_main$i = { name: "config-switch", props: ["data"], methods: { handleDicClear() {
        this.data.dicData = [{ label: "", value: "0" }, { label: "", value: "1" }];
      } } };
      function _sfc_render$g(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("el-button"), a = require$$0.resolveComponent("el-input"), u = require$$0.resolveComponent("el-form-item");
        return require$$0.openBlock(), require$$0.createBlock(u, { label: "自定义" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { text: "", type: "primary", onClick: d.handleDicClear, class: "danger" }, { default: require$$0.withCtx(() => r[0] || (r[0] = [require$$0.createTextVNode(" 清空 ")])), _: 1, __: [0] }, 8, ["onClick"]), (require$$0.openBlock(true), require$$0.createElementBlock(require$$0.Fragment, null, require$$0.renderList(t.data.dicData, (c, f) => (require$$0.openBlock(), require$$0.createElementBlock("div", { class: "dic", key: f }, [require$$0.createVNode(a, { modelValue: c.label, "onUpdate:modelValue": (m) => c.label = m, placeholder: "自定义文字", clearable: "", style: { "margin-right": "5px" } }, null, 8, ["modelValue", "onUpdate:modelValue"]), require$$0.createVNode(a, { modelValue: c.value, "onUpdate:modelValue": (m) => c.value = m, clearable: "", placeholder: "自定义值" }, null, 8, ["modelValue", "onUpdate:modelValue"])]))), 128))]), _: 1 });
      }
      const Switch = _export_sfc(_sfc_main$i, [["render", _sfc_render$g], ["__scopeId", "data-v-8312aa7f"]]), _sfc_main$h = { name: "config-rate", props: ["data"], data() {
        return { textVisible: false, textValue: "", colorVisible: false, colorValue: "", iconVisible: false, iconValue: "" };
      }, methods: { handleTextClose(o) {
        this.data.texts.splice(this.data.texts.indexOf(o), 1);
      }, showTextInput() {
        this.textVisible = true, this.$nextTick(() => {
          this.$refs.textTag.$refs.input.focus();
        });
      }, handleTextConfirm() {
        this.textValue && this.data.texts.push(this.textValue), this.textVisible = false, this.textValue = "";
      }, handleColorClose(o) {
        this.data.colors.splice(this.data.colors.indexOf(o), 1);
      }, handleColorConfirm() {
        this.colorValue && this.data.colors.push(this.colorValue), this.colorValue = "";
      } } };
      function _sfc_render$f(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("el-input-number"), a = require$$0.resolveComponent("el-form-item"), u = require$$0.resolveComponent("el-switch"), c = require$$0.resolveComponent("el-tag"), f = require$$0.resolveComponent("el-input"), m = require$$0.resolveComponent("el-button"), $ = require$$0.resolveComponent("el-color-picker");
        return require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, null, [require$$0.createVNode(a, { label: "最大星数" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.max, "onUpdate:modelValue": r[0] || (r[0] = (p) => t.data.max = p), "controls-position": "right", placeholder: "最大星数" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "显示文本", "label-width": "110px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.showText, "onUpdate:modelValue": r[1] || (r[1] = (p) => t.data.showText = p) }, null, 8, ["modelValue"])]), _: 1 }), t.data.showText ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 0, label: "自定义文本", "label-width": "110px" }, { default: require$$0.withCtx(() => [(require$$0.openBlock(true), require$$0.createElementBlock(require$$0.Fragment, null, require$$0.renderList(t.data.texts, (p, g) => (require$$0.openBlock(), require$$0.createBlock(c, { key: g, size: "default", closable: "", onClose: (b) => d.handleTextClose(p) }, { default: require$$0.withCtx(() => [require$$0.createTextVNode(require$$0.toDisplayString(p), 1)]), _: 2 }, 1032, ["onClose"]))), 128)), n.textVisible ? (require$$0.openBlock(), require$$0.createBlock(f, { key: 0, class: "input-new-tag", modelValue: n.textValue, "onUpdate:modelValue": r[2] || (r[2] = (p) => n.textValue = p), ref: "textTag", clearable: "", size: "default", onKeyup: require$$0.withKeys(d.handleTextConfirm, ["enter"]), onBlur: d.handleTextConfirm }, null, 8, ["modelValue", "onKeyup", "onBlur"])) : require$$0.createCommentVNode("", true), !n.textVisible && t.data.texts.length < t.data.max ? (require$$0.openBlock(), require$$0.createBlock(m, { key: 1, onClick: d.showTextInput, icon: "el-icon-plus", circle: "", size: "default", style: { "margin-left": "5px" } }, null, 8, ["onClick"])) : require$$0.createCommentVNode("", true)]), _: 1 })) : require$$0.createCommentVNode("", true), require$$0.createVNode(a, { label: "自定义颜色", "label-width": "110px" }, { default: require$$0.withCtx(() => [(require$$0.openBlock(true), require$$0.createElementBlock(require$$0.Fragment, null, require$$0.renderList(t.data.colors, (p, g) => (require$$0.openBlock(), require$$0.createBlock(c, { key: g, closable: "", size: "default", onClose: (b) => d.handleColorClose(p), style: require$$0.normalizeStyle({ color: p }) }, { default: require$$0.withCtx(() => [require$$0.createTextVNode(require$$0.toDisplayString(p), 1)]), _: 2 }, 1032, ["onClose", "style"]))), 128)), require$$0.createVNode($, { modelValue: n.colorValue, "onUpdate:modelValue": r[3] || (r[3] = (p) => n.colorValue = p), onChange: d.handleColorConfirm, class: "color-picker" }, null, 8, ["modelValue", "onChange"])]), _: 1 })], 64);
      }
      const Rate = _export_sfc(_sfc_main$h, [["render", _sfc_render$f], ["__scopeId", "data-v-6c6260d7"]]), _sfc_main$g = { name: "config-slider", props: ["data"], watch: { "data.range"(o) {
        o && (this.data.dataType = "string");
      } } };
      function _sfc_render$e(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("el-input-number"), a = require$$0.resolveComponent("el-form-item"), u = require$$0.resolveComponent("el-switch");
        return require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, null, [require$$0.createVNode(a, { label: "最小值" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.min, "onUpdate:modelValue": r[0] || (r[0] = (c) => t.data.min = c), "controls-position": "right", placeholder: "最小值", max: t.data.max - 1, min: 0 }, null, 8, ["modelValue", "max"])]), _: 1 }), require$$0.createVNode(a, { label: "最大值" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.max, "onUpdate:modelValue": r[1] || (r[1] = (c) => t.data.max = c), "controls-position": "right", placeholder: "最大值", min: t.data.min + 1 }, null, 8, ["modelValue", "min"])]), _: 1 }), require$$0.createVNode(a, { label: "步长" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.step, "onUpdate:modelValue": r[2] || (r[2] = (c) => t.data.step = c), "controls-position": "right", placeholder: "步长", min: 1, max: t.data.max - t.data.min }, null, 8, ["modelValue", "max"])]), _: 1 }), require$$0.createVNode(a, { label: "显示间隔点", "label-width": "100px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.showStops, "onUpdate:modelValue": r[3] || (r[3] = (c) => t.data.showStops = c) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "范围" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.range, "onUpdate:modelValue": r[4] || (r[4] = (c) => t.data.range = c) }, null, 8, ["modelValue"])]), _: 1 }), t.data.range ? require$$0.createCommentVNode("", true) : (require$$0.openBlock(), require$$0.createBlock(a, { key: 0, label: "显示输入框", "label-width": "100px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.showInput, "onUpdate:modelValue": r[5] || (r[5] = (c) => t.data.showInput = c) }, null, 8, ["modelValue"])]), _: 1 }))], 64);
      }
      const Slider = _export_sfc(_sfc_main$g, [["render", _sfc_render$e]]), _sfc_main$f = { name: "config-color", props: ["data"] };
      function _sfc_render$d(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("el-input"), a = require$$0.resolveComponent("el-form-item");
        return require$$0.openBlock(), require$$0.createBlock(a, { label: "占位内容" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.placeholder, "onUpdate:modelValue": r[0] || (r[0] = (u) => t.data.placeholder = u), clearable: "", placeholder: "占位内容" }, null, 8, ["modelValue"])]), _: 1 });
      }
      const Color = _export_sfc(_sfc_main$f, [["render", _sfc_render$d]]), _hoisted_1$7 = { class: "el-form--label-top" }, _hoisted_2$7 = { class: "el-form-item" }, _hoisted_3$6 = { class: "el-form-item__content" }, _hoisted_4$5 = { style: { "margin-bottom": "5px" } }, _hoisted_5$3 = { style: { "margin-left": "22px" } }, _hoisted_6$3 = { style: { display: "flex", "margin-top": "10px" } }, _hoisted_7$3 = { style: { display: "flex", "margin-top": "10px" } }, _hoisted_8$2 = { style: { display: "flex", "margin-top": "10px" } }, _hoisted_9$2 = { key: 0 }, _hoisted_10$1 = { key: 1 }, _hoisted_11$1 = { class: "el-form--label-top" }, _hoisted_12$1 = { class: "el-form-item" }, _hoisted_13$1 = { class: "el-form-item__content" }, _hoisted_14$1 = { style: { width: "50px" } }, _hoisted_15$1 = { class: "el-form--label-top" }, _hoisted_16$1 = { class: "el-form-item" }, _hoisted_17$1 = { class: "el-form-item__content" }, _hoisted_18$1 = { style: { "margin-bottom": "5px" } }, _hoisted_19$1 = { style: { "margin-left": "22px" } }, __default__$1 = { name: "config-select", props: ["data"], data() {
        return { option: { column: [{ type: "input", prop: "key", label: "key" }, { type: "input", prop: "value", label: "value" }] }, options: { fullScreen: true, minimap: { enabled: false } }, dicFormatter: "" };
      }, watch: { "data.prop": { handler() {
        const { dicFormatter: o } = this.data;
        this.dicFormatter = o ? o + "" : `(res) => {\r
  return res.data\r
}`;
      }, immediate: true }, dicFormatter: { handler(o) {
        try {
          this.data.dicFormatter = Function('"use strict";return (' + o + ")")();
        } catch {
        }
      } } }, methods: { handleRemoveFields(o) {
        this.data.dicData.splice(o, 1);
      }, handleAddFields() {
        const o = Math.ceil(Math.random() * 99999);
        this.data.dicData.push({ label: `字段${o}`, value: `col_${o}` });
      }, handleRemoveCascaderItemFields(o) {
        this.data.cascader.splice(o, 1);
      }, handleAddCascaderItemFields() {
        this.data.cascader.push("");
      }, handleTabClick({ paneName: o }) {
        o == "remote" && !this.data.dicQueryConfig && (this.data.dicQueryConfig = []);
      } } }, _sfc_main$e = Object.assign(__default__$1, { setup(o) {
        return (r, t) => {
          const s = require$$0.resolveComponent("el-input"), n = require$$0.resolveComponent("el-form-item"), d = require$$0.resolveComponent("el-icon-operation"), l = require$$0.resolveComponent("el-icon"), a = require$$0.resolveComponent("el-button"), u = require$$0.resolveComponent("el-tab-pane"), c = require$$0.resolveComponent("el-switch"), f = require$$0.resolveComponent("el-option"), m = require$$0.resolveComponent("el-select"), $ = require$$0.resolveComponent("nf-dynamic"), p = require$$0.resolveComponent("nf-codemirror"), g = require$$0.resolveComponent("el-tabs"), b = require$$0.resolveComponent("el-input-number"), h = require$$0.resolveComponent("el-radio-button"), q = require$$0.resolveComponent("el-radio-group");
          return require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, null, [o.data.type == "select" ? (require$$0.openBlock(), require$$0.createBlock(n, { key: 0, label: "占位内容" }, { default: require$$0.withCtx(() => [require$$0.createVNode(s, { modelValue: o.data.placeholder, "onUpdate:modelValue": t[0] || (t[0] = (v) => o.data.placeholder = v), clearable: "", placeholder: "占位内容" }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), require$$0.createElementVNode("div", _hoisted_1$7, [require$$0.createElementVNode("div", _hoisted_2$7, [t[21] || (t[21] = require$$0.createElementVNode("label", { class: "el-form-item__label", style: { padding: "0" } }, "字典配置：", -1)), require$$0.createElementVNode("div", _hoisted_3$6, [require$$0.createVNode(g, { modelValue: o.data.dicOption, "onUpdate:modelValue": t[6] || (t[6] = (v) => o.data.dicOption = v), stretch: "", onTabClick: r.handleTabClick }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { label: "静态数据", name: "static" }, { default: require$$0.withCtx(() => [require$$0.createVNode(require$$0.unref(Draggable), { style: { "margin-top": "10px" }, tag: "ul", list: o.data.dicData, group: { name: "dic" }, "ghost-class": "ghost", "item-key": "id", handle: ".drag-item" }, { item: require$$0.withCtx(({ element: v, index: y }) => [require$$0.createElementVNode("li", _hoisted_4$5, [require$$0.createVNode(l, { size: 30 }, { default: require$$0.withCtx(() => [require$$0.createVNode(d, { class: "drag-item", style: { margin: "0 5px", cursor: "move" } })]), _: 1 }), require$$0.createVNode(s, { style: { "margin-right": "5px" }, clearable: "", modelValue: v.label, "onUpdate:modelValue": (w) => v.label = w, placeholder: "label" }, null, 8, ["modelValue", "onUpdate:modelValue"]), require$$0.createVNode(s, { style: require$$0.normalizeStyle({ "margin-right": o.data.type == "select" ? "5px" : "0" }), clearable: "", modelValue: v.value, "onUpdate:modelValue": (w) => v.value = w, placeholder: "value" }, null, 8, ["style", "modelValue", "onUpdate:modelValue"]), o.data.type == "select" ? (require$$0.openBlock(), require$$0.createBlock(s, { key: 0, clearable: "", modelValue: v.desc, "onUpdate:modelValue": (w) => v.desc = w, placeholder: "描述" }, null, 8, ["modelValue", "onUpdate:modelValue"])) : require$$0.createCommentVNode("", true), require$$0.createVNode(a, { style: { "margin-left": "5px" }, onClick: (w) => r.handleRemoveFields(y), circle: "", plain: "", type: "danger", size: "default", icon: "el-icon-minus" }, null, 8, ["onClick"])])]), _: 1 }, 8, ["list"]), require$$0.createElementVNode("div", _hoisted_5$3, [require$$0.createVNode(a, { text: "", type: "primary", onClick: r.handleAddFields }, { default: require$$0.withCtx(() => t[15] || (t[15] = [require$$0.createTextVNode(" 添加列 ")])), _: 1, __: [15] }, 8, ["onClick"])])]), _: 1 }), require$$0.createVNode(u, { label: "远端数据", name: "remote" }, { default: require$$0.withCtx(() => [require$$0.createElementVNode("div", _hoisted_6$3, [t[16] || (t[16] = require$$0.createElementVNode("span", { style: { width: "90px" } }, "网址：", -1)), require$$0.createVNode(s, { type: "textarea", modelValue: o.data.dicUrl, "onUpdate:modelValue": t[1] || (t[1] = (v) => o.data.dicUrl = v), placeholder: "远端数据字典网址", autosize: "" }, null, 8, ["modelValue"])]), require$$0.createElementVNode("div", _hoisted_7$3, [t[17] || (t[17] = require$$0.createElementVNode("span", { style: { width: "80px" } }, "远程搜索：", -1)), require$$0.createVNode(c, { modelValue: o.data.remote, "onUpdate:modelValue": t[2] || (t[2] = (v) => o.data.remote = v) }, null, 8, ["modelValue"])]), require$$0.createElementVNode("div", _hoisted_8$2, [t[18] || (t[18] = require$$0.createElementVNode("span", { style: { width: "90px" } }, "请求方法：", -1)), require$$0.createVNode(m, { modelValue: o.data.dicMethod, "onUpdate:modelValue": t[3] || (t[3] = (v) => o.data.dicMethod = v), placeholder: "请求方法" }, { default: require$$0.withCtx(() => [require$$0.createVNode(f, { label: "POST", value: "post" }), require$$0.createVNode(f, { label: "GET", value: "get" })]), _: 1 }, 8, ["modelValue"])]), o.data.dicMethod == "post" ? (require$$0.openBlock(), require$$0.createElementBlock("p", _hoisted_9$2, [t[19] || (t[19] = require$$0.createTextVNode(" 请求参数 ")), require$$0.createVNode($, { modelValue: o.data.dicQueryConfig, "onUpdate:modelValue": t[4] || (t[4] = (v) => o.data.dicQueryConfig = v), children: r.option }, null, 8, ["modelValue", "children"])])) : require$$0.createCommentVNode("", true), o.data.dicUrl ? (require$$0.openBlock(), require$$0.createElementBlock("p", _hoisted_10$1, [t[20] || (t[20] = require$$0.createTextVNode(" 返回结构 ")), (require$$0.openBlock(), require$$0.createBlock(p, { key: `dict-formatter-${o.data.prop}`, modelValue: r.dicFormatter, "onUpdate:modelValue": t[5] || (t[5] = (v) => r.dicFormatter = v), border: "", style: { height: "80px" } }, null, 8, ["modelValue"]))])) : require$$0.createCommentVNode("", true)]), _: 1 })]), _: 1 }, 8, ["modelValue", "onTabClick"])])])]), require$$0.createElementVNode("div", _hoisted_11$1, [require$$0.createElementVNode("div", _hoisted_12$1, [t[22] || (t[22] = require$$0.createElementVNode("label", { class: "el-form-item__label", style: { padding: "0" } }, " 字典key配置： ", -1)), require$$0.createElementVNode("div", _hoisted_13$1, [require$$0.createElementVNode("ul", null, [(require$$0.openBlock(true), require$$0.createElementBlock(require$$0.Fragment, null, require$$0.renderList(o.data.props, (v, y) => (require$$0.openBlock(), require$$0.createElementBlock("li", { key: y, style: { "margin-bottom": "5px" } }, [require$$0.createElementVNode("span", _hoisted_14$1, require$$0.toDisplayString(y), 1), require$$0.createVNode(s, { modelValue: o.data.props[y], "onUpdate:modelValue": (w) => o.data.props[y] = w, clearable: "", placeholder: "key配置" }, null, 8, ["modelValue", "onUpdate:modelValue"])]))), 128))])])])]), o.data.type == "select" ? (require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, { key: 1 }, [require$$0.createVNode(n, { label: "是否多选" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: o.data.multiple, "onUpdate:modelValue": t[7] || (t[7] = (v) => o.data.multiple = v) }, null, 8, ["modelValue"])]), _: 1 }), o.data.multiple ? (require$$0.openBlock(), require$$0.createBlock(n, { key: 0, label: "多选数量限制", "label-width": "110px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(b, { modelValue: o.data.limit, "onUpdate:modelValue": t[8] || (t[8] = (v) => o.data.limit = v), "controls-position": "right", placeholder: "多选限制", min: 0, max: o.data.dicData.length }, null, 8, ["modelValue", "max"])]), _: 1 })) : require$$0.createCommentVNode("", true), require$$0.createElementVNode("div", _hoisted_15$1, [require$$0.createElementVNode("div", _hoisted_16$1, [t[24] || (t[24] = require$$0.createElementVNode("label", { class: "el-form-item__label", style: { padding: "0" } }, "级联配置：", -1)), require$$0.createElementVNode("div", _hoisted_17$1, [require$$0.createVNode(require$$0.unref(Draggable), { tag: "ul", list: o.data.cascader, group: { name: "cascader" }, "ghost-class": "ghost", "item-key": "id", handle: ".drag-item" }, { item: require$$0.withCtx(({ index: v }) => [require$$0.createElementVNode("li", _hoisted_18$1, [require$$0.createVNode(l, { size: 30 }, { default: require$$0.withCtx(() => [require$$0.createVNode(d, { class: "drag-item", style: { margin: "0 5px", cursor: "move" } })]), _: 1 }), require$$0.createVNode(s, { modelValue: o.data.cascader[v], "onUpdate:modelValue": (y) => o.data.cascader[v] = y, clearable: "", placeholder: "级联属性值" }, null, 8, ["modelValue", "onUpdate:modelValue"]), require$$0.createVNode(a, { style: { "margin-left": "5px" }, onClick: (y) => r.handleRemoveCascaderItemFields(v), circle: "", plain: "", type: "danger", size: "default", icon: "el-icon-minus" }, null, 8, ["onClick"])])]), _: 1 }, 8, ["list"]), require$$0.createElementVNode("div", _hoisted_19$1, [require$$0.createVNode(a, { text: "", type: "primary", onClick: r.handleAddCascaderItemFields }, { default: require$$0.withCtx(() => t[23] || (t[23] = [require$$0.createTextVNode(" 添加列 ")])), _: 1, __: [23] }, 8, ["onClick"])])])])]), o.data.cascader && o.data.cascader.length > 0 ? (require$$0.openBlock(), require$$0.createBlock(n, { key: 1, label: "级联默认选中", "label-width": "110px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(b, { modelValue: o.data.cascaderIndex, "onUpdate:modelValue": t[9] || (t[9] = (v) => o.data.cascaderIndex = v), "controls-position": "right", placeholder: "级联默认选中", min: 0 }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), require$$0.createVNode(n, { label: "是否可清空", "label-width": "110px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: o.data.clearable, "onUpdate:modelValue": t[10] || (t[10] = (v) => o.data.clearable = v) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(n, { label: "是否可搜索", "label-width": "110px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: o.data.filterable, "onUpdate:modelValue": t[11] || (t[11] = (v) => o.data.filterable = v) }, null, 8, ["modelValue"])]), _: 1 })], 64)) : require$$0.createCommentVNode("", true), ["radio", "checkbox"].includes(o.data.type) ? (require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, { key: 2 }, [o.data.button ? require$$0.createCommentVNode("", true) : (require$$0.openBlock(), require$$0.createBlock(n, { key: 0, label: "边框" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: o.data.border, "onUpdate:modelValue": t[12] || (t[12] = (v) => o.data.border = v) }, null, 8, ["modelValue"])]), _: 1 })), o.data.border ? require$$0.createCommentVNode("", true) : (require$$0.openBlock(), require$$0.createBlock(n, { key: 1, label: "按钮" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: o.data.button, "onUpdate:modelValue": t[13] || (t[13] = (v) => o.data.button = v) }, null, 8, ["modelValue"])]), _: 1 }))], 64)) : require$$0.createCommentVNode("", true), o.data.button || o.data.border ? (require$$0.openBlock(), require$$0.createBlock(n, { key: 3, label: "尺寸" }, { default: require$$0.withCtx(() => [require$$0.createVNode(q, { modelValue: o.data.size, "onUpdate:modelValue": t[14] || (t[14] = (v) => o.data.size = v) }, { default: require$$0.withCtx(() => [require$$0.createVNode(h, { label: "large", value: "large" }, { default: require$$0.withCtx(() => t[25] || (t[25] = [require$$0.createTextVNode("大")])), _: 1, __: [25] }), require$$0.createVNode(h, { label: "default", value: "default" }, { default: require$$0.withCtx(() => t[26] || (t[26] = [require$$0.createTextVNode("默认")])), _: 1, __: [26] }), require$$0.createVNode(h, { label: "small", value: "small" }, { default: require$$0.withCtx(() => t[27] || (t[27] = [require$$0.createTextVNode("小")])), _: 1, __: [27] })]), _: 1 }, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true)], 64);
        };
      } }), _hoisted_1$6 = { class: "el-form--label-top" }, _hoisted_2$6 = { class: "el-form-item" }, _hoisted_3$5 = { class: "el-form-item__content" }, _hoisted_4$4 = { class: "custom-tree-node" }, _hoisted_5$2 = { style: { "margin-left": "22px" } }, _hoisted_6$2 = { style: { display: "flex", "margin-top": "10px" } }, _hoisted_7$2 = { style: { display: "flex", "margin-top": "10px" } }, _hoisted_8$1 = { style: { display: "flex", "margin-top": "10px" } }, _hoisted_9$1 = { key: 0 }, _hoisted_10 = { key: 1 }, _hoisted_11 = { class: "el-form--label-top" }, _hoisted_12 = { class: "el-form-item" }, _hoisted_13 = { class: "el-form-item__content" }, _hoisted_14 = { style: { width: "70px" } }, _hoisted_15 = { class: "el-form--label-top" }, _hoisted_16 = { class: "el-form-item" }, _hoisted_17 = { class: "el-form-item__content" }, _hoisted_18 = { style: { "margin-bottom": "5px" } }, _hoisted_19 = { style: { "margin-left": "22px" } }, __default__ = { name: "config-tree", props: ["data"], data() {
        return { dialogForm: {}, dialogVisible: false, dialogRules: { label: { required: true, message: "请输入label" }, value: { required: true, message: "请输入value" } }, dialogStatus: "add", selectData: void 0, dialogInputType: "text", option: { column: [{ type: "input", prop: "key", label: "key" }, { type: "input", prop: "value", label: "value" }] }, options: { fullScreen: true, minimap: { enabled: false } }, dicFormatter: "" };
      }, watch: { "data.prop": { handler() {
        const { dicFormatter: o } = this.data;
        this.dicFormatter = o ? o + "" : `(res) => {\r
  return res.data\r
}`;
      }, immediate: true }, dicFormatter: { handler(o) {
        try {
          this.data.dicFormatter = Function('"use strict";return (' + o + ")")();
        } catch {
        }
      } } }, methods: { handleRemoveCascaderItemFields(o) {
        this.data.cascader.splice(o, 1);
      }, handleAddCascaderItemFields() {
        this.data.cascader.push("");
      }, handleTabClick({ paneName: o }) {
        o == "remote" && !this.data.dicQueryConfig && (this.data.dicQueryConfig = []);
      }, handleParentNodeAdd() {
        this.selectData = void 0, this.dialogStatus = "add", this.dialogVisible = true;
      }, handleNodeAdd(o) {
        this.selectData = o, this.dialogStatus = "add", this.dialogVisible = true;
      }, handleNodeRemove(o, r) {
        const t = o.parent, s = t.data.children || t.data, n = s.findIndex((d) => d.value === r.value);
        s.splice(n, 1);
      }, handleDialogAdd() {
        this.$refs.dialogForm.validate((o) => {
          if (o) {
            const { label: r, value: t } = this.dialogForm;
            if (this.$refs.tree.getNode(t)) this.$message.error("value重复");
            else {
              const n = this.selectData, d = { label: r, value: this.dialogInputType == "number" ? Number(t) : t };
              n ? (n.children || (n.children = []), n.children.push(d)) : this.data.dicData[this.data.dicData.length] = d, this.beforeClose();
            }
          }
        });
      }, beforeClose() {
        this.$refs.dialogForm.clearValidate(), this.dialogForm = {}, this.dialogVisible = false;
      } } }, _sfc_main$d = Object.assign(__default__, { setup(o) {
        return (r, t) => {
          const s = require$$0.resolveComponent("el-input"), n = require$$0.resolveComponent("el-form-item"), d = require$$0.resolveComponent("el-button"), l = require$$0.resolveComponent("el-tree"), a = require$$0.resolveComponent("el-tab-pane"), u = require$$0.resolveComponent("el-switch"), c = require$$0.resolveComponent("el-option"), f = require$$0.resolveComponent("el-select"), m = require$$0.resolveComponent("nf-dynamic"), $ = require$$0.resolveComponent("nf-codemirror"), p = require$$0.resolveComponent("el-tabs"), g = require$$0.resolveComponent("el-icon-operation"), b = require$$0.resolveComponent("el-icon"), h = require$$0.resolveComponent("el-form"), q = require$$0.resolveComponent("el-dialog");
          return require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, null, [require$$0.createVNode(n, { label: "占位内容" }, { default: require$$0.withCtx(() => [require$$0.createVNode(s, { modelValue: o.data.placeholder, "onUpdate:modelValue": t[0] || (t[0] = (v) => o.data.placeholder = v), clearable: "", placeholder: "占位内容" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createElementVNode("div", _hoisted_1$6, [require$$0.createElementVNode("div", _hoisted_2$6, [t[24] || (t[24] = require$$0.createElementVNode("label", { class: "el-form-item__label", style: { padding: "0" } }, "字典配置：", -1)), require$$0.createElementVNode("div", _hoisted_3$5, [require$$0.createVNode(p, { modelValue: o.data.dicOption, "onUpdate:modelValue": t[6] || (t[6] = (v) => o.data.dicOption = v), stretch: "", onTabClick: r.handleTabClick }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { label: "静态数据", name: "static" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { ref: "tree", data: o.data.dicData, "default-expand-all": "", draggable: "", "node-key": "value", "expand-on-click-node": false, style: { "margin-top": "10px" } }, { default: require$$0.withCtx(({ node: v, data: y }) => [require$$0.createElementVNode("span", _hoisted_4$4, [require$$0.createElementVNode("span", null, require$$0.toDisplayString(v.label), 1), require$$0.createElementVNode("span", null, [require$$0.createVNode(d, { text: "", type: "primary", icon: "el-icon-plus", onClick: (w) => r.handleNodeAdd(y) }, null, 8, ["onClick"]), require$$0.createVNode(d, { class: "danger", text: "", type: "primary", icon: "el-icon-delete", onClick: (w) => r.handleNodeRemove(v, y) }, null, 8, ["onClick"])])])]), _: 1 }, 8, ["data"]), require$$0.createElementVNode("div", _hoisted_5$2, [require$$0.createVNode(d, { text: "", type: "primary", onClick: r.handleParentNodeAdd }, { default: require$$0.withCtx(() => t[18] || (t[18] = [require$$0.createTextVNode(" 添加父级 ")])), _: 1, __: [18] }, 8, ["onClick"])])]), _: 1 }), require$$0.createVNode(a, { label: "远端数据", name: "remote" }, { default: require$$0.withCtx(() => [require$$0.createElementVNode("div", _hoisted_6$2, [t[19] || (t[19] = require$$0.createElementVNode("span", { style: { width: "90px" } }, "网址：", -1)), require$$0.createVNode(s, { type: "textarea", modelValue: o.data.dicUrl, "onUpdate:modelValue": t[1] || (t[1] = (v) => o.data.dicUrl = v), placeholder: "远端数据字典网址", autosize: "" }, null, 8, ["modelValue"])]), require$$0.createElementVNode("div", _hoisted_7$2, [t[20] || (t[20] = require$$0.createElementVNode("span", { style: { width: "80px" } }, "远程搜索：", -1)), require$$0.createVNode(u, { modelValue: o.data.remote, "onUpdate:modelValue": t[2] || (t[2] = (v) => o.data.remote = v) }, null, 8, ["modelValue"])]), require$$0.createElementVNode("div", _hoisted_8$1, [t[21] || (t[21] = require$$0.createElementVNode("span", { style: { width: "90px" } }, "请求方法：", -1)), require$$0.createVNode(f, { modelValue: o.data.dicMethod, "onUpdate:modelValue": t[3] || (t[3] = (v) => o.data.dicMethod = v), placeholder: "请求方法" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { label: "POST", value: "post" }), require$$0.createVNode(c, { label: "GET", value: "get" })]), _: 1 }, 8, ["modelValue"])]), o.data.dicMethod == "post" ? (require$$0.openBlock(), require$$0.createElementBlock("p", _hoisted_9$1, [t[22] || (t[22] = require$$0.createTextVNode(" 请求参数 ")), require$$0.createVNode(m, { modelValue: o.data.dicQueryConfig, "onUpdate:modelValue": t[4] || (t[4] = (v) => o.data.dicQueryConfig = v), children: r.option }, null, 8, ["modelValue", "children"])])) : require$$0.createCommentVNode("", true), o.data.dicUrl ? (require$$0.openBlock(), require$$0.createElementBlock("p", _hoisted_10, [t[23] || (t[23] = require$$0.createTextVNode(" 返回结构 ")), (require$$0.openBlock(), require$$0.createBlock($, { key: `dict-formatter-${o.data.prop}`, modelValue: r.dicFormatter, "onUpdate:modelValue": t[5] || (t[5] = (v) => r.dicFormatter = v), border: "", style: { height: "80px" } }, null, 8, ["modelValue"]))])) : require$$0.createCommentVNode("", true)]), _: 1 })]), _: 1 }, 8, ["modelValue", "onTabClick"])])])]), require$$0.createElementVNode("div", _hoisted_11, [require$$0.createElementVNode("div", _hoisted_12, [t[25] || (t[25] = require$$0.createElementVNode("label", { class: "el-form-item__label", style: { padding: "0" } }, " 字典key配置： ", -1)), require$$0.createElementVNode("div", _hoisted_13, [require$$0.createElementVNode("ul", null, [(require$$0.openBlock(true), require$$0.createElementBlock(require$$0.Fragment, null, require$$0.renderList(o.data.props, (v, y) => (require$$0.openBlock(), require$$0.createElementBlock("li", { key: y, style: { "margin-bottom": "5px" } }, [require$$0.createElementVNode("span", _hoisted_14, require$$0.toDisplayString(y), 1), require$$0.createVNode(s, { modelValue: o.data.props[y], "onUpdate:modelValue": (w) => o.data.props[y] = w, placeholder: "key配置" }, null, 8, ["modelValue", "onUpdate:modelValue"])]))), 128))])])])]), require$$0.createElementVNode("div", _hoisted_15, [require$$0.createElementVNode("div", _hoisted_16, [t[27] || (t[27] = require$$0.createElementVNode("label", { class: "el-form-item__label", style: { padding: "0" } }, "级联配置：", -1)), require$$0.createElementVNode("div", _hoisted_17, [require$$0.createVNode(require$$0.unref(Draggable), { tag: "ul", list: o.data.cascader, group: { name: "cascader" }, "ghost-class": "ghost", "item-key": "id", handle: ".drag-item" }, { item: require$$0.withCtx(({ index: v }) => [require$$0.createElementVNode("li", _hoisted_18, [require$$0.createVNode(b, { size: 30 }, { default: require$$0.withCtx(() => [require$$0.createVNode(g, { class: "drag-item", style: { margin: "0 5px", cursor: "move" } })]), _: 1 }), require$$0.createVNode(s, { modelValue: o.data.cascader[v], "onUpdate:modelValue": (y) => o.data.cascader[v] = y, clearable: "", placeholder: "级联属性值" }, null, 8, ["modelValue", "onUpdate:modelValue"]), require$$0.createVNode(d, { style: { "margin-left": "5px" }, onClick: (y) => r.handleRemoveCascaderItemFields(v), circle: "", plain: "", type: "danger", size: "default", icon: "el-icon-minus" }, null, 8, ["onClick"])])]), _: 1 }, 8, ["list"]), require$$0.createElementVNode("div", _hoisted_19, [require$$0.createVNode(d, { text: "", type: "primary", onClick: r.handleAddCascaderItemFields }, { default: require$$0.withCtx(() => t[26] || (t[26] = [require$$0.createTextVNode(" 添加列 ")])), _: 1, __: [26] }, 8, ["onClick"])])])])]), o.data.type == "tree" ? (require$$0.openBlock(), require$$0.createBlock(n, { key: 0, label: "当有子级时,是否可选择父级", "label-width": "200px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: o.data.parent, "onUpdate:modelValue": t[7] || (t[7] = (v) => o.data.parent = v) }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), o.data.type == "cascader" ? (require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, { key: 1 }, [require$$0.createVNode(n, { label: "选项分隔符", "label-width": "100px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(s, { modelValue: o.data.separator, "onUpdate:modelValue": t[8] || (t[8] = (v) => o.data.separator = v), clearable: "", placeholder: "选项分隔符" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(n, { label: "是否显示选中值的完整路径", "label-width": "200px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: o.data.showAllLevels, "onUpdate:modelValue": t[9] || (t[9] = (v) => o.data.showAllLevels = v) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(n, { label: "是否可搜索", "label-width": "100px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: o.data.filterable, "onUpdate:modelValue": t[10] || (t[10] = (v) => o.data.filterable = v) }, null, 8, ["modelValue"])]), _: 1 })], 64)) : require$$0.createCommentVNode("", true), require$$0.createVNode(n, { label: "是否多选" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: o.data.multiple, "onUpdate:modelValue": t[11] || (t[11] = (v) => o.data.multiple = v) }, null, 8, ["modelValue"])]), _: 1 }), o.data.type == "tree" && o.data.multiple ? (require$$0.openBlock(), require$$0.createBlock(n, { key: 2, label: "勾选时，父子不关联", "label-width": "160px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: o.data.checkStrictly, "onUpdate:modelValue": t[12] || (t[12] = (v) => o.data.checkStrictly = v) }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), require$$0.createVNode(q, { modelValue: r.dialogVisible, "onUpdate:modelValue": t[17] || (t[17] = (v) => r.dialogVisible = v), "before-close": r.beforeClose }, { footer: require$$0.withCtx(() => [require$$0.createVNode(d, { onClick: t[16] || (t[16] = (v) => r.dialogVisible = false) }, { default: require$$0.withCtx(() => t[28] || (t[28] = [require$$0.createTextVNode("取 消")])), _: 1, __: [28] }), r.dialogStatus == "add" ? (require$$0.openBlock(), require$$0.createBlock(d, { key: 0, type: "primary", onClick: r.handleDialogAdd }, { default: require$$0.withCtx(() => t[29] || (t[29] = [require$$0.createTextVNode(" 确 定 ")])), _: 1, __: [29] }, 8, ["onClick"])) : require$$0.createCommentVNode("", true)]), default: require$$0.withCtx(() => [require$$0.createVNode(h, { ref: "dialogForm", model: r.dialogForm, "label-width": "80px", rules: r.dialogRules }, { default: require$$0.withCtx(() => [require$$0.createVNode(n, { label: "label" }, { default: require$$0.withCtx(() => [require$$0.createVNode(s, { modelValue: r.dialogForm.label, "onUpdate:modelValue": t[13] || (t[13] = (v) => r.dialogForm.label = v), placeholder: "label" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(n, { label: "value" }, { default: require$$0.withCtx(() => [require$$0.createVNode(s, { modelValue: r.dialogForm.value, "onUpdate:modelValue": t[15] || (t[15] = (v) => r.dialogForm.value = v), placeholder: "value", type: r.dialogInputType }, { append: require$$0.withCtx(() => [require$$0.createVNode(f, { modelValue: r.dialogInputType, "onUpdate:modelValue": t[14] || (t[14] = (v) => r.dialogInputType = v), placeholder: "数据类型", style: { width: "100px" } }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { label: "String", value: "text" }), require$$0.createVNode(c, { label: "Number", value: "number" })]), _: 1 }, 8, ["modelValue"])]), _: 1 }, 8, ["modelValue", "type"])]), _: 1 })]), _: 1 }, 8, ["model", "rules"])]), _: 1 }, 8, ["modelValue", "before-close"])], 64);
        };
      } }), Tree = _export_sfc(_sfc_main$d, [["__scopeId", "data-v-78d6bafb"]]), _sfc_main$c = { name: "config-date", props: ["data"] };
      function _sfc_render$c(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("el-input"), a = require$$0.resolveComponent("el-form-item"), u = require$$0.resolveComponent("el-switch");
        return require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, null, [t.data.type.indexOf("range") != -1 ? (require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, { key: 0 }, [require$$0.createVNode(a, { label: "开始占位内容", "label-width": "110px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.startPlaceholder, "onUpdate:modelValue": r[0] || (r[0] = (c) => t.data.startPlaceholder = c), clearable: "", placeholder: "开始占位内容" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "结束占位内容", "label-width": "110px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.endPlaceholder, "onUpdate:modelValue": r[1] || (r[1] = (c) => t.data.endPlaceholder = c), clearable: "", placeholder: "结束占位内容" }, null, 8, ["modelValue"])]), _: 1 })], 64)) : (require$$0.openBlock(), require$$0.createBlock(a, { key: 1, label: "占位内容" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.placeholder, "onUpdate:modelValue": r[2] || (r[2] = (c) => t.data.placeholder = c), clearable: "", placeholder: "占位内容" }, null, 8, ["modelValue"])]), _: 1 })), require$$0.createVNode(a, { label: "显示格式化", "label-width": "100px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.format, "onUpdate:modelValue": r[3] || (r[3] = (c) => t.data.format = c), clearable: "", placeholder: "显示格式化" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "值格式化" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.valueFormat, "onUpdate:modelValue": r[4] || (r[4] = (c) => t.data.valueFormat = c), clearable: "", placeholder: "值格式化" }, null, 8, ["modelValue"])]), _: 1 }), ["timerange", "daterange", "datetimerange"].includes(t.data.type) ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 2, label: "取消范围联动", "label-width": "110px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.unlinkPanels, "onUpdate:modelValue": r[5] || (r[5] = (c) => t.data.unlinkPanels = c) }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true)], 64);
      }
      const Date$1 = _export_sfc(_sfc_main$c, [["render", _sfc_render$c]]), _sfc_main$b = { name: "config-upload", props: ["data"], data() {
        return { option: { column: [{ type: "input", prop: "key", label: "key" }, { type: "input", prop: "value", label: "value" }] } };
      }, watch: { "data.drag": function(o) {
        o && delete this.data.listType;
      }, "data.showCanvas"(o) {
        o ? (this.data.canvasOption = {}, this.data.accept = "image/*") : delete this.data.canvasOption;
      } } }, _hoisted_1$5 = { class: "el-form--label-top" }, _hoisted_2$5 = { class: "el-form-item" }, _hoisted_3$4 = { class: "el-form-item" }, _hoisted_4$3 = { class: "el-form-item__label", style: { padding: "0" } };
      function _sfc_render$b(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("el-input"), a = require$$0.resolveComponent("el-form-item"), u = require$$0.resolveComponent("el-switch"), c = require$$0.resolveComponent("el-option"), f = require$$0.resolveComponent("el-select"), m = require$$0.resolveComponent("nf-dynamic"), $ = require$$0.resolveComponent("el-icon-question-filled"), p = require$$0.resolveComponent("el-icon"), g = require$$0.resolveComponent("el-link"), b = require$$0.resolveComponent("el-input-number");
        return require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, null, [t.data.oss ? require$$0.createCommentVNode("", true) : (require$$0.openBlock(), require$$0.createBlock(a, { key: 0, label: "上传地址" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.action, "onUpdate:modelValue": r[0] || (r[0] = (h) => t.data.action = h), clearable: "", placeholder: "上传地址" }, null, 8, ["modelValue"])]), _: 1 })), t.data.oss ? require$$0.createCommentVNode("", true) : (require$$0.openBlock(), require$$0.createBlock(a, { key: 1, label: "接受类型" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.accept, "onUpdate:modelValue": r[1] || (r[1] = (h) => t.data.accept = h), clearable: "", placeholder: "接受文件类型，如：image/png,image/jpg" }, null, 8, ["modelValue"])]), _: 1 })), require$$0.createVNode(a, { label: "拖拽上传" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.drag, "onUpdate:modelValue": r[2] || (r[2] = (h) => t.data.drag = h) }, null, 8, ["modelValue"])]), _: 1 }), t.data.drag ? require$$0.createCommentVNode("", true) : (require$$0.openBlock(), require$$0.createBlock(a, { key: 2, label: "列表类型" }, { default: require$$0.withCtx(() => [require$$0.createVNode(f, { modelValue: t.data.listType, "onUpdate:modelValue": r[3] || (r[3] = (h) => t.data.listType = h), placeholder: "文件列表类型", clearable: "" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { label: "附件", value: "text" }), require$$0.createVNode(c, { label: "照片墙", value: "picture-card" }), require$$0.createVNode(c, { label: "头像", value: "picture-img" }), require$$0.createVNode(c, { label: "缩略图", value: "picture" })]), _: 1 }, 8, ["modelValue"])]), _: 1 })), require$$0.createElementVNode("div", _hoisted_1$5, [require$$0.createElementVNode("div", _hoisted_2$5, [r[16] || (r[16] = require$$0.createElementVNode("label", { class: "el-form-item__label", style: { padding: "0" } }, "参数设置：", -1)), require$$0.createElementVNode("div", null, [r[14] || (r[14] = require$$0.createElementVNode("h4", null, "请求头", -1)), require$$0.createVNode(m, { modelValue: t.data.headersConfig, "onUpdate:modelValue": r[4] || (r[4] = (h) => t.data.headersConfig = h), children: n.option }, null, 8, ["modelValue", "children"]), r[15] || (r[15] = require$$0.createElementVNode("h4", null, "请求体", -1)), require$$0.createVNode(m, { modelValue: t.data.dataConfig, "onUpdate:modelValue": r[5] || (r[5] = (h) => t.data.dataConfig = h), children: n.option }, null, 8, ["modelValue", "children"])])]), require$$0.createElementVNode("div", _hoisted_3$4, [require$$0.createElementVNode("label", _hoisted_4$3, [require$$0.createVNode(g, { underline: false, href: "https://avuejs.com/form/form-upload.html", target: "_blank" }, { default: require$$0.withCtx(() => [r[17] || (r[17] = require$$0.createTextVNode(" 上传参数设置 ")), require$$0.createVNode(p, null, { default: require$$0.withCtx(() => [require$$0.createVNode($)]), _: 1 })]), _: 1, __: [17] })]), require$$0.createElementVNode("div", null, [r[18] || (r[18] = require$$0.createElementVNode("h4", null, "返回结构体的图片地址(url)", -1)), require$$0.createVNode(l, { modelValue: t.data.propsHttp.url, "onUpdate:modelValue": r[6] || (r[6] = (h) => t.data.propsHttp.url = h), placeholder: "上传成功返回结构体的图片地址" }, null, 8, ["modelValue"]), r[19] || (r[19] = require$$0.createElementVNode("h4", null, "返回结构体的图片的名称(name)", -1)), require$$0.createVNode(l, { modelValue: t.data.propsHttp.name, "onUpdate:modelValue": r[7] || (r[7] = (h) => t.data.propsHttp.name = h), placeholder: "上传成功返回结构体的图片的姓名" }, null, 8, ["modelValue"]), r[20] || (r[20] = require$$0.createElementVNode("h4", null, "返回结构体的层次(res)", -1)), require$$0.createVNode(l, { modelValue: t.data.propsHttp.res, "onUpdate:modelValue": r[8] || (r[8] = (h) => t.data.propsHttp.res = h), placeholder: "返回结构体的层次" }, null, 8, ["modelValue"]), r[21] || (r[21] = require$$0.createElementVNode("h4", null, "文件名称", -1)), require$$0.createVNode(l, { modelValue: t.data.propsHttp.fileName, "onUpdate:modelValue": r[9] || (r[9] = (h) => t.data.propsHttp.fileName = h), placeholder: "文件名称，默认file" }, null, 8, ["modelValue"])])])]), require$$0.createVNode(a, { label: "显示上传列表", "label-width": "110px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.showFileList, "onUpdate:modelValue": r[10] || (r[10] = (h) => t.data.showFileList = h) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "文件大小" }, { default: require$$0.withCtx(() => [require$$0.createVNode(b, { modelValue: t.data.fileSize, "onUpdate:modelValue": r[11] || (r[11] = (h) => t.data.fileSize = h), "controls-position": "right", placeholder: "文件大小限制（KB）", min: 0, style: { width: "100%" } }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "多文件上传", "label-width": "100px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.multiple, "onUpdate:modelValue": r[12] || (r[12] = (h) => t.data.multiple = h) }, null, 8, ["modelValue"])]), _: 1 }), t.data.multiple ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 3, label: "数量限制", "label-width": "100px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(b, { modelValue: t.data.limit, "onUpdate:modelValue": r[13] || (r[13] = (h) => t.data.limit = h), "controls-position": "right", placeholder: "多文件上传数量限制", min: 1, style: { width: "100%" } }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true)], 64);
      }
      const Upload = _export_sfc(_sfc_main$b, [["render", _sfc_render$b], ["__scopeId", "data-v-4d83c9aa"]]), _sfc_main$a = { name: "config-ueditor", props: ["data"], watch: { "data.options.oss": function(o) {
        debugger;
        o == "ali" ? this.data.options.qiniu = {} : o == "qiniu" && (this.data.options.ali = {});
      } } }, _hoisted_1$4 = { class: "el-form--label-top" }, _hoisted_2$4 = { class: "el-form-item__content" };
      function _sfc_render$a(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("el-input"), a = require$$0.resolveComponent("el-form-item");
        return require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, null, [require$$0.createVNode(a, { label: "图片上传地址", "label-width": "110px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.options.action, "onUpdate:modelValue": r[0] || (r[0] = (u) => t.data.options.action = u), clearable: "", placeholder: "图片上传地址" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createElementVNode("div", _hoisted_1$4, [r[5] || (r[5] = require$$0.createElementVNode("label", { class: "el-form-item__label", style: { padding: "0" } }, "参数设置：", -1)), require$$0.createElementVNode("div", _hoisted_2$4, [r[3] || (r[3] = require$$0.createTextVNode(" 返回的数据结构层次 ")), require$$0.createVNode(l, { modelValue: t.data.options.props.res, "onUpdate:modelValue": r[1] || (r[1] = (u) => t.data.options.props.res = u), size: "default", placeholder: "返回的数据结构层次" }, null, 8, ["modelValue"]), r[4] || (r[4] = require$$0.createTextVNode(" 返回结构体图片地址字段 ")), require$$0.createVNode(l, { modelValue: t.data.options.props.url, "onUpdate:modelValue": r[2] || (r[2] = (u) => t.data.options.props.url = u), size: "default", placeholder: "返回结构体图片地址字段" }, null, 8, ["modelValue"])])])], 64);
      }
      const UEditor = _export_sfc(_sfc_main$a, [["render", _sfc_render$a]]), _sfc_main$9 = { name: "config-map", props: ["data"], watch: { map: { handler() {
        const { longitude: o, latitude: r, text: t } = this.map;
        o && r && t ? this.data.value = [o, r, t] : this.data.value = [];
      }, deep: true } }, data() {
        return { map: { longitude: 0, latitude: 0, text: "" } };
      } };
      function _sfc_render$9(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("el-input-number"), a = require$$0.resolveComponent("el-form-item"), u = require$$0.resolveComponent("el-input");
        return require$$0.openBlock(), require$$0.createBlock(a, { label: "默认值（以下三个属性全部填写时生效）", class: "nf-form--top", "label-width": "360px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { label: "经度(lng)", class: "nf-form--top", "label-width": "360px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: n.map.longitude, "onUpdate:modelValue": r[0] || (r[0] = (c) => n.map.longitude = c), placeholder: "经度", "controls-position": "right" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "纬度(lat)", class: "nf-form--top", "label-width": "360px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: n.map.latitude, "onUpdate:modelValue": r[1] || (r[1] = (c) => n.map.latitude = c), placeholder: "纬度", "controls-position": "right" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "显示文本", class: "nf-form--top", "label-width": "360px" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: n.map.text, "onUpdate:modelValue": r[2] || (r[2] = (c) => n.map.text = c), placeholder: "显示文本", "allow-clear": "" }, null, 8, ["modelValue"])]), _: 1 })]), _: 1 });
      }
      const Map = _export_sfc(_sfc_main$9, [["render", _sfc_render$9]]), _sfc_main$8 = { name: "config-group", props: ["data"] };
      function _sfc_render$8(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("el-switch"), a = require$$0.resolveComponent("el-form-item");
        return require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, null, [require$$0.createVNode(a, { label: "开启折叠" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.arrow, "onUpdate:modelValue": r[0] || (r[0] = (u) => t.data.arrow = u) }, null, 8, ["modelValue"])]), _: 1 }), t.data.arrow ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 0, label: "默认展开" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.collapse, "onUpdate:modelValue": r[1] || (r[1] = (u) => t.data.collapse = u) }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true)], 64);
      }
      const Group = _export_sfc(_sfc_main$8, [["render", _sfc_render$8]]), _sfc_main$7 = { name: "config-array", props: ["data"] };
      function _sfc_render$7(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("el-input"), a = require$$0.resolveComponent("el-form-item"), u = require$$0.resolveComponent("el-radio-button"), c = require$$0.resolveComponent("el-radio-group");
        return require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, null, [require$$0.createVNode(a, { label: "占位内容" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.placeholder, "onUpdate:modelValue": r[0] || (r[0] = (f) => t.data.placeholder = f), clearable: "", placeholder: "占位内容" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "尺寸" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: t.data.size, "onUpdate:modelValue": r[1] || (r[1] = (f) => t.data.size = f) }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { label: "large", value: "large" }, { default: require$$0.withCtx(() => r[2] || (r[2] = [require$$0.createTextVNode("大")])), _: 1, __: [2] }), require$$0.createVNode(u, { label: "default", value: "default" }, { default: require$$0.withCtx(() => r[3] || (r[3] = [require$$0.createTextVNode("默认")])), _: 1, __: [3] }), require$$0.createVNode(u, { label: "small", value: "small" }, { default: require$$0.withCtx(() => r[4] || (r[4] = [require$$0.createTextVNode("小")])), _: 1, __: [4] })]), _: 1 }, 8, ["modelValue"])]), _: 1 })], 64);
      }
      const Array$1 = _export_sfc(_sfc_main$7, [["render", _sfc_render$7]]), _sfc_main$6 = { name: "config-title", props: ["data"], watch: { "data.styles": { handler(o) {
        this.isUpdating || (this.styles = o || {});
      }, immediate: true }, styles(o) {
        if (o != "{}") try {
          this.isUpdating = true, this.data.styles = Function('"use strict";return (' + o + ")")(), this.$nextTick(() => {
            this.isUpdating = false;
          });
        } catch {
          this.isUpdating = false;
        }
      }, "data.value": { handler(o) {
        o && !this.data.textValue && (this.data.textValue = o);
      }, immediate: true } }, data() {
        return { options: { fullScreen: true, minimap: { enabled: false } }, styles: this.data.styles || {}, isUpdating: false };
      } }, _hoisted_1$3 = { class: "el-form--label-top" }, _hoisted_2$3 = { class: "el-form-item" }, _hoisted_3$3 = { class: "el-form-item__content" };
      function _sfc_render$6(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("el-input"), a = require$$0.resolveComponent("el-form-item"), u = require$$0.resolveComponent("nf-codemirror");
        return require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, null, [require$$0.createVNode(a, { label: "内容" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.textValue, "onUpdate:modelValue": r[0] || (r[0] = (c) => t.data.textValue = c), clearable: "", placeholder: "内容" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createElementVNode("div", _hoisted_1$3, [require$$0.createElementVNode("div", _hoisted_2$3, [r[2] || (r[2] = require$$0.createElementVNode("label", { class: "el-form-item__label", style: { padding: "0" } }, "Styles：", -1)), require$$0.createElementVNode("div", _hoisted_3$3, [(require$$0.openBlock(), require$$0.createBlock(u, { key: `event-styles-${t.data.prop}`, modelValue: n.styles, "onUpdate:modelValue": r[1] || (r[1] = (c) => n.styles = c), border: "", style: { height: "300px" } }, null, 8, ["modelValue"]))])])])], 64);
      }
      const Title = _export_sfc(_sfc_main$6, [["render", _sfc_render$6]]), _sfc_main$5 = { name: "config-event", props: ["data"], watch: { "data.prop": { handler() {
        const { change: o, click: r, focus: t, blur: s } = this.data;
        this.change = o ? o + "" : `({value}) => {\r
\r
}`, this.click = r ? r + "" : `({value}) => {\r
\r
}`, this.focus = t ? t + "" : `({value}) => {\r
\r
}`, this.blur = s ? s + "" : `({value}) => {\r
\r
}`;
      }, immediate: true } }, data() {
        return { change: "", click: "", focus: "", blur: "", options: { fullScreen: true, minimap: { enabled: false } } };
      }, mounted() {
        this.$nextTick(() => {
          ["change", "click", "focus", "blur"].forEach((r) => {
            this.$watch(`${r}`, (t) => {
              if (t != `({value}) => {\r
\r
}`) try {
                this.data[r] = Function('"use strict";return (' + t + ")")();
              } catch {
              }
            });
          });
        });
      } }, _hoisted_1$2 = { class: "el-form--label-top" }, _hoisted_2$2 = { class: "el-form-item" }, _hoisted_3$2 = { class: "el-form-item__content" }, _hoisted_4$2 = { class: "el-form-item" }, _hoisted_5$1 = { class: "el-form-item__content" }, _hoisted_6$1 = { class: "el-form-item" }, _hoisted_7$1 = { class: "el-form-item__content" }, _hoisted_8 = { class: "el-form-item" }, _hoisted_9 = { class: "el-form-item__content" };
      function _sfc_render$5(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("nf-codemirror");
        return require$$0.openBlock(), require$$0.createElementBlock("div", _hoisted_1$2, [require$$0.createElementVNode("div", _hoisted_2$2, [r[4] || (r[4] = require$$0.createElementVNode("label", { class: "el-form-item__label", style: { padding: "0" } }, "change：", -1)), require$$0.createElementVNode("div", _hoisted_3$2, [(require$$0.openBlock(), require$$0.createBlock(l, { key: `event-change-${t.data.prop}`, modelValue: n.change, "onUpdate:modelValue": r[0] || (r[0] = (a) => n.change = a), border: "", style: { height: "200px" } }, null, 8, ["modelValue"]))])]), require$$0.createElementVNode("div", _hoisted_4$2, [r[5] || (r[5] = require$$0.createElementVNode("label", { class: "el-form-item__label", style: { padding: "0" } }, "click：", -1)), require$$0.createElementVNode("div", _hoisted_5$1, [(require$$0.openBlock(), require$$0.createBlock(l, { key: `event-click-${t.data.prop}`, modelValue: n.click, "onUpdate:modelValue": r[1] || (r[1] = (a) => n.click = a), border: "", style: { height: "200px" } }, null, 8, ["modelValue"]))])]), require$$0.createElementVNode("div", _hoisted_6$1, [r[6] || (r[6] = require$$0.createElementVNode("label", { class: "el-form-item__label", style: { padding: "0" } }, "focus：", -1)), require$$0.createElementVNode("div", _hoisted_7$1, [(require$$0.openBlock(), require$$0.createBlock(l, { key: `event-focus-${t.data.prop}`, modelValue: n.focus, "onUpdate:modelValue": r[2] || (r[2] = (a) => n.focus = a), border: "", style: { height: "200px" } }, null, 8, ["modelValue"]))])]), require$$0.createElementVNode("div", _hoisted_8, [r[7] || (r[7] = require$$0.createElementVNode("label", { class: "el-form-item__label", style: { padding: "0" } }, "blur：", -1)), require$$0.createElementVNode("div", _hoisted_9, [(require$$0.openBlock(), require$$0.createBlock(l, { key: `event-blur-${t.data.prop}`, modelValue: n.blur, "onUpdate:modelValue": r[3] || (r[3] = (a) => n.blur = a), border: "", style: { height: "200px" } }, null, 8, ["modelValue"]))])])]);
      }
      const Event = _export_sfc(_sfc_main$5, [["render", _sfc_render$5]]), _sfc_main$4 = { name: "config-table-select", props: ["data"], watch: { "data.prop": { handler() {
        const { onLoad: o, formatter: r } = this.data;
        this.onLoad = o ? o + "" : "", this.formatter = r ? r + "" : "";
      }, immediate: true }, "data.children.props": { handler(o) {
        this.handleOnLoad(o);
      }, deep: true }, "data.children.props.lazy": { handler(o) {
        o && (this.data.children.props.needPage = false);
      } }, onLoad: { handler(o) {
        try {
          this.data.onLoad = Function('"use strict";return (' + o + ")")();
        } catch (r) {
          console.error(r);
        }
      } } }, data() {
        return { options: { fullScreen: true, minimap: { enabled: false } }, onLoad: "", formatter: "", activeName: "1" };
      }, methods: { handleColumn(o) {
        this.data.children.column = o;
      }, handleOnLoad(o) {
        const { url: r, method: t, needPage: s, currentPageKey: n, pageSizeKey: d, totalKey: l, recordsKey: a, resKey: u, auto: c } = o;
        if (!c) return;
        if (!r) {
          this.onLoad = "(res, cb) => { }";
          return;
        }
        const f = `({ page, value, data }, cb) => {
        if (!page) page = { currentPage: 1, pageSize: 10 };
        const { currentPage, pageSize } = page;
				const option = {
					url: '${r}',
					method: '${t}',
				};
        let params = { ...data, ${n || "current"}: currentPage, ${d || "page"}: pageSize };

        if ('${t || "get"}' == 'get') {
					option.params = params;
				} else {
					option.data = params;
				}

        this.$axios(option).then(res => {
          const response = this.getAsVal(res, '${u}');
          if (!response) this.$message.error('未查询到数据或者返回层级配置错误');

          let result = {};
          if (${s}) {
            const total = this.getAsVal(response, '${l}');
						result.total = total || 0;
          }
					const records = this.getAsVal(response, '${a}');
					result.data = records || [];

          cb(result);
        })
      }`;
        this.onLoad = f;
      } } }, _hoisted_1$1 = { class: "el-form--label-top" }, _hoisted_2$1 = { class: "el-form-item" }, _hoisted_3$1 = { class: "el-form-item__content" }, _hoisted_4$1 = { class: "el-form--label-top" }, _hoisted_5 = { class: "el-form-item" }, _hoisted_6 = { class: "el-form-item__label", style: { padding: "0" } }, _hoisted_7 = { class: "el-form-item__content" };
      function _sfc_render$4(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("el-switch"), a = require$$0.resolveComponent("el-form-item"), u = require$$0.resolveComponent("el-input"), c = require$$0.resolveComponent("el-radio-button"), f = require$$0.resolveComponent("el-radio-group"), m = require$$0.resolveComponent("nf-codemirror"), $ = require$$0.resolveComponent("el-tab-pane"), p = require$$0.resolveComponent("el-alert"), g = require$$0.resolveComponent("el-radio"), b = require$$0.resolveComponent("el-option"), h = require$$0.resolveComponent("el-select"), q = require$$0.resolveComponent("el-icon-question-filled"), v = require$$0.resolveComponent("el-icon"), y = require$$0.resolveComponent("el-tooltip"), w = require$$0.resolveComponent("el-link"), T = require$$0.resolveComponent("el-popover"), E = require$$0.resolveComponent("el-input-number"), P = require$$0.resolveComponent("el-tabs");
        return require$$0.openBlock(), require$$0.createBlock(P, { modelValue: n.activeName, "onUpdate:modelValue": r[29] || (r[29] = (V) => n.activeName = V), stretch: "" }, { default: require$$0.withCtx(() => [require$$0.createVNode($, { label: "基础", name: "1" }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { label: "多选" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.multiple, "onUpdate:modelValue": r[0] || (r[0] = (V) => t.data.multiple = V) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "显示label" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.props.label, "onUpdate:modelValue": r[1] || (r[1] = (V) => t.data.props.label = V), placeholder: "显示label", clearable: "" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "rowKey" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.props.value, "onUpdate:modelValue": r[2] || (r[2] = (V) => t.data.props.value = V), placeholder: "rowKey", clearable: "" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "尺寸" }, { default: require$$0.withCtx(() => [require$$0.createVNode(f, { modelValue: t.data.children.size, "onUpdate:modelValue": r[3] || (r[3] = (V) => t.data.children.size = V) }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { label: "large", value: "large" }, { default: require$$0.withCtx(() => r[30] || (r[30] = [require$$0.createTextVNode("大")])), _: 1, __: [30] }), require$$0.createVNode(c, { label: "default", value: "default" }, { default: require$$0.withCtx(() => r[31] || (r[31] = [require$$0.createTextVNode(" 默认 ")])), _: 1, __: [31] }), require$$0.createVNode(c, { label: "small", value: "small" }, { default: require$$0.withCtx(() => r[32] || (r[32] = [require$$0.createTextVNode("小")])), _: 1, __: [32] })]), _: 1 }, 8, ["modelValue"])]), _: 1 }), require$$0.createElementVNode("div", _hoisted_1$1, [require$$0.createElementVNode("div", _hoisted_2$1, [r[33] || (r[33] = require$$0.createElementVNode("label", { class: "el-form-item__label", style: { padding: "0" } }, " formatter(自定义label显示)： ", -1)), require$$0.createElementVNode("div", _hoisted_3$1, [(require$$0.openBlock(), require$$0.createBlock(m, { key: `table-formatter-${t.data.prop}`, modelValue: n.formatter, "onUpdate:modelValue": r[4] || (r[4] = (V) => n.formatter = V), border: "", style: { height: "200px" } }, null, 8, ["modelValue"]))])])])]), _: 1 }), require$$0.createVNode($, { label: "表格", name: "2" }, { default: require$$0.withCtx(() => [require$$0.createVNode(p, { title: "当返回的列表数据中含有children字段或hasChildren字段为true时会自动展示为树形表格。树形数据时请不要使用分页配置。", type: "success", closable: false }), require$$0.createVNode(a, { label: "边框" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.children.border, "onUpdate:modelValue": r[5] || (r[5] = (V) => t.data.children.border = V) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "序号" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.children.index, "onUpdate:modelValue": r[6] || (r[6] = (V) => t.data.children.index = V) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "对齐方式" }, { default: require$$0.withCtx(() => [require$$0.createVNode(f, { modelValue: t.data.children.align, "onUpdate:modelValue": r[7] || (r[7] = (V) => t.data.children.align = V) }, { default: require$$0.withCtx(() => [require$$0.createVNode(g, { label: "left", value: "left" }, { default: require$$0.withCtx(() => r[34] || (r[34] = [require$$0.createTextVNode("左")])), _: 1, __: [34] }), require$$0.createVNode(g, { label: "center", value: "center" }, { default: require$$0.withCtx(() => r[35] || (r[35] = [require$$0.createTextVNode("中")])), _: 1, __: [35] }), require$$0.createVNode(g, { label: "right", value: "right" }, { default: require$$0.withCtx(() => r[36] || (r[36] = [require$$0.createTextVNode("右")])), _: 1, __: [36] })]), _: 1 }, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "请求地址" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.children.props.url, "onUpdate:modelValue": r[8] || (r[8] = (V) => t.data.children.props.url = V), type: "textarea", placeholder: "请求地址", autosize: "" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "请求方法" }, { default: require$$0.withCtx(() => [require$$0.createVNode(h, { modelValue: t.data.children.props.method, "onUpdate:modelValue": r[9] || (r[9] = (V) => t.data.children.props.method = V), placeholder: "请求方法", clearable: "" }, { default: require$$0.withCtx(() => [require$$0.createVNode(b, { label: "GET", value: "get" }), require$$0.createVNode(b, { label: "POST", value: "post" })]), _: 1 }, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { labelWidth: "120px" }, { label: require$$0.withCtx(() => [require$$0.createVNode(y, { content: "多选时父子节点勾选是否不关联，因elp限制只在懒加载时有效。" }, { default: require$$0.withCtx(() => [require$$0.createElementVNode("span", null, [r[37] || (r[37] = require$$0.createTextVNode(" 父子不关联 ")), require$$0.createVNode(v, null, { default: require$$0.withCtx(() => [require$$0.createVNode(q)]), _: 1 })])]), _: 1 })]), default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.children.props.checkStrictly, "onUpdate:modelValue": r[10] || (r[10] = (V) => t.data.children.props.checkStrictly = V) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, null, { label: require$$0.withCtx(() => [require$$0.createVNode(y, { content: "若需要子节点懒加载勾选此项，分页将失效。" }, { default: require$$0.withCtx(() => [require$$0.createElementVNode("span", null, [r[38] || (r[38] = require$$0.createTextVNode(" 懒加载 ")), require$$0.createVNode(v, null, { default: require$$0.withCtx(() => [require$$0.createVNode(q)]), _: 1 })])]), _: 1 })]), default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.children.props.lazy, "onUpdate:modelValue": r[11] || (r[11] = (V) => t.data.children.props.lazy = V) }, null, 8, ["modelValue"])]), _: 1 }), t.data.children.props.lazy ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 0, label: "父级" }, { label: require$$0.withCtx(() => [require$$0.createVNode(y, { content: "懒加载节点点击展开时传的参数" }, { default: require$$0.withCtx(() => [require$$0.createElementVNode("span", null, [r[39] || (r[39] = require$$0.createTextVNode(" 父级 ")), require$$0.createVNode(v, null, { default: require$$0.withCtx(() => [require$$0.createVNode(q)]), _: 1 })])]), _: 1 })]), default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.children.props.parentKey, "onUpdate:modelValue": r[12] || (r[12] = (V) => t.data.children.props.parentKey = V), placeholder: "父级parentKey", clearable: "" }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), require$$0.createVNode(a, { label: "children" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.children.props.children, "onUpdate:modelValue": r[13] || (r[13] = (V) => t.data.children.props.children = V), placeholder: "children", clearable: "" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "hasChildren" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.children.props.hasChildren, "onUpdate:modelValue": r[14] || (r[14] = (V) => t.data.children.props.hasChildren = V), placeholder: "hasChildren", clearable: "" }, null, 8, ["modelValue"])]), _: 1 }), t.data.children.props.lazy ? require$$0.createCommentVNode("", true) : (require$$0.openBlock(), require$$0.createBlock(a, { key: 1, label: "分页" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.children.props.needPage, "onUpdate:modelValue": r[15] || (r[15] = (V) => t.data.children.props.needPage = V) }, null, 8, ["modelValue"])]), _: 1 })), t.data.children.props.needPage ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 2, label: "当前页" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.children.props.currentPageKey, "onUpdate:modelValue": r[16] || (r[16] = (V) => t.data.children.props.currentPageKey = V), placeholder: "当前页", clearable: "" }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), t.data.children.props.needPage ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 3, label: "每页条数" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.children.props.pageSizeKey, "onUpdate:modelValue": r[17] || (r[17] = (V) => t.data.children.props.pageSizeKey = V), placeholder: "每页条数", clearable: "" }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), t.data.children.props.needPage ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 4, label: "总条数" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.children.props.totalKey, "onUpdate:modelValue": r[18] || (r[18] = (V) => t.data.children.props.totalKey = V), placeholder: "总条数", clearable: "" }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), require$$0.createVNode(a, { label: "列表" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.children.props.recordsKey, "onUpdate:modelValue": r[19] || (r[19] = (V) => t.data.children.props.recordsKey = V), placeholder: "列表", clearable: "" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "返回层级" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.children.props.resKey, "onUpdate:modelValue": r[20] || (r[20] = (V) => t.data.children.props.resKey = V), placeholder: "返回层级", clearable: "" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "自动生成" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.children.props.auto, "onUpdate:modelValue": r[21] || (r[21] = (V) => t.data.children.props.auto = V) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createElementVNode("div", _hoisted_4$1, [require$$0.createElementVNode("div", _hoisted_5, [require$$0.createElementVNode("label", _hoisted_6, [require$$0.createVNode(T, { placement: "top-start", width: 200, trigger: "hover", content: "填写以上配置后会自动生成，若觉得生成的不对请自行修改或关闭自动生成。" }, { reference: require$$0.withCtx(() => [require$$0.createVNode(w, { href: "https://avuejs.com/form/form-input-table.html", underline: false, target: "_blank" }, { default: require$$0.withCtx(() => [r[40] || (r[40] = require$$0.createTextVNode(" onLoad： ")), require$$0.createVNode(v, null, { default: require$$0.withCtx(() => [require$$0.createVNode(q)]), _: 1 })]), _: 1, __: [40] })]), _: 1 })]), require$$0.createElementVNode("div", _hoisted_7, [(require$$0.openBlock(), require$$0.createBlock(m, { key: `table-onload-${t.data.prop}`, modelValue: n.onLoad, "onUpdate:modelValue": r[22] || (r[22] = (V) => n.onLoad = V), border: "", style: { height: "500px" } }, null, 8, ["modelValue"]))])])])]), _: 1 }), require$$0.createVNode($, { label: "搜索", name: "3" }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { label: "开启搜索" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.children.search, "onUpdate:modelValue": r[23] || (r[23] = (V) => t.data.children.search = V) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "尺寸" }, { default: require$$0.withCtx(() => [require$$0.createVNode(f, { modelValue: t.data.children.searchSize, "onUpdate:modelValue": r[24] || (r[24] = (V) => t.data.children.searchSize = V) }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { label: "large", value: "large" }, { default: require$$0.withCtx(() => r[41] || (r[41] = [require$$0.createTextVNode("大")])), _: 1, __: [41] }), require$$0.createVNode(c, { label: "default", value: "default" }, { default: require$$0.withCtx(() => r[42] || (r[42] = [require$$0.createTextVNode(" 默认 ")])), _: 1, __: [42] }), require$$0.createVNode(c, { label: "small", value: "small" }, { default: require$$0.withCtx(() => r[43] || (r[43] = [require$$0.createTextVNode("小")])), _: 1, __: [43] })]), _: 1 }, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "按钮栅格" }, { default: require$$0.withCtx(() => [require$$0.createVNode(E, { modelValue: t.data.children.searchMenuSpan, "onUpdate:modelValue": r[25] || (r[25] = (V) => t.data.children.searchMenuSpan = V) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "按钮收缩" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.children.searchIcon, "onUpdate:modelValue": r[26] || (r[26] = (V) => t.data.children.searchIcon = V) }, null, 8, ["modelValue"])]), _: 1 }), t.data.children.searchIcon ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 0, label: "收缩个数" }, { default: require$$0.withCtx(() => [require$$0.createVNode(E, { modelValue: t.data.children.searchIconIndex, "onUpdate:modelValue": r[27] || (r[27] = (V) => t.data.children.searchIconIndex = V) }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), require$$0.createVNode(a, { label: "label宽度" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.children.searchLabelWidth, "onUpdate:modelValue": r[28] || (r[28] = (V) => t.data.children.searchLabelWidth = V), placeholder: "90px", clearable: "" }, null, 8, ["modelValue"])]), _: 1 })]), _: 1 })]), _: 1 }, 8, ["modelValue"]);
      }
      const TableSelect = _export_sfc(_sfc_main$4, [["render", _sfc_render$4]]), _sfc_main$3 = { name: "config-table-select-v2", props: ["data"], watch: { "data.prop": { handler() {
        const { onLoad: o, formatter: r } = this.data;
        this.onLoad = o ? o + "" : "", this.formatter = r ? r + "" : "";
      }, immediate: true }, "data.children.props": { handler(o) {
        this.handleOnLoad(o);
      }, deep: true }, "data.children.props.lazy": { handler(o) {
        o && (this.data.children.props.needPage = false);
      } }, onLoad: { handler(o) {
        try {
          this.data.onLoad = Function('"use strict";return (' + o + ")")();
        } catch (r) {
          console.error(r);
        }
      } } }, data() {
        return { options: { fullScreen: true, minimap: { enabled: false } }, onLoad: "", formatter: "", activeName: "1" };
      }, methods: { handleColumn(o) {
        this.data.children.column = o;
      }, handleOnLoad(o) {
        const { url: r, method: t, needPage: s, currentPageKey: n, pageSizeKey: d, totalKey: l, recordsKey: a, resKey: u, auto: c } = o;
        if (!c) return;
        if (!r) {
          this.onLoad = "(res, cb) => { }";
          return;
        }
        const f = `({ page, value, data }, cb) => {
        if (!page) page = { currentPage: 1, pageSize: 10 };
        const { currentPage, pageSize } = page;
				const option = {
					url: '${r}',
					method: '${t}',
				};
        let params = { ...data, ${n || "current"}: currentPage, ${d || "page"}: pageSize };

        if ('${t || "get"}' == 'get') {
					option.params = params;
				} else {
					option.data = params;
				}

        this.$axios(option).then(res => {
          const response = this.getAsVal(res, '${u}');
          if (!response) this.$message.error('未查询到数据或者返回层级配置错误');

          let result = {};
          if (${s}) {
            const total = this.getAsVal(response, '${l}');
						result.total = total || 0;
          }
					const records = this.getAsVal(response, '${a}');
					result.data = records || [];

          cb(result);
        })
      }`;
        this.onLoad = f;
      } } }, _hoisted_1 = { class: "el-form--label-top" }, _hoisted_2 = { class: "el-form-item" }, _hoisted_3 = { class: "el-form-item__label", style: { padding: "0" } }, _hoisted_4 = { class: "el-form-item__content" };
      function _sfc_render$3(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("el-input"), a = require$$0.resolveComponent("el-form-item"), u = require$$0.resolveComponent("el-radio-button"), c = require$$0.resolveComponent("el-radio-group"), f = require$$0.resolveComponent("el-switch"), m = require$$0.resolveComponent("el-tab-pane"), $ = require$$0.resolveComponent("el-alert"), p = require$$0.resolveComponent("el-radio"), g = require$$0.resolveComponent("el-option"), b = require$$0.resolveComponent("el-select"), h = require$$0.resolveComponent("el-icon-question-filled"), q = require$$0.resolveComponent("el-icon"), v = require$$0.resolveComponent("el-tooltip"), y = require$$0.resolveComponent("el-link"), w = require$$0.resolveComponent("el-popover"), T = require$$0.resolveComponent("nf-codemirror"), E = require$$0.resolveComponent("el-input-number"), P = require$$0.resolveComponent("el-tabs");
        return require$$0.openBlock(), require$$0.createBlock(P, { modelValue: n.activeName, "onUpdate:modelValue": r[29] || (r[29] = (V) => n.activeName = V), stretch: "" }, { default: require$$0.withCtx(() => [require$$0.createVNode(m, { label: "基础", name: "1" }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { label: "按钮文本" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.children.btnText, "onUpdate:modelValue": r[0] || (r[0] = (V) => t.data.children.btnText = V), placeholder: "按钮文本，默认新 增", clearable: "" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "按钮位置" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: t.data.children.btnPosition, "onUpdate:modelValue": r[1] || (r[1] = (V) => t.data.children.btnPosition = V) }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { label: "左", value: "left" }, { default: require$$0.withCtx(() => r[30] || (r[30] = [require$$0.createTextVNode("左")])), _: 1, __: [30] }), require$$0.createVNode(u, { label: "右", value: "right" }, { default: require$$0.withCtx(() => r[31] || (r[31] = [require$$0.createTextVNode("右")])), _: 1, __: [31] })]), _: 1 }, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "多选" }, { default: require$$0.withCtx(() => [require$$0.createVNode(f, { modelValue: t.data.multiple, "onUpdate:modelValue": r[2] || (r[2] = (V) => t.data.multiple = V) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "rowKey" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.props.value, "onUpdate:modelValue": r[3] || (r[3] = (V) => t.data.props.value = V), placeholder: "rowKey，默认id", clearable: "" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "尺寸" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: t.data.children.size, "onUpdate:modelValue": r[4] || (r[4] = (V) => t.data.children.size = V) }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { label: "large", value: "large" }, { default: require$$0.withCtx(() => r[32] || (r[32] = [require$$0.createTextVNode("大")])), _: 1, __: [32] }), require$$0.createVNode(u, { label: "default", value: "default" }, { default: require$$0.withCtx(() => r[33] || (r[33] = [require$$0.createTextVNode(" 默认 ")])), _: 1, __: [33] }), require$$0.createVNode(u, { label: "small", value: "small" }, { default: require$$0.withCtx(() => r[34] || (r[34] = [require$$0.createTextVNode("小")])), _: 1, __: [34] })]), _: 1 }, 8, ["modelValue"])]), _: 1 })]), _: 1 }), require$$0.createVNode(m, { label: "表格", name: "2" }, { default: require$$0.withCtx(() => [require$$0.createVNode($, { title: "当返回的列表数据中含有children字段或hasChildren字段为true时会自动展示为树形表格。树形数据时请不要使用分页配置。", type: "success", closable: false }), require$$0.createVNode(a, { label: "边框" }, { default: require$$0.withCtx(() => [require$$0.createVNode(f, { modelValue: t.data.children.border, "onUpdate:modelValue": r[5] || (r[5] = (V) => t.data.children.border = V) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "序号" }, { default: require$$0.withCtx(() => [require$$0.createVNode(f, { modelValue: t.data.children.index, "onUpdate:modelValue": r[6] || (r[6] = (V) => t.data.children.index = V) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "对齐方式" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: t.data.children.align, "onUpdate:modelValue": r[7] || (r[7] = (V) => t.data.children.align = V) }, { default: require$$0.withCtx(() => [require$$0.createVNode(p, { label: "left", value: "left" }, { default: require$$0.withCtx(() => r[35] || (r[35] = [require$$0.createTextVNode("左")])), _: 1, __: [35] }), require$$0.createVNode(p, { label: "center", value: "center" }, { default: require$$0.withCtx(() => r[36] || (r[36] = [require$$0.createTextVNode("中")])), _: 1, __: [36] }), require$$0.createVNode(p, { label: "right", value: "right" }, { default: require$$0.withCtx(() => r[37] || (r[37] = [require$$0.createTextVNode("右")])), _: 1, __: [37] })]), _: 1 }, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "请求地址" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.children.props.url, "onUpdate:modelValue": r[8] || (r[8] = (V) => t.data.children.props.url = V), type: "textarea", placeholder: "请求地址", autosize: "" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "请求方法" }, { default: require$$0.withCtx(() => [require$$0.createVNode(b, { modelValue: t.data.children.props.method, "onUpdate:modelValue": r[9] || (r[9] = (V) => t.data.children.props.method = V), placeholder: "请求方法", clearable: "" }, { default: require$$0.withCtx(() => [require$$0.createVNode(g, { label: "GET", value: "get" }), require$$0.createVNode(g, { label: "POST", value: "post" })]), _: 1 }, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { labelWidth: "120px" }, { label: require$$0.withCtx(() => [require$$0.createVNode(v, { content: "多选时父子节点勾选是否不关联，因elp限制只在懒加载时有效。" }, { default: require$$0.withCtx(() => [require$$0.createElementVNode("span", null, [r[38] || (r[38] = require$$0.createTextVNode(" 父子不关联 ")), require$$0.createVNode(q, null, { default: require$$0.withCtx(() => [require$$0.createVNode(h)]), _: 1 })])]), _: 1 })]), default: require$$0.withCtx(() => [require$$0.createVNode(f, { modelValue: t.data.children.props.checkStrictly, "onUpdate:modelValue": r[10] || (r[10] = (V) => t.data.children.props.checkStrictly = V) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, null, { label: require$$0.withCtx(() => [require$$0.createVNode(v, { content: "若需要子节点懒加载勾选此项，分页将失效。" }, { default: require$$0.withCtx(() => [require$$0.createElementVNode("span", null, [r[39] || (r[39] = require$$0.createTextVNode(" 懒加载 ")), require$$0.createVNode(q, null, { default: require$$0.withCtx(() => [require$$0.createVNode(h)]), _: 1 })])]), _: 1 })]), default: require$$0.withCtx(() => [require$$0.createVNode(f, { modelValue: t.data.children.props.lazy, "onUpdate:modelValue": r[11] || (r[11] = (V) => t.data.children.props.lazy = V) }, null, 8, ["modelValue"])]), _: 1 }), t.data.children.props.lazy ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 0, label: "父级" }, { label: require$$0.withCtx(() => [require$$0.createVNode(v, { content: "懒加载节点点击展开时传的参数" }, { default: require$$0.withCtx(() => [require$$0.createElementVNode("span", null, [r[40] || (r[40] = require$$0.createTextVNode(" 父级 ")), require$$0.createVNode(q, null, { default: require$$0.withCtx(() => [require$$0.createVNode(h)]), _: 1 })])]), _: 1 })]), default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.children.props.parentKey, "onUpdate:modelValue": r[12] || (r[12] = (V) => t.data.children.props.parentKey = V), placeholder: "父级parentKey", clearable: "" }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), require$$0.createVNode(a, { label: "children" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.children.props.children, "onUpdate:modelValue": r[13] || (r[13] = (V) => t.data.children.props.children = V), placeholder: "children", clearable: "" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "hasChildren" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.children.props.hasChildren, "onUpdate:modelValue": r[14] || (r[14] = (V) => t.data.children.props.hasChildren = V), placeholder: "hasChildren", clearable: "" }, null, 8, ["modelValue"])]), _: 1 }), t.data.children.props.lazy ? require$$0.createCommentVNode("", true) : (require$$0.openBlock(), require$$0.createBlock(a, { key: 1, label: "分页" }, { default: require$$0.withCtx(() => [require$$0.createVNode(f, { modelValue: t.data.children.props.needPage, "onUpdate:modelValue": r[15] || (r[15] = (V) => t.data.children.props.needPage = V) }, null, 8, ["modelValue"])]), _: 1 })), t.data.children.props.needPage ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 2, label: "当前页" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.children.props.currentPageKey, "onUpdate:modelValue": r[16] || (r[16] = (V) => t.data.children.props.currentPageKey = V), placeholder: "当前页", clearable: "" }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), t.data.children.props.needPage ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 3, label: "每页条数" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.children.props.pageSizeKey, "onUpdate:modelValue": r[17] || (r[17] = (V) => t.data.children.props.pageSizeKey = V), placeholder: "每页条数", clearable: "" }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), t.data.children.props.needPage ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 4, label: "总条数" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.children.props.totalKey, "onUpdate:modelValue": r[18] || (r[18] = (V) => t.data.children.props.totalKey = V), placeholder: "总条数", clearable: "" }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), require$$0.createVNode(a, { label: "列表" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.children.props.recordsKey, "onUpdate:modelValue": r[19] || (r[19] = (V) => t.data.children.props.recordsKey = V), placeholder: "列表", clearable: "" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "返回层级" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.children.props.resKey, "onUpdate:modelValue": r[20] || (r[20] = (V) => t.data.children.props.resKey = V), placeholder: "返回层级", clearable: "" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "自动生成" }, { default: require$$0.withCtx(() => [require$$0.createVNode(f, { modelValue: t.data.children.props.auto, "onUpdate:modelValue": r[21] || (r[21] = (V) => t.data.children.props.auto = V) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createElementVNode("div", _hoisted_1, [require$$0.createElementVNode("div", _hoisted_2, [require$$0.createElementVNode("label", _hoisted_3, [require$$0.createVNode(w, { placement: "top-start", width: 200, trigger: "hover", content: "填写以上配置后会自动生成，若觉得生成的不对请自行修改或关闭自动生成。" }, { reference: require$$0.withCtx(() => [require$$0.createVNode(y, { href: "https://avuejs.com/form/form-input-table.html", underline: false, target: "_blank" }, { default: require$$0.withCtx(() => [r[41] || (r[41] = require$$0.createTextVNode(" onLoad： ")), require$$0.createVNode(q, null, { default: require$$0.withCtx(() => [require$$0.createVNode(h)]), _: 1 })]), _: 1, __: [41] })]), _: 1 })]), require$$0.createElementVNode("div", _hoisted_4, [(require$$0.openBlock(), require$$0.createBlock(T, { key: `table-onload-${t.data.prop}`, modelValue: n.onLoad, "onUpdate:modelValue": r[22] || (r[22] = (V) => n.onLoad = V), border: "", style: { height: "500px" } }, null, 8, ["modelValue"]))])])])]), _: 1 }), require$$0.createVNode(m, { label: "搜索", name: "3" }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { label: "开启搜索" }, { default: require$$0.withCtx(() => [require$$0.createVNode(f, { modelValue: t.data.children.search, "onUpdate:modelValue": r[23] || (r[23] = (V) => t.data.children.search = V) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "尺寸" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: t.data.children.searchSize, "onUpdate:modelValue": r[24] || (r[24] = (V) => t.data.children.searchSize = V) }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { label: "large", value: "large" }, { default: require$$0.withCtx(() => r[42] || (r[42] = [require$$0.createTextVNode("大")])), _: 1, __: [42] }), require$$0.createVNode(u, { label: "default", value: "default" }, { default: require$$0.withCtx(() => r[43] || (r[43] = [require$$0.createTextVNode(" 默认 ")])), _: 1, __: [43] }), require$$0.createVNode(u, { label: "small", value: "small" }, { default: require$$0.withCtx(() => r[44] || (r[44] = [require$$0.createTextVNode("小")])), _: 1, __: [44] })]), _: 1 }, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "按钮栅格" }, { default: require$$0.withCtx(() => [require$$0.createVNode(E, { modelValue: t.data.children.searchMenuSpan, "onUpdate:modelValue": r[25] || (r[25] = (V) => t.data.children.searchMenuSpan = V) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "按钮收缩" }, { default: require$$0.withCtx(() => [require$$0.createVNode(f, { modelValue: t.data.children.searchIcon, "onUpdate:modelValue": r[26] || (r[26] = (V) => t.data.children.searchIcon = V) }, null, 8, ["modelValue"])]), _: 1 }), t.data.children.searchIcon ? (require$$0.openBlock(), require$$0.createBlock(a, { key: 0, label: "收缩个数" }, { default: require$$0.withCtx(() => [require$$0.createVNode(E, { modelValue: t.data.children.searchIconIndex, "onUpdate:modelValue": r[27] || (r[27] = (V) => t.data.children.searchIconIndex = V) }, null, 8, ["modelValue"])]), _: 1 })) : require$$0.createCommentVNode("", true), require$$0.createVNode(a, { label: "label宽度" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.children.searchLabelWidth, "onUpdate:modelValue": r[28] || (r[28] = (V) => t.data.children.searchLabelWidth = V), placeholder: "90px", clearable: "" }, null, 8, ["modelValue"])]), _: 1 })]), _: 1 })]), _: 1 }, 8, ["modelValue"]);
      }
      const TableSelectV2 = _export_sfc(_sfc_main$3, [["render", _sfc_render$3]]), _sfc_main$2 = { name: "config-table", props: ["data"], data() {
        return { rows: 1, cols: 1 };
      }, created() {
        const { rows: o } = this.data;
        o && o.length > 0 ? (this.rows = o.length, this.cols = this.getCols) : (this.rows = 0, this.cols = 0);
      }, computed: { getCols() {
        const { rows: o } = this.data;
        if (o && o.length > 0) {
          const r = o[0].cols;
          let t = 0;
          return r.forEach((s) => {
            s.colspan == 0 ? t += 0 : t += s.colspan || 1;
          }), t;
        } else return 0;
      } }, methods: { handleRows() {
        const o = this.data.rows, r = this.getCols, { rows: t, cols: s } = this;
        if (t <= o.length) o.length = t;
        else for (let n = o.length; n < t; n++) {
          const d = [];
          for (let l = 0; l < r; l++) d.push({ column: [], colspan: 1, rowspan: 1 });
          o.push({ cols: d });
        }
        s <= r ? o.forEach((n) => {
          n.cols.length = s;
        }) : o.forEach((n) => {
          for (let d = r; d < s; d++) n.cols.push({ column: [], colspan: 1, rowspan: 1 });
        });
      } } };
      function _sfc_render$2(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("el-input-number"), a = require$$0.resolveComponent("el-form-item"), u = require$$0.resolveComponent("el-button");
        return require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, null, [require$$0.createVNode(a, { label: "行" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: n.rows, "onUpdate:modelValue": r[0] || (r[0] = (c) => n.rows = c), clearable: "", placeholder: "行", min: 1 }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "列" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: n.cols, "onUpdate:modelValue": r[1] || (r[1] = (c) => n.cols = c), clearable: "", placeholder: "列", min: 1 }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, null, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { type: "primary", onClick: d.handleRows }, { default: require$$0.withCtx(() => r[2] || (r[2] = [require$$0.createTextVNode("确 认")])), _: 1, __: [2] }, 8, ["onClick"])]), _: 1 })], 64);
      }
      const Table = _export_sfc(_sfc_main$2, [["render", _sfc_render$2]]), _sfc_main$1 = { name: "config-td", props: ["data"], watch: { "data.value": { handler(o) {
        o && !this.data.textValue && (this.data.textValue = o);
      }, immediate: true }, "data.style": { handler(o) {
        this.style = o || {};
      }, immediate: true }, style: { handler(o) {
        this.data.style = o;
      }, deep: true } }, data() {
        return { style: this.data.style || {} };
      } };
      function _sfc_render$1(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("el-option"), a = require$$0.resolveComponent("el-select"), u = require$$0.resolveComponent("el-form-item"), c = require$$0.resolveComponent("el-input"), f = require$$0.resolveComponent("el-color-picker");
        return require$$0.openBlock(), require$$0.createElementBlock(require$$0.Fragment, null, [require$$0.createVNode(u, { label: "文字位置" }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { modelValue: n.style.textAlign, "onUpdate:modelValue": r[0] || (r[0] = (m) => n.style.textAlign = m), clearable: "" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { label: "左", value: "left" }), require$$0.createVNode(l, { label: "右", value: "right" }), require$$0.createVNode(l, { label: "居中", value: "center" })]), _: 1 }, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(u, { label: "高度" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: n.style.height, "onUpdate:modelValue": r[1] || (r[1] = (m) => n.style.height = m), placeholder: "例: 100px", clearable: "" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(u, { label: "宽度" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: n.style.width, "onUpdate:modelValue": r[2] || (r[2] = (m) => n.style.width = m), placeholder: "例: 100px", clearable: "" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(u, { label: "边框" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: n.style.border, "onUpdate:modelValue": r[3] || (r[3] = (m) => n.style.border = m), placeholder: "例: 1px solid #xxx", clearable: "" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(u, { label: "内间距" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: n.style.padding, "onUpdate:modelValue": r[4] || (r[4] = (m) => n.style.padding = m), placeholder: "例: 10px", clearable: "" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(u, { label: "背景颜色" }, { default: require$$0.withCtx(() => [require$$0.createVNode(f, { modelValue: n.style.backgroundColor, "onUpdate:modelValue": r[5] || (r[5] = (m) => n.style.backgroundColor = m) }, null, 8, ["modelValue"])]), _: 1 })], 64);
      }
      const Td = _export_sfc(_sfc_main$1, [["render", _sfc_render$1]]), _sfc_main = { name: "config-sign", props: ["data"], data() {
        return { activeName: "1" };
      } };
      function _sfc_render(o, r, t, s, n, d) {
        const l = require$$0.resolveComponent("el-input"), a = require$$0.resolveComponent("el-form-item"), u = require$$0.resolveComponent("el-input-number"), c = require$$0.resolveComponent("el-color-picker"), f = require$$0.resolveComponent("el-tab-pane"), m = require$$0.resolveComponent("el-tabs");
        return require$$0.openBlock(), require$$0.createBlock(m, { modelValue: n.activeName, "onUpdate:modelValue": r[10] || (r[10] = ($) => n.activeName = $), stretch: "" }, { default: require$$0.withCtx(() => [require$$0.createVNode(f, { label: "签名", name: "1" }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { label: "签名文本" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.btnText, "onUpdate:modelValue": r[0] || (r[0] = ($) => t.data.btnText = $), placeholder: "签名" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "起点大小" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.options.dotSize, "onUpdate:modelValue": r[1] || (r[1] = ($) => t.data.options.dotSize = $), precision: 1, placeholder: "1.5" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "画笔最小" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.options.minWidth, "onUpdate:modelValue": r[2] || (r[2] = ($) => t.data.options.minWidth = $), precision: 1, placeholder: "0.5" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "画笔最大" }, { default: require$$0.withCtx(() => [require$$0.createVNode(u, { modelValue: t.data.options.maxWidth, "onUpdate:modelValue": r[3] || (r[3] = ($) => t.data.options.maxWidth = $), precision: 1, placeholder: "2.5" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "画笔颜色" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: t.data.options.penColor, "onUpdate:modelValue": r[4] || (r[4] = ($) => t.data.options.penColor = $) }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createVNode(a, { label: "背景颜色" }, { default: require$$0.withCtx(() => [require$$0.createVNode(c, { modelValue: t.data.options.backgroundColor, "onUpdate:modelValue": r[5] || (r[5] = ($) => t.data.options.backgroundColor = $) }, null, 8, ["modelValue"])]), _: 1 })]), _: 1 }), require$$0.createVNode(f, { label: "上传", name: "2" }, { default: require$$0.withCtx(() => [require$$0.createVNode(a, { label: "上传地址" }, { default: require$$0.withCtx(() => [require$$0.createVNode(l, { modelValue: t.data.action, "onUpdate:modelValue": r[6] || (r[6] = ($) => t.data.action = $), clearable: "", placeholder: "上传地址" }, null, 8, ["modelValue"])]), _: 1 }), require$$0.createElementVNode("div", null, [r[11] || (r[11] = require$$0.createElementVNode("h4", null, "返回结构体的层次(res)", -1)), require$$0.createVNode(l, { modelValue: t.data.propsHttp.res, "onUpdate:modelValue": r[7] || (r[7] = ($) => t.data.propsHttp.res = $), placeholder: "返回结构体的层次" }, null, 8, ["modelValue"]), r[12] || (r[12] = require$$0.createElementVNode("h4", null, "返回结构体的图片地址(url)", -1)), require$$0.createVNode(l, { modelValue: t.data.propsHttp.url, "onUpdate:modelValue": r[8] || (r[8] = ($) => t.data.propsHttp.url = $), placeholder: "上传成功返回结构体的图片地址" }, null, 8, ["modelValue"]), r[13] || (r[13] = require$$0.createElementVNode("h4", null, "文件名称", -1)), require$$0.createVNode(l, { modelValue: t.data.propsHttp.fileName, "onUpdate:modelValue": r[9] || (r[9] = ($) => t.data.propsHttp.fileName = $), placeholder: "文件名称，默认file" }, null, 8, ["modelValue"])])]), _: 1 })]), _: 1 }, 8, ["modelValue"]);
      }
      const Sign = _export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-58f6bf02"]]), components = [Form, Crud, Database, Custom, Input, InputTag, Textarea, Number$1, Dynamic, Switch, Rate, Slider, Color, _sfc_main$e, Tree, Date$1, UEditor, Upload, Map, Group, Array$1, Title, Event, TableSelect, TableSelectV2, Table, Td, Sign], Config$1 = { install(o) {
        this.installed || (this.installed = true, components.map((r) => {
          o.component("nfd-" + r.name, r);
        }));
      } }, name = "@saber/nf-form-design-elp", version = "1.5.7", author = "ssc <<EMAIL>>", type = "module", description = "Nutflow表单设计器 ElementPlus版本", main = "lib/nf-form-design-elp.js", files = ["lib"], engines = { node: "^18.0.0 || >=20.0.0" }, scripts = { build: "vite build", dev: "vite --host", lib: "vite build --mode lib", prettier: "prettier ./packages/**/*.{vue,js} --write" }, dependencies = { vuedraggable: "^4.1.0" }, devDependencies = { "@saber/nf-form-elp": "^1.5.7", "@so1ve/prettier-config": "^3.1.0", "@vitejs/plugin-vue": "^5.2.4", axios: "^1.10.0", "element-plus": "2.10.2", prettier: "^3.5.3", "rollup-plugin-visualizer": "^5.14.0", sass: "^1.89.2", vite: "^5.4.18", vue: "^3.5.16" }, Config = { name, version, author, type, description, main, files, engines, scripts, dependencies, devDependencies }, index = { install(o) {
        o.use(Config$1), o.component("nf-form-design", NfFormDesign), o.config.globalProperties.getAsVal = getAsVal;
      }, author: "ssc", email: "<EMAIL>", version: Config.version };
      return index;
    });
  }
});
export default require_nf_form_design_elp();
/*! Bundled license information:

@saber/nf-form-design-elp/lib/nf-form-design-elp.js:
  (**!
  * Sortable 1.14.0
  * <AUTHOR>   <<EMAIL>>
  * <AUTHOR>    <<EMAIL>>
  * @license MIT
  *)
*/
//# sourceMappingURL=@saber_nf-form-design-elp.js.map
