{"version": 3, "sources": ["../../avue-plugin-ueditor/packages/ueditor/index.js", "../../avue-plugin-ueditor/packages/index.js"], "sourcesContent": ["import Ueditor from './src/index.vue';\nHTMLElement.prototype.appendHTML = function (html) {\n  var divTemp = document.createElement(\"div\"), nodes = null\n    // 文档片段，一次性append，提高性能\n    , fragment = document.createDocumentFragment();\n  divTemp.innerHTML = html;\n  nodes = divTemp.childNodes;\n  for (var i = 0, length = nodes.length; i < length; i += 1) {\n    fragment.appendChild(nodes[i].cloneNode(true));\n  }\n  this.appendChild(fragment);\n  // 据说下面这样子世界会更清净\n  nodes = null;\n  fragment = null;\n};\nfunction hasClass (obj, cls) {\n  return obj.className.match(new RegExp('(\\\\s|^)' + cls + '(\\\\s|$)'));\n}\n\nfunction addClass (obj, cls) {\n  if (!hasClass(obj, cls)) obj.className += \" \" + cls;\n}\n\nfunction removeClass (obj, cls) {\n  if (hasClass(obj, cls)) {\n    var reg = new RegExp('(\\\\s|^)' + cls + '(\\\\s|$)');\n    obj.className = obj.className.replace(reg, ' ');\n  }\n}\n\nHTMLElement.prototype.toggleClass = function (cls) {\n  if (hasClass(this, cls)) {\n    removeClass(this, cls);\n  } else {\n    addClass(this, cls);\n  }\n}\nUeditor.install = function (Vue) {\n  Vue.component(Ueditor.name, Ueditor);\n};\n\nexport default Ueditor;", "import Ueditor from './ueditor/index.js';\nconst components = [\n  Ueditor\n];\n\nfunction install (Vue, opt = {}) {\n  Vue.config.globalProperties.$axios = opt.axios || window.axios;\n  components.map(component => {\n    Vue.component(component.name, component);\n  });\n}\n\n\nexport default install;"], "mappings": ";;;AAAA,OAAO,aAAa;AACpB,YAAY,UAAU,aAAa,SAAU,MAAM;AACjD,MAAI,UAAU,SAAS,cAAc,KAAK,GAAG,QAAQ,MAEjD,WAAW,SAAS,uBAAuB;AAC/C,UAAQ,YAAY;AACpB,UAAQ,QAAQ;AAChB,WAAS,IAAI,GAAG,SAAS,MAAM,QAAQ,IAAI,QAAQ,KAAK,GAAG;AACzD,aAAS,YAAY,MAAM,CAAC,EAAE,UAAU,IAAI,CAAC;AAAA,EAC/C;AACA,OAAK,YAAY,QAAQ;AAEzB,UAAQ;AACR,aAAW;AACb;AACA,SAAS,SAAU,KAAK,KAAK;AAC3B,SAAO,IAAI,UAAU,MAAM,IAAI,OAAO,YAAY,MAAM,SAAS,CAAC;AACpE;AAEA,SAAS,SAAU,KAAK,KAAK;AAC3B,MAAI,CAAC,SAAS,KAAK,GAAG,EAAG,KAAI,aAAa,MAAM;AAClD;AAEA,SAAS,YAAa,KAAK,KAAK;AAC9B,MAAI,SAAS,KAAK,GAAG,GAAG;AACtB,QAAI,MAAM,IAAI,OAAO,YAAY,MAAM,SAAS;AAChD,QAAI,YAAY,IAAI,UAAU,QAAQ,KAAK,GAAG;AAAA,EAChD;AACF;AAEA,YAAY,UAAU,cAAc,SAAU,KAAK;AACjD,MAAI,SAAS,MAAM,GAAG,GAAG;AACvB,gBAAY,MAAM,GAAG;AAAA,EACvB,OAAO;AACL,aAAS,MAAM,GAAG;AAAA,EACpB;AACF;AACA,QAAQ,UAAU,SAAU,KAAK;AAC/B,MAAI,UAAU,QAAQ,MAAM,OAAO;AACrC;AAEA,IAAO,kBAAQ;;;ACxCf,IAAM,aAAa;AAAA,EACjB;AACF;AAEA,SAAS,QAAS,KAAK,MAAM,CAAC,GAAG;AAC/B,MAAI,OAAO,iBAAiB,SAAS,IAAI,SAAS,OAAO;AACzD,aAAW,IAAI,eAAa;AAC1B,QAAI,UAAU,UAAU,MAAM,SAAS;AAAA,EACzC,CAAC;AACH;AAGA,IAAO,mBAAQ;", "names": []}