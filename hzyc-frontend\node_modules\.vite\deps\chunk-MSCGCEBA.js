import {
  require_vue
} from "./chunk-CZCGTF2U.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/vue-demi/lib/index.cjs
var require_lib = __commonJS({
  "node_modules/vue-demi/lib/index.cjs"(exports) {
    var Vue = require_vue();
    Object.keys(Vue).forEach(function(key) {
      exports[key] = Vue[key];
    });
    exports.set = function(target, key, val) {
      if (Array.isArray(target)) {
        target.length = Math.max(target.length, key);
        target.splice(key, 1, val);
        return val;
      }
      target[key] = val;
      return val;
    };
    exports.del = function(target, key) {
      if (Array.isArray(target)) {
        target.splice(key, 1);
        return;
      }
      delete target[key];
    };
    exports.Vue = Vue;
    exports.Vue2 = void 0;
    exports.isVue2 = false;
    exports.isVue3 = true;
    exports.install = function() {
    };
  }
});

export {
  require_lib
};
//# sourceMappingURL=chunk-MSCGCEBA.js.map
