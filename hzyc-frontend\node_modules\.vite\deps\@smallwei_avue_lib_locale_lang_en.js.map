{"version": 3, "sources": ["../../@smallwei/avue/lib/locale/lang/en.js"], "sourcesContent": ["export default {\r\n  common: {\r\n    submitBtn: 'Submit',\r\n    cancelBtn: 'Cancel',\r\n    condition: 'Condition',\r\n    display: 'Display',\r\n    hide: 'Hide'\r\n  },\r\n  tip: {\r\n    select: 'Please select',\r\n    input: 'Please input'\r\n  },\r\n  check: {\r\n    checkAll: 'Select All'\r\n  },\r\n  upload: {\r\n    upload: 'Click to Upload',\r\n    tip: 'Drop files here, or'\r\n  },\r\n  time: {\r\n    start: 'Start',\r\n    end: 'End'\r\n  },\r\n  date: {\r\n    start: 'Start Date',\r\n    end: 'End Date',\r\n    t: 'Today',\r\n    y: 'Yesterday',\r\n    n: 'Last 7 days',\r\n    a: 'All'\r\n  },\r\n  form: {\r\n    printBtn: 'Print',\r\n    mockBtn: 'Mock',\r\n    submitBtn: 'Submit',\r\n    emptyBtn: 'Empty'\r\n  },\r\n  crud: {\r\n    excel: {\r\n      name: 'File Name',\r\n      type: 'Data',\r\n      typeDic: {\r\n        true: 'Data on this page (all of the data on this page)',\r\n        false: 'Selected Data (the selected data on this page)'\r\n      },\r\n      prop: 'Field',\r\n      params: 'Parameters',\r\n      paramsDic: {\r\n        header: 'Table Header',\r\n        data: 'Data Source',\r\n        headers: 'Complex Table Header',\r\n        sum: 'Total'\r\n      }\r\n    },\r\n    filter: {\r\n      addBtn: 'Add',\r\n      clearBtn: 'Clear',\r\n      resetBtn: 'Reset',\r\n      cancelBtn: 'Cancel',\r\n      submitBtn: 'Submit'\r\n    },\r\n    column: {\r\n      name: 'Name',\r\n      hide: 'Hide',\r\n      fixed: 'Fixed',\r\n      filters: 'Filter',\r\n      sortable: 'Sort',\r\n      index: 'Index',\r\n      width: 'Width'\r\n    },\r\n    emptyText: 'No Data',\r\n    tipStartTitle: 'Selected',\r\n    tipEndTitle: 'Items　',\r\n    editTitle: 'Edit',\r\n    copyTitle: 'Copy',\r\n    addTitle: 'Add',\r\n    viewTitle: 'View',\r\n    filterTitle: 'Filter Conditions',\r\n    showTitle: 'Column Display',\r\n    menu: 'Menu',\r\n    addBtn: 'Add',\r\n    show: 'Show',\r\n    hide: 'Hide',\r\n    open: 'Open',\r\n    shrink: 'Shrink',\r\n    printBtn: 'Print',\r\n    mockBtn: 'Mock',\r\n    excelBtn: 'Export',\r\n    updateBtn: 'Update',\r\n    cancelBtn: 'Cancel',\r\n    searchBtn: 'Search',\r\n    emptyBtn: 'Empty',\r\n    menuBtn: 'Menu',\r\n    saveBtn: 'Save',\r\n    viewBtn: 'View',\r\n    editBtn: 'Edit',\r\n    copyBtn: 'Copy',\r\n    delBtn: 'Delete'\r\n  }\r\n};\r\n"], "mappings": ";;;AAAA,IAAO,aAAQ;AAAA,EACb,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,WAAW;AAAA,IACX,SAAS;AAAA,IACT,MAAM;AAAA,EACR;AAAA,EACA,KAAK;AAAA,IACH,QAAQ;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,UAAU;AAAA,EACZ;AAAA,EACA,QAAQ;AAAA,IACN,QAAQ;AAAA,IACR,KAAK;AAAA,EACP;AAAA,EACA,MAAM;AAAA,IACJ,OAAO;AAAA,IACP,KAAK;AAAA,EACP;AAAA,EACA,MAAM;AAAA,IACJ,OAAO;AAAA,IACP,KAAK;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AAAA,EACA,MAAM;AAAA,IACJ,UAAU;AAAA,IACV,SAAS;AAAA,IACT,WAAW;AAAA,IACX,UAAU;AAAA,EACZ;AAAA,EACA,MAAM;AAAA,IACJ,OAAO;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,MACA,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,WAAW;AAAA,QACT,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,SAAS;AAAA,QACT,KAAK;AAAA,MACP;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW;AAAA,MACX,WAAW;AAAA,IACb;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,IACX,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU;AAAA,IACV,WAAW;AAAA,IACX,aAAa;AAAA,IACb,WAAW;AAAA,IACX,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,SAAS;AAAA,IACT,UAAU;AAAA,IACV,WAAW;AAAA,IACX,WAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU;AAAA,IACV,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AACF;", "names": []}