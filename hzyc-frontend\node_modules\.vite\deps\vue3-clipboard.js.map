{"version": 3, "sources": ["../../vue3-clipboard/dist/vue3-clipboard-es.js"], "sourcesContent": ["function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nvar commonjsGlobal = typeof globalThis !== 'undefined' ? globalThis : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};\n\nfunction unwrapExports (x) {\n\treturn x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;\n}\n\nfunction createCommonjsModule(fn, module) {\n\treturn module = { exports: {} }, fn(module, module.exports), module.exports;\n}\n\nvar clipboard_min = createCommonjsModule(function (module, exports) {\n  /*!\n   * clipboard.js v2.0.6\n   * https://clipboardjs.com/\n   * \n   * Licensed MIT © Zeno Rocha\n   */\n  !function (t, e) {\n     module.exports = e() ;\n  }(commonjsGlobal, function () {\n    return o = {}, r.m = n = [function (t, e) {\n      t.exports = function (t) {\n        var e;\n        if (\"SELECT\" === t.nodeName) t.focus(), e = t.value;else if (\"INPUT\" === t.nodeName || \"TEXTAREA\" === t.nodeName) {\n          var n = t.hasAttribute(\"readonly\");\n          n || t.setAttribute(\"readonly\", \"\"), t.select(), t.setSelectionRange(0, t.value.length), n || t.removeAttribute(\"readonly\"), e = t.value;\n        } else {\n          t.hasAttribute(\"contenteditable\") && t.focus();\n          var o = window.getSelection(),\n              r = document.createRange();\n          r.selectNodeContents(t), o.removeAllRanges(), o.addRange(r), e = o.toString();\n        }\n        return e;\n      };\n    }, function (t, e) {\n      function n() {}\n\n      n.prototype = {\n        on: function (t, e, n) {\n          var o = this.e || (this.e = {});\n          return (o[t] || (o[t] = [])).push({\n            fn: e,\n            ctx: n\n          }), this;\n        },\n        once: function (t, e, n) {\n          var o = this;\n\n          function r() {\n            o.off(t, r), e.apply(n, arguments);\n          }\n\n          return r._ = e, this.on(t, r, n);\n        },\n        emit: function (t) {\n          for (var e = [].slice.call(arguments, 1), n = ((this.e || (this.e = {}))[t] || []).slice(), o = 0, r = n.length; o < r; o++) n[o].fn.apply(n[o].ctx, e);\n\n          return this;\n        },\n        off: function (t, e) {\n          var n = this.e || (this.e = {}),\n              o = n[t],\n              r = [];\n          if (o && e) for (var i = 0, a = o.length; i < a; i++) o[i].fn !== e && o[i].fn._ !== e && r.push(o[i]);\n          return r.length ? n[t] = r : delete n[t], this;\n        }\n      }, t.exports = n, t.exports.TinyEmitter = n;\n    }, function (t, e, n) {\n      var d = n(3),\n          h = n(4);\n\n      t.exports = function (t, e, n) {\n        if (!t && !e && !n) throw new Error(\"Missing required arguments\");\n        if (!d.string(e)) throw new TypeError(\"Second argument must be a String\");\n        if (!d.fn(n)) throw new TypeError(\"Third argument must be a Function\");\n        if (d.node(t)) return s = e, f = n, (u = t).addEventListener(s, f), {\n          destroy: function () {\n            u.removeEventListener(s, f);\n          }\n        };\n        if (d.nodeList(t)) return a = t, c = e, l = n, Array.prototype.forEach.call(a, function (t) {\n          t.addEventListener(c, l);\n        }), {\n          destroy: function () {\n            Array.prototype.forEach.call(a, function (t) {\n              t.removeEventListener(c, l);\n            });\n          }\n        };\n        if (d.string(t)) return o = t, r = e, i = n, h(document.body, o, r, i);\n        throw new TypeError(\"First argument must be a String, HTMLElement, HTMLCollection, or NodeList\");\n        var o, r, i, a, c, l, u, s, f;\n      };\n    }, function (t, n) {\n      n.node = function (t) {\n        return void 0 !== t && t instanceof HTMLElement && 1 === t.nodeType;\n      }, n.nodeList = function (t) {\n        var e = Object.prototype.toString.call(t);\n        return void 0 !== t && (\"[object NodeList]\" === e || \"[object HTMLCollection]\" === e) && \"length\" in t && (0 === t.length || n.node(t[0]));\n      }, n.string = function (t) {\n        return \"string\" == typeof t || t instanceof String;\n      }, n.fn = function (t) {\n        return \"[object Function]\" === Object.prototype.toString.call(t);\n      };\n    }, function (t, e, n) {\n      var a = n(5);\n\n      function i(t, e, n, o, r) {\n        var i = function (e, n, t, o) {\n          return function (t) {\n            t.delegateTarget = a(t.target, n), t.delegateTarget && o.call(e, t);\n          };\n        }.apply(this, arguments);\n\n        return t.addEventListener(n, i, r), {\n          destroy: function () {\n            t.removeEventListener(n, i, r);\n          }\n        };\n      }\n\n      t.exports = function (t, e, n, o, r) {\n        return \"function\" == typeof t.addEventListener ? i.apply(null, arguments) : \"function\" == typeof n ? i.bind(null, document).apply(null, arguments) : (\"string\" == typeof t && (t = document.querySelectorAll(t)), Array.prototype.map.call(t, function (t) {\n          return i(t, e, n, o, r);\n        }));\n      };\n    }, function (t, e) {\n      if (\"undefined\" != typeof Element && !Element.prototype.matches) {\n        var n = Element.prototype;\n        n.matches = n.matchesSelector || n.mozMatchesSelector || n.msMatchesSelector || n.oMatchesSelector || n.webkitMatchesSelector;\n      }\n\n      t.exports = function (t, e) {\n        for (; t && 9 !== t.nodeType;) {\n          if (\"function\" == typeof t.matches && t.matches(e)) return t;\n          t = t.parentNode;\n        }\n      };\n    }, function (t, e, n) {\n\n      n.r(e);\n      var o = n(0),\n          r = n.n(o),\n          i = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (t) {\n        return typeof t;\n      } : function (t) {\n        return t && \"function\" == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? \"symbol\" : typeof t;\n      };\n\n      function a(t, e) {\n        for (var n = 0; n < e.length; n++) {\n          var o = e[n];\n          o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(t, o.key, o);\n        }\n      }\n\n      function c(t) {\n        !function (t, e) {\n          if (!(t instanceof e)) throw new TypeError(\"Cannot call a class as a function\");\n        }(this, c), this.resolveOptions(t), this.initSelection();\n      }\n\n      var l = (function (t, e, n) {\n        return e && a(t.prototype, e), n && a(t, n), t;\n      }(c, [{\n        key: \"resolveOptions\",\n        value: function (t) {\n          var e = 0 < arguments.length && void 0 !== t ? t : {};\n          this.action = e.action, this.container = e.container, this.emitter = e.emitter, this.target = e.target, this.text = e.text, this.trigger = e.trigger, this.selectedText = \"\";\n        }\n      }, {\n        key: \"initSelection\",\n        value: function () {\n          this.text ? this.selectFake() : this.target && this.selectTarget();\n        }\n      }, {\n        key: \"selectFake\",\n        value: function () {\n          var t = this,\n              e = \"rtl\" == document.documentElement.getAttribute(\"dir\");\n          this.removeFake(), this.fakeHandlerCallback = function () {\n            return t.removeFake();\n          }, this.fakeHandler = this.container.addEventListener(\"click\", this.fakeHandlerCallback) || !0, this.fakeElem = document.createElement(\"textarea\"), this.fakeElem.style.fontSize = \"12pt\", this.fakeElem.style.border = \"0\", this.fakeElem.style.padding = \"0\", this.fakeElem.style.margin = \"0\", this.fakeElem.style.position = \"absolute\", this.fakeElem.style[e ? \"right\" : \"left\"] = \"-9999px\";\n          var n = window.pageYOffset || document.documentElement.scrollTop;\n          this.fakeElem.style.top = n + \"px\", this.fakeElem.setAttribute(\"readonly\", \"\"), this.fakeElem.value = this.text, this.container.appendChild(this.fakeElem), this.selectedText = r()(this.fakeElem), this.copyText();\n        }\n      }, {\n        key: \"removeFake\",\n        value: function () {\n          this.fakeHandler && (this.container.removeEventListener(\"click\", this.fakeHandlerCallback), this.fakeHandler = null, this.fakeHandlerCallback = null), this.fakeElem && (this.container.removeChild(this.fakeElem), this.fakeElem = null);\n        }\n      }, {\n        key: \"selectTarget\",\n        value: function () {\n          this.selectedText = r()(this.target), this.copyText();\n        }\n      }, {\n        key: \"copyText\",\n        value: function () {\n          var e = void 0;\n\n          try {\n            e = document.execCommand(this.action);\n          } catch (t) {\n            e = !1;\n          }\n\n          this.handleResult(e);\n        }\n      }, {\n        key: \"handleResult\",\n        value: function (t) {\n          this.emitter.emit(t ? \"success\" : \"error\", {\n            action: this.action,\n            text: this.selectedText,\n            trigger: this.trigger,\n            clearSelection: this.clearSelection.bind(this)\n          });\n        }\n      }, {\n        key: \"clearSelection\",\n        value: function () {\n          this.trigger && this.trigger.focus(), document.activeElement.blur(), window.getSelection().removeAllRanges();\n        }\n      }, {\n        key: \"destroy\",\n        value: function () {\n          this.removeFake();\n        }\n      }, {\n        key: \"action\",\n        set: function (t) {\n          var e = 0 < arguments.length && void 0 !== t ? t : \"copy\";\n          if (this._action = e, \"copy\" !== this._action && \"cut\" !== this._action) throw new Error('Invalid \"action\" value, use either \"copy\" or \"cut\"');\n        },\n        get: function () {\n          return this._action;\n        }\n      }, {\n        key: \"target\",\n        set: function (t) {\n          if (void 0 !== t) {\n            if (!t || \"object\" !== (void 0 === t ? \"undefined\" : i(t)) || 1 !== t.nodeType) throw new Error('Invalid \"target\" value, use a valid Element');\n            if (\"copy\" === this.action && t.hasAttribute(\"disabled\")) throw new Error('Invalid \"target\" attribute. Please use \"readonly\" instead of \"disabled\" attribute');\n            if (\"cut\" === this.action && (t.hasAttribute(\"readonly\") || t.hasAttribute(\"disabled\"))) throw new Error('Invalid \"target\" attribute. You can\\'t cut text from elements with \"readonly\" or \"disabled\" attributes');\n            this._target = t;\n          }\n        },\n        get: function () {\n          return this._target;\n        }\n      }]), c),\n          u = n(1),\n          s = n.n(u),\n          f = n(2),\n          d = n.n(f),\n          h = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (t) {\n        return typeof t;\n      } : function (t) {\n        return t && \"function\" == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? \"symbol\" : typeof t;\n      },\n          p = function (t, e, n) {\n        return e && y(t.prototype, e), n && y(t, n), t;\n      };\n\n      function y(t, e) {\n        for (var n = 0; n < e.length; n++) {\n          var o = e[n];\n          o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(t, o.key, o);\n        }\n      }\n\n      var m = (function (t, e) {\n        if (\"function\" != typeof e && null !== e) throw new TypeError(\"Super expression must either be null or a function, not \" + typeof e);\n        t.prototype = Object.create(e && e.prototype, {\n          constructor: {\n            value: t,\n            enumerable: !1,\n            writable: !0,\n            configurable: !0\n          }\n        }), e && (Object.setPrototypeOf ? Object.setPrototypeOf(t, e) : t.__proto__ = e);\n      }(v, s.a), p(v, [{\n        key: \"resolveOptions\",\n        value: function (t) {\n          var e = 0 < arguments.length && void 0 !== t ? t : {};\n          this.action = \"function\" == typeof e.action ? e.action : this.defaultAction, this.target = \"function\" == typeof e.target ? e.target : this.defaultTarget, this.text = \"function\" == typeof e.text ? e.text : this.defaultText, this.container = \"object\" === h(e.container) ? e.container : document.body;\n        }\n      }, {\n        key: \"listenClick\",\n        value: function (t) {\n          var e = this;\n          this.listener = d()(t, \"click\", function (t) {\n            return e.onClick(t);\n          });\n        }\n      }, {\n        key: \"onClick\",\n        value: function (t) {\n          var e = t.delegateTarget || t.currentTarget;\n          this.clipboardAction && (this.clipboardAction = null), this.clipboardAction = new l({\n            action: this.action(e),\n            target: this.target(e),\n            text: this.text(e),\n            container: this.container,\n            trigger: e,\n            emitter: this\n          });\n        }\n      }, {\n        key: \"defaultAction\",\n        value: function (t) {\n          return b(\"action\", t);\n        }\n      }, {\n        key: \"defaultTarget\",\n        value: function (t) {\n          var e = b(\"target\", t);\n          if (e) return document.querySelector(e);\n        }\n      }, {\n        key: \"defaultText\",\n        value: function (t) {\n          return b(\"text\", t);\n        }\n      }, {\n        key: \"destroy\",\n        value: function () {\n          this.listener.destroy(), this.clipboardAction && (this.clipboardAction.destroy(), this.clipboardAction = null);\n        }\n      }], [{\n        key: \"isSupported\",\n        value: function (t) {\n          var e = 0 < arguments.length && void 0 !== t ? t : [\"copy\", \"cut\"],\n              n = \"string\" == typeof e ? [e] : e,\n              o = !!document.queryCommandSupported;\n          return n.forEach(function (t) {\n            o = o && !!document.queryCommandSupported(t);\n          }), o;\n        }\n      }]), v);\n\n      function v(t, e) {\n        !function (t, e) {\n          if (!(t instanceof e)) throw new TypeError(\"Cannot call a class as a function\");\n        }(this, v);\n\n        var n = function (t, e) {\n          if (!t) throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n          return !e || \"object\" != typeof e && \"function\" != typeof e ? t : e;\n        }(this, (v.__proto__ || Object.getPrototypeOf(v)).call(this));\n\n        return n.resolveOptions(e), n.listenClick(t), n;\n      }\n\n      function b(t, e) {\n        var n = \"data-clipboard-\" + t;\n        if (e.hasAttribute(n)) return e.getAttribute(n);\n      }\n\n      e.default = m;\n    }], r.c = o, r.d = function (t, e, n) {\n      r.o(t, e) || Object.defineProperty(t, e, {\n        enumerable: !0,\n        get: n\n      });\n    }, r.r = function (t) {\n      \"undefined\" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(t, Symbol.toStringTag, {\n        value: \"Module\"\n      }), Object.defineProperty(t, \"__esModule\", {\n        value: !0\n      });\n    }, r.t = function (e, t) {\n      if (1 & t && (e = r(e)), 8 & t) return e;\n      if (4 & t && \"object\" == typeof e && e && e.__esModule) return e;\n      var n = Object.create(null);\n      if (r.r(n), Object.defineProperty(n, \"default\", {\n        enumerable: !0,\n        value: e\n      }), 2 & t && \"string\" != typeof e) for (var o in e) r.d(n, o, function (t) {\n        return e[t];\n      }.bind(null, o));\n      return n;\n    }, r.n = function (t) {\n      var e = t && t.__esModule ? function () {\n        return t.default;\n      } : function () {\n        return t;\n      };\n      return r.d(e, \"a\", e), e;\n    }, r.o = function (t, e) {\n      return Object.prototype.hasOwnProperty.call(t, e);\n    }, r.p = \"\", r(r.s = 6).default;\n\n    function r(t) {\n      if (o[t]) return o[t].exports;\n      var e = o[t] = {\n        i: t,\n        l: !1,\n        exports: {}\n      };\n      return n[t].call(e.exports, e, e.exports, r), e.l = !0, e.exports;\n    }\n\n    var n, o;\n  });\n});\nvar Clipboard = unwrapExports(clipboard_min);\nvar clipboard_min_1 = clipboard_min.ClipboardJS;\n\nvar VueClipboardConfig = {\n  autoSetContainer: false,\n  appendToBody: true\n};\nfunction copyText(_text, container, callback) {\n  var fakeElement = document.createElement('button');\n  var clipboard = new Clipboard(fakeElement, {\n    text: function text() {\n      return _text;\n    },\n    action: function action() {\n      return 'copy';\n    },\n    container: _typeof(container) === 'object' ? container : document.body\n  });\n  clipboard.on('success', function (e) {\n    clipboard.destroy();\n    callback(undefined, e);\n  });\n  clipboard.on('error', function (e) {\n    clipboard.destroy();\n    callback(e, undefined);\n  });\n  if (VueClipboardConfig.appendToBody) document.body.appendChild(fakeElement);\n  fakeElement.click();\n  if (VueClipboardConfig.appendToBody) document.body.removeChild(fakeElement);\n}\nfunction index (app, vueClipboardConfig) {\n  VueClipboardConfig = vueClipboardConfig;\n  app.config.globalProperties.$copyText = copyText;\n  app.directive('clipboard', {\n    mounted: function mounted(el, binding) {\n      if (binding.arg === 'success') {\n        el._vClipboard_success = binding.value;\n      } else if (binding.arg === 'error') {\n        el._vClipboard_error = binding.value;\n      } else {\n        var clipboard = new Clipboard(el, {\n          text: function text() {\n            return binding.value;\n          },\n          action: function action() {\n            return binding.arg === 'cut' ? 'cut' : 'copy';\n          },\n          container: vueClipboardConfig.autoSetContainer ? el : undefined\n        });\n        clipboard.on('success', function (e) {\n          var callback = el._vClipboard_success;\n          callback && callback(e);\n        });\n        clipboard.on('error', function (e) {\n          var callback = el._vClipboard_error;\n          callback && callback(e);\n        });\n        el._vClipboard = clipboard;\n      }\n    },\n    updated: function updated(el, binding) {\n      if (binding.arg === 'success') {\n        el._vClipboard_success = binding.value;\n      } else if (binding.arg === 'error') {\n        el._vClipboard_error = binding.value;\n      } else {\n        el._vClipboard.text = function () {\n          return binding.value;\n        };\n\n        el._vClipboard.action = function () {\n          return binding.arg === 'cut' ? 'cut' : 'copy';\n        };\n      }\n    },\n    unmounted: function unmounted(el, binding) {\n      if (binding.arg === 'success') {\n        delete el._vClipboard_success;\n      } else if (binding.arg === 'error') {\n        delete el._vClipboard_error;\n      } else {\n        el._vClipboard.destroy();\n\n        delete el._vClipboard;\n      }\n    }\n  });\n}\n\nexport default index;\nexport { copyText };\n"], "mappings": ";;;AAAA,SAAS,QAAQ,KAAK;AACpB;AAEA,MAAI,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,UAAU;AACvE,cAAU,SAAUA,MAAK;AACvB,aAAO,OAAOA;AAAA,IAChB;AAAA,EACF,OAAO;AACL,cAAU,SAAUA,MAAK;AACvB,aAAOA,QAAO,OAAO,WAAW,cAAcA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,IAC3H;AAAA,EACF;AAEA,SAAO,QAAQ,GAAG;AACpB;AAEA,IAAI,iBAAiB,OAAO,eAAe,cAAc,aAAa,OAAO,WAAW,cAAc,SAAS,OAAO,WAAW,cAAc,SAAS,OAAO,SAAS,cAAc,OAAO,CAAC;AAE9L,SAAS,cAAe,GAAG;AAC1B,SAAO,KAAK,EAAE,cAAc,OAAO,UAAU,eAAe,KAAK,GAAG,SAAS,IAAI,EAAE,SAAS,IAAI;AACjG;AAEA,SAAS,qBAAqB,IAAI,QAAQ;AACzC,SAAO,SAAS,EAAE,SAAS,CAAC,EAAE,GAAG,GAAG,QAAQ,OAAO,OAAO,GAAG,OAAO;AACrE;AAEA,IAAI,gBAAgB,qBAAqB,SAAU,QAAQ,SAAS;AAOlE,GAAC,SAAU,GAAG,GAAG;AACd,WAAO,UAAU,EAAE;AAAA,EACtB,EAAE,gBAAgB,WAAY;AAC5B,WAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,SAAU,GAAG,GAAG;AACxC,QAAE,UAAU,SAAUC,IAAG;AACvB,YAAIC;AACJ,YAAI,aAAaD,GAAE,SAAU,CAAAA,GAAE,MAAM,GAAGC,KAAID,GAAE;AAAA,iBAAe,YAAYA,GAAE,YAAY,eAAeA,GAAE,UAAU;AAChH,cAAIE,KAAIF,GAAE,aAAa,UAAU;AACjC,UAAAE,MAAKF,GAAE,aAAa,YAAY,EAAE,GAAGA,GAAE,OAAO,GAAGA,GAAE,kBAAkB,GAAGA,GAAE,MAAM,MAAM,GAAGE,MAAKF,GAAE,gBAAgB,UAAU,GAAGC,KAAID,GAAE;AAAA,QACrI,OAAO;AACL,UAAAA,GAAE,aAAa,iBAAiB,KAAKA,GAAE,MAAM;AAC7C,cAAIG,KAAI,OAAO,aAAa,GACxBC,KAAI,SAAS,YAAY;AAC7B,UAAAA,GAAE,mBAAmBJ,EAAC,GAAGG,GAAE,gBAAgB,GAAGA,GAAE,SAASC,EAAC,GAAGH,KAAIE,GAAE,SAAS;AAAA,QAC9E;AACA,eAAOF;AAAA,MACT;AAAA,IACF,GAAG,SAAU,GAAG,GAAG;AACjB,eAASC,KAAI;AAAA,MAAC;AAEd,MAAAA,GAAE,YAAY;AAAA,QACZ,IAAI,SAAUF,IAAGC,IAAGC,IAAG;AACrB,cAAIC,KAAI,KAAK,MAAM,KAAK,IAAI,CAAC;AAC7B,kBAAQA,GAAEH,EAAC,MAAMG,GAAEH,EAAC,IAAI,CAAC,IAAI,KAAK;AAAA,YAChC,IAAIC;AAAA,YACJ,KAAKC;AAAA,UACP,CAAC,GAAG;AAAA,QACN;AAAA,QACA,MAAM,SAAUF,IAAGC,IAAGC,IAAG;AACvB,cAAIC,KAAI;AAER,mBAASC,KAAI;AACX,YAAAD,GAAE,IAAIH,IAAGI,EAAC,GAAGH,GAAE,MAAMC,IAAG,SAAS;AAAA,UACnC;AAEA,iBAAOE,GAAE,IAAIH,IAAG,KAAK,GAAGD,IAAGI,IAAGF,EAAC;AAAA,QACjC;AAAA,QACA,MAAM,SAAUF,IAAG;AACjB,mBAASC,KAAI,CAAC,EAAE,MAAM,KAAK,WAAW,CAAC,GAAGC,OAAM,KAAK,MAAM,KAAK,IAAI,CAAC,IAAIF,EAAC,KAAK,CAAC,GAAG,MAAM,GAAGG,KAAI,GAAGC,KAAIF,GAAE,QAAQC,KAAIC,IAAGD,KAAK,CAAAD,GAAEC,EAAC,EAAE,GAAG,MAAMD,GAAEC,EAAC,EAAE,KAAKF,EAAC;AAEtJ,iBAAO;AAAA,QACT;AAAA,QACA,KAAK,SAAUD,IAAGC,IAAG;AACnB,cAAIC,KAAI,KAAK,MAAM,KAAK,IAAI,CAAC,IACzBC,KAAID,GAAEF,EAAC,GACPI,KAAI,CAAC;AACT,cAAID,MAAKF,GAAG,UAAS,IAAI,GAAG,IAAIE,GAAE,QAAQ,IAAI,GAAG,IAAK,CAAAA,GAAE,CAAC,EAAE,OAAOF,MAAKE,GAAE,CAAC,EAAE,GAAG,MAAMF,MAAKG,GAAE,KAAKD,GAAE,CAAC,CAAC;AACrG,iBAAOC,GAAE,SAASF,GAAEF,EAAC,IAAII,KAAI,OAAOF,GAAEF,EAAC,GAAG;AAAA,QAC5C;AAAA,MACF,GAAG,EAAE,UAAUE,IAAG,EAAE,QAAQ,cAAcA;AAAA,IAC5C,GAAG,SAAU,GAAG,GAAGA,IAAG;AACpB,UAAI,IAAIA,GAAE,CAAC,GACP,IAAIA,GAAE,CAAC;AAEX,QAAE,UAAU,SAAUF,IAAGC,IAAGC,IAAG;AAC7B,YAAI,CAACF,MAAK,CAACC,MAAK,CAACC,GAAG,OAAM,IAAI,MAAM,4BAA4B;AAChE,YAAI,CAAC,EAAE,OAAOD,EAAC,EAAG,OAAM,IAAI,UAAU,kCAAkC;AACxE,YAAI,CAAC,EAAE,GAAGC,EAAC,EAAG,OAAM,IAAI,UAAU,mCAAmC;AACrE,YAAI,EAAE,KAAKF,EAAC,EAAG,QAAO,IAAIC,IAAG,IAAIC,KAAI,IAAIF,IAAG,iBAAiB,GAAG,CAAC,GAAG;AAAA,UAClE,SAAS,WAAY;AACnB,cAAE,oBAAoB,GAAG,CAAC;AAAA,UAC5B;AAAA,QACF;AACA,YAAI,EAAE,SAASA,EAAC,EAAG,QAAO,IAAIA,IAAG,IAAIC,IAAG,IAAIC,IAAG,MAAM,UAAU,QAAQ,KAAK,GAAG,SAAUF,IAAG;AAC1F,UAAAA,GAAE,iBAAiB,GAAG,CAAC;AAAA,QACzB,CAAC,GAAG;AAAA,UACF,SAAS,WAAY;AACnB,kBAAM,UAAU,QAAQ,KAAK,GAAG,SAAUA,IAAG;AAC3C,cAAAA,GAAE,oBAAoB,GAAG,CAAC;AAAA,YAC5B,CAAC;AAAA,UACH;AAAA,QACF;AACA,YAAI,EAAE,OAAOA,EAAC,EAAG,QAAOG,KAAIH,IAAGI,KAAIH,IAAG,IAAIC,IAAG,EAAE,SAAS,MAAMC,IAAGC,IAAG,CAAC;AACrE,cAAM,IAAI,UAAU,2EAA2E;AAC/F,YAAID,IAAGC,IAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAAA,MAC9B;AAAA,IACF,GAAG,SAAU,GAAGF,IAAG;AACjB,MAAAA,GAAE,OAAO,SAAUF,IAAG;AACpB,eAAO,WAAWA,MAAKA,cAAa,eAAe,MAAMA,GAAE;AAAA,MAC7D,GAAGE,GAAE,WAAW,SAAUF,IAAG;AAC3B,YAAI,IAAI,OAAO,UAAU,SAAS,KAAKA,EAAC;AACxC,eAAO,WAAWA,OAAM,wBAAwB,KAAK,8BAA8B,MAAM,YAAYA,OAAM,MAAMA,GAAE,UAAUE,GAAE,KAAKF,GAAE,CAAC,CAAC;AAAA,MAC1I,GAAGE,GAAE,SAAS,SAAUF,IAAG;AACzB,eAAO,YAAY,OAAOA,MAAKA,cAAa;AAAA,MAC9C,GAAGE,GAAE,KAAK,SAAUF,IAAG;AACrB,eAAO,wBAAwB,OAAO,UAAU,SAAS,KAAKA,EAAC;AAAA,MACjE;AAAA,IACF,GAAG,SAAU,GAAG,GAAGE,IAAG;AACpB,UAAI,IAAIA,GAAE,CAAC;AAEX,eAAS,EAAEF,IAAGC,IAAGC,IAAGC,IAAGC,IAAG;AACxB,YAAIC,KAAI,SAAUJ,IAAGC,IAAGF,IAAGG,IAAG;AAC5B,iBAAO,SAAUH,IAAG;AAClB,YAAAA,GAAE,iBAAiB,EAAEA,GAAE,QAAQE,EAAC,GAAGF,GAAE,kBAAkBG,GAAE,KAAKF,IAAGD,EAAC;AAAA,UACpE;AAAA,QACF,EAAE,MAAM,MAAM,SAAS;AAEvB,eAAOA,GAAE,iBAAiBE,IAAGG,IAAGD,EAAC,GAAG;AAAA,UAClC,SAAS,WAAY;AACnB,YAAAJ,GAAE,oBAAoBE,IAAGG,IAAGD,EAAC;AAAA,UAC/B;AAAA,QACF;AAAA,MACF;AAEA,QAAE,UAAU,SAAUJ,IAAGC,IAAGC,IAAGC,IAAGC,IAAG;AACnC,eAAO,cAAc,OAAOJ,GAAE,mBAAmB,EAAE,MAAM,MAAM,SAAS,IAAI,cAAc,OAAOE,KAAI,EAAE,KAAK,MAAM,QAAQ,EAAE,MAAM,MAAM,SAAS,KAAK,YAAY,OAAOF,OAAMA,KAAI,SAAS,iBAAiBA,EAAC,IAAI,MAAM,UAAU,IAAI,KAAKA,IAAG,SAAUA,IAAG;AACzP,iBAAO,EAAEA,IAAGC,IAAGC,IAAGC,IAAGC,EAAC;AAAA,QACxB,CAAC;AAAA,MACH;AAAA,IACF,GAAG,SAAU,GAAG,GAAG;AACjB,UAAI,eAAe,OAAO,WAAW,CAAC,QAAQ,UAAU,SAAS;AAC/D,YAAIF,KAAI,QAAQ;AAChB,QAAAA,GAAE,UAAUA,GAAE,mBAAmBA,GAAE,sBAAsBA,GAAE,qBAAqBA,GAAE,oBAAoBA,GAAE;AAAA,MAC1G;AAEA,QAAE,UAAU,SAAUF,IAAGC,IAAG;AAC1B,eAAOD,MAAK,MAAMA,GAAE,YAAW;AAC7B,cAAI,cAAc,OAAOA,GAAE,WAAWA,GAAE,QAAQC,EAAC,EAAG,QAAOD;AAC3D,UAAAA,KAAIA,GAAE;AAAA,QACR;AAAA,MACF;AAAA,IACF,GAAG,SAAU,GAAG,GAAGE,IAAG;AAEpB,MAAAA,GAAE,EAAE,CAAC;AACL,UAAIC,KAAID,GAAE,CAAC,GACPE,KAAIF,GAAE,EAAEC,EAAC,GACT,IAAI,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUH,IAAG;AACvF,eAAO,OAAOA;AAAA,MAChB,IAAI,SAAUA,IAAG;AACf,eAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,MACpH;AAEA,eAAS,EAAEA,IAAGC,IAAG;AACf,iBAASC,KAAI,GAAGA,KAAID,GAAE,QAAQC,MAAK;AACjC,cAAIC,KAAIF,GAAEC,EAAC;AACX,UAAAC,GAAE,aAAaA,GAAE,cAAc,OAAIA,GAAE,eAAe,MAAI,WAAWA,OAAMA,GAAE,WAAW,OAAK,OAAO,eAAeH,IAAGG,GAAE,KAAKA,EAAC;AAAA,QAC9H;AAAA,MACF;AAEA,eAAS,EAAEH,IAAG;AACZ,SAAC,SAAUA,IAAGC,IAAG;AACf,cAAI,EAAED,cAAaC,IAAI,OAAM,IAAI,UAAU,mCAAmC;AAAA,QAChF,EAAE,MAAM,CAAC,GAAG,KAAK,eAAeD,EAAC,GAAG,KAAK,cAAc;AAAA,MACzD;AAEA,UAAI,KAAK,SAAUA,IAAGC,IAAGC,IAAG;AAC1B,eAAOD,MAAK,EAAED,GAAE,WAAWC,EAAC,GAAGC,MAAK,EAAEF,IAAGE,EAAC,GAAGF;AAAA,MAC/C,EAAE,GAAG,CAAC;AAAA,QACJ,KAAK;AAAA,QACL,OAAO,SAAUA,IAAG;AAClB,cAAIC,KAAI,IAAI,UAAU,UAAU,WAAWD,KAAIA,KAAI,CAAC;AACpD,eAAK,SAASC,GAAE,QAAQ,KAAK,YAAYA,GAAE,WAAW,KAAK,UAAUA,GAAE,SAAS,KAAK,SAASA,GAAE,QAAQ,KAAK,OAAOA,GAAE,MAAM,KAAK,UAAUA,GAAE,SAAS,KAAK,eAAe;AAAA,QAC5K;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,WAAY;AACjB,eAAK,OAAO,KAAK,WAAW,IAAI,KAAK,UAAU,KAAK,aAAa;AAAA,QACnE;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,WAAY;AACjB,cAAID,KAAI,MACJC,KAAI,SAAS,SAAS,gBAAgB,aAAa,KAAK;AAC5D,eAAK,WAAW,GAAG,KAAK,sBAAsB,WAAY;AACxD,mBAAOD,GAAE,WAAW;AAAA,UACtB,GAAG,KAAK,cAAc,KAAK,UAAU,iBAAiB,SAAS,KAAK,mBAAmB,KAAK,MAAI,KAAK,WAAW,SAAS,cAAc,UAAU,GAAG,KAAK,SAAS,MAAM,WAAW,QAAQ,KAAK,SAAS,MAAM,SAAS,KAAK,KAAK,SAAS,MAAM,UAAU,KAAK,KAAK,SAAS,MAAM,SAAS,KAAK,KAAK,SAAS,MAAM,WAAW,YAAY,KAAK,SAAS,MAAMC,KAAI,UAAU,MAAM,IAAI;AACzX,cAAIC,KAAI,OAAO,eAAe,SAAS,gBAAgB;AACvD,eAAK,SAAS,MAAM,MAAMA,KAAI,MAAM,KAAK,SAAS,aAAa,YAAY,EAAE,GAAG,KAAK,SAAS,QAAQ,KAAK,MAAM,KAAK,UAAU,YAAY,KAAK,QAAQ,GAAG,KAAK,eAAeE,GAAE,EAAE,KAAK,QAAQ,GAAG,KAAK,SAAS;AAAA,QACpN;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,WAAY;AACjB,eAAK,gBAAgB,KAAK,UAAU,oBAAoB,SAAS,KAAK,mBAAmB,GAAG,KAAK,cAAc,MAAM,KAAK,sBAAsB,OAAO,KAAK,aAAa,KAAK,UAAU,YAAY,KAAK,QAAQ,GAAG,KAAK,WAAW;AAAA,QACtO;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,WAAY;AACjB,eAAK,eAAeA,GAAE,EAAE,KAAK,MAAM,GAAG,KAAK,SAAS;AAAA,QACtD;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,WAAY;AACjB,cAAIH,KAAI;AAER,cAAI;AACF,YAAAA,KAAI,SAAS,YAAY,KAAK,MAAM;AAAA,UACtC,SAASD,IAAG;AACV,YAAAC,KAAI;AAAA,UACN;AAEA,eAAK,aAAaA,EAAC;AAAA,QACrB;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAUD,IAAG;AAClB,eAAK,QAAQ,KAAKA,KAAI,YAAY,SAAS;AAAA,YACzC,QAAQ,KAAK;AAAA,YACb,MAAM,KAAK;AAAA,YACX,SAAS,KAAK;AAAA,YACd,gBAAgB,KAAK,eAAe,KAAK,IAAI;AAAA,UAC/C,CAAC;AAAA,QACH;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,WAAY;AACjB,eAAK,WAAW,KAAK,QAAQ,MAAM,GAAG,SAAS,cAAc,KAAK,GAAG,OAAO,aAAa,EAAE,gBAAgB;AAAA,QAC7G;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,WAAY;AACjB,eAAK,WAAW;AAAA,QAClB;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,KAAK,SAAUA,IAAG;AAChB,cAAIC,KAAI,IAAI,UAAU,UAAU,WAAWD,KAAIA,KAAI;AACnD,cAAI,KAAK,UAAUC,IAAG,WAAW,KAAK,WAAW,UAAU,KAAK,QAAS,OAAM,IAAI,MAAM,oDAAoD;AAAA,QAC/I;AAAA,QACA,KAAK,WAAY;AACf,iBAAO,KAAK;AAAA,QACd;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,KAAK,SAAUD,IAAG;AAChB,cAAI,WAAWA,IAAG;AAChB,gBAAI,CAACA,MAAK,cAAc,WAAWA,KAAI,cAAc,EAAEA,EAAC,MAAM,MAAMA,GAAE,SAAU,OAAM,IAAI,MAAM,6CAA6C;AAC7I,gBAAI,WAAW,KAAK,UAAUA,GAAE,aAAa,UAAU,EAAG,OAAM,IAAI,MAAM,mFAAmF;AAC7J,gBAAI,UAAU,KAAK,WAAWA,GAAE,aAAa,UAAU,KAAKA,GAAE,aAAa,UAAU,GAAI,OAAM,IAAI,MAAM,uGAAwG;AACjN,iBAAK,UAAUA;AAAA,UACjB;AAAA,QACF;AAAA,QACA,KAAK,WAAY;AACf,iBAAO,KAAK;AAAA,QACd;AAAA,MACF,CAAC,CAAC,GAAG,IACD,IAAIE,GAAE,CAAC,GACP,IAAIA,GAAE,EAAE,CAAC,GACT,IAAIA,GAAE,CAAC,GACP,IAAIA,GAAE,EAAE,CAAC,GACT,IAAI,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUF,IAAG;AACvF,eAAO,OAAOA;AAAA,MAChB,IAAI,SAAUA,IAAG;AACf,eAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,MACpH,GACI,IAAI,SAAUA,IAAGC,IAAGC,IAAG;AACzB,eAAOD,MAAK,EAAED,GAAE,WAAWC,EAAC,GAAGC,MAAK,EAAEF,IAAGE,EAAC,GAAGF;AAAA,MAC/C;AAEA,eAAS,EAAEA,IAAGC,IAAG;AACf,iBAASC,KAAI,GAAGA,KAAID,GAAE,QAAQC,MAAK;AACjC,cAAIC,KAAIF,GAAEC,EAAC;AACX,UAAAC,GAAE,aAAaA,GAAE,cAAc,OAAIA,GAAE,eAAe,MAAI,WAAWA,OAAMA,GAAE,WAAW,OAAK,OAAO,eAAeH,IAAGG,GAAE,KAAKA,EAAC;AAAA,QAC9H;AAAA,MACF;AAEA,UAAI,KAAK,SAAUH,IAAGC,IAAG;AACvB,YAAI,cAAc,OAAOA,MAAK,SAASA,GAAG,OAAM,IAAI,UAAU,6DAA6D,OAAOA,EAAC;AACnI,QAAAD,GAAE,YAAY,OAAO,OAAOC,MAAKA,GAAE,WAAW;AAAA,UAC5C,aAAa;AAAA,YACX,OAAOD;AAAA,YACP,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,cAAc;AAAA,UAChB;AAAA,QACF,CAAC,GAAGC,OAAM,OAAO,iBAAiB,OAAO,eAAeD,IAAGC,EAAC,IAAID,GAAE,YAAYC;AAAA,MAChF,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;AAAA,QACf,KAAK;AAAA,QACL,OAAO,SAAUD,IAAG;AAClB,cAAIC,KAAI,IAAI,UAAU,UAAU,WAAWD,KAAIA,KAAI,CAAC;AACpD,eAAK,SAAS,cAAc,OAAOC,GAAE,SAASA,GAAE,SAAS,KAAK,eAAe,KAAK,SAAS,cAAc,OAAOA,GAAE,SAASA,GAAE,SAAS,KAAK,eAAe,KAAK,OAAO,cAAc,OAAOA,GAAE,OAAOA,GAAE,OAAO,KAAK,aAAa,KAAK,YAAY,aAAa,EAAEA,GAAE,SAAS,IAAIA,GAAE,YAAY,SAAS;AAAA,QACvS;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAUD,IAAG;AAClB,cAAIC,KAAI;AACR,eAAK,WAAW,EAAE,EAAED,IAAG,SAAS,SAAUA,IAAG;AAC3C,mBAAOC,GAAE,QAAQD,EAAC;AAAA,UACpB,CAAC;AAAA,QACH;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAUA,IAAG;AAClB,cAAIC,KAAID,GAAE,kBAAkBA,GAAE;AAC9B,eAAK,oBAAoB,KAAK,kBAAkB,OAAO,KAAK,kBAAkB,IAAI,EAAE;AAAA,YAClF,QAAQ,KAAK,OAAOC,EAAC;AAAA,YACrB,QAAQ,KAAK,OAAOA,EAAC;AAAA,YACrB,MAAM,KAAK,KAAKA,EAAC;AAAA,YACjB,WAAW,KAAK;AAAA,YAChB,SAASA;AAAA,YACT,SAAS;AAAA,UACX,CAAC;AAAA,QACH;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAUD,IAAG;AAClB,iBAAO,EAAE,UAAUA,EAAC;AAAA,QACtB;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAUA,IAAG;AAClB,cAAIC,KAAI,EAAE,UAAUD,EAAC;AACrB,cAAIC,GAAG,QAAO,SAAS,cAAcA,EAAC;AAAA,QACxC;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAUD,IAAG;AAClB,iBAAO,EAAE,QAAQA,EAAC;AAAA,QACpB;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,WAAY;AACjB,eAAK,SAAS,QAAQ,GAAG,KAAK,oBAAoB,KAAK,gBAAgB,QAAQ,GAAG,KAAK,kBAAkB;AAAA,QAC3G;AAAA,MACF,CAAC,GAAG,CAAC;AAAA,QACH,KAAK;AAAA,QACL,OAAO,SAAUA,IAAG;AAClB,cAAIC,KAAI,IAAI,UAAU,UAAU,WAAWD,KAAIA,KAAI,CAAC,QAAQ,KAAK,GAC7DE,KAAI,YAAY,OAAOD,KAAI,CAACA,EAAC,IAAIA,IACjCE,KAAI,CAAC,CAAC,SAAS;AACnB,iBAAOD,GAAE,QAAQ,SAAUF,IAAG;AAC5B,YAAAG,KAAIA,MAAK,CAAC,CAAC,SAAS,sBAAsBH,EAAC;AAAA,UAC7C,CAAC,GAAGG;AAAA,QACN;AAAA,MACF,CAAC,CAAC,GAAG;AAEL,eAAS,EAAEH,IAAGC,IAAG;AACf,SAAC,SAAUD,IAAGC,IAAG;AACf,cAAI,EAAED,cAAaC,IAAI,OAAM,IAAI,UAAU,mCAAmC;AAAA,QAChF,EAAE,MAAM,CAAC;AAET,YAAIC,KAAI,SAAUF,IAAGC,IAAG;AACtB,cAAI,CAACD,GAAG,OAAM,IAAI,eAAe,2DAA2D;AAC5F,iBAAO,CAACC,MAAK,YAAY,OAAOA,MAAK,cAAc,OAAOA,KAAID,KAAIC;AAAA,QACpE,EAAE,OAAO,EAAE,aAAa,OAAO,eAAe,CAAC,GAAG,KAAK,IAAI,CAAC;AAE5D,eAAOC,GAAE,eAAeD,EAAC,GAAGC,GAAE,YAAYF,EAAC,GAAGE;AAAA,MAChD;AAEA,eAAS,EAAEF,IAAGC,IAAG;AACf,YAAIC,KAAI,oBAAoBF;AAC5B,YAAIC,GAAE,aAAaC,EAAC,EAAG,QAAOD,GAAE,aAAaC,EAAC;AAAA,MAChD;AAEA,QAAE,UAAU;AAAA,IACd,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,IAAI,SAAU,GAAG,GAAGA,IAAG;AACpC,QAAE,EAAE,GAAG,CAAC,KAAK,OAAO,eAAe,GAAG,GAAG;AAAA,QACvC,YAAY;AAAA,QACZ,KAAKA;AAAA,MACP,CAAC;AAAA,IACH,GAAG,EAAE,IAAI,SAAU,GAAG;AACpB,qBAAe,OAAO,UAAU,OAAO,eAAe,OAAO,eAAe,GAAG,OAAO,aAAa;AAAA,QACjG,OAAO;AAAA,MACT,CAAC,GAAG,OAAO,eAAe,GAAG,cAAc;AAAA,QACzC,OAAO;AAAA,MACT,CAAC;AAAA,IACH,GAAG,EAAE,IAAI,SAAU,GAAG,GAAG;AACvB,UAAI,IAAI,MAAM,IAAI,EAAE,CAAC,IAAI,IAAI,EAAG,QAAO;AACvC,UAAI,IAAI,KAAK,YAAY,OAAO,KAAK,KAAK,EAAE,WAAY,QAAO;AAC/D,UAAIA,KAAI,uBAAO,OAAO,IAAI;AAC1B,UAAI,EAAE,EAAEA,EAAC,GAAG,OAAO,eAAeA,IAAG,WAAW;AAAA,QAC9C,YAAY;AAAA,QACZ,OAAO;AAAA,MACT,CAAC,GAAG,IAAI,KAAK,YAAY,OAAO,EAAG,UAASC,MAAK,EAAG,GAAE,EAAED,IAAGC,IAAG,SAAUH,IAAG;AACzE,eAAO,EAAEA,EAAC;AAAA,MACZ,EAAE,KAAK,MAAMG,EAAC,CAAC;AACf,aAAOD;AAAA,IACT,GAAG,EAAE,IAAI,SAAU,GAAG;AACpB,UAAI,IAAI,KAAK,EAAE,aAAa,WAAY;AACtC,eAAO,EAAE;AAAA,MACX,IAAI,WAAY;AACd,eAAO;AAAA,MACT;AACA,aAAO,EAAE,EAAE,GAAG,KAAK,CAAC,GAAG;AAAA,IACzB,GAAG,EAAE,IAAI,SAAU,GAAG,GAAG;AACvB,aAAO,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAAA,IAClD,GAAG,EAAE,IAAI,IAAI,EAAE,EAAE,IAAI,CAAC,EAAE;AAExB,aAAS,EAAE,GAAG;AACZ,UAAI,EAAE,CAAC,EAAG,QAAO,EAAE,CAAC,EAAE;AACtB,UAAI,IAAI,EAAE,CAAC,IAAI;AAAA,QACb,GAAG;AAAA,QACH,GAAG;AAAA,QACH,SAAS,CAAC;AAAA,MACZ;AACA,aAAO,EAAE,CAAC,EAAE,KAAK,EAAE,SAAS,GAAG,EAAE,SAAS,CAAC,GAAG,EAAE,IAAI,MAAI,EAAE;AAAA,IAC5D;AAEA,QAAI,GAAG;AAAA,EACT,CAAC;AACH,CAAC;AACD,IAAI,YAAY,cAAc,aAAa;AAC3C,IAAI,kBAAkB,cAAc;AAEpC,IAAI,qBAAqB;AAAA,EACvB,kBAAkB;AAAA,EAClB,cAAc;AAChB;AACA,SAAS,SAAS,OAAO,WAAW,UAAU;AAC5C,MAAI,cAAc,SAAS,cAAc,QAAQ;AACjD,MAAI,YAAY,IAAI,UAAU,aAAa;AAAA,IACzC,MAAM,SAAS,OAAO;AACpB,aAAO;AAAA,IACT;AAAA,IACA,QAAQ,SAAS,SAAS;AACxB,aAAO;AAAA,IACT;AAAA,IACA,WAAW,QAAQ,SAAS,MAAM,WAAW,YAAY,SAAS;AAAA,EACpE,CAAC;AACD,YAAU,GAAG,WAAW,SAAU,GAAG;AACnC,cAAU,QAAQ;AAClB,aAAS,QAAW,CAAC;AAAA,EACvB,CAAC;AACD,YAAU,GAAG,SAAS,SAAU,GAAG;AACjC,cAAU,QAAQ;AAClB,aAAS,GAAG,MAAS;AAAA,EACvB,CAAC;AACD,MAAI,mBAAmB,aAAc,UAAS,KAAK,YAAY,WAAW;AAC1E,cAAY,MAAM;AAClB,MAAI,mBAAmB,aAAc,UAAS,KAAK,YAAY,WAAW;AAC5E;AACA,SAAS,MAAO,KAAK,oBAAoB;AACvC,uBAAqB;AACrB,MAAI,OAAO,iBAAiB,YAAY;AACxC,MAAI,UAAU,aAAa;AAAA,IACzB,SAAS,SAAS,QAAQ,IAAI,SAAS;AACrC,UAAI,QAAQ,QAAQ,WAAW;AAC7B,WAAG,sBAAsB,QAAQ;AAAA,MACnC,WAAW,QAAQ,QAAQ,SAAS;AAClC,WAAG,oBAAoB,QAAQ;AAAA,MACjC,OAAO;AACL,YAAI,YAAY,IAAI,UAAU,IAAI;AAAA,UAChC,MAAM,SAAS,OAAO;AACpB,mBAAO,QAAQ;AAAA,UACjB;AAAA,UACA,QAAQ,SAAS,SAAS;AACxB,mBAAO,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,UACzC;AAAA,UACA,WAAW,mBAAmB,mBAAmB,KAAK;AAAA,QACxD,CAAC;AACD,kBAAU,GAAG,WAAW,SAAU,GAAG;AACnC,cAAI,WAAW,GAAG;AAClB,sBAAY,SAAS,CAAC;AAAA,QACxB,CAAC;AACD,kBAAU,GAAG,SAAS,SAAU,GAAG;AACjC,cAAI,WAAW,GAAG;AAClB,sBAAY,SAAS,CAAC;AAAA,QACxB,CAAC;AACD,WAAG,cAAc;AAAA,MACnB;AAAA,IACF;AAAA,IACA,SAAS,SAAS,QAAQ,IAAI,SAAS;AACrC,UAAI,QAAQ,QAAQ,WAAW;AAC7B,WAAG,sBAAsB,QAAQ;AAAA,MACnC,WAAW,QAAQ,QAAQ,SAAS;AAClC,WAAG,oBAAoB,QAAQ;AAAA,MACjC,OAAO;AACL,WAAG,YAAY,OAAO,WAAY;AAChC,iBAAO,QAAQ;AAAA,QACjB;AAEA,WAAG,YAAY,SAAS,WAAY;AAClC,iBAAO,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,QACzC;AAAA,MACF;AAAA,IACF;AAAA,IACA,WAAW,SAAS,UAAU,IAAI,SAAS;AACzC,UAAI,QAAQ,QAAQ,WAAW;AAC7B,eAAO,GAAG;AAAA,MACZ,WAAW,QAAQ,QAAQ,SAAS;AAClC,eAAO,GAAG;AAAA,MACZ,OAAO;AACL,WAAG,YAAY,QAAQ;AAEvB,eAAO,GAAG;AAAA,MACZ;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAEA,IAAO,4BAAQ;", "names": ["obj", "t", "e", "n", "o", "r", "i"]}